name: Create Release PR

on:
  # push:
  #   branches:
  #     - main
  workflow_dispatch:
    inputs:
      image_tag:
        description: "Image tag (leave empty to use latest tag)"
        required: false
        default: ""

jobs:
  prod-deployment:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Fetch all tags for latest tag detection

      - name: checkout github actions repo
        uses: actions/checkout@v4
        with:
          repository: upkeepapp/github-actions
          ref: main
          path: github-actions
          token: ${{ secrets.TECHOPS_PAT }}

      - name: Get latest tag
        id: get_latest_tag
        run: |
          # Get the latest tag excluding beta tags
          LATEST_TAG=$(git tag --sort=-version:refname | grep -v beta | head -n 1)
          echo "latest_tag=$LATEST_TAG" >> $GITHUB_OUTPUT
          echo "Latest tag: $LATEST_TAG"

      - name: Set image tag
        id: set_image_tag
        run: |
          # Use workflow_dispatch input if provided, otherwise use latest tag
          if [ -n "${{ github.event.inputs.image_tag }}" ]; then
            IMAGE_TAG="${{ github.event.inputs.image_tag }}"
          else
            IMAGE_TAG="${{ steps.get_latest_tag.outputs.latest_tag }}"
          fi
          echo "image_tag=$IMAGE_TAG" >> $GITHUB_OUTPUT
          echo "Using image tag: $IMAGE_TAG"

      - name: Create release PR
        uses: ./github-actions/.github/trigger-workflow
        with:
          owner: upkeepapp
          repo: upkeep
          github_token: ${{ secrets.TECHOPS_PAT }}
          github_user: upkeeptechops
          workflow_file_name: update_tag.yml
          ref: prod/upkeep/ehs-release
          client_payload: '{"repo_name": "ehs", "image_tag": "${{ steps.set_image_tag.outputs.image_tag }}"}'
          propagate_failure: true
          trigger_workflow: true
          wait_workflow: false
