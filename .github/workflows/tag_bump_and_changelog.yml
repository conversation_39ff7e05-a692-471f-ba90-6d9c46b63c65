name: Bump Tag | Add Changelog | Conditionally Generate Release

env:
  GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

on:
  # push:
  #   branches:
  #     # - 'deploy-**'
  #     - 'hotfix-**'
  #     - 'main'
  workflow_dispatch:
jobs:
  build-tags:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.sha }}
          fetch-depth: 0
      - name: Set owner and repo variables
        run: |
          echo "OWNER=$(echo ${{ github.repository }} | cut -d/ -f1)" >> $GITHUB_ENV
          echo "REPO=$(echo ${{ github.repository }} | cut -d/ -f2)" >> $GITHUB_ENV
      - name: Determine Version Bump
        id: intended-version-bump
        env:
          RAW_COMMIT_MESSAGE: ${{ github.event.head_commit.message }}
        run: |
          echo "${RAW_COMMIT_MESSAGE}"
          COMMIT_MSG=$(echo "$RAW_COMMIT_MESSAGE" | tr -d '\n')
          if [[ $COMMIT_MSG == *"hotfix-"* ]] || [[ "${{ github.ref }}" == *"hotfix-"* ]]; then
            echo "bump=patch" >> $GITHUB_OUTPUT
          else
            echo "bump=minor" >> $GITHUB_OUTPUT
          fi
      - name: Bump version and push tag
        id: tag-version
        uses: anothrNick/github-tag-action@f278d49d30cdd8775cc3e7dd00b5ee11686ee297 # v1.71.0
        env:
          GITHUB_TOKEN: ${{ secrets.TECHOPS_PAT }}
          WITH_V: true
          PRERELEASE: ${{ github.ref != 'refs/heads/main' }}
          RELEASE_BRANCHES: main
          DEFAULT_BUMP: ${{ steps.intended-version-bump.outputs.bump }}
      - name: showcase output values
        run: |
          echo "${{ steps.tag-version.outputs.new_tag }}"
          echo "${{ steps.tag-version.outputs.tag }}"
      - name: add changelog to tag and conditionally make release
        run: |
          git config --global user.name "Github Action"
          git config --global user.email "<EMAIL>"
          git fetch origin tag ${{ steps.tag-version.outputs.new_tag }}
          VERSION=$(gh release list | grep "Latest" | cut -f3)
          if [[ "${{ steps.tag-version.outputs.new_tag }}" == "$VERSION" ]]; then
            echo "Version was not bumped, skip this step"
            exit 0
          elif [[ "${{ steps.tag-version.outputs.new_tag }}" == *"beta"* ]] && [[ "${{ steps.tag-version.outputs.new_tag }}" != *"beta.0" ]]; then
            # it means this is a patch.. get the previous beta for the diff
            IFS=. read -r major minor patch rc <<<"${{ steps.tag-version.outputs.new_tag }}"
            ((rc--))
            printf -v VERSION '%s.%s.%s.%d' "$major" "$minor" "$patch" "$((rc))"
          fi
          git fetch origin tag $VERSION
          DIFF="$(git shortlog --no-merges $VERSION..${{ steps.tag-version.outputs.new_tag }})"
          git tag -fm "$DIFF" ${{ steps.tag-version.outputs.new_tag }}
          git push -f origin ${{ steps.tag-version.outputs.new_tag }}
          if [[ "${{ github.ref }}" == 'refs/heads/main' ]]; then
            gh release create ${{ steps.tag-version.outputs.new_tag }} \
              --notes "$DIFF
              [Diff](https://github.com/$OWNER/$REPO/compare/$VERSION...${{ steps.tag-version.outputs.new_tag }})" \
              --title "${{ steps.tag-version.outputs.new_tag }}"
          fi
          echo "$DIFF"
