import { SUPPORTED_MIME_TYPES } from './types/files.types';

export const MAX_FILE_SIZE_MB = 20;
export const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024; // 20MB in bytes

type FileValidationResult = {
  isValid: boolean;
  errors: string[];
};

type FileValidationOptions = {
  maxSize?: number; // in MB
  maxFiles?: number;
  allowedTypes?: string[];
  existingFileCount?: number;
};

/**
 * Validates a single file against the provided constraints
 */
const validateFile = (file: File, options: FileValidationOptions = {}) => {
  const errors: string[] = [];
  const {
    maxSize = MAX_FILE_SIZE_MB, // Default from centralized constants
    allowedTypes = Object.keys(SUPPORTED_MIME_TYPES),
  } = options;

  if (!allowedTypes.includes(file.type)) {
    const supportedFormats = Object.values(SUPPORTED_MIME_TYPES).join(', ');
    errors.push(`File "${file.name}" is not a supported format. Supported: ${supportedFormats}`);
  }

  const maxSizeBytes = maxSize * 1024 * 1024;

  if (file.size === 0) {
    errors.push(`File "${file.name}" is empty`);
  }

  if (file.size > maxSizeBytes) {
    errors.push(
      `File "${file.name}" exceeds the maximum size of ${maxSize}MB. Current size: ${Math.round(file.size / (1024 * 1024))}MB`,
    );
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Validates multiple files including total count constraints
 */
export const validateFiles = (files: FileList | File[], options: FileValidationOptions = {}): FileValidationResult => {
  const errors: string[] = [];
  const { maxFiles = 3, existingFileCount = 0 } = options;

  const fileArray = Array.from(files);

  const totalFiles = fileArray.length + existingFileCount;
  if (totalFiles > maxFiles) {
    errors.push(
      `Cannot upload ${fileArray.length} files. Maximum allowed: ${maxFiles} (${existingFileCount} already uploaded)`,
    );
    return { isValid: false, errors };
  }

  for (const file of fileArray) {
    const fileResult = validateFile(file, options);
    errors.push(...fileResult.errors);
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Gets human-readable error messages for validation results
 */
export const getValidationErrorMessage = (result: FileValidationResult) => {
  if (result.isValid) {
    return null;
  }

  if (result.errors.length === 1) {
    return [result.errors[0]];
  }

  return [`Multiple validation errors:`].concat(result.errors);
};

const bytesToMB = (bytes: number) => {
  return Math.round(bytes / (1024 * 1024));
};

/**
 * Gets a user-friendly error message for file size violations
 */
export const getFileSizeErrorMessage = (fileName: string, fileSizeBytes: number) => {
  if (fileSizeBytes === 0) {
    return `File "${fileName}" is empty`;
  }

  if (fileSizeBytes > MAX_FILE_SIZE_BYTES) {
    return `File "${fileName}" exceeds the maximum size of ${MAX_FILE_SIZE_MB}MB. Current size: ${bytesToMB(fileSizeBytes)}MB`;
  }

  return `File "${fileName}" has an invalid size`;
};
