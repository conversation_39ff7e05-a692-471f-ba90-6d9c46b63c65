import {
  capaEffectivenessStatusEnum,
  capaPriorityEnum,
  capas,
  capaTagsEnum,
  capaTypeEnum,
  rcaMethodEnum,
  rootCauseEnum,
  statusEnum,
} from '@shared/schema';
import { InferSelectModel } from 'drizzle-orm';
import { createInsertSchema, createSelectSchema, createUpdateSchema } from 'drizzle-zod';
import z from 'zod';
import { TransientFileSchema } from './files.types';
import { PaginationInputSchema, SortInputSchema } from './schema.types';

export const CAPA_PRIORITY_MAP: Record<(typeof capaPriorityEnum.enumValues)[number], string> = {
  [capaPriorityEnum.enumValues[0]]: 'High',
  [capaPriorityEnum.enumValues[1]]: 'Medium',
  [capaPriorityEnum.enumValues[2]]: 'Low',
};

export const CAPA_TYPE_MAP: Record<(typeof capaTypeEnum.enumValues)[number], string> = {
  [capaTypeEnum.enumValues[0]]: 'Corrective',
  [capaTypeEnum.enumValues[1]]: 'Preventive',
  [capaTypeEnum.enumValues[2]]: 'Both',
};

export const CAPA_TAGS_MAP: Record<(typeof capaTagsEnum.enumValues)[number], string> = {
  [capaTagsEnum.enumValues[0]]: 'Training',
  [capaTagsEnum.enumValues[1]]: 'Policy',
  [capaTagsEnum.enumValues[2]]: 'Hazard',
  [capaTagsEnum.enumValues[3]]: 'Equipment',
  [capaTagsEnum.enumValues[4]]: 'Procedure',
  [capaTagsEnum.enumValues[5]]: 'Personnel',
  [capaTagsEnum.enumValues[6]]: 'Safety',
};

export const CAPA_EFFECTIVENESS_STATUS_MAP: Record<(typeof capaEffectivenessStatusEnum.enumValues)[number], string> = {
  [capaEffectivenessStatusEnum.enumValues[0]]: 'Effective',
  [capaEffectivenessStatusEnum.enumValues[1]]: 'Partial',
  [capaEffectivenessStatusEnum.enumValues[2]]: 'Not Effective',
  [capaEffectivenessStatusEnum.enumValues[3]]: 'Not Evaluated',
};

export const ROOT_CAUSE_MAP: Record<(typeof rootCauseEnum.enumValues)[number], string> = {
  [rootCauseEnum.enumValues[0]]: 'Human Error',
  [rootCauseEnum.enumValues[1]]: 'Equipment Failure',
  [rootCauseEnum.enumValues[2]]: 'Environmental',
  [rootCauseEnum.enumValues[3]]: 'Procedural',
  [rootCauseEnum.enumValues[4]]: 'Other',
};

export const RCA_METHOD_MAP: Record<(typeof rcaMethodEnum.enumValues)[number], string> = {
  [rcaMethodEnum.enumValues[0]]: '5 Whys',
  [rcaMethodEnum.enumValues[1]]: 'Fishbone Diagram',
  [rcaMethodEnum.enumValues[2]]: 'Fault Tree Analysis',
  [rcaMethodEnum.enumValues[3]]: 'Other Method',
  [rcaMethodEnum.enumValues[4]]: 'Not Selected',
};

export const CapaValidations = {
  title: z.string({ error: 'Title is required' }).min(1, 'Title must be at least 1 character'),
  ownerId: z.string({ error: 'Owner is required' }).min(1),
  type: z.enum(capaTypeEnum.enumValues, {
      error: 'Type is required'
}),
  rcaFindings: z
    .string({ error: 'RCA findings are required' })
    .min(1, 'RCA findings must be at least 1 character'),
  priority: z.enum(capaPriorityEnum.enumValues, {
      error: 'Priority is required'
}),
  status: z.enum(statusEnum.enumValues, {
      error: 'Status is required'
}),
  actionsToAddress: z
    .string({ error: 'Proposed actions are required' })
    .min(1, 'Proposed actions must be at least 1 character'),
};

export const CreateCapasSchema = createInsertSchema(capas, CapaValidations);
export const UpdateCapasSchema = createUpdateSchema(capas);
export const SelectCapasSchema = createSelectSchema(capas);

export type CreateCapa = z.infer<typeof CreateCapasSchema>;
export type Capa = InferSelectModel<typeof capas>;

export const CreateCapasFormSchema = CreateCapasSchema.omit({
  upkeepCompanyId: true,
  createdBy: true,
  slug: true,
  id: true,
  createdAt: true,
  updatedAt: true,
  deletedAt: true,
  aiConfidenceScore: true,
  archivedAt: true,
}).extend({
  attachments: z.array(TransientFileSchema).optional(),
  linkedJhaInstanceIds: z.array(z.uuid()).optional(),
  workOrderIds: z.array(z.string()).optional(),
});

export type CreateCapasForm = z.infer<typeof CreateCapasFormSchema>;

export const EditCapasFormSchema = UpdateCapasSchema.omit({
  upkeepCompanyId: true,
  createdBy: true,
  slug: true,
  createdAt: true,
  updatedAt: true,
  deletedAt: true,
  aiSuggestedAction: true,
}).extend({
  attachments: z.array(TransientFileSchema).optional(),
  id: z.cuid2(),
  linkedJhaInstanceIds: z.array(z.uuid()).optional(),
  workOrderIds: z.array(z.string()).optional(),
});

export type EditCapasForm = z.infer<typeof EditCapasFormSchema>;

export const CapasFiltersSchema = z.object({
  search: z.string().optional(),
  mustIncludeObjectIds: z.array(z.string()).optional(),
  status: z.array(z.enum(statusEnum.enumValues)).optional(),
  type: z.array(z.enum(capaTypeEnum.enumValues)).optional(),
  priority: z.array(z.enum(capaPriorityEnum.enumValues)).optional(),
  owner: z.array(z.string()).optional(),
  dueDateRange: z
    .object({
      from: z.date().optional(),
      to: z.date().optional(),
    })
    .optional(),
  includeArchived: z.boolean().optional(),
  tags: z.array(z.enum(capaTagsEnum.enumValues)).optional(),
  eventId: z.cuid2().optional(),
});

export type CapasFilters = z.infer<typeof CapasFiltersSchema>;

export const ListCapasSchema = PaginationInputSchema.and(SortInputSchema).and(CapasFiltersSchema);

export const ExportCapasSchema = SortInputSchema.and(CapasFiltersSchema);
