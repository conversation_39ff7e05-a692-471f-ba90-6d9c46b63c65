import { accessPoints, accessPointStatusEnum } from '@shared/schema';
import { InferInsertModel, InferSelectModel } from 'drizzle-orm';
import { createInsertSchema, createSelectSchema, createUpdateSchema } from 'drizzle-zod';
import { z } from 'zod';
import { AssetSchema, LocationSchema } from './assets.types';
import { BulkImportResult, PaginationInputSchema, SortInputSchema } from './schema.types';

export const CreateAccessPointSchema = createInsertSchema(accessPoints);
export const UpdateAccessPointSchema = createUpdateSchema(accessPoints);
export const SelectAccessPointSchema = createSelectSchema(accessPoints);

export const CreateAccessPointFormSchema = CreateAccessPointSchema.pick({
  name: true,
  locationId: true,
  assetId: true,
}).extend({
  name: z.string().min(1, 'Name is required'),
  assetId: z.string().optional(),
});

export type CreateAccessPoint = InferInsertModel<typeof accessPoints>;
export type AccessPoint = InferSelectModel<typeof accessPoints>;

export const ACCESS_POINT_STATUS_MAP: Record<(typeof accessPointStatusEnum.enumValues)[number], string> = {
  [accessPointStatusEnum.enumValues[0]]: 'Active',
  [accessPointStatusEnum.enumValues[1]]: 'Inactive',
};

export const AccessPointsFiltersSchema = z.object({
  search: z.string().optional(),
  mustIncludeObjectIds: z.array(z.string()).optional(),
  includeArchived: z.boolean().optional(),
  locationId: z.array(z.string()).optional(),
  status: z.array(z.enum(accessPointStatusEnum.enumValues)).optional(),
  createdBy: z.array(z.string()).optional(),
  createdDateRange: z
    .object({
      from: z.date().optional(),
      to: z.date().optional(),
    })
    .optional(),
});

export type AccessPointsFilters = z.infer<typeof AccessPointsFiltersSchema>;

export const ListAccessPointsSchema = PaginationInputSchema.and(SortInputSchema).and(AccessPointsFiltersSchema);

export const ExportAccessPointsSchema = SortInputSchema.and(AccessPointsFiltersSchema);

export const BulkCreateAccessPointInputSchema = z.object({
  name: z.string().min(1),
  location: z.string().min(1),
});

export type BulkCreateAccessPointInput = z.infer<typeof BulkCreateAccessPointInputSchema>;

// Helper function to convert API response to client format
export function convertBulkImportResult(apiResult: BulkImportResult): BulkImportResult {
  return {
    total: apiResult.total || 0,
    created: apiResult.created || 0,
    failed: apiResult.failed || 0,
    failedItems: apiResult.failedItems || [],
  };
}

export const TransientAccessPointSchema = SelectAccessPointSchema.pick({
  id: true,
  upkeepCompanyId: true,
  name: true,
  archivedAt: true,
  status: true,
  locationId: true,
  assetId: true,
}).extend({
  location: LocationSchema.optional(),
  asset: AssetSchema.pick({
    id: true,
    name: true,
  }).optional(),
});

export type TransientAccessPoint = z.infer<typeof TransientAccessPointSchema>;

export const EditAccessPointNameSchema = z.object({
  name: z.string().min(1, 'Name is required'),
});

export type EditAccessPointNameData = z.infer<typeof EditAccessPointNameSchema>;
