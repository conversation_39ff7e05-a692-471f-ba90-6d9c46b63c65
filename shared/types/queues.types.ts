import z from 'zod';
import { Comments, CreateCommentFormSchema } from './comments.types';
import { RouterOutputs } from './router.types';
import { User } from './users.types';

// HTTP Headers type for job payloads
export type QueueHeaders = Record<string, string | string[] | undefined>;

// Comment Mention Notification Job Payload - matches service signature exactly
export type CommentMentionNotificationJobPayload = {
  newComment: Comments;
  savedMentions:
    | Array<{
        id: string;
        createdAt: Date;
        userId: string;
        commentId: string;
        position: number | null;
        mentionText: string | null;
      }>
    | undefined;
  user: User;
  headers: QueueHeaders;
  input: z.infer<typeof CreateCommentFormSchema>;
};

// Event Public Create Notification Job Payload - matches service signature exactly
export type EventPublicCreateNotificationJobPayload = {
  createdEvent: RouterOutputs['event']['createPublic'];
  headers: QueueHeaders;
  upkeepCompanyId: string;
  reporterInfo: {
    email: string;
    fullName: string;
    id?: string;
  };
  teamMembersToNotify: {
    email: string;
    fullName: string;
    id?: string;
  }[];
};

// Event Create Notification Job Payload - matches service signature exactly
export type EventCreateNotificationJobPayload = {
  createdEvent: RouterOutputs['event']['create'];
  user: User;
  headers: QueueHeaders;
  teamMembersToNotify?: string[];
};

// Event Update Notification Job Payload - matches service signature exactly
export type EventUpdateNotificationJobPayload = {
  updatedEvent: Partial<Event>;
  user: User;
  headers: QueueHeaders;
  actionPrefix?: string;
};

// CAPA Update Notification Job Payload - matches service signature exactly
export type CapaUpdateNotificationJobPayload = {
  capa: RouterOutputs['capa']['update'];
  user: User;
  headers: QueueHeaders;
  needPartialCheck: boolean;
  actionPrefix?: string;
};

// CAPA Assigned Notification Job Payload - matches service signature exactly
export type CapaAssignedNotificationJobPayload = {
  capa: RouterOutputs['capa']['create'];
  user: User;
  headers: QueueHeaders;
  needPartialCheck: boolean;
};

// JHA Create Notification Job Payload - matches service signature exactly
export type JhaRevivedNotificationJobPayload = {
  updatedJha: RouterOutputs['jha']['updateStatus'];
  user: User;
  headers: QueueHeaders;
};

// JHA Approved Notification Job Payload - matches service signature exactly
export type JhaApprovedNotificationJobPayload = {
  updatedJha: RouterOutputs['jha']['updateStatus'];
  user: User;
  headers: QueueHeaders;
};

// JHA Rejected Notification Job Payload - matches service signature exactly
export type JhaRejectedNotificationJobPayload = {
  updatedJha: RouterOutputs['jha']['updateStatus'];
  user: User;
  headers: QueueHeaders;
  rejectionReason: string;
};

// Alert Notification Job Payload - Common payload for all notification types
export type AlertNotificationJobPayload = {
  upkeepCompanyId: string;
  userIds: string[];
  title: string;
  description?: string;
  url?: string;
};

// Template prop types for email jobs
export type EmailTemplateType =
  | 'capa-assigned'
  | 'capa-overdue'
  | 'capa-update'
  | 'comment-mention'
  | 'event-create'
  | 'event-update'
  | 'jha-approved'
  | 'jha-revised'
  | 'jha-overdue'
  | 'jha-rejected';

// Email Job Payload - For queuing email sending
export type EmailJobPayload = {
  templateType: EmailTemplateType;
  templateProps: Record<string, unknown>; // Will be narrowed in worker
  options: {
    to: Array<{
      email: string;
      fullName: string;
      type: 'to' | 'cc' | 'bcc';
    }>;
    subject: string;
    attachments?: Array<{
      type: string;
      name: string;
      content: string;
    }>;
    tags?: string[];
  };
};
