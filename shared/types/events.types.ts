import { createInsertSchema, createSelectSchema, createUpdateSchema } from 'drizzle-zod';

import { auditTrailActionEnum, events, reportTypeEnum, severityEnum, statusEnum } from '@shared/schema';
import { isBefore } from 'date-fns';
import { InferInsertModel, InferSelectModel } from 'drizzle-orm';
import z from 'zod';
import { LocationSchema } from './assets.types';
import { TransientFileSchema } from './files.types';
import { PaginationInputSchema, SortInputSchema, UpkeepCompanyIdSchema } from './schema.types';
import { UserSchema } from './users.types';

export const EventValidations = {
  title: z
    .string({
      error: 'Title is required',
    })
    .min(1, {
      error: 'Title is required',
    }),
  reportedAt: z.date({ error: 'Date & Time is required' }).refine((date) => isBefore(date, new Date()), {
    error: 'Reported at must be in the past',
  }),
  type: z.enum(reportTypeEnum.enumValues, {
    error: 'Type is required',
  }),
  status: z.enum(statusEnum.enumValues, {
    error: 'Status is required',
  }),
  customerPhoneNumber: z
    .string()
    .regex(
      /^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/,
      'Please enter a valid US phone number (e.g., (*************, ************, or ************)',
    )
    .optional()
    .or(z.literal('')),
  witnesses: z
    .array(
      z.object({
        fullName: z.string().min(1, 'Full name is required'),
        phoneNumber: z
          .string()
          .regex(
            /^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/,
            'Please enter a valid US phone number (e.g., (*************, ************, or ************)',
          )
          .optional()
          .or(z.literal('')),
        email: z.email('Please enter a valid email address').optional().or(z.literal('')),
        notes: z.string().optional(),
      }),
    )
    .optional(),
};

export const CreateEventSchema = createInsertSchema(events, EventValidations);
export const UpdateEventSchema = createUpdateSchema(events);
export const SelectEventSchema = createSelectSchema(events);

export type CreateEvent = InferInsertModel<typeof events>;
export type Event = InferSelectModel<typeof events>;

export const CreateEventFormSchema = CreateEventSchema.omit({
  id: true,
  oshaReportable: true,
  aiConfidenceScore: true,
  archivedAt: true,
  deletedAt: true,
  reportedBy: true,
  slug: true,
  upkeepCompanyId: true,
  updatedAt: true,
  status: true,
  reportedByEmail: true,
  reportedByName: true,
}).extend({
  media: z.array(TransientFileSchema).optional(),
});

export const EditEventFormSchema = UpdateEventSchema.omit({
  aiConfidenceScore: true,
  deletedAt: true,
  reportedBy: true,
  slug: true,
  upkeepCompanyId: true,
  updatedAt: true,
}).extend({
  media: z.array(TransientFileSchema).optional(),
  id: z.cuid2(),
});

export const CreateEventFormPublicSchema = CreateEventFormSchema.extend({
  name: z.string({ error: 'Name is required' }).min(1, {
    error: 'Name is required',
  }),
  email: z.email({ error: 'Invalid email address' }),
  accessPointCreatedBy: z.string(),
  reportedBy: z.string().optional(),
}).and(UpkeepCompanyIdSchema);

export const EventsFiltersSchema = z.object({
  search: z.string().optional(),
  mustIncludeObjectIds: z.array(z.string()).optional(),
  status: z.array(z.enum(statusEnum.enumValues)).optional(),
  type: z.array(z.enum(reportTypeEnum.enumValues)).optional(),
  severity: z.array(z.enum(severityEnum.enumValues)).optional(),
  oshaReportable: z.boolean().optional(),
  includeArchived: z.boolean().optional(),
  locationIds: z.array(z.string()).optional(),
});

export type EventsFilters = z.infer<typeof EventsFiltersSchema>;

export const ListEventSchema = PaginationInputSchema.and(SortInputSchema).and(EventsFiltersSchema);

export const ExportEventsSchema = SortInputSchema.and(EventsFiltersSchema);

// Event Notification Schemas (defined here after LocationSchema is available)
export const EventNotificationSchema = SelectEventSchema.omit({
  type: true,
  category: true,
  archivedAt: true,
  immediateActions: true,
  oshaReportable: true,
  aiConfidenceScore: true,
  deletedAt: true,
  upkeepCompanyId: true,
  updatedAt: true,
}).extend({
  location: LocationSchema.optional(),
  action: z.enum(auditTrailActionEnum.enumValues).optional(),
  teamMembersToNotify: z.array(UserSchema.pick({ id: true, fullName: true, email: true })).optional(),
});
