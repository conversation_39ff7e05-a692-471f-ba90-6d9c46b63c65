import { entityTypeEnum, files } from '@shared/schema';
import { createInsertSchema, createSelectSchema, createUpdateSchema } from 'drizzle-zod';
import z from 'zod';
import { FileStatusSchema, UpkeepCompanyIdSchema } from './schema.types';

export const SUPPORTED_MIME_TYPES = {
  // Images - Common web and mobile formats
  'image/jpeg': ['.jpg', '.jpeg'],
  'image/png': ['.png'],
  'image/webp': ['.webp'],
  'image/heic': ['.heic'], // Apple's High Efficiency Image Container
  'image/heif': ['.heic'], // High Efficiency Image File Format

  // Videos - Web-compatible formats
  'video/mp4': ['.mp4'],
  'video/quicktime': ['.mov'], // Apple QuickTime format

  // Documents - Office and text formats
  'application/pdf': ['.pdf'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'], // Word 2007+
  'application/msword': ['.doc'], // Legacy Word format
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'], // Excel 2007+
  'application/vnd.ms-excel': ['.xls'], // Legacy Excel format
  'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx'], // PowerPoint 2007+
  'application/vnd.ms-powerpoint': ['.ppt'], // Legacy PowerPoint format
  'text/csv': ['.csv'], // Standard CSV MIME type
  'application/csv': ['.csv'], // Alternative CSV MIME type
  'text/plain': ['.txt'], // Plain text files
  'application/rtf': ['.rtf'], // Rich Text Format
} as const;

export type SupportedMimeType = keyof typeof SUPPORTED_MIME_TYPES;

export const SUPPORTED_MIME_TYPES_LIST = Object.keys(SUPPORTED_MIME_TYPES) as readonly SupportedMimeType[];

/**
 * Compile-time validation that ensures SUPPORTED_MIME_TYPES has exactly the expected structure.
 * This prevents drift between the object keys and the SupportedMimeType union type.
 *
 * If this line causes a TypeScript error, it means SUPPORTED_MIME_TYPES keys
 * don't match the expected MIME types or extensions arrays.
 */
const _typeValidation: Record<SupportedMimeType, readonly string[]> = SUPPORTED_MIME_TYPES;
// Prevent unused variable warning while keeping the validation
void _typeValidation;

/**
 * Flattened array of all accepted file extensions across all supported MIME types.
 * Duplicates are automatically removed using Set deduplication.
 */
export const ACCEPTED_FORMATS = Array.from(new Set(Object.values(SUPPORTED_MIME_TYPES).flat()));

/**
 * Document MIME types only (excludes images and videos).
 * Internal use only for type definitions and internal functions.
 */
const _DOCUMENT_MIME_TYPES = [
  'application/pdf',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'application/vnd.ms-powerpoint',
  'text/csv',
  'application/csv',
  'text/plain',
  'application/rtf',
] as const;

export type DocumentMimeType = (typeof _DOCUMENT_MIME_TYPES)[number];

export const DOCUMENT_TYPE_BY_MIME_TYPE: Record<DocumentMimeType, string> = {
  'application/pdf': 'pdf',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'spreadsheet',
  'application/vnd.ms-excel': 'spreadsheet',
  'text/csv': 'spreadsheet',
  'application/csv': 'spreadsheet',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'word',
  'application/msword': 'word',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'presentation',
  'application/vnd.ms-powerpoint': 'presentation',
  'text/plain': 'text',
  'application/rtf': 'text',
};

export enum MediaCategory {
  IMAGE = 'Image',
  VIDEO = 'Video',
  DOCUMENT = 'Document',
  UNKNOWN = 'Unknown',
}

export enum MediaLabel {
  PHOTO = 'Photo',
  VIDEO = 'Video',
  DOCUMENT = 'Document',
  UNKNOWN = 'Unknown',
}

export const FILE_TYPE_LABEL_BY_MIME_TYPE: Record<SupportedMimeType, MediaLabel> = {
  // Images - Common web and mobile formats
  'image/jpeg': MediaLabel.PHOTO,
  'image/png': MediaLabel.PHOTO,
  'image/heic': MediaLabel.PHOTO,
  'image/heif': MediaLabel.PHOTO,
  'image/webp': MediaLabel.PHOTO,

  // Videos - Web-compatible formats
  'video/mp4': MediaLabel.VIDEO,
  'video/quicktime': MediaLabel.VIDEO,

  // Documents - Office and text formats
  'application/pdf': MediaLabel.DOCUMENT,
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': MediaLabel.DOCUMENT,
  'application/msword': MediaLabel.DOCUMENT,
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': MediaLabel.DOCUMENT,
  'application/vnd.ms-excel': MediaLabel.DOCUMENT,
  'application/vnd.openxmlformats-officedocument.presentationml.presentation': MediaLabel.DOCUMENT,
  'application/vnd.ms-powerpoint': MediaLabel.DOCUMENT,
  'text/csv': MediaLabel.DOCUMENT,
  'application/csv': MediaLabel.DOCUMENT,
  'text/plain': MediaLabel.DOCUMENT,
  'application/rtf': MediaLabel.DOCUMENT,
};

export function isSupportedMimeType(mimeType: string): mimeType is SupportedMimeType {
  return mimeType in SUPPORTED_MIME_TYPES;
}

// Helper function to get file icon based on mime type using the proper type system
export const getFileIcon = (mimeType: string): string => {
  const category = getMediaCategory(mimeType);

  if (category === MediaCategory.IMAGE) return '📸';
  if (category === MediaCategory.VIDEO) return '🎬';

  if (category === MediaCategory.DOCUMENT && isSupportedMimeType(mimeType)) {
    const docType = DOCUMENT_TYPE_BY_MIME_TYPE[mimeType as DocumentMimeType];
    switch (docType) {
      case 'pdf':
        return '📄';
      case 'word':
        return '📝';
      case 'spreadsheet':
        return '📊';
      case 'presentation':
        return '📽️';
      case 'text':
        return '📃';
      default:
        return '📄';
    }
  }

  return '📁';
};

export function getMediaCategory(mimeType: string | null | undefined) {
  if (!mimeType || !isSupportedMimeType(mimeType)) {
    return MediaCategory.UNKNOWN;
  }

  if (mimeType.startsWith('image/')) {
    return MediaCategory.IMAGE;
  }

  if (mimeType.startsWith('video/')) {
    return MediaCategory.VIDEO;
  }

  return MediaCategory.DOCUMENT;
}

export function getMediaLabel(mimeType: string | null | undefined) {
  if (!mimeType || !isSupportedMimeType(mimeType)) {
    return MediaLabel.UNKNOWN;
  }

  return FILE_TYPE_LABEL_BY_MIME_TYPE[mimeType];
}

export function isImage(mimeType: string | null | undefined) {
  return getMediaCategory(mimeType) === MediaCategory.IMAGE;
}

export function isVideo(mimeType: string | null | undefined) {
  return getMediaCategory(mimeType) === MediaCategory.VIDEO;
}

export function isDocument(mimeType: string | null | undefined) {
  return getMediaCategory(mimeType) === MediaCategory.DOCUMENT;
}

/**
 * Checks if a MIME type represents a HEIC/HEIF image format.
 * Used to determine if HEIC conversion is needed for preview.
 */
export function isHEIC(mimeType: string | null | undefined) {
  return mimeType === 'image/heic' || mimeType === 'image/heif';
}

export enum DocumentPreviewCapability {
  OBJECT_EMBED = 'object_embed', // PDF via object/embed tags
  TEXT_CONTENT = 'text_content', // Plain text, CSV as text
  DOWNLOAD_ONLY = 'download_only', // Office docs, RTF - download only
  UNSUPPORTED = 'unsupported', // Unsupported document types
}

export const DOCUMENT_PREVIEW_CAPABILITY: Record<DocumentMimeType, DocumentPreviewCapability> = {
  // PDFs - can be embedded using object/embed tags
  'application/pdf': DocumentPreviewCapability.OBJECT_EMBED,

  // Text files - can display content directly
  'text/plain': DocumentPreviewCapability.TEXT_CONTENT,
  'text/csv': DocumentPreviewCapability.TEXT_CONTENT,
  'application/csv': DocumentPreviewCapability.TEXT_CONTENT,

  // Office documents - browser support is inconsistent, better to download
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': DocumentPreviewCapability.DOWNLOAD_ONLY,
  'application/msword': DocumentPreviewCapability.DOWNLOAD_ONLY,
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': DocumentPreviewCapability.DOWNLOAD_ONLY,
  'application/vnd.ms-excel': DocumentPreviewCapability.DOWNLOAD_ONLY,
  'application/vnd.openxmlformats-officedocument.presentationml.presentation': DocumentPreviewCapability.DOWNLOAD_ONLY,
  'application/vnd.ms-powerpoint': DocumentPreviewCapability.DOWNLOAD_ONLY,
  'application/rtf': DocumentPreviewCapability.DOWNLOAD_ONLY,
};

export function getDocumentPreviewCapability(mimeType: string | null | undefined) {
  if (!mimeType || !isDocument(mimeType)) {
    return DocumentPreviewCapability.UNSUPPORTED;
  }

  // Type assertion is safe here because we've checked isDocument() above
  return DOCUMENT_PREVIEW_CAPABILITY[mimeType as DocumentMimeType] ?? DocumentPreviewCapability.DOWNLOAD_ONLY;
}

/**
 * Checks if a document can be previewed inline (not just downloaded).
 * Returns true for PDFs and text files that can be shown in the browser.
 */
export function isDocumentPreviewable(mimeType: string | null | undefined) {
  const capability = getDocumentPreviewCapability(mimeType);
  return capability !== DocumentPreviewCapability.DOWNLOAD_ONLY && capability !== DocumentPreviewCapability.UNSUPPORTED;
}

/**
 * Checks if document should use object/embed tags for preview.
 * Currently applies to PDF files.
 */
export function requiresObjectEmbed(mimeType: string | null | undefined) {
  return getDocumentPreviewCapability(mimeType) === DocumentPreviewCapability.OBJECT_EMBED;
}

/**
 * Checks if document content should be loaded and displayed as text.
 * Applies to plain text, CSV, and other text-based formats.
 */
export function requiresTextContent(mimeType: string | null | undefined) {
  return getDocumentPreviewCapability(mimeType) === DocumentPreviewCapability.TEXT_CONTENT;
}

/**
 * Utility to check if a file type can be previewed in the media viewer.
 * Now supports images, videos, and previewable documents.
 */
export function isPreviewableMedia(mimeType: string | null | undefined) {
  const category = getMediaCategory(mimeType);
  return (
    category === MediaCategory.IMAGE ||
    category === MediaCategory.VIDEO ||
    (category === MediaCategory.DOCUMENT && isDocumentPreviewable(mimeType))
  );
}

export function formatFileSize(bytes: number) {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
}

export const CreateFileSchema = createInsertSchema(files);
export const UpdateFileSchema = createUpdateSchema(files, { id: z.cuid2() });
export const SelectFileSchema = createSelectSchema(files);

export const UpdateFilePublicSchema = UpdateFileSchema.and(UpkeepCompanyIdSchema);

export const GetPresignedUrlInputSchema = CreateFileSchema.pick({
  fileName: true,
  fileSize: true,
  mimeType: true,
  entityType: true,
  entityId: true,
}).extend({
  fileSize: z.number().positive('File size must be positive'),
  suffix: z.string().optional(),
});
export const GetPresignedUrlInputPublicSchema = GetPresignedUrlInputSchema.and(UpkeepCompanyIdSchema);

export const ListFilesSchema = z.object({
  entityType: z.enum(entityTypeEnum.enumValues),
  entityId: z.string().min(1),
  status: FileStatusSchema.optional(),
});

export const TransientFileSchema = z.object({
  id: z.string().optional(),
  name: z.string(),
  url: z.string(),
  type: z.string(),
  size: z.number(),
  file: z.instanceof(File).optional(),
});

export const FileUpdateStatusSchema = z.enum(['completed', 'failed'] as const);

// S3 Key schema for read operations
export const S3KeyInputSchema = z.object({
  s3Key: z.string().min(1, 'S3 key is required'),
});

export const CreateImageVariantsSchema = CreateFileSchema.pick({
  fileName: true,
  mimeType: true,
  entityType: true,
  entityId: true,
}).extend({
  outputFormat: z.enum(['jpeg', 'png', 'webp']).optional(),
  fileData: z.string(),
  quality: z.number().optional(),
  maxWidth: z.number().optional(),
  maxHeight: z.number().optional(),
  kernel: z.enum(['lanczos3', 'cubic', 'mitchell']).optional(),
});

export const CreateImageVariantsPublicSchema = CreateImageVariantsSchema.and(UpkeepCompanyIdSchema);

export type CreateImageVariantsSchemaOptions = z.infer<typeof CreateImageVariantsSchema> & {
  fileBuffer?: Buffer<ArrayBufferLike>;
};
