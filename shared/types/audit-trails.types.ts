import { auditTrail, entityTypeEnum } from '@shared/schema';
import { InferInsertModel, InferSelectModel } from 'drizzle-orm';
import { createInsertSchema, createSelectSchema, createUpdateSchema } from 'drizzle-zod';
import z from 'zod';

export const CreateAuditTrailSchema = createInsertSchema(auditTrail);
export const UpdateAuditTrailSchema = createUpdateSchema(auditTrail);
export const SelectAuditTrailSchema = createSelectSchema(auditTrail);

export const GetAuditTrailSchema = z.object({
  entityId: z.string(),
  entityType: z.enum(entityTypeEnum.enumValues),
});

export type CreateAuditTrail = InferInsertModel<typeof auditTrail>;
export type AuditTrail = InferSelectModel<typeof auditTrail>;
