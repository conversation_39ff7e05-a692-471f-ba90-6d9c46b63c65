import { comments, statusEnum } from '@shared/schema';
import { PaginationInputSchema } from './schema.types';
import { InferInsertModel, InferSelectModel } from 'drizzle-orm';
import { createInsertSchema, createSelectSchema, createUpdateSchema } from 'drizzle-zod';
import z from 'zod';

export const CreateCommentsSchema = createInsertSchema(comments);

// Schema for comment creation form (includes entity information)
export const CreateCommentFormSchema = CreateCommentsSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  upkeepCompanyId: true,
  userId: true,
}).extend({
  entityId: z.string(),
  entitySlug: z.string().nullable(),
  entityTitle: z.string(),
  status: z.enum(statusEnum.enumValues),
  entityType: z.enum(['event', 'capa']),
});

export const CreateCommentFormSchemaValidations = {
  content: z
    .string()
    .transform((value) => value.trim())
    .pipe(z.string().min(1, 'Comment is required')),
};

// Schema for listing comments without entityType (added by router)
export const ListCommentsInputSchema = z.object({
  entityId: z.string(),
  options: z
    .object({
      limit: z.number().optional(),
      offset: z.number().optional(),
    })
    .optional(),
});

// Legacy schema with entityType (kept for service calls)
export const ListCommentsSchema = z.object({
  entityId: z.string(),
  entityType: z.enum(['event', 'capa']),
  options: PaginationInputSchema.optional(),
});

export const UpdateCommentsSchema = createUpdateSchema(comments);
export const SelectCommentsSchema = createSelectSchema(comments);

export type CreateComments = InferInsertModel<typeof comments>;
export type Comments = InferSelectModel<typeof comments>;
