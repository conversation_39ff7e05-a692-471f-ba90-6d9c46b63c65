import { z } from 'zod';
import {
  accessPointStatusEnum,
  controlMeasureCategoryEnum,
  entityTypeEnum,
  fileStatusEnum,
  hazardCategoryEnum,
  reportTypeEnum,
  roleEnum,
  rootCauseEnum,
  severityEnum,
  statusEnum,
} from '../schema';
import { UserAccountTypeSchema } from '../user-permissions';

// ========================================
// COMMON TYPES AND INTERFACES
// ========================================

export const FeatureFlagsSchema = z.object({
  ehsSop: z.boolean().nullish(),
});

export interface PaginatedResponse<T> {
  noResults: boolean;
  result: T[];
  nextCursor: number | undefined;
}

export interface StatusConfig {
  label: string;
  color: string;
  backgroundColor: string;
}

export interface BulkImportResult {
  total: number;
  created: number;
  failed: number;
  failedItems: Array<{
    name: string;
    location: string;
    reason: string;
  }>;
}

export interface BulkImportSummary {
  successCount: number;
  failureCount: number;
  totalProcessed: number;
  failures: Array<{
    name: string;
    location: string;
    reason: string;
  }>;
}

export type RawParseObject = {
  __type?: string;
  className?: string;
  objectId: string;
};

// ========================================
// COMMON SCHEMAS
// ========================================

// Cursor-based Pagination Schema for useInfiniteQuery
export const PaginationInputSchema = z.object({
  cursor: z.number().min(0).optional(),
  limit: z.number().min(1).max(100).optional(),
});

export type PaginationInput = z.infer<typeof PaginationInputSchema>;

// Generic Sorting Schemas and Types
export const SortInputSchema = z.object({
  sortBy: z.string().optional(),
  sortOrder: z.string().optional(),
});

export type SortInput = z.infer<typeof SortInputSchema>;

export const IdSchema = z.object({
  id: z.cuid2(),
});

export const UuidSchema = z.object({
  id: z.uuid(),
});

export const IdArraySchema = z.array(z.cuid2());

export const UpkeepCompanyIdSchema = z.object({
  upkeepCompanyId: z.string().min(1).max(10),
});

export const PublicSearchSchema = z
  .object({
    upkeepCompanyId: z.string().min(1, 'Required'),
    locationId: z.string().optional(),
    userAccountType: z
      .union([UserAccountTypeSchema, z.array(UserAccountTypeSchema)])
      .nullable()
      .optional(),
    objectId: z.union([z.string(), z.array(z.string())]).optional(),
    mustIncludeObjectIds: z.array(z.string()).optional(),
    search: z.string().optional(),
    includeAllUserTypes: z.boolean().optional(),
  })
  .and(PaginationInputSchema);

export type PublicSearchInput = z.infer<typeof PublicSearchSchema>;

export const DateRangeSchema = z.object({
  from: z.date().optional(),
  to: z.date().optional(),
});

// ========================================
// ENUM SCHEMAS AND MAPPINGS
// ========================================

export const ReportTypeSchema = z.enum(reportTypeEnum.enumValues);
export const SeveritySchema = z.enum(severityEnum.enumValues);
export const RootCauseSchema = z.enum(rootCauseEnum.enumValues);
export const StatusSchema = z.enum(statusEnum.enumValues);
export const RoleSchema = z.enum(roleEnum.enumValues);
export const HazardCategorySchema = z.enum(hazardCategoryEnum.enumValues);
export const ControlMeasureCategorySchema = z.enum(controlMeasureCategoryEnum.enumValues);
export const FileStatusSchema = z.enum(fileStatusEnum.enumValues);
export const AccessPointStatusSchema = z.enum(accessPointStatusEnum.enumValues);

// Status Mappings
export const STATUS_MAP: Record<(typeof statusEnum.enumValues)[number], string> = {
  [statusEnum.enumValues[0]]: 'Open',
  [statusEnum.enumValues[1]]: 'In Review',
  [statusEnum.enumValues[2]]: 'Closed',
};

export const SEVERITY_MAP: Record<(typeof severityEnum.enumValues)[number], string> = {
  [severityEnum.enumValues[0]]: 'Low',
  [severityEnum.enumValues[1]]: 'Medium',
  [severityEnum.enumValues[2]]: 'High',
  [severityEnum.enumValues[3]]: 'Critical',
};

export const REPORT_TYPE_MAP: Record<(typeof reportTypeEnum.enumValues)[number], string> = {
  [reportTypeEnum.enumValues[0]]: 'Incident',
  [reportTypeEnum.enumValues[1]]: 'Near Miss',
  [reportTypeEnum.enumValues[2]]: 'Observation',
  [reportTypeEnum.enumValues[3]]: 'Customer Incident',
};

export const CATEGORY_MAP: Record<(typeof hazardCategoryEnum.enumValues)[number], string> = {
  [hazardCategoryEnum.enumValues[0]]: 'Chemical',
  [hazardCategoryEnum.enumValues[1]]: 'Electrical',
  [hazardCategoryEnum.enumValues[2]]: 'Physical',
  [hazardCategoryEnum.enumValues[3]]: 'Environmental',
  [hazardCategoryEnum.enumValues[4]]: 'Ergonomic',
  [hazardCategoryEnum.enumValues[5]]: 'Fall',
  [hazardCategoryEnum.enumValues[6]]: 'Biological',
  [hazardCategoryEnum.enumValues[7]]: 'Fire',
  [hazardCategoryEnum.enumValues[8]]: 'Mechanical',
  [hazardCategoryEnum.enumValues[9]]: 'Radiation',
  [hazardCategoryEnum.enumValues[10]]: 'Noise',
  [hazardCategoryEnum.enumValues[11]]: 'Thermal',
  [hazardCategoryEnum.enumValues[12]]: 'Atmospheric',
  [hazardCategoryEnum.enumValues[13]]: 'Spill',
  [hazardCategoryEnum.enumValues[14]]: 'Transportation',
  [hazardCategoryEnum.enumValues[15]]: 'Violence',
  [hazardCategoryEnum.enumValues[16]]: 'Other',
  [hazardCategoryEnum.enumValues[17]]: 'Custom',
};

export const ENTITY_TYPE_MAP: Record<(typeof entityTypeEnum.enumValues)[number], string> = {
  [entityTypeEnum.enumValues[0]]: 'Safety Event',
  [entityTypeEnum.enumValues[1]]: 'CAPA',
  [entityTypeEnum.enumValues[2]]: 'Access Point',
  [entityTypeEnum.enumValues[3]]: 'Comment',
  [entityTypeEnum.enumValues[4]]: 'Global Location',
  [entityTypeEnum.enumValues[5]]: 'Audit Trail',
  [entityTypeEnum.enumValues[6]]: 'File',
  [entityTypeEnum.enumValues[7]]: 'OSHA Audit Trail',
  [entityTypeEnum.enumValues[8]]: 'OSHA Report',
  [entityTypeEnum.enumValues[9]]: 'OSHA Company Information',
  [entityTypeEnum.enumValues[10]]: 'OSHA Agency Report',
  [entityTypeEnum.enumValues[11]]: 'JHA',
  [entityTypeEnum.enumValues[12]]: 'Hazard',
  [entityTypeEnum.enumValues[13]]: 'Control Measure',
  [entityTypeEnum.enumValues[14]]: 'SOP',
  [entityTypeEnum.enumValues[15]]: 'SOP Section',
};

// Status Style Configurations
export const STATUS_STYLES: Record<(typeof statusEnum.enumValues)[number], StatusConfig> = {
  [statusEnum.enumValues[0]]: {
    label: 'Open',
    backgroundColor: '#e0e7ff',
    color: '#2563eb',
  },
  [statusEnum.enumValues[1]]: {
    label: 'In Review',
    backgroundColor: '#fef9c3',
    color: '#b45309',
  },
  [statusEnum.enumValues[2]]: {
    label: 'Closed',
    backgroundColor: '#dcfce7',
    color: '#166534',
  },
};

export const SEVERITY_STYLES: Record<(typeof severityEnum.enumValues)[number], StatusConfig> = {
  [severityEnum.enumValues[0]]: {
    label: 'Low',
    backgroundColor: '#dcfce7',
    color: '#15803d',
  },
  [severityEnum.enumValues[1]]: {
    label: 'Medium',
    backgroundColor: '#fef3c7',
    color: '#d97706',
  },
  [severityEnum.enumValues[2]]: {
    label: 'High',
    backgroundColor: '#fee2e2',
    color: '#b91c1c',
  },
  [severityEnum.enumValues[3]]: {
    label: 'Critical',
    backgroundColor: '#7f1d1d',
    color: '#fecaca',
  },
};
