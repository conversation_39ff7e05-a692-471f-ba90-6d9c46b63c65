import { createInsertSchema, createUpdateSchema } from 'drizzle-zod';
import { z } from 'zod';
import { controlMeasureCategoryEnum, hazardCategoryEnum, jha, approvalStatusEnum, jhaSteps } from '../schema';
import { PaginationInputSchema, SortInputSchema, UuidSchema } from './schema.types';

export const JhaReviewStatusEnum = z.enum(['overdue', 'due_soon', 'no_review_date']);
export const JhaRiskLevelEnum = z.enum(['high', 'medium', 'low']);

export const JHA_REVIEW_STATUS_MAP: Record<z.infer<typeof JhaReviewStatusEnum>, string> = {
  [JhaReviewStatusEnum.enum.overdue]: 'Overdue',
  [JhaReviewStatusEnum.enum.due_soon]: 'Due Soon',
  [JhaReviewStatusEnum.enum.no_review_date]: 'No Review Date',
};

export const JHA_RISK_LEVEL_MAP: Record<z.infer<typeof JhaRiskLevelEnum>, string> = {
  [JhaRiskLevelEnum.enum.high]: 'High',
  [JhaRiskLevelEnum.enum.medium]: 'Medium',
  [JhaRiskLevelEnum.enum.low]: 'Low',
};

export const JHA_STATUS_MAP: Record<(typeof approvalStatusEnum.enumValues)[number], string> = {
  [approvalStatusEnum.enumValues[0]]: 'Draft',
  [approvalStatusEnum.enumValues[1]]: 'Under Review',
  [approvalStatusEnum.enumValues[2]]: 'Approved/Active',
};

export const CONTROL_MEASURE_TYPE_MAP: Record<(typeof controlMeasureCategoryEnum.enumValues)[number], string> = {
  [controlMeasureCategoryEnum.enumValues[0]]: 'Engineering Controls',
  [controlMeasureCategoryEnum.enumValues[1]]: 'Administrative Controls',
  [controlMeasureCategoryEnum.enumValues[2]]: 'Personal Protective Equipment',
  [controlMeasureCategoryEnum.enumValues[3]]: 'Specialized Advanced Controls',
  [controlMeasureCategoryEnum.enumValues[4]]: 'Other',
  [controlMeasureCategoryEnum.enumValues[5]]: 'Custom',
};

export const getRiskLevel = (score: number) => {
  if (score >= 15)
    return {
      level: 'High',
      color: 'bg-red-100 text-red-800 border-red-200',
      iconType: 'alert',
    };
  if (score >= 6)
    return {
      level: 'Medium',
      color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      iconType: 'flag',
    };
  return {
    level: 'Low',
    color: 'bg-green-100 text-green-800 border-green-200',
  };
};

export const JhaFiltersSchema = z.object({
  search: z.string().optional(),
  mustIncludeObjectIds: z.array(z.string()).optional(),
  status: z.array(z.enum(approvalStatusEnum.enumValues)).optional(),
  ownerId: z.array(z.string()).optional(),
  riskLevel: JhaRiskLevelEnum.optional(),
  reviewStatus: JhaReviewStatusEnum.optional(),
  includeArchived: z.boolean().optional(),
  locationIds: z.array(z.string()).optional(),
  includeLocation: z.boolean().optional(),
});

export type JhaFilters = z.infer<typeof JhaFiltersSchema>;

export const ListJhaSchema = PaginationInputSchema.and(SortInputSchema).and(JhaFiltersSchema);

export const ExportJhaSchema = SortInputSchema.and(JhaFiltersSchema);

const JhaInsertSchema = createInsertSchema(jha).omit({
  id: true,
  upkeepCompanyId: true,
  slug: true,
  version: true,
  highestSeverity: true,
  createdBy: true,
  createdAt: true,
});

const JhaUpdateSchema = createUpdateSchema(jha, {
  id: z.cuid2(),
  title: z.string().min(1, 'Title is required'),
  ownerId: z.string(),
  approverId: z.string(),
}).omit({
  upkeepCompanyId: true,
  slug: true,
  version: true,
  highestSeverity: true,
  createdBy: true,
  createdAt: true,
});

const ItemsToCreateSchema = z.object({
  hazardsToCreate: z
    .array(
      z.object({
        name: z.string().min(1, 'Hazard name is required'),
        type: z.enum(hazardCategoryEnum.enumValues),
      }),
    )
    .nullish(),
  controlMeasuresToCreate: z
    .array(
      z.object({
        name: z.string().min(1, 'Control measure name is required'),
        type: z.enum(controlMeasureCategoryEnum.enumValues),
      }),
    )
    .nullish(),
});

const JhaStepValidations = {
  title: z.string().min(1, 'Step title is required'),
};

export const CreateJhaStepsSchema = createInsertSchema(jhaSteps, JhaStepValidations)
  .omit({
    id: true,
    upkeepCompanyId: true,
    jhaId: true,
    createdBy: true,
    createdAt: true,
    updatedAt: true,
    archivedAt: true,
  })
  .and(ItemsToCreateSchema);

export const UpdateJhaStepsSchema = createUpdateSchema(jhaSteps, JhaStepValidations)
  .omit({
    upkeepCompanyId: true,
    jhaId: true,
    createdBy: true,
    createdAt: true,
    archivedAt: true,
  })
  .and(ItemsToCreateSchema);

export const GetJhaByInstanceIdSchema = UuidSchema.extend({
  versionId: z.cuid2().nullish(),
});

export const CreateJhaFormSchema = z.object({
  jha: JhaInsertSchema,
  steps: z.array(CreateJhaStepsSchema).min(1, 'At least one step is required'),
  isAiGenerated: z.boolean().optional(),
  disclaimerAccepted: z.boolean().optional(),
});

// AI-optimized schemas for JSON Schema compatibility
export const CreateJhaAiSchema = CreateJhaFormSchema.extend({
  jha: JhaInsertSchema.omit({ reviewDate: true, updatedAt: true, archivedAt: true }),
});

export const UpdateJhaFormSchema = z.object({
  jha: JhaUpdateSchema,
  steps: z.array(UpdateJhaStepsSchema).min(1, 'At least one step is required'),
});

export const UpdateJhaStatusSchema = z
  .object({
    id: z.cuid2(),
    status: z.enum(approvalStatusEnum.enumValues),
    rejectionReason: z.string().nullish(),
  })
  .refine(
    (data) => {
      // If status is 'draft' (rejection), rejectionReason is required
      if (data.status === approvalStatusEnum.enumValues[0] && !data.rejectionReason?.trim()) {
        return false;
      }
      return true;
    },
    {
      path: ['rejectionReason'],
      error: 'Rejection reason is required when rejecting a JHA',
    },
  );

export type UpdateJhaStatus = z.infer<typeof UpdateJhaStatusSchema>;

export type CreateJhaType = z.infer<typeof CreateJhaFormSchema>;

export type UpdateJhaType = z.infer<typeof UpdateJhaFormSchema>;
