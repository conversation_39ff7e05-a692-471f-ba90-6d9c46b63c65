import { FeatureFlagsSchema } from '@shared/types/schema.types';
import { UserAccountTypeSchema, UserPermissionSchema } from '@shared/user-permissions';
import z from 'zod';

export const UserSchema = z.object({
  id: z.string(),
  username: z.string(),
  email: z.string(),
  firstName: z.string(),
  lastName: z.string(),
  fullName: z.string(),
  upkeepCompanyId: z.string(),
  role: UserAccountTypeSchema,
  permissions: UserPermissionSchema,
  featureFlags: FeatureFlagsSchema,
  createdAt: z.string().optional(),
  lastLoginAt: z.string().optional(),
  hasEhsEnabled: z.boolean(),
});

export const UserPublicSchema = UserSchema.pick({
  id: true,
  firstName: true,
  lastName: true,
  username: true,
}).extend({
  lastName: z.string().optional(),
  fullName: z.string().optional(),
  email: z.string().optional(),
});

export type User = z.infer<typeof UserSchema>;
export type UserPublic = z.infer<typeof UserPublicSchema>;
