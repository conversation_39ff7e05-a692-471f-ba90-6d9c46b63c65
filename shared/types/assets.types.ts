import { PaginationInputSchema } from './schema.types';
import { z } from 'zod';

export const AssetSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
});

export type Asset = z.infer<typeof AssetSchema>;

export const AssetSearchInputSchema = PaginationInputSchema.extend({
  locationId: z.string().optional(),
  search: z.string().optional(),
  mustIncludeObjectIds: z.array(z.string()).optional(),
});

export type AssetSearchInput = z.infer<typeof AssetSearchInputSchema>;

export const LocationSchema = z.object({
  id: z.string(),
  name: z.string(),
});

export type Location = z.infer<typeof LocationSchema>;

export const LocationSearchInputSchema = PaginationInputSchema.extend({
  restrictionsLevel: z.number().optional(),
  search: z.string().optional(),
  mustIncludeObjectIds: z.array(z.string()).optional(),
});

export type LocationSearchInput = z.infer<typeof LocationSearchInputSchema>;

export type UpkeepAsset = {
  id: string;
  Name: string;
  Description: string;
  objectLocation?: { stringName: string };
};
