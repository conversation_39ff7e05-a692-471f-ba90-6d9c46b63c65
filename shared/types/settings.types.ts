import { createInsertSchema, createSelectSchema, createUpdateSchema } from 'drizzle-zod';
import { z } from 'zod';
import { controlMeasures, oshaLocations, hazards, hazardCategoryEnum, controlMeasureCategoryEnum } from '../schema';
import { DateRangeSchema, PaginationInputSchema, SortInputSchema } from './schema.types';

export const CreateOshaLocationSchema = createInsertSchema(oshaLocations)
  .omit({
    id: true,
    upkeepCompanyId: true,
    createdBy: true,
    createdAt: true,
  })
  .extend({
    name: z
      .string({ error: 'Name is required' })
      .trim()
      .min(1, 'Name is required')
      .max(255, 'Name must be less than 255 characters'),
  });
export const UpdateOshaLocationSchema = createUpdateSchema(oshaLocations).omit({
  upkeepCompanyId: true,
  createdBy: true,
  createdAt: true,
});

export const OshaLocationsFiltersSchema = z.object({
  search: z.string().optional(),
  mustIncludeObjectIds: z.array(z.string()).optional(),
  includeArchived: z.boolean().optional(),
  createdBy: z.array(z.string()).optional(),
  createdDateRange: DateRangeSchema.optional(),
});

export type OshaLocationsFilters = z.infer<typeof OshaLocationsFiltersSchema>;

export const ListOshaLocationsSchema = PaginationInputSchema.and(SortInputSchema).and(OshaLocationsFiltersSchema);

export const ExportOshaLocationsSchema = SortInputSchema.and(OshaLocationsFiltersSchema);

export const SelectOshaLocationSchema = createSelectSchema(oshaLocations);

export const ArchiveOshaLocationSchema = z.object({
  id: z.string(),
});

export type CreateOshaLocation = z.infer<typeof CreateOshaLocationSchema>;
export type ArchiveOshaLocation = z.infer<typeof ArchiveOshaLocationSchema>;
export type UpdateOshaLocation = z.infer<typeof UpdateOshaLocationSchema>;
export type SelectOshaLocation = z.infer<typeof SelectOshaLocationSchema>;

const ControlMeasuresValidations = {
  name: z.string().min(1, 'Name is required and cannot be empty'),
  type: z.enum(controlMeasureCategoryEnum.enumValues),
};

export const ControlMeasuresCreateSchema = createInsertSchema(controlMeasures, ControlMeasuresValidations).omit({
  id: true,
  upkeepCompanyId: true,
  createdBy: true,
  createdAt: true,
});

export const ControlMeasuresUpdateSchema = createUpdateSchema(controlMeasures, ControlMeasuresValidations).omit({
  upkeepCompanyId: true,
  createdBy: true,
  createdAt: true,
});

export const HazardsValidations = {
  name: z.string().min(1, 'Name is required and cannot be empty'),
  type: z.enum(hazardCategoryEnum.enumValues),
};

export const HazardsCreateSchema = createInsertSchema(hazards, HazardsValidations).omit({
  id: true,
  upkeepCompanyId: true,
  createdBy: true,
  createdAt: true,
  archivedAt: true,
});

export const HazardsUpdateSchema = createUpdateSchema(hazards, { ...HazardsValidations, id: z.cuid2() }).omit({
  upkeepCompanyId: true,
  createdBy: true,
  createdAt: true,
});

export const HazardsTypesSchema = z.enum(hazardCategoryEnum.enumValues);
export const ControlMeasuresCategorySchema = z.enum(controlMeasureCategoryEnum.enumValues);
export const HazardCategorySchema = z.enum(hazardCategoryEnum.enumValues);

export const BulkHazardsCreateSchema = z.array(HazardsCreateSchema).min(1, 'At least one hazard is required');

export const BulkControlMeasuresCreateSchema = z
  .array(ControlMeasuresCreateSchema)
  .min(1, 'At least one control measure is required');

export const HazardsFiltersSchema = z.object({
  search: z.string().optional(),
  mustIncludeObjectIds: z.array(z.string()).optional(),
  includeArchived: z.boolean().optional(),
  type: z.array(z.enum(hazardCategoryEnum.enumValues)).optional(),
  createdBy: z.array(z.string()).optional(),
  createdDateRange: DateRangeSchema.optional(),
});

export type HazardsFilters = z.infer<typeof HazardsFiltersSchema>;

export const ListHazardsSchema = PaginationInputSchema.and(SortInputSchema).and(HazardsFiltersSchema);

export const ExportHazardsSchema = SortInputSchema.and(HazardsFiltersSchema);

export const ControlMeasuresFiltersSchema = z.object({
  search: z.string().optional(),
  mustIncludeObjectIds: z.array(z.string()).optional(),
  includeArchived: z.boolean().optional(),
  type: z.array(z.enum(controlMeasureCategoryEnum.enumValues)).optional(),
  createdBy: z.array(z.string()).optional(),
  createdDateRange: DateRangeSchema.optional(),
});

export type ControlMeasuresFilters = z.infer<typeof ControlMeasuresFiltersSchema>;

export const ListControlMeasuresSchema = PaginationInputSchema.and(SortInputSchema).and(ControlMeasuresFiltersSchema);

export const ExportControlMeasuresSchema = SortInputSchema.and(ControlMeasuresFiltersSchema);
