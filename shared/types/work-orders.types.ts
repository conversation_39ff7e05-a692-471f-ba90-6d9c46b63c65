import { CreateCapasSchema } from './capas.types';
import { PaginationInputSchema } from './schema.types';
import z from 'zod';

// Work Order Status Enum (matching upstream values)
export const WorkOrderStatusEnum = z.enum(['open', 'inProgress', 'onHold', 'complete']);

// Work Order Priority Enum
export const WorkOrderPriorityEnum = z.enum(['low', 'medium', 'high']);

// Work Order Priority Number Enum (for backend compatibility)
export const WorkOrderPriorityNumberEnum = z.enum(['1', '2', '3']);

// Priority conversion utilities (re-exported for consistency)
export const convertNumberToPriority = (priorityNumber: number): 'low' | 'medium' | 'high' => {
  switch (priorityNumber) {
    case 1:
      return 'low';
    case 2:
      return 'medium';
    case 3:
      return 'high';
    default:
      return 'medium'; // Default to medium for unknown values
  }
};

export const convertPriorityToNumber = (priority: 'low' | 'medium' | 'high'): number => {
  switch (priority) {
    case 'low':
      return 1;
    case 'medium':
      return 2;
    case 'high':
      return 3;
    default:
      return 2; // Default to medium
  }
};

// Work Order Status Display Map
export const WORK_ORDER_STATUS_MAP: Record<z.infer<typeof WorkOrderStatusEnum>, string> = {
  [WorkOrderStatusEnum.enum.open]: 'Open',
  [WorkOrderStatusEnum.enum.inProgress]: 'In Progress',
  [WorkOrderStatusEnum.enum.onHold]: 'On Hold',
  [WorkOrderStatusEnum.enum.complete]: 'Complete',
};

// Work Order Priority Display Map
export const WORK_ORDER_PRIORITY_MAP: Record<z.infer<typeof WorkOrderPriorityEnum>, string> = {
  [WorkOrderPriorityEnum.enum.low]: 'Low',
  [WorkOrderPriorityEnum.enum.medium]: 'Medium',
  [WorkOrderPriorityEnum.enum.high]: 'High',
};

// Work Order Priority Number to Display Map
export const WORK_ORDER_PRIORITY_NUMBER_MAP: Record<number, string> = {
  1: 'Low',
  2: 'Medium',
  3: 'High',
};

export type WorkOrder = {
  id: string;
  workOrderNumber: string;
  title: string;
  currentStatus: z.infer<typeof WorkOrderStatusEnum>;
  priority: z.infer<typeof WorkOrderPriorityEnum>;
  dueDate: string;
  assignedTo: string;
  locationId: string;
  assetId: string;
  assetName: string;
  capaId?: string;
  capaSlug?: string;
  capaTitle?: string;
};

export const CreateWorkOrderFromCapaSchema = CreateCapasSchema.pick({
  title: true,
  actionsToAddress: true,
  priority: true,
  dueDate: true,
  locationId: true,
  assetId: true,
})
  .extend({
    id: z.string().min(1, 'CAPA ID is required'),
    slug: z.string().min(1, 'Slug is required'),
    userAssignedTo: z.string().optional(),
  })
  .extend({ dueDate: z.string().min(1, 'Due date is required') }); // avoid conflicts with the dueDate type from the schema

export type CreateWorkOrderFromCapaInput = z.infer<typeof CreateWorkOrderFromCapaSchema>;

export const WorkOrderSearchInputSchema = PaginationInputSchema.extend({
  search: z.string().nullish(),
  includes: z.array(z.string()).nullish(),
  mode: z.string().nullish(),
  capaId: z.array(z.string()).nullish(),
  sort: z.string().nullish().prefault('createdAt DESC'),
});

export type WorkOrderSearchInput = z.infer<typeof WorkOrderSearchInputSchema>;

export const CountWorkOrdersByCapaIdSchema = z.object({
  capaId: z.array(z.string()),
});

export type CountWorkOrdersByCapaIdInput = z.infer<typeof CountWorkOrdersByCapaIdSchema>;

export type CreateWorkOrderParams = {
  mainDescription: string; // Title
  note: string; // Description
  priorityNumber: number;
  dueDate: string;
  objectLocationForWorkOrder?: string;
  objectAsset?: string;
  userAssignedTo?: string;
  capaId: string;
};
