import { ControlMeasuresCategorySchema } from '@shared/types/settings.types';
import { z } from 'zod';

type ControlMeasureItem = {
  control_measure_id: string;
  name: string;
  type: z.infer<typeof ControlMeasuresCategorySchema>;
};

export const CONTROL_MEASURES: ControlMeasureItem[] = [
  // Engineering Controls
  { control_measure_id: 'fume_hood_local_exhaust', name: 'Fume Hood / Local Exhaust', type: 'engineering_controls' },
  { control_measure_id: 'spill_containment', name: 'Spill Containment', type: 'engineering_controls' },
  { control_measure_id: 'substitution_elimination', name: 'Substitution or Elimination', type: 'engineering_controls' },
  {
    control_measure_id: 'interlocks_safety_sensors',
    name: 'Interlocks / Safety Sensors',
    type: 'engineering_controls',
  },
  {
    control_measure_id: 'dust_collection_explosion_venting',
    name: 'Dust Collection / Explosion Venting',
    type: 'engineering_controls',
  },
  { control_measure_id: 'arc_resistant_barriers', name: 'Arc-Resistant Barriers', type: 'engineering_controls' },
  { control_measure_id: 'emergency_eyewash_shower', name: 'Emergency Eyewash / Shower', type: 'engineering_controls' },
  { control_measure_id: 'ventilation', name: 'Ventilation', type: 'engineering_controls' },
  { control_measure_id: 'machine_guarding', name: 'Machine Guarding', type: 'engineering_controls' },
  {
    control_measure_id: 'fire_suppression_extinguisher',
    name: 'Fire Suppression / Extinguisher',
    type: 'engineering_controls',
  },
  { control_measure_id: 'insulated_tools', name: 'Insulated Tools', type: 'engineering_controls' },

  // Administrative Controls – General
  { control_measure_id: 'lockout_tagout_loto', name: 'Lockout/Tagout (LOTO)', type: 'administrative_controls' },
  {
    control_measure_id: 'spotter_standby_personnel',
    name: 'Spotter / Standby Personnel',
    type: 'administrative_controls',
  },
  { control_measure_id: 'pre_task_risk_assessment', name: 'Pre-Task Risk Assessment', type: 'administrative_controls' },
  { control_measure_id: 'permit_to_work', name: 'Permit to Work', type: 'administrative_controls' },
  { control_measure_id: 'job_safety_analysis', name: 'Job Safety Analysis (JSA)', type: 'administrative_controls' },
  {
    control_measure_id: 'pre_job_safety_brief',
    name: 'Pre-Job Safety Brief / Toolbox Talk',
    type: 'administrative_controls',
  },

  // Administrative Controls – Permits & Written Plans
  { control_measure_id: 'confined_space_permit', name: 'Confined Space Permit', type: 'administrative_controls' },
  { control_measure_id: 'work_at_height_permit', name: 'Work-at-Height Permit', type: 'administrative_controls' },
  { control_measure_id: 'respirator_fit_testing', name: 'Respirator Fit Testing', type: 'administrative_controls' },
  {
    control_measure_id: 'radiation_work_procedure',
    name: 'Radiation Work Procedure / Dosimetry',
    type: 'administrative_controls',
  },
  { control_measure_id: 'hot_work_permit', name: 'Hot Work Permit', type: 'administrative_controls' },
  { control_measure_id: 'fall_protection_plan', name: 'Fall Protection Plan', type: 'administrative_controls' },
  {
    control_measure_id: 'bloodborne_pathogen_plan',
    name: 'Bloodborne Pathogen Exposure Control Plan',
    type: 'administrative_controls',
  },

  // Personal Protective Equipment (PPE)
  { control_measure_id: 'goggles', name: 'Goggles', type: 'personal_protective_equipment' },
  { control_measure_id: 'face_shield', name: 'Face Shield', type: 'personal_protective_equipment' },
  { control_measure_id: 'respirator', name: 'Respirator', type: 'personal_protective_equipment' },
  { control_measure_id: 'gloves_nitrile', name: 'Gloves (Nitrile)', type: 'personal_protective_equipment' },
  { control_measure_id: 'gloves_insulated', name: 'Gloves (Insulated)', type: 'personal_protective_equipment' },
  {
    control_measure_id: 'gloves_chemical_resistant',
    name: 'Gloves (Chemical-resistant)',
    type: 'personal_protective_equipment',
  },
  { control_measure_id: 'gloves_leather', name: 'Gloves (Leather)', type: 'personal_protective_equipment' },
  { control_measure_id: 'lab_coat', name: 'Lab Coat / Protective Clothing', type: 'personal_protective_equipment' },
  { control_measure_id: 'hi_vis_clothing', name: 'Hi-Vis Clothing', type: 'personal_protective_equipment' },
  { control_measure_id: 'chemical_apron', name: 'Chemical Apron', type: 'personal_protective_equipment' },
  { control_measure_id: 'hard_hat', name: 'Hard Hat', type: 'personal_protective_equipment' },
  { control_measure_id: 'steel_toed_boots', name: 'Steel-toed Boots', type: 'personal_protective_equipment' },
  {
    control_measure_id: 'fall_harness_lifeline',
    name: 'Fall Harness / Lifeline',
    type: 'personal_protective_equipment',
  },
  { control_measure_id: 'hearing_protection', name: 'Hearing Protection', type: 'personal_protective_equipment' },

  // Specialized Advanced Controls
  {
    control_measure_id: 'remote_energy_isolation',
    name: 'Remote Energy Isolation',
    type: 'specialized_advanced_controls',
  },
  {
    control_measure_id: 'equipment_shutdown_checklist',
    name: 'Equipment Shutdown Checklist',
    type: 'specialized_advanced_controls',
  },
  {
    control_measure_id: 'arc_flash_risk_assessment',
    name: 'Arc Flash Risk Assessment',
    type: 'specialized_advanced_controls',
  },
  {
    control_measure_id: 'safety_signage',
    name: 'Safety Signage / Visual Warnings',
    type: 'specialized_advanced_controls',
  },
  {
    control_measure_id: 'emergency_response_plan',
    name: 'Emergency Response Plan',
    type: 'specialized_advanced_controls',
  },
];
