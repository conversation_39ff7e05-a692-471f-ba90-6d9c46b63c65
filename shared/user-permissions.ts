import { z } from 'zod';

export const USER_ACCOUNT_KEYS = {
  ADMIN: 'ADMIN',
  TECHNICIAN: 'TECHNICIAN',
  VIEW_ONLY: 'VIEW_ONLY',
  REQUESTOR: 'REQUESTOR',
  LIMITED_TECHNICIAN: 'LIMITED_TECHNICIAN',
  VENDOR: 'VENDOR',
} as const;

export const USER_ACCOUNT_TYPES = {
  [USER_ACCOUNT_KEYS.ADMIN]: 'admin',
  [USER_ACCOUNT_KEYS.TECHNICIAN]: 'user',
  [USER_ACCOUNT_KEYS.VIEW_ONLY]: 'view-only',
} as const;

export const UPKEEP_USER_ACCOUNT_TYPES = {
  [USER_ACCOUNT_KEYS.ADMIN]: '1',
  [USER_ACCOUNT_KEYS.TECHNICIAN]: '2',
  [USER_ACCOUNT_KEYS.VIEW_ONLY]: '3',
  [USER_ACCOUNT_KEYS.REQUESTOR]: '4',
  [USER_ACCOUNT_KEYS.LIMITED_TECHNICIAN]: '5',
  [USER_ACCOUNT_KEYS.VENDOR]: '6',
} as const;

export const MODULES = {
  EHS_EVENT: 'ehs-event',
  EHS_CAPA: 'ehs-capa',
  EHS_ACCESS_POINT: 'ehs-access-point',
  EHS_OSHA_REPORTS: 'ehs-osha-reports',
  EHS_JHA: 'ehs-jha',
  OSHA_LOCATIONS: 'osha-locations',
  HAZARDS: 'hazards',
  CONTROL_MEASURES: 'control-measures',
  AI_ASSISTANT: 'ai-assistant',
  COMMENT: 'comment',
  SOP: 'sop',
} as const;

export const ALLOWED_ACTIONS = {
  CREATE: 'create',
  VIEW: 'view',
  EDIT: 'edit',
  DELETE: 'delete',
  EXPORT: 'export',
  COMMENT: 'comment',
} as const;

export const PERMISSION_LEVELS = {
  FULL: 'full',
  PARTIAL: 'partial',
  NONE: 'none',
} as const;

/**
 * Permission matrix - cleaner, more maintainable structure
 * Easy to see differences between user types at a glance
 */
export const PERMISSION_MATRIX = {
  [MODULES.EHS_ACCESS_POINT]: {
    [ALLOWED_ACTIONS.CREATE]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.VIEW]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.EDIT]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.EXPORT]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
  },
  [MODULES.EHS_EVENT]: {
    [ALLOWED_ACTIONS.CREATE]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.VIEW]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.PARTIAL,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.PARTIAL,
    },
    [ALLOWED_ACTIONS.EDIT]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.PARTIAL,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.EXPORT]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.COMMENT]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
  },
  [MODULES.EHS_CAPA]: {
    [ALLOWED_ACTIONS.CREATE]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.VIEW]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.PARTIAL,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.PARTIAL,
    },
    [ALLOWED_ACTIONS.EDIT]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.PARTIAL,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.EXPORT]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.COMMENT]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
  },
  [MODULES.EHS_OSHA_REPORTS]: {
    [ALLOWED_ACTIONS.CREATE]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.VIEW]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.EDIT]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.EXPORT]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
  },
  [MODULES.EHS_JHA]: {
    [ALLOWED_ACTIONS.CREATE]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.VIEW]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.PARTIAL,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.PARTIAL,
    },
    [ALLOWED_ACTIONS.EDIT]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.EXPORT]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
  },
  [MODULES.SOP]: {
    [ALLOWED_ACTIONS.CREATE]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.VIEW]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.PARTIAL,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.PARTIAL,
    },
    [ALLOWED_ACTIONS.EDIT]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.EXPORT]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
  },
  [MODULES.OSHA_LOCATIONS]: {
    [ALLOWED_ACTIONS.CREATE]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.VIEW]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.EDIT]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.EXPORT]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
  },
  [MODULES.HAZARDS]: {
    [ALLOWED_ACTIONS.CREATE]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.VIEW]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.FULL,
    },
    [ALLOWED_ACTIONS.EDIT]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.EXPORT]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
  },
  [MODULES.CONTROL_MEASURES]: {
    [ALLOWED_ACTIONS.CREATE]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.VIEW]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.FULL,
    },
    [ALLOWED_ACTIONS.EDIT]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.EXPORT]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.NONE,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
  },
  [MODULES.AI_ASSISTANT]: {
    [ALLOWED_ACTIONS.CREATE]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
  },
  [MODULES.COMMENT]: {
    [ALLOWED_ACTIONS.CREATE]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.EDIT]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
    [ALLOWED_ACTIONS.VIEW]: {
      [USER_ACCOUNT_TYPES.ADMIN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.TECHNICIAN]: PERMISSION_LEVELS.FULL,
      [USER_ACCOUNT_TYPES.VIEW_ONLY]: PERMISSION_LEVELS.NONE,
    },
  },
} as const;

export const AllowedActionsSchema = z.enum(Object.values(ALLOWED_ACTIONS));
export const PermissionLevelsSchema = z.enum(Object.values(PERMISSION_LEVELS));
export const ModulesSchema = z.enum(Object.values(MODULES));

export const UserPermissionSchema = z.partialRecord(
  ModulesSchema,
  z.partialRecord(AllowedActionsSchema, PermissionLevelsSchema),
);

export const UserAccountKeysSchema = z.enum(USER_ACCOUNT_KEYS);
export const UserAccountTypeSchema = z.enum(USER_ACCOUNT_TYPES);

export type AllowedActions = z.infer<typeof AllowedActionsSchema>;
export type UserPermission = z.infer<typeof UserPermissionSchema>;
export type Modules = z.infer<typeof ModulesSchema>;

export type PermissionLevel = z.infer<typeof PermissionLevelsSchema>;
export type UserAccountType = z.infer<typeof UserAccountTypeSchema>;
export type UserAccountKeys = z.infer<typeof UserAccountKeysSchema>;

/**
 * Generate permission arrays from matrix
 */
export const generatePermissions = (
  userType:
    | typeof USER_ACCOUNT_TYPES.ADMIN
    | typeof USER_ACCOUNT_TYPES.TECHNICIAN
    | typeof USER_ACCOUNT_TYPES.VIEW_ONLY,
): Record<keyof typeof PERMISSION_MATRIX, Record<AllowedActions, PermissionLevel>> => {
  const permissions: Record<keyof typeof PERMISSION_MATRIX, Record<AllowedActions, PermissionLevel>> = {} as Record<
    keyof typeof PERMISSION_MATRIX,
    Record<AllowedActions, PermissionLevel>
  >;

  for (const [moduleName, actions] of Object.entries(PERMISSION_MATRIX)) {
    permissions[moduleName as keyof typeof PERMISSION_MATRIX] = {} as Record<AllowedActions, PermissionLevel>;

    for (const [action, levels] of Object.entries(actions)) {
      permissions[moduleName as keyof typeof PERMISSION_MATRIX][action as AllowedActions] = levels[
        userType
      ] as PermissionLevel;
    }
  }

  return permissions;
};

export const PERMISSIONS = {
  [USER_ACCOUNT_TYPES.ADMIN]: generatePermissions(USER_ACCOUNT_TYPES.ADMIN),
  [USER_ACCOUNT_TYPES.TECHNICIAN]: generatePermissions(USER_ACCOUNT_TYPES.TECHNICIAN),
  [USER_ACCOUNT_TYPES.VIEW_ONLY]: generatePermissions(USER_ACCOUNT_TYPES.VIEW_ONLY),
};
