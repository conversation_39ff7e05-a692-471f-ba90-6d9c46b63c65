export const ROUTES = {
  LOGIN: '/web/login',
  UPKEEP_WORK_ORDERS: '/web/work-orders',
  BASE: '/ehs',
  NOT_FOUND: '/ehs/404',
  PUBLIC_BASE: '/ehs/public',
  EVENT_NEW: '/ehs/events/new',
  EVENT_PUBLIC_REPORT: '/ehs/public/events/new',
  EVENT_EDIT: '/ehs/events/:id/edit',
  EVENT_DETAILS: '/ehs/events/:id',
  EVENT_LIST: '/ehs/events',
  ACCESS_POINTS_LIST: '/ehs/access-points',
  ACCESS_POINTS_NEW: '/ehs/access-points/new',
  OSHA_LOCATIONS_LIST: '/ehs/osha-locations',
  OSHA_LOCATIONS_NEW: '/ehs/osha-locations/new',
  HAZARDS_LIST: '/ehs/hazards',
  HAZARDS_NEW: '/ehs/hazards/new',
  CONTROL_MEASURES_LIST: '/ehs/control-measures',
  CONTROL_MEASURES_NEW: '/ehs/control-measures/new',
  CAPA_NEW: '/ehs/capas/new',
  CAPA_EDIT: '/ehs/capas/:id/edit',
  CAPA_DETAILS: '/ehs/capas/:id',
  CAPA_LIST: '/ehs/capas',
  OSHA_REPORTS: '/ehs/osha/reports',
  OSHA_REPORT_NEW: '/ehs/osha/reports/new',
  OSHA_REPORT_EDIT: '/ehs/osha/reports/:id/edit',
  OSHA_REPORT_DETAILS: '/ehs/osha/reports/:id',
  OSHA_SUMMARY: '/ehs/osha/summary',
  OSHA_AGENCY_REPORTS: '/ehs/osha/agency-reports',
  OSHA_AGENCY_REPORTS_NEW: '/ehs/osha/agency-reports/new',
  OSHA_AGENCY_REPORT_DETAILS: '/ehs/osha/agency-reports/:id',
  OSHA_AGENCY_REPORT_EDIT: '/ehs/osha/agency-reports/:id/edit',
  // SOP routes
  SOP_NEW: '/ehs/sops/new',
  SOP_EDIT: '/ehs/sops/:id/edit',
  SOP_DETAILS: '/ehs/sops/:id',
  SOP_LIST: '/ehs/sops',
  JHA_NEW: '/ehs/jha/new',
  JHA_EDIT: '/ehs/jha/:id/edit',
  JHA_DETAILS: '/ehs/jha/:id',
  JHA_LIST: '/ehs/jha',
  BUILD_EVENT_EDIT_PATH: (id: string) => `/ehs/events/${id}/edit`,
  BUILD_EVENT_DETAILS_PATH: (id: string) => `/ehs/events/${id}`,
  BUILD_CAPA_EDIT_PATH: (id: string) => `/ehs/capas/${id}/edit`,
  BUILD_CAPA_DUPLICATE_PATH: (sourceCapaId: string) => `${ROUTES.CAPA_NEW}?sourceCapaId=${sourceCapaId}`,
  BUILD_CAPA_DETAILS_PATH: (id: string) => `/ehs/capas/${id}`,
  BUILD_SOP_EDIT_PATH: (id: string) => `/ehs/sops/${id}/edit`,
  BUILD_SOP_DUPLICATE_PATH: (sourceSopId: string) => `${ROUTES.SOP_NEW}?sourceSopId=${sourceSopId}`,
  BUILD_SOP_DETAILS_PATH: (id: string) => `/ehs/sops/${id}`,
  BUILD_OSHA_REPORT_EDIT_PATH: (id: string) => `/ehs/osha/reports/${id}/edit`,
  BUILD_OSHA_REPORT_DETAILS_PATH: (id: string) => `/ehs/osha/reports/${id}`,
  BUILD_OSHA_AGENCY_REPORT_DETAILS_PATH: (id: string) => `/ehs/osha/agency-reports/${id}`,
  BUILD_OSHA_AGENCY_REPORT_EDIT_PATH: (id: string) => `/ehs/osha/agency-reports/${id}/edit`,
  BUILD_JHA_EDIT_PATH: (id: string) => `/ehs/jha/${id}/edit`,
  BUILD_JHA_DETAILS_PATH: (instanceId: string, versionId?: string) =>
    `/ehs/jha/${instanceId}${versionId ? `?version=${versionId}` : ''}`,
  BUILD_WORK_ORDER_DETAILS_PATH: (id: string) => `/web/work-orders/list?id=${id}&modal=workorder&tab=details`,
} as const;
