import { defineConfig } from 'vitest/config';
import { resolve } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __dirname = dirname(fileURLToPath(import.meta.url));

export default defineConfig({
  test: {
    globals: true,
    setupFiles: ['./vitest.setup.ts'],
    maxConcurrency: 1,
    testTimeout: 30000, // 30 seconds for integration tests
    projects: [
      {
        test: {
          name: 'server',
          environment: 'node',
          include: ['server/**/__tests__/**/*.test.ts', 'server/test/**/*.test.ts'],
          exclude: ['node_modules/**', 'dist/**', 'server/upkeep/__tests__/**'],
          globals: true,
          setupFiles: ['./vitest.setup.ts'],
          maxConcurrency: 1,
          testTimeout: 30000, // 30 seconds for server integration tests
        },
        resolve: {
          alias: {
            '@server': resolve(__dirname, './server'),
            '@shared': resolve(__dirname, './shared'),
            '@client': resolve(__dirname, './client'),
          },
        },
      },
      {
        test: {
          name: 'client',
          environment: 'jsdom',
          include: ['client/**/__tests__/**/*.test.ts'],
          exclude: ['node_modules/**', 'dist/**'],
          globals: true,
          setupFiles: ['./vitest.setup.ts'],
          maxConcurrency: 1,
          testTimeout: 15000, // 15 seconds for client tests
        },
        resolve: {
          alias: {
            '@server': resolve(__dirname, './server'),
            '@shared': resolve(__dirname, './shared'),
            '@client': resolve(__dirname, './client'),
          },
        },
      },
    ],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      include: ['server/**/*.ts', 'client/**/*.ts'],
      exclude: [
        'server/**/__tests__/**/*.ts',
        'server/**/*.spec.ts',
        'client/**/__tests__/**/*.ts',
        'client/**/*.spec.ts',
      ],
      // Thresholds disabled - CI won't fail due to coverage requirements
      // thresholds: {
      //   statements: 35,
      //   branches: 50,
      //   functions: 60,
      //   lines: 35,
      // }
    },
  },
  resolve: {
    alias: {
      '@server': resolve(__dirname, './server'),
      '@shared': resolve(__dirname, './shared'),
      '@client': resolve(__dirname, './client'),
    },
  },
});
