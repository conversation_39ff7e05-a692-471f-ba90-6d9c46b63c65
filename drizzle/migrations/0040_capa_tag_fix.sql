-- Custom SQL migration file, put your code below! --
-- DO $$
-- BEGIN
--     IF NOT EXISTS (
--         SELECT 1
--         FROM pg_enum e
--         JOIN pg_type t ON e.enumtypid = t.oid
--         WHERE t.typname = 'capa_tags'
--           AND e.enumlabel = 'safety'
--     ) THEN
--         ALTER TYPE "public"."capa_tags" ADD VALUE 'safety';
--     END IF;
-- END$$;
-- DO $$
-- BEGIN
--     IF EXISTS (
--         SELECT 1
--         FROM information_schema.columns
--         WHERE table_schema = 'public'
--           AND table_name = 'capas'
--           AND column_name = 'tags'
--           AND udt_name <> 'capa_tags[]'
--     ) THEN
--         ALTER TABLE "capas"
--         ALTER COLUMN "tags"
--         SET DATA TYPE "public"."capa_tags"[]
--         USING "tags"::"public"."capa_tags"[];
--     END IF;
-- END$$;