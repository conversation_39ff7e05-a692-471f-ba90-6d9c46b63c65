CREATE TABLE "notifications" (
	"id" varchar(24) PRIMARY KEY NOT NULL,
	"upkeep_company_id" varchar(10) NOT NULL,
	"title" varchar(255) NOT NULL,
	"description" text,
	"url" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp,
	"archived_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "user_notifications" (
	"id" varchar(24) PRIMARY KEY NOT NULL,
	"user_id" varchar(10) NOT NULL,
	"notification_id" varchar(24) NOT NULL,
	"read_at" timestamp,
	"archived_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp,
	CONSTRAINT "user_notifications_unique" UNIQUE("user_id","notification_id")
);
--> statement-breakpoint
ALTER TABLE "user_notifications" ADD CONSTRAINT "user_notifications_notification_id_notifications_id_fk" FOREIGN KEY ("notification_id") REFERENCES "public"."notifications"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "notifications_upkeep_company_id_index" ON "notifications" USING btree ("upkeep_company_id");--> statement-breakpoint
CREATE INDEX "notifications_archived_at_index" ON "notifications" USING btree ("archived_at");--> statement-breakpoint
CREATE INDEX "notifications_company_archived_created_index" ON "notifications" USING btree ("upkeep_company_id","archived_at","created_at");--> statement-breakpoint
CREATE INDEX "user_notifications_user_id_index" ON "user_notifications" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "user_notifications_notification_id_index" ON "user_notifications" USING btree ("notification_id");--> statement-breakpoint
CREATE INDEX "user_notifications_user_archived_read_index" ON "user_notifications" USING btree ("user_id","archived_at","read_at");--> statement-breakpoint
CREATE INDEX "user_notifications_notification_user_archived_index" ON "user_notifications" USING btree ("notification_id","user_id","archived_at");