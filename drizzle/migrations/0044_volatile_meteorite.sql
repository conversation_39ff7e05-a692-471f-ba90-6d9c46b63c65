CREATE TYPE "public"."sop_section_type" AS ENUM('general', 'emergency', 'step', 'pre_procedure', 'procedure', 'post_procedure');--> statement-breakpoint
ALTER TYPE "public"."jha_status" RENAME TO "approval_status";--> statement-breakpoint
ALTER TYPE "public"."audit_trail_entity_type" ADD VALUE 'sop';--> statement-breakpoint
ALTER TYPE "public"."entity_type" ADD VALUE 'sop';--> statement-breakpoint
ALTER TYPE "public"."entity_type" ADD VALUE 'sop_section';--> statement-breakpoint
CREATE TABLE "sop_section" (
	"id" varchar(24) PRIMARY KEY NOT NULL,
	"label" text,
	"value" text NOT NULL,
	"section_type" "sop_section_type" NOT NULL,
	"hazard_ids" varchar(24)[],
	"control_measure_ids" varchar(24)[],
	"severity" integer,
	"sop_id" varchar(24) NOT NULL,
	"serial" integer DEFAULT 1 NOT NULL,
	"upkeep_company_id" varchar(10) NOT NULL,
	"created_by" varchar(10) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp,
	"archived_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "sops" (
	"id" varchar(24) PRIMARY KEY NOT NULL,
	"upkeep_company_id" varchar(10) NOT NULL,
	"title" varchar(255) NOT NULL,
	"purpose" text,
	"responsibilities" text,
	"slug" text,
	"version" varchar(10) DEFAULT '1.0' NOT NULL,
	"instance_id" uuid,
	"owner_id" varchar(10) NOT NULL,
	"approver_id" varchar(10) NOT NULL,
	"status" "approval_status" DEFAULT 'draft' NOT NULL,
	"review_date" timestamp,
	"location_id" varchar(10),
	"asset_ids" varchar(10)[],
	"highest_severity" integer DEFAULT 0 NOT NULL,
	"notes" text,
	"is_public" boolean DEFAULT false NOT NULL,
	"reason_for_revision" text,
	"created_by" varchar(10) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp,
	"archived_at" timestamp,
	CONSTRAINT "sop_unique_index" UNIQUE("slug","upkeep_company_id","version")
);
--> statement-breakpoint
ALTER TABLE "sop_section" ADD CONSTRAINT "sop_section_sop_id_sops_id_fk" FOREIGN KEY ("sop_id") REFERENCES "public"."sops"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "sop_section_upkeep_company_id_index" ON "sop_section" USING btree ("upkeep_company_id");--> statement-breakpoint
CREATE INDEX "sop_section_serial_index" ON "sop_section" USING btree ("serial","archived_at","sop_id","section_type");--> statement-breakpoint
CREATE INDEX "sop_steps_upkeep_company_id_index" ON "sops" USING btree ("upkeep_company_id");--> statement-breakpoint
CREATE INDEX "sop_steps_serial_index" ON "sops" USING btree ("upkeep_company_id","archived_at","created_at");--> statement-breakpoint
CREATE INDEX "sop_steps_instance_id_index" ON "sops" USING btree ("instance_id","upkeep_company_id");

-- Add function to generate SOP slug
CREATE OR REPLACE FUNCTION generate_sop_slug(p_upkeep_company_id TEXT, p_version TEXT)
RETURNS TEXT AS $$
DECLARE
    seq_name TEXT := format('sop_slug_seq_%s', LOWER(p_upkeep_company_id));
    next_slug INTEGER;
BEGIN
    IF NOT EXISTS (
        SELECT FROM pg_class WHERE relkind = 'S' AND relname = seq_name
    ) THEN
        EXECUTE format('CREATE SEQUENCE %I START 1;', seq_name);
    END IF;

    EXECUTE format('SELECT nextval(%L)', seq_name) INTO next_slug;

    RETURN format('SOP-%s-%s', LPAD(next_slug::TEXT, 4, '0'), p_version);
END;
$$ LANGUAGE plpgsql;

-- Add function to get next SOP version
CREATE OR REPLACE FUNCTION get_next_sop_version(p_instance_id UUID)
RETURNS TEXT AS $$
DECLARE
    count_versions INTEGER;
BEGIN
    SELECT COUNT(*) INTO count_versions
    FROM sops
    WHERE instance_id = p_instance_id;

    RETURN (count_versions + 1)::TEXT || '.0';
END;
$$ LANGUAGE plpgsql;

-- Add trigger to generate slug, version and instance_id on insert
CREATE OR REPLACE FUNCTION sop_before_insert_trigger()
RETURNS TRIGGER AS $$
DECLARE
    resolved_instance_id UUID;
    resolved_version TEXT;
    base_slug TEXT;
    base_number TEXT;
BEGIN
    -- Set instance_id if missing
    IF NEW.instance_id IS NULL THEN
        NEW.instance_id := gen_random_uuid();
    END IF;
    resolved_instance_id := NEW.instance_id;

    -- Calculate version
    resolved_version := get_next_sop_version(resolved_instance_id);
    NEW.version := resolved_version;

    -- Set updated_at for current insert
    NEW.updated_at := now();

    -- New instance → generate new slug
    IF resolved_version = '1.0' THEN
        NEW.slug := generate_sop_slug(NEW.upkeep_company_id::TEXT, resolved_version::TEXT);
    ELSE
        -- Existing instance → reuse base number for slug
        SELECT slug INTO base_slug
        FROM sops
        WHERE instance_id = resolved_instance_id AND version = '1.0'
        LIMIT 1;

        base_number := regexp_replace(base_slug, '^SOP-(\d{4})-.*$', '\1');
        NEW.slug := format('SOP-%s-%s', base_number, resolved_version);

        -- Archive all previous versions
        UPDATE sops
        SET archived_at = now(), updated_at = now()
        WHERE instance_id = resolved_instance_id
          AND id != NEW.id;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add trigger to generate slug, version and instance_id on insert
CREATE TRIGGER sop_before_insert
BEFORE INSERT ON sops
FOR EACH ROW
EXECUTE FUNCTION sop_before_insert_trigger();