CREATE TABLE "capas_jhas" (
	"id" varchar(24) PRIMARY KEY NOT NULL,
	"upkeep_company_id" varchar(10) NOT NULL,
	"capa_id" varchar(24) NOT NULL,
	"jha_instance_id" uuid NOT NULL,
	"created_by" varchar(10) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"deleted_at" timestamp,
	CONSTRAINT "capas_jhas_capa_instance_unique" UNIQUE("capa_id","jha_instance_id")
);
--> statement-breakpoint
ALTER TABLE "capas_jhas" ADD CONSTRAINT "capas_jhas_capa_id_capas_id_fk" FOREIGN KEY ("capa_id") REFERENCES "public"."capas"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "capas_jhas_company_index" ON "capas_jhas" USING btree ("upkeep_company_id");--> statement-breakpoint
CREATE INDEX "capas_jhas_capa_index" ON "capas_jhas" USING btree ("capa_id");--> statement-breakpoint
CREATE INDEX "capas_jhas_jha_instance_index" ON "capas_jhas" USING btree ("jha_instance_id");