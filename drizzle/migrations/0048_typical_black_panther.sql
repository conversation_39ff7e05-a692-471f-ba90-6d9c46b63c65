CREATE TABLE "disclaimer_trail" (
	"id" varchar(24) PRIMARY KEY NOT NULL,
	"upkeep_company_id" varchar(10) NOT NULL,
	"entity_type" varchar(50) NOT NULL,
	"entity_id" varchar(24) NOT NULL,
	"type" varchar(100) NOT NULL,
	"data" jsonb NOT NULL,
	"accepted_by" varchar(10) NOT NULL,
	"accepted_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "disclaimer_trail_unique_acceptance" UNIQUE("upkeep_company_id","entity_type","entity_id","type","accepted_by")
);
--> statement-breakpoint
CREATE INDEX "disclaimer_trail_lookup_index" ON "disclaimer_trail" USING btree ("upkeep_company_id","entity_type","entity_id","type","accepted_by");--> statement-breakpoint
CREATE INDEX "disclaimer_trail_entity_company_index" ON "disclaimer_trail" USING btree ("upkeep_company_id","entity_type","entity_id");--> statement-breakpoint
CREATE INDEX "disclaimer_trail_user_company_index" ON "disclaimer_trail" USING btree ("upkeep_company_id","accepted_by","accepted_at");--> statement-breakpoint
CREATE INDEX "disclaimer_trail_date_company_index" ON "disclaimer_trail" USING btree ("upkeep_company_id","accepted_at");