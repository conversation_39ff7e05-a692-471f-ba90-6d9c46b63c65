DROP INDEX "capa_archived_index";--> statement-breakpoint
DROP INDEX "event_archived_index";--> statement-breakpoint
DROP INDEX "osha_agency_report_company_year_osha_location_index";--> statement-breakpoint
ALTER TABLE "capas" ADD COLUMN "archived_at" timestamp;--> statement-breakpoint
ALTER TABLE "events" ADD COLUMN "archived_at" timestamp;--> statement-breakpoint
ALTER TABLE "osha_agency_report" ADD COLUMN "archived_at" timestamp;--> statement-breakpoint
UPDATE "access_points" SET "archived_at" = NOW() WHERE "archived" = true;--> statement-breakpoint
UPDATE "capas" SET "archived_at" = NOW() WHERE "archived" = true;--> statement-breakpoint
UPDATE "events" SET "archived_at" = NOW() WHERE "archived" = true;--> statement-breakpoint
UPDATE "osha_agency_report" SET "archived_at" = NOW() WHERE "archived" = true;--> statement-breakpoint
UPDATE "osha_company_information" SET "archived_at" = NOW() WHERE "archived" = true;--> statement-breakpoint
UPDATE "osha_reports" SET "archived_at" = NOW() WHERE "archived" = true;--> statement-breakpoint
CREATE INDEX "capa_archived_at_index" ON "capas" USING btree ("archived_at");--> statement-breakpoint
CREATE INDEX "event_archived_at_index" ON "events" USING btree ("archived_at");--> statement-breakpoint
CREATE INDEX "osha_agency_report_company_year_osha_location_index" ON "osha_agency_report" USING btree ("upkeep_company_id","archived_at","created_at","osha_location_id");--> statement-breakpoint
ALTER TABLE "access_points" DROP COLUMN "archived";--> statement-breakpoint
ALTER TABLE "capas" DROP COLUMN "archived";--> statement-breakpoint
ALTER TABLE "events" DROP COLUMN "archived";--> statement-breakpoint
ALTER TABLE "osha_agency_report" DROP COLUMN "archived";--> statement-breakpoint
ALTER TABLE "osha_company_information" DROP COLUMN "archived";--> statement-breakpoint
ALTER TABLE "osha_reports" DROP COLUMN "archived";