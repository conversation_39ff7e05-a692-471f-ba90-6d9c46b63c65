-- Phase 1: Add new entity columns and migrate data (keep old columns for zero-downtime)
-- This migration adds the new entity_type and entity_id columns, migrates existing data,
-- and keeps the old event_id and capa_id columns to prevent downtime during deployment.

-- Step 1: Add new columns as nullable first
ALTER TABLE "comments" ADD COLUMN "entity_type" "entity_type";
--> statement-breakpoint
ALTER TABLE "comments" ADD COLUMN "entity_id" varchar(24);
--> statement-breakpoint

-- Step 2: Migrate existing data from old columns to new columns
UPDATE "comments" SET "entity_type" = 'event', "entity_id" = "event_id" WHERE "event_id" IS NOT NULL;
--> statement-breakpoint
UPDATE "comments" SET "entity_type" = 'capa', "entity_id" = "capa_id" WHERE "capa_id" IS NOT NULL;
--> statement-breakpoint

-- Step 3: Make new columns NOT NULL (now that data is migrated)
ALTER TABLE "comments" ALTER COLUMN "entity_type" SET NOT NULL;
--> statement-breakpoint
ALTER TABLE "comments" ALTER COLUMN "entity_id" SET NOT NULL;
--> statement-breakpoint

-- Step 4: Create new index for better query performance
CREATE INDEX "comment_entity_index" ON "comments" USING btree ("entity_type","entity_id");
--> statement-breakpoint

-- Note: Old columns (event_id, capa_id) and their constraints are kept for now
-- They will be dropped in Phase 2 migration after the new code is deployed
