ALTER TYPE "public"."control_measure_category" ADD VALUE 'custom';--> statement-breakpoint
ALTER TYPE "public"."hazard_category" ADD VALUE 'custom';--> statement-breakpoint
ALTER TABLE "jha" ALTER COLUMN "status" SET DATA TYPE text;--> statement-breakpoint
ALTER TABLE "jha" ALTER COLUMN "status" SET DEFAULT 'draft'::text;--> statement-breakpoint
DROP TYPE "public"."jha_status";--> statement-breakpoint
CREATE TYPE "public"."jha_status" AS ENUM('draft', 'review', 'approved');--> statement-breakpoint
ALTER TABLE "jha" ALTER COLUMN "status" SET DEFAULT 'draft'::"public"."jha_status";--> statement-breakpoint
ALTER TABLE "jha" ALTER COLUMN "status" SET DATA TYPE "public"."jha_status" USING "status"::"public"."jha_status";--> statement-breakpoint
ALTER TABLE "jha" ADD COLUMN "reason_for_revision" text;