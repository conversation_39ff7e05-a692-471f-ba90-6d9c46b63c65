apiVersion: batch/v1
kind: Job
metadata:
  name: ehs-drop-database-job
spec:
  backoffLimit: 0
  ttlSecondsAfterFinished: 172800
  template:
    spec:
      restartPolicy: Never
      containers:
      - name: ehs-drop-database-job
        image: 967579385234.dkr.ecr.us-east-1.amazonaws.com/ehs:develop
        command: ["/bin/sh", "-c", "pnpm ehs-drop-database"]
        imagePullPolicy: Always
        env:
        - name: EHS_DROP_DATABASE_REGEXP_ALLOW_LIST
          value: 'upkeep_(ashish|bcdr|gary|manish|nikhil|zuora|dev|qa|integration|staging\d)\d*_ehs'
        - name: PG_MIGRATION_CONNECTION
          valueFrom:
            secretKeyRef:
              key: pg-migration-connection
              name: pg-migration
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              key: PG_CONNECTION_EHS
              name: upkeep-config
