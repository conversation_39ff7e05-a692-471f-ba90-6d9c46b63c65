#!/usr/bin/env node

import { URL } from 'url';
import pkg from 'pg';
const { Client } = pkg;

const required_vars = ['DATABASE_URL', 'PG_MIGRATION_CONNECTION', 'EHS_DROP_DATABASE_REGEXP_ALLOW_LIST'];
const missing_vars = required_vars.filter(varName => !process.env[varName]);
if (missing_vars.length > 0) {
  console.error(`error: missing environment variables: ${missing_vars.join(', ')}`);
  process.exit(1);
}

const PG_MIGRATION_CONNECTION = process.env.PG_MIGRATION_CONNECTION;
const DATABASE_URL = process.env.DATABASE_URL;
const EHS_DROP_DATABASE_REGEXP_ALLOW_LIST = process.env.EHS_DROP_DATABASE_REGEXP_ALLOW_LIST;

async function dropAndCreateDatabase(pgDatabase, pgUser, connectionString) {
  const client = new Client({
    connectionString: connectionString
  });
  
  try {
    await client.connect();
    await client.query(`DROP DATABASE IF EXISTS "${pgDatabase}" WITH (FORCE)`);
    await client.query(`CREATE DATABASE "${pgDatabase}"`);
    await client.query(`GRANT CONNECT ON DATABASE "${pgDatabase}" TO "${pgUser}"`);
    await client.query(`GRANT CREATE ON DATABASE "${pgDatabase}" TO "${pgUser}"`);
    console.log(`database ${pgDatabase} dropped and recreated successfully`);
  } catch (error) {
    console.error('Database operation failed:', error.message);
    process.exit(1);
  } finally {
    await client.end();
  }
}

try {
  const dbUrl = new URL(DATABASE_URL);
  const pgUser = dbUrl.username;
  const pgDatabase = dbUrl.pathname.slice(1); // Remove leading slash

  const allowedPattern = new RegExp(EHS_DROP_DATABASE_REGEXP_ALLOW_LIST);
  if (!allowedPattern.test(pgDatabase)) {
    console.error(`error: deleting database name '${pgDatabase}' is not allowed. Must match regexp pattern: ${EHS_DROP_DATABASE_REGEXP_ALLOW_LIST}`);
    process.exit(1);
  }
  
  await dropAndCreateDatabase(pgDatabase, pgUser, PG_MIGRATION_CONNECTION);
  process.exit(0);
} catch (error) {
  console.error(`error: failed to parse database URL: ${error.message}`);
  process.exit(1);
}