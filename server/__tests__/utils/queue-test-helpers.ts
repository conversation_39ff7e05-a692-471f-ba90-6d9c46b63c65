import { Capa } from '@shared/types/capas.types';
import { CapaAssignedNotificationJobPayload, CapaUpdateNotificationJobPayload } from '@shared/types/queues.types';
import { User } from '@shared/types/users.types';
import { JobsOptions } from 'bullmq';
import { expect, MockedFunction, vi } from 'vitest';

// Helper types for more flexible mocking
type MockCapaUpdatePayload = {
  capa?: Partial<CapaUpdateNotificationJobPayload['capa']>;
  user?: Partial<User>;
  headers?: Record<string, string>;
  needPartialCheck?: boolean;
  actionPrefix?: string;
};

type MockCapaAssignedPayload = {
  capa?: Partial<CapaAssignedNotificationJobPayload['capa']>;
  user?: Partial<User>;
  headers?: Record<string, string>;
  needPartialCheck?: boolean;
};

/**
 * Test utilities for queue and job testing
 */

// Type definitions for mock objects
interface MockQueueJob {
  id: string;
  data: CapaUpdateNotificationJobPayload | CapaAssignedNotificationJobPayload;
  opts?: JobsOptions;
  queueName: string;
  attemptsMade: number;
}

interface MockJob {
  id: string;
  attemptsMade: number;
  data: CapaUpdateNotificationJobPayload | CapaAssignedNotificationJobPayload;
  [key: string]: unknown;
}

interface MockQueue {
  add: MockedFunction<(queueName: string, jobData: unknown, options?: JobsOptions) => Promise<MockQueueJob>>;
  close: MockedFunction<() => void>;
  getJob: MockedFunction<(jobId: string) => MockQueueJob | undefined>;
  getJobs: MockedFunction<() => Promise<MockQueueJob[]>>;
  clean: MockedFunction<() => void>;
}

interface MockLogger {
  info: MockedFunction<(message: string, meta?: Record<string, unknown>) => void>;
  error: MockedFunction<(message: string, meta?: Record<string, unknown>) => void>;
  warn: MockedFunction<(message: string, meta?: Record<string, unknown>) => void>;
  debug: MockedFunction<(message: string, meta?: Record<string, unknown>) => void>;
  [key: string]: MockedFunction<(message: string, meta?: Record<string, unknown>) => void>;
}

// Mock job factory
export const createMockJob = (overrides: Partial<MockJob> = {}): MockJob => ({
  id: `job-${Date.now()}`,
  attemptsMade: 0,
  data: createMockJobPayload(),
  ...overrides,
});

// Mock job payload factory for CAPA updates - matches router output structure
export const createMockJobPayload = (overrides: MockCapaUpdatePayload = {}): CapaUpdateNotificationJobPayload => ({
  capa: {
    id: 'capa-test-123',
    title: 'Test CAPA',
    slug: 'CAPA-TEST-001',
    ownerId: 'owner-123',
    teamMembersToNotify: ['user-456', 'user-789'],
    archivedAt: null,
    locationId: 'location-123',
    eventId: null,
    status: 'open',
    createdBy: 'user-123',
    upkeepCompanyId: '1',
    type: 'corrective',
    priority: 'medium',
    dueDate: new Date('2024-12-31'),
    rcaFindings: 'Test RCA findings',
    ...(overrides.capa || {}),
  },
  user: {
    id: 'user-123',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    fullName: 'Test User',
    upkeepCompanyId: '1',
    username: 'testuser',
    permissions: {},
    featureFlags: {},
    role: 'admin',
    hasEhsEnabled: true,
    ...(overrides.user || {}),
  },
  headers: {
    'x-user-token': 'test-token',
    cookie: 'test-cookie',
    ...(overrides.headers || {}),
  },
  needPartialCheck: overrides.needPartialCheck ?? false,
  actionPrefix: overrides.actionPrefix ?? 'Updated',
});

// Mock job payload factory for CAPA assignment - matches router output structure
export const createMockAssignedJobPayload = (
  overrides: MockCapaAssignedPayload = {},
): CapaAssignedNotificationJobPayload => ({
  capa: {
    id: 'capa-test-123',
    title: 'Test CAPA',
    slug: 'CAPA-TEST-001',
    ownerId: 'owner-123',
    teamMembersToNotify: ['user-456', 'user-789'],
    locationId: 'location-123',
    eventId: null,
    status: 'open',
    createdBy: 'user-123',
    upkeepCompanyId: '1',
    type: 'corrective',
    priority: 'medium',
    dueDate: new Date('2024-12-31'),
    rcaFindings: 'Test RCA findings',
    summary: 'Test CAPA summary',
    ...(overrides.capa || {}),
  },
  user: {
    id: 'user-123',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    fullName: 'Test User',
    upkeepCompanyId: '1',
    username: 'testuser',
    permissions: {},
    featureFlags: {},
    role: 'admin',
    hasEhsEnabled: true,
    ...(overrides.user || {}),
  },
  headers: {
    'x-user-token': 'test-token',
    cookie: 'test-cookie',
    ...(overrides.headers || {}),
  },
  needPartialCheck: overrides.needPartialCheck ?? false,
});

// Mock CAPA factory
export const createMockCapa = (overrides: Partial<Capa> = {}): Capa =>
  ({
    id: 'capa-test-123',
    title: 'Test CAPA Title',
    slug: 'CAPA-TEST-001',
    ownerId: 'owner-123',
    teamMembersToNotify: ['user-456', 'user-789'],
    archivedAt: null,
    locationId: 'location-123',
    eventId: null,
    type: 'corrective' as const,
    priority: 'medium' as const,
    status: 'open' as const,
    createdAt: new Date(),
    updatedAt: new Date(),
    // Required fields with defaults
    upkeepCompanyId: '1',
    createdBy: 'user-123',
    rcaFindings: 'Test RCA findings',
    actionsToAddress: 'Test actions to address',
    dueDate: new Date('2024-12-31'),
    // Additional required fields
    summary: null,
    assetId: null,
    rcaMethod: 'not_selected',
    rootCauses: null,
    otherRootCause: null,
    aiSuggestedAction: null,
    aiConfidenceScore: null,
    tags: null,
    privateToAdmins: false,
    triggerWorkOrder: false,
    actionsImplemented: null,
    implementationDate: null,
    implementedBy: null,
    voeCompletedDate: null,
    voeAssignedTo: null,
    voeResults: null,
    effectivenessStatus: 'not_evaluated',
    ...overrides,
  }) as Capa;

// Mock user factory
export const createMockUser = (overrides: Partial<User> = {}): User => ({
  id: 'user-test-123',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  fullName: 'Test User',
  upkeepCompanyId: '1',
  username: 'testuser',
  role: 'admin',
  permissions: {
    'ehs-capa': { view: 'full', create: 'full', edit: 'full' },
  },
  featureFlags: {},
  hasEhsEnabled: true,
  ...overrides,
});

// Queue mock factory
export const createMockQueue = () => {
  const jobs: MockQueueJob[] = [];

  const mockQueue: MockQueue = {
    add: vi.fn((queueName, jobData, options) => {
      const job: MockQueueJob = {
        id: `job-${Date.now()}-${Math.random()}`,
        data: jobData as CapaUpdateNotificationJobPayload | CapaAssignedNotificationJobPayload,
        opts: options,
        queueName,
        attemptsMade: 0,
      };
      jobs.push(job);
      return Promise.resolve(job);
    }),
    close: vi.fn(),
    getJob: vi.fn((jobId) => jobs.find((job) => job.id === jobId)),
    getJobs: vi.fn(() => Promise.resolve(jobs)),
    clean: vi.fn(),
  };

  return {
    jobs,
    mockQueue,
    // Helper methods
    getAddedJobs: () => jobs,
    getLastJob: () => jobs[jobs.length - 1],
    clearJobs: () => jobs.splice(0, jobs.length),
  };
};

// Worker mock factory
export const createMockWorker = () => {
  let processor: ((job: MockJob) => Promise<unknown>) | null = null;

  return {
    mockWorker: {
      close: vi.fn(),
      processor,
    },
    // Helper to simulate job processing
    processJob: async (job: MockJob): Promise<unknown> => {
      if (processor) {
        return await processor(job);
      }
      throw new Error('No processor set');
    },
    // Helper to set processor (from Worker constructor)
    setProcessor: (proc: (job: MockJob) => Promise<unknown>) => {
      processor = proc;
    },
  };
};

// Service mocks factory
export const createServiceMocks = () => ({
  capaService: {
    toggleArchiveCapa: vi.fn(),
    updateCapa: vi.fn(),
    getCapaById: vi.fn(),
    createCapa: vi.fn(),
    listCapas: vi.fn(),
  },
  notificationService: {
    sendCapaUpdateNotification: vi.fn(),
    sendCapaAssignedNotification: vi.fn(),
    sendCapaOverdueNotification: vi.fn(),
  },
  userService: {
    getUsersPublic: vi.fn(),
    getUserPublic: vi.fn(),
  },
  locationService: {
    getLocationById: vi.fn(),
  },
  incidentService: {
    getEventById: vi.fn(),
  },
  emailService: {
    sendEmail: vi.fn(),
  },
});

// Test scenario builders
export const testScenarios = {
  // Success scenarios for updates
  successfulUpdate: () => ({
    payload: createMockJobPayload({
      actionPrefix: 'Updated',
      capa: { title: 'Successfully Updated CAPA' },
    }),
    expectation: 'should process successfully',
  }),

  successfulArchive: () => ({
    payload: createMockJobPayload({
      actionPrefix: 'Archived',
      capa: { archivedAt: new Date() },
    }),
    expectation: 'should archive successfully',
  }),

  successfulUnarchive: () => ({
    payload: createMockJobPayload({
      actionPrefix: 'Unarchived',
      capa: { archivedAt: null },
    }),
    expectation: 'should unarchive successfully',
  }),

  // Success scenarios for assignments
  successfulAssignment: () => ({
    payload: createMockAssignedJobPayload({
      capa: { title: 'Successfully Assigned CAPA' },
    }),
    expectation: 'should assign successfully',
  }),

  // Error scenarios
  emailServiceFailure: () => ({
    payload: createMockJobPayload({ actionPrefix: 'Updated' }),
    serviceError: new Error('Email service unavailable'),
    expectation: 'should handle email service failures',
  }),

  userServiceFailure: () => ({
    payload: createMockJobPayload({ actionPrefix: 'Updated' }),
    serviceError: new Error('User service unavailable'),
    expectation: 'should handle user service failures',
  }),

  // Edge cases
  capaWithoutTeamMembers: () => ({
    payload: createMockJobPayload({
      actionPrefix: 'Updated',
      capa: { teamMembersToNotify: null },
    }),
    expectation: 'should handle CAPAs without team members',
  }),

  capaWithoutLocation: () => ({
    payload: createMockJobPayload({
      actionPrefix: 'Updated',
      capa: { locationId: null },
    }),
    expectation: 'should handle CAPAs without location',
  }),

  capaWithEvent: () => ({
    payload: createMockJobPayload({
      actionPrefix: 'Updated',
      capa: { eventId: 'event-123' },
    }),
    expectation: 'should handle CAPAs with linked events',
  }),
};

// Performance test helpers
export const performanceTestHelpers = {
  // Create multiple jobs for load testing
  createBulkJobs: (count: number) => {
    return Array.from({ length: count }, (_, index) =>
      createMockJobPayload({
        actionPrefix: `Bulk Update ${index}`,
        capa: { id: `capa-bulk-${index}` },
      }),
    );
  },

  // Measure processing time
  measureProcessingTime: async (operation: () => Promise<unknown>): Promise<number> => {
    const startTime = Date.now();
    await operation();
    return Date.now() - startTime;
  },

  // Create delayed operations for retry testing
  createDelayedOperation: (delay: number, shouldFail = false) => {
    return vi.fn().mockImplementation(async (): Promise<string> => {
      await new Promise((resolve) => setTimeout(resolve, delay));
      if (shouldFail) {
        throw new Error('Delayed operation failed');
      }
      return 'success';
    });
  },
};

// Assertion helpers
export const assertionHelpers = {
  // Assert job was added with correct options
  expectJobAdded: (
    mockQueue: MockQueue,
    expectedPayload: CapaUpdateNotificationJobPayload | CapaAssignedNotificationJobPayload,
    expectedOptions: JobsOptions,
  ) => {
    expect(mockQueue.add).toHaveBeenCalledWith(
      expect.any(String),
      expectedPayload,
      expect.objectContaining(expectedOptions),
    );
  },

  // Assert service was called with correct parameters
  expectServiceCalled: (
    mockService: MockedFunction<(...args: unknown[]) => unknown>,
    expectedPayload: CapaUpdateNotificationJobPayload | CapaAssignedNotificationJobPayload,
  ) => {
    expect(mockService).toHaveBeenCalledWith(expectedPayload);
  },

  // Assert retry configuration is correct
  expectRetryConfig: (mockQueue: MockQueue, expectedAttempts: number, expectedDelay: number) => {
    expect(mockQueue.add).toHaveBeenCalledWith(
      expect.any(String),
      expect.any(Object),
      expect.objectContaining({
        attempts: expectedAttempts,
        backoff: {
          type: 'exponential',
          delay: expectedDelay,
        },
      }),
    );
  },

  // Assert logging occurred
  expectLogging: (mockLogger: MockLogger, level: string, message: string) => {
    expect(mockLogger[level]).toHaveBeenCalledWith(message, expect.any(Object));
  },
};
