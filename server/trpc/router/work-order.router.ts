import {
  createWorkOrderFromCapa,
  searchWorkOrdersByCapaId,
  getWorkOrdersCountByCapa,
  getWorkOrders,
} from '@server/services/work-order.service';
import { privateProcedure, trpc } from '@server/trpc/trpc';
import {
  CreateWorkOrderFromCapaSchema,
  WorkOrderSearchInputSchema,
  CountWorkOrdersByCapaIdSchema,
  WorkOrder,
} from '@shared/types/work-orders.types';
import { PaginatedResponse } from '@shared/types/schema.types';
import { getCapasByIds } from '@server/services/capa.service';

export const workOrderRouter = trpc.router({
  // General work order search with cursor pagination
  search: privateProcedure
    .input(WorkOrderSearchInputSchema)
    .query(async ({ input, ctx }): Promise<PaginatedResponse<WorkOrder>> => {
      const workOrders = await getWorkOrders(input, ctx.req.headers);

      const capaIds = Array.from(
        new Set(
          workOrders.result
            .map((workOrder) => workOrder.capaId)
            .filter((capaId): capaId is string => capaId !== undefined && capaId !== ''),
        ),
      );

      const capas = await getCapasByIds(capaIds, ctx.user);

      workOrders.result = workOrders.result.map((workOrder) => {
        const capa = capas.find((capa) => capa.id === workOrder.capaId);
        return { ...workOrder, capaSlug: capa?.slug ?? undefined, capaTitle: capa?.title ?? undefined };
      });

      return workOrders;
    }),

  createFromCapa: privateProcedure.input(CreateWorkOrderFromCapaSchema).mutation(async ({ input, ctx }) => {
    return await createWorkOrderFromCapa(input, ctx.req.headers);
  }),

  getByCapa: privateProcedure
    .input(WorkOrderSearchInputSchema)
    .query(async ({ input, ctx }): Promise<PaginatedResponse<WorkOrder>> => {
      const { cursor = 0, limit = 10, capaId } = input;

      // Get data with one extra item to determine if there's a next page
      const workOrders = await searchWorkOrdersByCapaId(
        {
          capaId: capaId ?? [],
          limit: limit + 1,
          offset: cursor,
          sort: 'createdAt DESC',
        },
        ctx.req.headers,
      );

      // Check if we got more results than requested (indicating more pages available)
      const hasMore = workOrders.length > limit;
      const result = hasMore ? workOrders.slice(0, limit) : workOrders;

      // Return cursor paginated response
      return {
        noResults: result.length === 0,
        result,
        nextCursor: hasMore ? cursor + limit : undefined,
      };
    }),

  getCountByCapa: privateProcedure
    .input(CountWorkOrdersByCapaIdSchema)
    .query(async ({ input, ctx }): Promise<number> => {
      return await getWorkOrdersCountByCapa(input, ctx.req.headers);
    }),
});
