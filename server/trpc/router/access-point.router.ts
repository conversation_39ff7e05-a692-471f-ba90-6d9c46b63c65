import {
  createAccessPointsBulk,
  exportAccessPoints,
  getAccessPointById,
  listAccessPoints,
  toggleArchiveAccessPoint,
  updateAccessPoint,
} from '@server/services/access-point.service';
import { matchLocationsWithAI } from '@server/services/ai.service';
import { getAssets } from '@server/services/asset.service';
import { getAllLocations, getLocations } from '@server/services/location.service';
import { getUsersPublic } from '@server/services/user.service';
import { privateProcedure, publicProcedure, trpc } from '@server/trpc/trpc';
import { createInputKey, matchManyLocations } from '@server/utils/match-many-locations';
import { NO_LOCATION_MATCHED } from '@server/utils/match-single-location';
import { prepareLocationContext } from '@server/utils/prepare-location-context';
import {
  BulkCreateAccessPointInputSchema,
  CreateAccessPointFormSchema,
  ExportAccessPointsSchema,
  ListAccessPointsSchema,
  UpdateAccessPointSchema,
} from '@shared/types/access-points.types';
import { Asset, Location } from '@shared/types/assets.types';
import { IdSchema, UpkeepCompanyIdSchema } from '@shared/types/schema.types';
import { UserPublic } from '@shared/types/users.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { TRPCError } from '@trpc/server';
import { z } from 'zod';

export const accessPointRouter = trpc.router({
  create: privateProcedure
    .hasPermission(MODULES.EHS_ACCESS_POINT, ALLOWED_ACTIONS.CREATE)
    .input(CreateAccessPointFormSchema)
    .mutation(async ({ input, ctx }) => {
      const [accessPoint] = await createAccessPointsBulk([input], ctx.user);

      if (!accessPoint) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Failed to create access point',
        });
      }

      return accessPoint;
    }),

  update: privateProcedure
    .hasPermission(MODULES.EHS_ACCESS_POINT, ALLOWED_ACTIONS.EDIT)
    .input(UpdateAccessPointSchema.extend({ id: IdSchema.shape.id }))
    .mutation(async ({ input: { id, ...data }, ctx }) => {
      const updatedAccessPoint = await updateAccessPoint(id, data, ctx.user);

      if (!updatedAccessPoint) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Failed to update access point',
        });
      }

      return updatedAccessPoint;
    }),

  // Cursor-based list for useInfiniteQuery
  list: privateProcedure
    .hasPermission(MODULES.EHS_ACCESS_POINT, ALLOWED_ACTIONS.VIEW)
    .input(ListAccessPointsSchema)
    .query(async ({ input, ctx }) => {
      const paginatedAccessPoints = await listAccessPoints(input, ctx.user);

      // Extract unique location IDs, asset IDs, and user IDs for batch fetching
      const { locationIds, assetIds, userIds } = paginatedAccessPoints.result.reduce(
        (acc, ap) => {
          if (ap.locationId) acc.locationIds.add(ap.locationId);
          if (ap.assetId) acc.assetIds.add(ap.assetId);
          if (ap.createdBy) acc.userIds.add(ap.createdBy);
          return acc;
        },
        { 
          locationIds: new Set<string>(), 
          assetIds: new Set<string>(),
          userIds: new Set<string>()
        }
      );

      const locationIdsArray = Array.from(locationIds);
      const assetIdsArray = Array.from(assetIds);
      const userIdsArray = Array.from(userIds);

      const [locations, assets, users] = await Promise.all([
        locationIdsArray.length > 0
          ? getLocations(
              {
                objectId: locationIdsArray,
                cursor: 0,
                limit: 100,
              },
              ctx.req.headers,
            )
          : { result: [], nextCursor: undefined, noResults: true },
        assetIdsArray.length > 0
          ? getAssets(
              {
                objectId: assetIdsArray,
                cursor: 0,
                limit: 100,
              },
              ctx.req.headers,
            )
          : { result: [], nextCursor: undefined, noResults: true },
        userIdsArray.length > 0
          ? getUsersPublic({ upkeepCompanyId: ctx.user.upkeepCompanyId, objectId: userIdsArray })
          : { result: [], nextCursor: undefined, noResults: true },
      ]);

      const locationMap = locations.result?.reduce(
        (acc, location) => {
          acc[location.id] = {
            id: location.id,
            name: location.name,
          };
          return acc;
        },
        {} as Record<string, Location>,
      );

      const assetMap = assets.result?.reduce(
        (acc, asset) => {
          acc[asset.id] = {
            id: asset.id,
            name: asset.name,
          };
          return acc;
        },
        {} as Record<string, Pick<Asset, 'id' | 'name'>>,
      );

      const userMap = users?.result?.reduce(
        (acc, user) => {
          acc[user.id] = user;
          return acc;
        },
        {} as Record<string, UserPublic>,
      );

      const populatedAccessPoints = paginatedAccessPoints.result.map((ap) => ({
        ...ap,
        createdByUser: ap.createdBy ? userMap[ap.createdBy] : undefined,
        location: ap.locationId ? locationMap[ap.locationId] : undefined,
        asset: ap.assetId ? assetMap[ap.assetId] : undefined,
      }));

      return {
        ...paginatedAccessPoints,
        result: populatedAccessPoints,
      };
    }),

  export: privateProcedure
    .hasPermission(MODULES.EHS_ACCESS_POINT, ALLOWED_ACTIONS.EXPORT)
    .input(ExportAccessPointsSchema)
    .mutation(async ({ input, ctx }) => {
      const accessPoints = await exportAccessPoints(input, ctx.user);

      const locationIds = Array.from(new Set(accessPoints.map((ap) => ap.locationId).filter(Boolean))) as string[];

      const assetIds = Array.from(new Set(accessPoints.map((ap) => ap.assetId).filter(Boolean))) as string[];

      const userIds = Array.from(new Set(accessPoints.map((ap) => ap.createdBy).filter(Boolean))) as string[];

      const [locationsResponse, assetsResponse, users] = await Promise.all([
        locationIds.length > 0
          ? getLocations(
              {
                objectId: locationIds,
                cursor: 0,
                limit: 100,
              },
              ctx.req.headers,
            )
          : { result: [] },
        assetIds.length > 0
          ? getAssets(
              {
                objectId: assetIds,
                cursor: 0,
                limit: 100,
              },
              ctx.req.headers,
            )
          : { result: [] },
        userIds.length > 0
          ? getUsersPublic({ upkeepCompanyId: ctx.user.upkeepCompanyId, objectId: userIds })
          : { result: [], nextCursor: undefined, noResults: true },
      ]);

      const locationMap = locationsResponse.result?.reduce(
        (acc, location) => {
          acc[location.id] = {
            id: location.id,
            name: location.name,
          };
          return acc;
        },
        {} as Record<string, Location>,
      );

      const assetMap = assetsResponse.result?.reduce(
        (acc, asset) => {
          acc[asset.id] = {
            id: asset.id,
            name: asset.name,
          };
          return acc;
        },
        {} as Record<string, Pick<Asset, 'id' | 'name'>>,
      );

      const userMap = users?.result?.reduce(
        (acc, user) => {
          acc[user.id] = user;
          return acc;
        },
        {} as Record<string, UserPublic>,
      );

      return accessPoints.map((ap) => ({
        ...ap,
        createdByUser: ap.createdBy ? userMap[ap.createdBy] : undefined,
        location: ap.locationId ? locationMap[ap.locationId] : undefined,
        asset: ap.assetId ? assetMap[ap.assetId] : undefined,
      }));
    }),

  getById: privateProcedure
    .hasPermission(MODULES.EHS_ACCESS_POINT, ALLOWED_ACTIONS.VIEW)
    .input(IdSchema)
    .query(async ({ input, ctx }) => {
      return await getAccessPointById(input.id, ctx.user);
    }),

  getByIdPublic: publicProcedure.input(IdSchema.and(UpkeepCompanyIdSchema)).query(async ({ input }) => {
    return await getAccessPointById(input.id, { upkeepCompanyId: input.upkeepCompanyId });
  }),
  bulkCreate: privateProcedure
    .hasPermission(MODULES.EHS_ACCESS_POINT, ALLOWED_ACTIONS.CREATE)
    .input(z.array(BulkCreateAccessPointInputSchema))
    .mutation(async ({ input: inputs, ctx }) => {
      // Validate input array
      if (!inputs || inputs.length === 0) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'No access points provided for bulk creation',
        });
      }

      const candidates = await getAllLocations(ctx.user.upkeepCompanyId);
      const context = prepareLocationContext(candidates);
      const initial = matchManyLocations(inputs, context);

      // AI matching for failed locations - run in parallel for better performance
      const aiMatchingPromises = Array.from(initial.failMap.values())
        .filter((item) => item.reason === NO_LOCATION_MATCHED)
        .map(async (item) => {
          try {
            const aiResponse = await matchLocationsWithAI(item.location, context.fuzzyList);
            if (aiResponse.locationId) {
              return {
                success: true as const,
                item,
                result: {
                  name: item.name,
                  location: item.location,
                  locationId: aiResponse.locationId,
                  locationName: aiResponse.locationName || '',
                },
              };
            }
          } catch (error) {
            console.error('AI location matching failed for:', item.location, error);
          }
          return { success: false as const, item };
        });

      // Process AI results
      const aiResults = await Promise.allSettled(aiMatchingPromises);
      for (const result of aiResults) {
        if (result.status === 'fulfilled' && result.value.success) {
          const { item, result: matchResult } = result.value;
          const key = createInputKey(item);
          initial.successMap.set(key, matchResult);
          initial.failMap.delete(key);
        }
      }

      const success = Array.from(initial.successMap.values());
      const failed = Array.from(initial.failMap.values());

      if (success.length > 0) {
        await createAccessPointsBulk(success, ctx.user);
      }

      return {
        success: true,
        created: success.length,
        failed: failed.length,
        total: inputs.length,
        failedItems: failed,
      };
    }),
  toggleArchive: privateProcedure
    .hasPermission(MODULES.EHS_ACCESS_POINT, ALLOWED_ACTIONS.EDIT)
    .input(IdSchema)
    .mutation(async ({ input, ctx }) => {
      const result = await toggleArchiveAccessPoint(input, ctx.user);
      if (!result) {
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Failed to delete access point' });
      }
      return result;
    }),
});
