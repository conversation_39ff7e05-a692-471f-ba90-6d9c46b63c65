import {
  bulkCreateHazards,
  createHazard,
  listHazards,
  exportHazards,
  toggleArchive,
  updateHazard,
} from '@server/services/hazards.service';
import { getUsersPublic } from '@server/services/user.service';
import { HAZARDS } from '@shared/seeds/hazards';
import { IdSchema } from '@shared/types/schema.types';
import {
  BulkHazardsCreateSchema,
  HazardsCreateSchema,
  ListHazardsSchema,
  ExportHazardsSchema,
  HazardsUpdateSchema,
} from '@shared/types/settings.types';
import { UserPublic } from '@shared/types/users.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { TRPCError } from '@trpc/server';
import { privateProcedure, trpc } from '../trpc';

export const hazardsRouter = trpc.router({
  create: privateProcedure
    .hasPermission(MODULES.HAZARDS, ALLOWED_ACTIONS.CREATE)
    .input(HazardsCreateSchema)
    .mutation(async ({ ctx, input }) => {
      const hazard = await createHazard(input, ctx.user);
      if (!hazard) {
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Failed to create hazard' });
      }
      return hazard;
    }),
  update: privateProcedure
    .hasPermission(MODULES.HAZARDS, ALLOWED_ACTIONS.EDIT)
    .input(HazardsUpdateSchema)
    .mutation(async ({ ctx, input }) => {
      const hazard = await updateHazard(input, ctx.user);
      if (!hazard) {
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Failed to update hazard' });
      }
      return hazard;
    }),
  list: privateProcedure
    .hasPermission(MODULES.HAZARDS, ALLOWED_ACTIONS.VIEW)
    .input(ListHazardsSchema)
    .query(async ({ ctx, input }) => {
      const paginatedHazards = await listHazards(input, ctx.user);

      // Get unique user IDs from the hazards
      const userIds = [...new Set(paginatedHazards.result.map((hazard) => hazard.createdBy))];

      const users = await getUsersPublic({ upkeepCompanyId: ctx.user.upkeepCompanyId, objectId: userIds });

      const userMap = users.result?.reduce(
        (acc, user) => {
          acc[user.id] = {
            id: user.id,
            fullName: user.fullName ?? 'Unknown',
            username: user.username ?? 'Unknown',
            firstName: user.firstName ?? 'Unknown',
          };

          return acc;
        },
        {} as Record<string, UserPublic>,
      );

      return {
        ...paginatedHazards,
        result: paginatedHazards.result.map((hazard) => ({
          ...hazard,
          createdBy: userMap?.[hazard.createdBy],
        })),
      };
    }),
  export: privateProcedure
    .hasPermission(MODULES.HAZARDS, ALLOWED_ACTIONS.EXPORT)
    .input(ExportHazardsSchema)
    .mutation(async ({ ctx, input }) => {
      const hazards = await exportHazards(input, ctx.user);

      // Get unique user IDs from the hazards
      const userIds = [...new Set(hazards.map((hazard) => hazard.createdBy))];

      const users = await getUsersPublic({ upkeepCompanyId: ctx.user.upkeepCompanyId, objectId: userIds });

      const userMap = users.result?.reduce(
        (acc, user) => {
          acc[user.id] = {
            id: user.id,
            fullName: user.fullName ?? 'Unknown',
            username: user.username ?? 'Unknown',
            email: user.email ?? '',
            firstName: user.firstName ?? 'Unknown',
          };

          return acc;
        },
        {} as Record<string, UserPublic>,
      );

      return hazards.map((hazard) => ({
        ...hazard,
        createdBy: userMap?.[hazard.createdBy],
      }));
    }),
  toggleArchive: privateProcedure
    .hasPermission(MODULES.HAZARDS, ALLOWED_ACTIONS.EDIT)
    .input(IdSchema)
    .mutation(async ({ ctx, input }) => {
      return toggleArchive(input.id, ctx.user);
    }),
  listDefault: privateProcedure.hasPermission(MODULES.HAZARDS, ALLOWED_ACTIONS.VIEW).query(async () => {
    return HAZARDS;
  }),
  bulkCreate: privateProcedure
    .hasPermission(MODULES.HAZARDS, ALLOWED_ACTIONS.CREATE)
    .input(BulkHazardsCreateSchema)
    .mutation(async ({ ctx, input }) => {
      return bulkCreateHazards(input, ctx.user);
    }),
});
