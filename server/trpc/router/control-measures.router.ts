import {
  bulkCreateControlMeasures,
  createControlMeasure,
  listControlMeasures,
  exportControlMeasures,
  toggleArchive,
  updateControlMeasure,
} from '@server/services/control-measures.service';
import { getUsersPublic } from '@server/services/user.service';
import { CONTROL_MEASURES } from '@shared/seeds/control-measures';
import { IdSchema } from '@shared/types/schema.types';
import {
  BulkControlMeasuresCreateSchema,
  ControlMeasuresCreateSchema,
  ListControlMeasuresSchema,
  ExportControlMeasuresSchema,
  ControlMeasuresUpdateSchema,
} from '@shared/types/settings.types';
import { UserPublic } from '@shared/types/users.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { TRPCError } from '@trpc/server';
import { privateProcedure, trpc } from '../trpc';

export const controlMeasuresRouter = trpc.router({
  create: privateProcedure
    .hasPermission(MODULES.CONTROL_MEASURES, ALLOWED_ACTIONS.CREATE)
    .input(ControlMeasuresCreateSchema)
    .mutation(async ({ ctx, input }) => {
      const controlMeasure = await createControlMeasure(input, ctx.user);
      if (!controlMeasure) {
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Failed to create control measure' });
      }
      return controlMeasure;
    }),

  update: privateProcedure
    .hasPermission(MODULES.CONTROL_MEASURES, ALLOWED_ACTIONS.EDIT)
    .input(ControlMeasuresUpdateSchema)
    .mutation(async ({ ctx, input }) => {
      const controlMeasure = await updateControlMeasure(input, ctx.user);
      if (!controlMeasure) {
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Failed to update control measure' });
      }
      return controlMeasure;
    }),

  list: privateProcedure
    .hasPermission(MODULES.CONTROL_MEASURES, ALLOWED_ACTIONS.VIEW)
    .input(ListControlMeasuresSchema)
    .query(async ({ ctx, input }) => {
      const paginatedControlMeasures = await listControlMeasures(input, ctx.user);

      // Get unique user IDs from the control measures
      const userIds = [...new Set(paginatedControlMeasures.result.map((controlMeasure) => controlMeasure.createdBy))];

      const users = await getUsersPublic({ upkeepCompanyId: ctx.user.upkeepCompanyId, objectId: userIds });

      const userMap = users.result?.reduce(
        (acc, user) => {
          acc[user.id] = {
            id: user.id,
            fullName: user.fullName ?? 'Unknown',
            username: user.username ?? 'Unknown',
            firstName: user.firstName ?? 'Unknown',
          };

          return acc;
        },
        {} as Record<string, UserPublic>,
      );

      return {
        ...paginatedControlMeasures,
        result: paginatedControlMeasures.result.map((controlMeasure) => ({
          ...controlMeasure,
          createdBy: userMap?.[controlMeasure.createdBy],
        })),
      };
    }),
  export: privateProcedure
    .hasPermission(MODULES.CONTROL_MEASURES, ALLOWED_ACTIONS.EXPORT)
    .input(ExportControlMeasuresSchema)
    .mutation(async ({ ctx, input }) => {
      const controlMeasures = await exportControlMeasures(input, ctx.user);

      // Get unique user IDs from the control measures
      const userIds = [...new Set(controlMeasures.map((controlMeasure) => controlMeasure.createdBy))];

      const users = await getUsersPublic({ upkeepCompanyId: ctx.user.upkeepCompanyId, objectId: userIds });

      const userMap = users.result?.reduce(
        (acc, user) => {
          acc[user.id] = {
            id: user.id,
            fullName: user.fullName ?? 'Unknown',
            username: user.username ?? 'Unknown',
            email: user.email ?? '',
            firstName: user.firstName ?? 'Unknown',
          };

          return acc;
        },
        {} as Record<string, UserPublic>,
      );

      return controlMeasures.map((controlMeasure) => ({
        ...controlMeasure,
        createdBy: userMap?.[controlMeasure.createdBy],
      }));
    }),
  toggleArchive: privateProcedure
    .hasPermission(MODULES.CONTROL_MEASURES, ALLOWED_ACTIONS.EDIT)
    .input(IdSchema)
    .mutation(async ({ ctx, input }) => {
      return toggleArchive(input.id, ctx.user);
    }),
  listDefault: privateProcedure.hasPermission(MODULES.CONTROL_MEASURES, ALLOWED_ACTIONS.VIEW).query(async () => {
    return CONTROL_MEASURES;
  }),
  bulkCreate: privateProcedure
    .hasPermission(MODULES.CONTROL_MEASURES, ALLOWED_ACTIONS.CREATE)
    .input(BulkControlMeasuresCreateSchema)
    .mutation(async ({ ctx, input }) => {
      return bulkCreateControlMeasures(input, ctx.user);
    }),
});
