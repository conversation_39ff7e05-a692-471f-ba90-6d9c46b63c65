import {
  getUserNotifications,
  getUnreadNotificationCount,
  markNotificationAsRead,
  markAllNotificationsAsRead,
} from '@server/services/notification.service';
import { notificationEmitter, emitNotificationChange } from '@server/services/notification-pubsub';
import { privateProcedure, trpc } from '../trpc';
import { z } from 'zod';
import { on } from 'events';

export const notificationRouter = trpc.router({
  getNotifications: privateProcedure.query(({ ctx }) => {
    return getUserNotifications(ctx.user);
  }),

  getUnreadCount: privateProcedure.query(async ({ ctx }) => {
    return getUnreadNotificationCount(ctx.user);
  }),

  markAsRead: privateProcedure
    .input(
      z.object({
        notificationId: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      await markNotificationAsRead(ctx.user, input.notificationId);

      // Emit notification update for real-time UI updates
      emitNotificationChange(ctx.user.upkeepCompanyId, 'update');

      return { success: true };
    }),

  markAllAsRead: privateProcedure.mutation(async ({ ctx }) => {
    await markAllNotificationsAsRead(ctx.user);

    // Emit notification update for real-time UI updates
    emitNotificationChange(ctx.user.upkeepCompanyId, 'update');

    return { success: true };
  }),

  // Real-time subscription for notifications
  onNotificationUpdate: privateProcedure.subscription(async function* (opts) {
    // Send initial notifications
    const initialNotifications = await getUserNotifications(opts.ctx.user);

    // Yield initial data as a batch
    yield {
      type: 'initial',
      notifications: initialNotifications,
    };

    // Listen for new notification events
    for await (const [data] of on(notificationEmitter, 'notificationChange', {
      signal: opts.signal,
    })) {
      const eventData = data as { upkeepCompanyId: string; type: 'new' | 'update' };

      // Only process events for the same company
      if (eventData.upkeepCompanyId === opts.ctx.user.upkeepCompanyId) {
        // Fetch the latest notifications for this user
        const updatedNotifications = await getUserNotifications(opts.ctx.user);

        // Yield all notifications as a batch
        yield {
          type: eventData.type,
          notifications: updatedNotifications,
        };
      }
    }
  }),
});
