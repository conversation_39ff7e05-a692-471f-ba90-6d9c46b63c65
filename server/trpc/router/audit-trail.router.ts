import { getAuditTrail } from '@server/services/audit-trail.service';
import { getUsersPublic } from '@server/services/user.service';
import { privateProcedure, trpc } from '@server/trpc/trpc';
import { GetAuditTrailSchema } from '@shared/types/audit-trails.types';
import { UserPublic } from '@shared/types/users.types';

export const auditTrailRouter = trpc.router({
  get: privateProcedure.input(GetAuditTrailSchema).query(async ({ input, ctx }) => {
    const auditTrail = await getAuditTrail(input.entityId, ctx.user);

    const userIds = Array.from(new Set(auditTrail.map((event) => event.userId).filter(Boolean))) as string[];

    const users = await getUsersPublic({ upkeepCompanyId: ctx.user.upkeepCompanyId, objectId: userIds });

    const mappedUsers = users.result.reduce(
      (acc, user) => {
        acc[user.id] = user;
        return acc;
      },
      {} as Record<string, UserPublic>,
    );

    const auditTrailWithUser = auditTrail.map((audit) => {
      return {
        ...audit,
        user: mappedUsers[audit.userId || ''],
      };
    });

    return auditTrailWithUser;
  }),
});
