import { searchAssetsPublic, getAssets } from '@server/services/asset.service';
import { privateProcedure, trpc, publicProcedure } from '@server/trpc/trpc';
import { PaginatedResponse, PublicSearchSchema } from '@shared/types/schema.types';
import { Asset, AssetSearchInputSchema } from '@shared/types/assets.types';

export const assetRouter = trpc.router({
  // Cursor-based search for useInfiniteQuery
  search: privateProcedure
    .input(AssetSearchInputSchema.default({ cursor: 0, limit: 10, search: '' }))
    .query(async ({ input, ctx }): Promise<PaginatedResponse<Asset>> => {
      const { cursor = 0, limit = 10, search, locationId, mustIncludeObjectIds } = input;

      const searchParams = {
        search,
        ...(locationId && { objectLocation: [locationId] }),
      };

      return await getAssets(
        {
          cursor,
          limit,
          ...searchParams,
          sort: 'createdAt DESC',
          mustIncludeObjectIds,
        },
        ctx.req.headers,
      );
    }),

  // Public asset search endpoint - requires only upkeepCompanyId
  searchPublic: publicProcedure
    .input(PublicSearchSchema)
    .query(async ({ input }): Promise<PaginatedResponse<Asset>> => {
      return await searchAssetsPublic(input);
    }),
});
