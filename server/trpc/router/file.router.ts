import { getFiles, getPresignedReadUrl, getPresignedUrl, removeFiles, updateFile } from '@server/services/file.service';
import {
  GetPresignedUrlInputPublicSchema,
  GetPresignedUrlInputSchema,
  ListFilesSchema,
  S3KeyInputSchema,
  UpdateFilePublicSchema,
  UpdateFileSchema,
} from '@shared/types/files.types';
import { IdArraySchema } from '@shared/types/schema.types';
import { privateProcedure, publicProcedure, trpc } from '../trpc';

export const fileRouter = trpc.router({
  getPresignedUrlPublic: publicProcedure.input(GetPresignedUrlInputPublicSchema).mutation(async ({ input }) => {
    return await getPresignedUrl(
      {
        fileName: input.fileName,
        fileSize: input.fileSize,
        mimeType: input.mimeType,
        entityType: input.entityType,
        entityId: input.entityId,
      },
      { upkeepCompanyId: input.upkeepCompanyId },
    );
  }),

  getPresignedUrl: privateProcedure.input(GetPresignedUrlInputSchema).mutation(async ({ ctx, input }) => {
    return await getPresignedUrl(
      {
        fileName: input.fileName,
        fileSize: input.fileSize,
        mimeType: input.mimeType,
        entityType: input.entityType,
        entityId: input.entityId,
      },
      ctx.user,
    );
  }),

  getPresignedReadUrl: publicProcedure.input(S3KeyInputSchema).query(async ({ input }) => {
    return await getPresignedReadUrl(input.s3Key);
  }),

  updatePublic: publicProcedure.input(UpdateFilePublicSchema).mutation(async ({ input }) => {
    return await updateFile(input, { upkeepCompanyId: input.upkeepCompanyId });
  }),

  update: privateProcedure.input(UpdateFileSchema).mutation(async ({ ctx, input }) => {
    return await updateFile(input, ctx.user);
  }),

  getFiles: privateProcedure.input(ListFilesSchema).query(async ({ ctx, input }) => {
    return await getFiles(input, ctx.user);
  }),

  removeFilesPublic: publicProcedure.input(IdArraySchema).mutation(async ({ input }) => {
    return await removeFiles(input);
  }),

  removeFiles: privateProcedure.input(IdArraySchema).mutation(async ({ input }) => {
    return await removeFiles(input);
  }),
});
