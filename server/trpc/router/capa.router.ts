import { QUEUE_JOB_NAMES } from '@server/queue/job-names';
import { addJobToQueue } from '@server/queue/queue-utils';
import { getAssetById } from '@server/services/asset.service';
import {
  createCapa,
  export<PERSON>apas,
  getCapaById,
  list<PERSON><PERSON>s,
  toggleArchiveCapa,
  updateCapa,
} from '@server/services/capa.service';
import { createComment, deleteComment, fetchCommentById, fetchComments } from '@server/services/comment.service';
import { getEventById } from '@server/services/event.service';
import { duplicateFiles } from '@server/services/file.service';
import { getLocationById } from '@server/services/location.service';
import { getUserPublic, getUsersPublic } from '@server/services/user.service';
import { getAllWorkOrdersByCapaId } from '@server/services/work-order.service';
import { privateProcedure, trpc } from '@server/trpc/trpc';
import {
  CreateCapasFormSchema,
  EditCapasFormSchema,
  ExportCapasSchema,
  ListCapasSchema,
} from '@shared/types/capas.types';
import { CreateCommentFormSchema, ListCommentsInputSchema } from '@shared/types/comments.types';
import { TransientFileSchema } from '@shared/types/files.types';
import {
  CapaAssignedNotificationJobPayload,
  CapaUpdateNotificationJobPayload,
  CommentMentionNotificationJobPayload,
} from '@shared/types/queues.types';
import { IdSchema } from '@shared/types/schema.types';
import { UserPublic } from '@shared/types/users.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { TRPCError } from '@trpc/server';
import { z } from 'zod';

export const capaRouter = trpc.router({
  // Create CAPA procedure - requires CAPA create permission
  create: privateProcedure
    .hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.CREATE)
    .input(CreateCapasFormSchema)
    .mutation(async ({ input, ctx }) => {
      const createdCapa = await createCapa(input, ctx.user, ctx.req.headers);

      if (!createdCapa) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Failed to create CAPA',
        });
      }

      // Create job payload for queue
      const jobPayload: CapaAssignedNotificationJobPayload = {
        capa: createdCapa,
        user: ctx.user,
        headers: ctx.req.headers,
        needPartialCheck: ctx.needPartialCheck || false,
      };

      // Generate idempotent job ID using CAPA ID and creation timestamp
      const jobId = `capa-${createdCapa.id}-assign-${Date.now()}`;

      addJobToQueue(QUEUE_JOB_NAMES.ASSIGNED_CAPA_NOTIFICATION, jobPayload, { jobId });

      return createdCapa;
    }),

  // Get CAPA by ID procedure - requires CAPA view permission
  getById: privateProcedure
    .hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.VIEW)
    .input(IdSchema)
    .query(async ({ input, ctx }) => {
      const capa = await getCapaById(input.id, ctx.user, ctx.needPartialCheck || false);

      // If CAPA not found, throw a NOT_FOUND error
      if (!capa) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: `CAPA with ID ${input.id} not found`,
        });
      }

      const [owner, implementedBy, voePerformedBy, asset, location, event, usersToNotify] = await Promise.all([
        getUserPublic({ id: capa.ownerId, upkeepCompanyId: ctx.user.upkeepCompanyId }),
        capa.implementedBy
          ? getUserPublic({ id: capa.implementedBy, upkeepCompanyId: ctx.user.upkeepCompanyId })
          : undefined,
        capa.voePerformedBy
          ? getUserPublic({ id: capa.voePerformedBy, upkeepCompanyId: ctx.user.upkeepCompanyId })
          : undefined,
        capa?.assetId ? getAssetById(capa?.assetId, ctx.req.headers) : undefined,
        capa?.locationId ? getLocationById(capa?.locationId, ctx.req.headers) : undefined,
        capa?.eventId ? getEventById(capa?.eventId, ctx.user, ctx.needPartialCheck || false) : undefined,
        capa?.teamMembersToNotify && capa?.teamMembersToNotify?.length > 0
          ? getUsersPublic({
              upkeepCompanyId: capa.upkeepCompanyId,
              objectId: capa?.teamMembersToNotify,
            })
          : {
              result: [],
              total: 0,
              hasMore: false,
              cursor: 0,
            },
      ]);

      return {
        ...capa,
        owner,
        implementedBy,
        voePerformedBy,
        asset,
        location,
        event,
        teamMembersToNotify: usersToNotify?.result,
      };
    }),

  getByIdForEdit: privateProcedure
    .hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.EDIT)
    .input(IdSchema)
    .query(async ({ input, ctx }) => {
      const capa = await getCapaById(input.id, ctx.user, ctx.needPartialCheck || false);

      if (!capa) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: `CAPA with ID ${input.id} not found`,
        });
      }

      // Fetch all work orders related to this CAPA
      const allWorkOrders = await getAllWorkOrdersByCapaId(capa.id, ctx.req.headers);

      return {
        ...capa,
        workOrders: allWorkOrders,
        // Extract work order IDs for the form component
        workOrderIds: allWorkOrders.map((workOrder) => workOrder.id),
      };
    }),

  getByIdForDuplicate: privateProcedure
    .hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.CREATE)
    .input(IdSchema)
    .query(async ({ input, ctx }) => {
      const capa = await getCapaById(input.id, ctx.user, ctx.needPartialCheck || false);

      if (!capa) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: `CAPA with ID ${input.id} not found`,
        });
      }

      const duplicatedAttachments = await duplicateFiles(
        {
          entityType: 'capa' as const,
          entityId: capa.id,
          status: 'completed',
        },
        ctx.user,
      );

      return {
        ...capa,
        id: undefined,
        status: undefined,
        attachments: duplicatedAttachments.map<z.infer<typeof TransientFileSchema>>((item) => ({
          id: item.id,
          name: item.fileName,
          url: item.presignedUrl,
          type: item.mimeType,
          size: item.fileSize,
        })),
      };
    }),

  // Cursor-based list for useInfiniteQuery
  list: privateProcedure
    .hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.VIEW)
    .input(
      ListCapasSchema.default({
        cursor: 0,
        limit: 10,
      }),
    )
    .query(async ({ ctx, input }) => {
      const capas = await listCapas(input, ctx.user, ctx.needPartialCheck || false);

      // Extract unique user IDs for batch fetching
      const { userIds } = capas.result.reduce(
        (acc, capa) => {
          if (capa.ownerId) acc.userIds.add(capa.ownerId);
          return acc;
        },
        { 
          userIds: new Set<string>()
        }
      );

      const userIdsArray = Array.from(userIds);

      const users = await getUsersPublic({ upkeepCompanyId: ctx.user.upkeepCompanyId, objectId: userIdsArray });

      const usersMap = users.result.reduce(
        (acc, user) => {
          acc[user.id] = user;
          return acc;
        },
        {} as Record<string, UserPublic>,
      );

      const capasWithUsers = capas.result.map((capa) => {
        const user = usersMap[capa.ownerId];
        return {
          ...capa,
          owner: user,
        };
      });

      return {
        ...capas,
        result: capasWithUsers,
      };
    }),

  export: privateProcedure
    .hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.EXPORT)
    .input(ExportCapasSchema)
    .mutation(async ({ input, ctx }) => {
      const capas = await exportCapas(input, ctx.user);

      // Extract unique user IDs for batch fetching
      const { userIds } = capas.reduce(
        (acc, capa) => {
          if (capa.ownerId) acc.userIds.add(capa.ownerId);
          return acc;
        },
        { 
          userIds: new Set<string>()
        }
      );

      const userIdsArray = Array.from(userIds);

      const users = await getUsersPublic({ upkeepCompanyId: ctx.user.upkeepCompanyId, objectId: userIdsArray });

      const usersMap = users.result.reduce(
        (acc, user) => {
          acc[user.id] = user;
          return acc;
        },
        {} as Record<string, UserPublic>,
      );

      return capas.map((capa) => {
        const user = usersMap[capa.ownerId];
        return {
          ...capa,
          owner: user,
        };
      });
    }),

  // Update CAPA procedure
  update: privateProcedure
    .hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.EDIT)
    .input(EditCapasFormSchema)
    .mutation(async ({ input, ctx }) => {
      const updatedCapa = await updateCapa(input.id, input, ctx.user, ctx.req.headers);

      if (!updatedCapa) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Failed to update CAPA',
        });
      }

      // Create job payload for queue
      const jobPayload: CapaUpdateNotificationJobPayload = {
        capa: updatedCapa,
        user: ctx.user,
        headers: ctx.req.headers,
        needPartialCheck: ctx.needPartialCheck || false,
        actionPrefix: 'Updated',
      };

      // Generate idempotent job ID using CAPA ID and update timestamp
      const jobId = `capa-${updatedCapa.id}-update-${Date.now()}`;

      addJobToQueue(QUEUE_JOB_NAMES.UPDATE_CAPA_NOTIFICATION, jobPayload, { jobId });

      return updatedCapa;
    }),
  toggleArchive: privateProcedure
    .hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.EDIT)
    .input(IdSchema)
    .mutation(async ({ input, ctx }) => {
      const updatedCapa = await toggleArchiveCapa(input, ctx.user);
      if (!updatedCapa) {
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Failed to delete capa' });
      }

      // Create job payload for queue
      const jobPayload: CapaUpdateNotificationJobPayload = {
        capa: updatedCapa,
        user: ctx.user,
        headers: ctx.req.headers,
        needPartialCheck: ctx.needPartialCheck || false,
        actionPrefix: updatedCapa.archivedAt ? 'Archived' : 'Unarchived',
      };

      // Generate idempotent job ID using CAPA ID and action
      const jobId = `capa-${updatedCapa.id}-${updatedCapa.archivedAt ? 'archive' : 'unarchive'}-${Date.now()}`;

      addJobToQueue(QUEUE_JOB_NAMES.UPDATE_CAPA_NOTIFICATION, jobPayload, { jobId });

      return updatedCapa;
    }),
  createComment: privateProcedure
    .hasPermission(MODULES.COMMENT, ALLOWED_ACTIONS.CREATE)
    .input(CreateCommentFormSchema)
    .mutation(async ({ input, ctx }) => {
      // Add entityType for the service call
      const commentInput = {
        ...input,
        entityType: 'capa' as const,
      };

      const result = await createComment(commentInput, ctx.user);

      if (!result) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Failed to create comment. Please try again.',
        });
      }

      const { newComment, savedMentions } = result;

      // Only send notification if there are mentions
      if (savedMentions && savedMentions.length > 0) {
        // Create job payload for queue
        const jobPayload: CommentMentionNotificationJobPayload = {
          newComment,
          savedMentions,
          user: ctx.user,
          headers: ctx.req.headers,
          input: commentInput,
        };

        // Generate idempotent job ID using comment ID and creation timestamp
        const jobId = `comment-${newComment.id}-mention-${Date.now()}`;

        addJobToQueue(QUEUE_JOB_NAMES.COMMENT_MENTION_NOTIFICATION, jobPayload, { jobId });
      }

      return newComment;
    }),
  listComments: privateProcedure
    .hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.VIEW)
    .input(ListCommentsInputSchema)
    .query(async ({ input, ctx }) => {
      // Add entityType for the service call
      const commentsInput = {
        ...input,
        entityType: 'capa' as const,
      };
      return await fetchComments(commentsInput, ctx.user);
    }),
  getCommentById: privateProcedure
    .hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.VIEW)
    .input(IdSchema)
    .query(async ({ input, ctx }) => {
      return await fetchCommentById({ id: input.id }, ctx.user);
    }),
  deleteComment: privateProcedure
    .hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.EDIT)
    .input(IdSchema)
    .mutation(async ({ input, ctx }) => {
      return await deleteComment({ id: input.id }, ctx.user);
    }),
});
