import type { Location } from '@shared/types/assets.types';
import { TRPCError } from '@trpc/server';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { getLocations, searchLocationsPublic } from '../../../services/location.service';
import { locationRouter } from '../location.router';
import { createAdminUser, createMockContext } from './test-utils';

// Mock the rate limiter to prevent rate limiting during tests
vi.mock('@trpc-limiter/memory', () => ({
  createTRPCStoreLimiter: vi.fn(
    () =>
      async ({ next }: any) =>
        await next(),
  ),
  defaultFingerPrint: vi.fn(() => 'test-fingerprint'),
}));

// Mock location service
vi.mock('../../../services/location.service', () => ({
  getLocations: vi.fn(),
  searchLocationsPublic: vi.fn(),
}));

// Mock pagination utilities
vi.mock('../../../utils/pagination', () => ({
  calculateOffset: vi.fn((page, limit) => (page - 1) * limit),
}));

describe('Location Router', () => {
  const mockUser = createAdminUser();
  const mockContext = createMockContext(mockUser) as any;
  const nullUserContext = createMockContext(null) as any;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('search procedure', () => {
    it('should return cursor-paginated locations with default parameters', async () => {
      const mockLocations: Location[] = [
        { id: 'location-1', name: 'Inventory Control Room' },
        { id: 'location-2', name: 'Tool Maintenance Room' },
      ];

      const mockResponse = {
        noResults: false,
        result: mockLocations,
        nextCursor: undefined,
      };

      vi.mocked(getLocations).mockResolvedValue(mockResponse);

      const caller = locationRouter.createCaller(mockContext);
      const result = await caller.search({});

      expect(getLocations).toHaveBeenCalledWith(
        {
          cursor: 0,
          limit: 10,
          search: undefined,
          sort: 'createdAt DESC',
        },
        mockContext.req.headers,
      );

      expect(result).toEqual(mockResponse);
    });

    it('should handle search with custom parameters', async () => {
      const mockLocations: Location[] = [{ id: 'location-1', name: 'Room 101' }];

      const mockResponse = {
        noResults: false,
        result: mockLocations,
        nextCursor: 15,
      };

      vi.mocked(getLocations).mockResolvedValue(mockResponse);

      const caller = locationRouter.createCaller(mockContext);
      const result = await caller.search({
        cursor: 10,
        limit: 5,
        search: 'room',
      });

      expect(getLocations).toHaveBeenCalledWith(
        {
          cursor: 10,
          limit: 5,
          search: 'room',
          sort: 'createdAt DESC',
        },
        mockContext.req.headers,
      );

      expect(result).toEqual(mockResponse);
    });

    it('should handle empty search results', async () => {
      const mockResponse = {
        noResults: true,
        result: [],
        nextCursor: undefined,
      };

      vi.mocked(getLocations).mockResolvedValue(mockResponse);

      const caller = locationRouter.createCaller(mockContext);
      const result = await caller.search({ search: 'nonexistent' });

      expect(result).toEqual(mockResponse);
    });

    it('should validate input parameters', async () => {
      const caller = locationRouter.createCaller(mockContext);

      await expect(caller.search({ cursor: -1 })).rejects.toThrow();
      await expect(caller.search({ limit: 101 })).rejects.toThrow();
      await expect(caller.search({ limit: 0 })).rejects.toThrow();
    });

    it('should throw UNAUTHORIZED when user is not authenticated', async () => {
      await expect(locationRouter.createCaller(nullUserContext).search({})).rejects.toThrow(TRPCError);
    });
  });

  describe('searchPublic procedure', () => {
    it('should return locations without authentication', async () => {
      const mockResponse = {
        noResults: false,
        result: [
          { id: 'location-1', name: 'Public Room 1' },
          { id: 'location-2', name: 'Public Room 2' },
        ],
        nextCursor: undefined,
      };

      vi.mocked(searchLocationsPublic).mockResolvedValue(mockResponse);

      const publicContext = { user: null, req: { headers: { 'x-forwarded-for': '127.0.0.1' } } };
      const caller = locationRouter.createCaller(publicContext as any);
      const result = await caller.searchPublic({ upkeepCompanyId: 'test-role-id', search: 'public' });

      expect(result).toEqual(mockResponse);
      expect(searchLocationsPublic).toHaveBeenCalledWith({
        upkeepCompanyId: 'test-role-id',
        search: 'public',
        limit: undefined,
      });
    });

    it('should handle empty public search results', async () => {
      const mockResponse = {
        noResults: true,
        result: [],
        nextCursor: undefined,
      };

      vi.mocked(searchLocationsPublic).mockResolvedValue(mockResponse);

      const publicContext = { user: null, req: { headers: { 'x-forwarded-for': '127.0.0.2' } } };
      const caller = locationRouter.createCaller(publicContext as any);
      const result = await caller.searchPublic({ upkeepCompanyId: 'test-role-id', search: 'nonexistent' });

      expect(result).toEqual(mockResponse);
    });
  });
});
