import { createId } from '@paralleldrive/cuid2';
import { createEvent, getEventById } from '@server/services/event.service';
import { getUserById, getUsersPublic, hasPermission } from '@server/services/user.service';
import { eventRouter } from '@server/trpc/router/event.router';
import { ALLOWED_ACTIONS, MODULES, PERMISSION_LEVELS, UserPermission } from '@shared/user-permissions';
import { TRPCError } from '@trpc/server';
import { subDays } from 'date-fns';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  createAdminUser,
  createMockContext,
  createTechnicianUser,
  createUnauthorizedUser,
  createUserWithCustomPermissions,
} from './test-utils';

const yesterday = subDays(new Date(), 1);

// Mock services
vi.mock('@server/services/event.service', () => ({
  createEvent: vi.fn(),
  getEventById: vi.fn(),
}));

vi.mock('@server/services/user.service', () => ({
  getUserById: vi.fn(),
  getUsersPublic: vi.fn(),
  hasPermission: vi.fn(),
}));

vi.mock('@server/services/location.service', () => ({
  getLocationById: vi.fn().mockResolvedValue({ id: 'location-1', name: 'Test Location' }),
  getLocations: vi.fn().mockResolvedValue([{ id: 'location-1', name: 'Test Location' }]),
}));

vi.mock('@server/services/asset.service', () => ({
  getAssets: vi.fn().mockResolvedValue([]),
  getAssetById: vi.fn().mockResolvedValue({ id: 'asset-1', name: 'Test Asset', description: 'Test Description' }),
}));

describe('Permission Integration Tests', () => {
  const mockEvent = {
    id: createId(),
    slug: 'test-event-slug',
    title: 'Test Event',
    description: 'Test Description',
    reportedBy: 'test-user-id',
    reportedByName: 'Test User',
    reportedByEmail: '<EMAIL>',
    type: 'incident' as const,
    category: 'chemical' as const,
    severity: 'high' as const,
    status: 'open' as const,
    reportedAt: new Date(),
    upkeepCompanyId: 'test-company-id',
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: null,
    archivedAt: null,
    oshaReportable: false,
    aiConfidenceScore: null,
    locationId: null,
    assetIds: [],
    immediateActions: null,
    teamMembersToNotify: [],
    customerName: null,
    customerPhoneNumber: null,
    customerAddress: null,
    witnesses: [],
    media: [],
  };

  beforeEach(() => {
    vi.clearAllMocks();
    const adminUser = createAdminUser();
    vi.mocked(getUserById).mockResolvedValue(adminUser);
    vi.mocked(getUsersPublic).mockResolvedValue({
      noResults: false,
      result: [
        {
          id: adminUser.id,
          firstName: adminUser.firstName,
          lastName: adminUser.lastName,
          fullName: adminUser.fullName,
          username: adminUser.email,
          email: adminUser.email,
        },
      ],
      nextCursor: undefined,
    });
    vi.mocked(getEventById).mockResolvedValue(mockEvent);
  });

  describe('Admin User Permissions', () => {
    const adminUser = createAdminUser();
    const adminContext = createMockContext(adminUser);

    beforeEach(() => {
      // Admin should have full permissions
      vi.mocked(hasPermission).mockReturnValue(true);
    });

    it('should allow admin to create events', async () => {
      vi.mocked(createEvent).mockResolvedValue({
        ...mockEvent,
      });

      await expect(
        eventRouter.createCaller(adminContext).create({
          title: 'Admin Event',
          description: 'Created by admin',
          type: 'incident',
          category: 'chemical',
          severity: 'high',
          reportedAt: yesterday,
        }),
      ).resolves.not.toThrow();

      expect(hasPermission).toHaveBeenCalledWith(adminUser, MODULES.EHS_EVENT, ALLOWED_ACTIONS.CREATE, false);
    });

    it('should allow admin to view any event', async () => {
      await expect(eventRouter.createCaller(adminContext).getById({ id: mockEvent.id })).resolves.toBeDefined();

      expect(hasPermission).toHaveBeenCalledWith(adminUser, MODULES.EHS_EVENT, ALLOWED_ACTIONS.VIEW, false);
    });
  });

  describe('Technician User Permissions', () => {
    const technicianUser = createTechnicianUser();
    const technicianContext = createMockContext(technicianUser);

    beforeEach(() => {
      // Technician has limited permissions
      vi.mocked(hasPermission).mockImplementation((user, module, action) => {
        if (module === MODULES.EHS_EVENT) {
          return [ALLOWED_ACTIONS.CREATE, ALLOWED_ACTIONS.VIEW, ALLOWED_ACTIONS.EDIT].includes(
            action as typeof ALLOWED_ACTIONS.CREATE,
          );
        }
        return false;
      });
    });

    it('should allow technician to create events', async () => {
      vi.mocked(createEvent).mockResolvedValue({
        ...mockEvent,
      });

      await expect(
        eventRouter.createCaller(technicianContext).create({
          title: 'Technician Event',
          description: 'Created by technician',
          type: 'incident',
          category: 'chemical',
          severity: 'high',
          reportedAt: yesterday,
        }),
      ).resolves.not.toThrow();
    });

    it('should allow technician to view events (partial permission)', async () => {
      await expect(eventRouter.createCaller(technicianContext).getById({ id: mockEvent.id })).resolves.toBeDefined();
    });
  });

  describe('Unauthorized User', () => {
    const unauthorizedUser = createUnauthorizedUser();
    const unauthorizedContext = createMockContext(unauthorizedUser);

    beforeEach(() => {
      // No permissions
      vi.mocked(hasPermission).mockReturnValue(false);
    });

    it('should deny unauthorized user from creating events', async () => {
      await expect(
        eventRouter.createCaller(unauthorizedContext).create({
          title: 'Unauthorized Event',
          description: 'Should fail',
          type: 'incident',
          category: 'chemical',
          severity: 'high',
          reportedAt: yesterday,
        }),
      ).rejects.toThrow(TRPCError);
    });

    it('should deny unauthorized user from viewing events', async () => {
      await expect(eventRouter.createCaller(unauthorizedContext).getById({ id: mockEvent.id })).rejects.toThrow(
        TRPCError,
      );
    });
  });

  describe('Partial Permissions with Resource Ownership', () => {
    const partialPermissions: UserPermission = {
      [MODULES.EHS_EVENT]: {
        [ALLOWED_ACTIONS.VIEW]: PERMISSION_LEVELS.PARTIAL,
        [ALLOWED_ACTIONS.EDIT]: PERMISSION_LEVELS.PARTIAL,
      },
    };

    const partialUser = createUserWithCustomPermissions(partialPermissions);
    const partialContext = createMockContext(partialUser);

    beforeEach(() => {
      // Partial permissions - need contextual checks
      vi.mocked(hasPermission).mockImplementation((user, module, action) => {
        const modulePermissions = user.permissions[module];
        if (modulePermissions) {
          const permission = modulePermissions[action];
          return !!permission && permission !== PERMISSION_LEVELS.NONE;
        }
        return false;
      });
    });

    it('should allow partial user to view their own events', async () => {
      // Mock that user owns this event
      const ownedEvent = {
        ...mockEvent,
        id: createId(),
        reportedBy: 'owned-user-id',
        archivedAt: null,
      };
      vi.mocked(getEventById).mockResolvedValue(ownedEvent);

      await expect(eventRouter.createCaller(partialContext).getById({ id: ownedEvent.id })).resolves.toBeDefined();
    });

    it('should demonstrate permission middleware is called', async () => {
      vi.mocked(getEventById).mockResolvedValue(mockEvent);

      await eventRouter.createCaller(partialContext).getById({ id: mockEvent.id });

      // Verify the permission system was called
      expect(hasPermission).toHaveBeenCalledWith(partialUser, MODULES.EHS_EVENT, ALLOWED_ACTIONS.VIEW, false);
    });
  });
});
