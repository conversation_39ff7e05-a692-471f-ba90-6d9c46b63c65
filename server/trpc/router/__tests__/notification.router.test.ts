import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { createMockContext } from '@server/trpc/router/__tests__/test-utils';
import { db } from '@server/db';
import { mockUser } from '@server/test/fixtures/user';
import { notifications, userNotifications } from '@shared/schema';
import { notificationRouter } from '@server/trpc/router/notification.router';
import { and, eq } from 'drizzle-orm';
import { createId } from '@paralleldrive/cuid2';

// Mock the emitNotificationChange function
vi.mock('@server/services/notification-pubsub', () => ({
  emitNotificationChange: vi.fn(),
  notificationEmitter: {
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn(),
  },
}));

describe('notificationRouter', () => {
  const mockContext = createMockContext(mockUser);
  // Wrap the router procedures in trpc.router for testing
  const caller = notificationRouter.createCaller(mockContext);

  // Helper function to create a notification
  const createTestNotification = async (title: string, description?: string, readAt?: Date | null) => {
    const notificationId = createId();

    // Insert notification
    await db.insert(notifications).values({
      id: notificationId,
      upkeepCompanyId: mockUser.upkeepCompanyId,
      title,
      description: description || null,
      url: '/test-url',
      createdAt: new Date(),
      updatedAt: null,
      archivedAt: null,
    });

    // Insert user notification
    await db.insert(userNotifications).values({
      id: createId(),
      userId: mockUser.id,
      notificationId,
      readAt: readAt || null,
      archivedAt: null,
      createdAt: new Date(),
      updatedAt: readAt ? new Date() : null, // Set updatedAt if readAt is provided
    });

    return notificationId;
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(async () => {
    await db.delete(userNotifications);
    await db.delete(notifications);
  });

  describe('getNotifications', () => {
    it('should return user-specific notifications', async () => {
      // Create notifications for this user
      await createTestNotification('Test Notification 1', 'Description 1');
      await createTestNotification('Test Notification 2', 'Description 2');

      const result = await caller.getNotifications();

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBe(2);

      // Verify notification data
      const notification1 = result.find((n) => n.title === 'Test Notification 1');
      const notification2 = result.find((n) => n.title === 'Test Notification 2');

      expect(notification1).toBeDefined();
      expect(notification1?.description).toBe('Description 1');
      expect(notification1?.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
      expect(notification1?.readAt).toBeNull();

      expect(notification2).toBeDefined();
      expect(notification2?.description).toBe('Description 2');
      expect(notification2?.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
      expect(notification2?.readAt).toBeNull();
    });

    it('should only return notifications for the current user', async () => {
      // Create notification for this user
      await createTestNotification('User A Notification');

      // Create notification for different user directly in database
      const otherNotificationId = createId();
      await db.insert(notifications).values({
        id: otherNotificationId,
        upkeepCompanyId: mockUser.upkeepCompanyId,
        title: 'User B Notification',
        description: null,
        url: '/other-url',
        createdAt: new Date(),
        updatedAt: null,
        archivedAt: null,
      });

      await db.insert(userNotifications).values({
        id: createId(),
        userId: '*********', // Different user
        notificationId: otherNotificationId,
        readAt: null,
        archivedAt: null,
        createdAt: new Date(),
        updatedAt: null,
      });

      const result = await caller.getNotifications();

      // Should only include our user's notification
      expect(result.length).toBe(1);
      expect(result[0].title).toBe('User A Notification');
    });

    it('should only return notifications for the current company', async () => {
      // Create notification for this company
      await createTestNotification('Company A Notification');

      // Create notification for different company directly in database
      const otherNotificationId = createId();
      await db.insert(notifications).values({
        id: otherNotificationId,
        upkeepCompanyId: '*********', // Different company
        title: 'Company B Notification',
        description: null,
        url: '/other-url',
        createdAt: new Date(),
        updatedAt: null,
        archivedAt: null,
      });

      await db.insert(userNotifications).values({
        id: createId(),
        userId: mockUser.id,
        notificationId: otherNotificationId,
        readAt: null,
        archivedAt: null,
        createdAt: new Date(),
        updatedAt: null,
      });

      const result = await caller.getNotifications();

      // Should only include our company's notification
      expect(result.length).toBe(1);
      expect(result[0].title).toBe('Company A Notification');
    });

    it('should not return archived notifications', async () => {
      // Create regular notification
      await createTestNotification('Active Notification');

      // Create archived notification
      const archivedNotificationId = createId();
      await db.insert(notifications).values({
        id: archivedNotificationId,
        upkeepCompanyId: mockUser.upkeepCompanyId,
        title: 'Archived Notification',
        description: null,
        url: '/archived-url',
        createdAt: new Date(),
        updatedAt: null,
        archivedAt: new Date(), // Archived
      });

      await db.insert(userNotifications).values({
        id: createId(),
        userId: mockUser.id,
        notificationId: archivedNotificationId,
        readAt: null,
        archivedAt: null,
        createdAt: new Date(),
        updatedAt: null,
      });

      const result = await caller.getNotifications();

      // Should only include active notification
      expect(result.length).toBe(1);
      expect(result[0].title).toBe('Active Notification');
    });

    it('should prioritize unread notifications over read ones', async () => {
      // Create read notification (older)
      const oldDate = new Date('2024-01-01');
      const readNotificationId = createId();
      await db.insert(notifications).values({
        id: readNotificationId,
        upkeepCompanyId: mockUser.upkeepCompanyId,
        title: 'Read Notification',
        description: null,
        url: '/read-url',
        createdAt: oldDate,
        updatedAt: null,
        archivedAt: null,
      });

      await db.insert(userNotifications).values({
        id: createId(),
        userId: mockUser.id,
        notificationId: readNotificationId,
        readAt: new Date(), // Marked as read
        archivedAt: null,
        createdAt: oldDate,
        updatedAt: null,
      });

      // Create unread notification (newer)
      const newDate = new Date('2024-01-02');
      const unreadNotificationId = createId();
      await db.insert(notifications).values({
        id: unreadNotificationId,
        upkeepCompanyId: mockUser.upkeepCompanyId,
        title: 'Unread Notification',
        description: null,
        url: '/unread-url',
        createdAt: newDate,
        updatedAt: null,
        archivedAt: null,
      });

      await db.insert(userNotifications).values({
        id: createId(),
        userId: mockUser.id,
        notificationId: unreadNotificationId,
        readAt: null, // Unread
        archivedAt: null,
        createdAt: newDate,
        updatedAt: null,
      });

      const result = await caller.getNotifications();

      expect(result.length).toBe(2);
      // Unread notification should come first despite being newer
      expect(result[0].title).toBe('Unread Notification');
      expect(result[0].readAt).toBeNull();
      expect(result[1].title).toBe('Read Notification');
      expect(result[1].readAt).not.toBeNull();
    });

    it('should limit results to 10 notifications', async () => {
      // Create 15 notifications
      for (let i = 1; i <= 15; i++) {
        await createTestNotification(`Notification ${i}`);
      }

      const result = await caller.getNotifications();

      expect(result.length).toBe(10);
    });
  });

  describe('getUnreadCount', () => {
    it('should return correct count of unread notifications', async () => {
      // Create unread notifications
      await createTestNotification('Unread 1');
      await createTestNotification('Unread 2');
      await createTestNotification('Unread 3');

      // Create read notification
      await createTestNotification('Read 1', 'Description', new Date());

      const result = await caller.getUnreadCount();

      expect(result).toBe(3);
    });

    it('should return 0 when no unread notifications exist', async () => {
      // Create only read notifications
      await createTestNotification('Read 1', 'Description', new Date());
      await createTestNotification('Read 2', 'Description', new Date());

      const result = await caller.getUnreadCount();

      expect(result).toBe(0);
    });

    it('should return 0 when no notifications exist', async () => {
      const result = await caller.getUnreadCount();

      expect(result).toBe(0);
    });

    it('should only count notifications for current user and company', async () => {
      // Create unread notification for this user
      await createTestNotification('User A Unread');

      // Create notification for different user
      const otherNotificationId = createId();
      await db.insert(notifications).values({
        id: otherNotificationId,
        upkeepCompanyId: mockUser.upkeepCompanyId,
        title: 'User B Unread',
        description: null,
        url: '/other-url',
        createdAt: new Date(),
        updatedAt: null,
        archivedAt: null,
      });

      await db.insert(userNotifications).values({
        id: createId(),
        userId: '*********', // Different user
        notificationId: otherNotificationId,
        readAt: null,
        archivedAt: null,
        createdAt: new Date(),
        updatedAt: null,
      });

      const result = await caller.getUnreadCount();

      expect(result).toBe(1); // Only count our user's notification
    });

    it('should not count archived notifications', async () => {
      // Create regular unread notification
      await createTestNotification('Active Unread');

      // Create archived unread notification
      const archivedNotificationId = createId();
      await db.insert(notifications).values({
        id: archivedNotificationId,
        upkeepCompanyId: mockUser.upkeepCompanyId,
        title: 'Archived Unread',
        description: null,
        url: '/archived-url',
        createdAt: new Date(),
        updatedAt: null,
        archivedAt: new Date(), // Archived
      });

      await db.insert(userNotifications).values({
        id: createId(),
        userId: mockUser.id,
        notificationId: archivedNotificationId,
        readAt: null, // Unread but archived
        archivedAt: null,
        createdAt: new Date(),
        updatedAt: null,
      });

      const result = await caller.getUnreadCount();

      expect(result).toBe(1); // Only count active notification
    });
  });

  describe('markAsRead', () => {
    it('should mark a notification as read', async () => {
      const notificationId = await createTestNotification('Test Notification');

      const result = await caller.markAsRead({ notificationId });

      expect(result.success).toBe(true);

      // Verify in database
      const userNotification = await db
        .select()
        .from(userNotifications)
        .where(and(eq(userNotifications.notificationId, notificationId), eq(userNotifications.userId, mockUser.id)));

      expect(userNotification.length).toBe(1);
      expect(userNotification[0].readAt).not.toBeNull();
      expect(userNotification[0].updatedAt).not.toBeNull();
    });

    it('should only mark notifications for the current user', async () => {
      // Create notification for this user
      const notificationId = await createTestNotification('Test Notification');

      // Create notification for different user with same notification ID
      await db.insert(userNotifications).values({
        id: createId(),
        userId: '*********', // Different user
        notificationId,
        readAt: null,
        archivedAt: null,
        createdAt: new Date(),
        updatedAt: null,
      });

      await caller.markAsRead({ notificationId });

      // Verify only our user's notification was marked as read
      const userNotificationsResult = await db
        .select()
        .from(userNotifications)
        .where(eq(userNotifications.notificationId, notificationId));

      expect(userNotificationsResult.length).toBe(2);

      const ourNotification = userNotificationsResult.find((un) => un.userId === mockUser.id);
      const otherNotification = userNotificationsResult.find((un) => un.userId === '*********');

      expect(ourNotification?.readAt).not.toBeNull();
      expect(otherNotification?.readAt).toBeNull();
    });

    it('should handle marking already read notification', async () => {
      // Create already read notification
      const notificationId = await createTestNotification('Already Read', 'Description', new Date());

      const result = await caller.markAsRead({ notificationId });

      expect(result.success).toBe(true);

      // Should still have readAt set
      const userNotification = await db
        .select()
        .from(userNotifications)
        .where(and(eq(userNotifications.notificationId, notificationId), eq(userNotifications.userId, mockUser.id)));

      expect(userNotification[0].readAt).not.toBeNull();
    });

    it('should handle non-existent notification gracefully', async () => {
      const nonExistentId = createId();

      const result = await caller.markAsRead({ notificationId: nonExistentId });

      expect(result.success).toBe(true); // Operation succeeds even if no rows affected
    });

    it('should validate required notificationId parameter', async () => {
      // Test missing notificationId field
      await expect(caller.markAsRead({} as any)).rejects.toThrow();

      // Test invalid id format (empty string should pass validation but CUID2 format validation would catch it)
      // Note: In current implementation, empty string passes through - this test verifies current behavior
      const result = await caller.markAsRead({ notificationId: '' });
      expect(result.success).toBe(true);
    });
  });

  describe('markAllAsRead', () => {
    it('should mark all unread notifications as read', async () => {
      // Create multiple unread notifications
      const notificationId1 = await createTestNotification('Unread 1');
      const notificationId2 = await createTestNotification('Unread 2');
      const notificationId3 = await createTestNotification('Unread 3');

      // Create already read notification
      const readNotificationId = await createTestNotification('Already Read', 'Description', new Date());

      const result = await caller.markAllAsRead();

      expect(result.success).toBe(true);

      // Verify all previously unread notifications are now read
      const userNotificationsResult = await db
        .select()
        .from(userNotifications)
        .where(eq(userNotifications.userId, mockUser.id));

      expect(userNotificationsResult.length).toBe(4);

      for (const userNotification of userNotificationsResult) {
        expect(userNotification.readAt).not.toBeNull();
        expect(userNotification.updatedAt).not.toBeNull();
      }
    });

    it('should only mark notifications for current user', async () => {
      // Create unread notification for this user
      const notificationId = await createTestNotification('User A Unread');

      // Create unread notification for different user
      const otherNotificationId = createId();
      await db.insert(notifications).values({
        id: otherNotificationId,
        upkeepCompanyId: mockUser.upkeepCompanyId,
        title: 'User B Unread',
        description: null,
        url: '/other-url',
        createdAt: new Date(),
        updatedAt: null,
        archivedAt: null,
      });

      await db.insert(userNotifications).values({
        id: createId(),
        userId: '*********', // Different user
        notificationId: otherNotificationId,
        readAt: null,
        archivedAt: null,
        createdAt: new Date(),
        updatedAt: null,
      });

      await caller.markAllAsRead();

      // Verify only our user's notification was marked as read
      const ourNotification = await db
        .select()
        .from(userNotifications)
        .where(and(eq(userNotifications.notificationId, notificationId), eq(userNotifications.userId, mockUser.id)));

      const otherNotification = await db
        .select()
        .from(userNotifications)
        .where(
          and(eq(userNotifications.notificationId, otherNotificationId), eq(userNotifications.userId, '*********')),
        );

      expect(ourNotification[0].readAt).not.toBeNull();
      expect(otherNotification[0].readAt).toBeNull();
    });

    it('should not affect archived user notifications', async () => {
      // Create regular unread notification
      const notificationId = await createTestNotification('Active Unread');

      // Create archived user notification
      const archivedNotificationId = createId();
      await db.insert(notifications).values({
        id: archivedNotificationId,
        upkeepCompanyId: mockUser.upkeepCompanyId,
        title: 'Archived Notification',
        description: null,
        url: '/archived-url',
        createdAt: new Date(),
        updatedAt: null,
        archivedAt: null, // Notification not archived
      });

      await db.insert(userNotifications).values({
        id: createId(),
        userId: mockUser.id,
        notificationId: archivedNotificationId,
        readAt: null,
        archivedAt: new Date(), // User notification archived
        createdAt: new Date(),
        updatedAt: null,
      });

      await caller.markAllAsRead();

      // Verify active notification was marked as read
      const activeNotification = await db
        .select()
        .from(userNotifications)
        .where(and(eq(userNotifications.notificationId, notificationId), eq(userNotifications.userId, mockUser.id)));

      // Verify archived user notification was not affected
      const archivedUserNotification = await db
        .select()
        .from(userNotifications)
        .where(
          and(eq(userNotifications.notificationId, archivedNotificationId), eq(userNotifications.userId, mockUser.id)),
        );

      expect(activeNotification[0].readAt).not.toBeNull();
      expect(archivedUserNotification[0].readAt).toBeNull(); // Should remain unread
    });

    it('should handle case with no unread notifications', async () => {
      // Create only read notifications
      await createTestNotification('Already Read 1', 'Description', new Date());
      await createTestNotification('Already Read 2', 'Description', new Date());

      const result = await caller.markAllAsRead();

      expect(result.success).toBe(true);
    });

    it('should handle empty notification list', async () => {
      const result = await caller.markAllAsRead();

      expect(result.success).toBe(true);
    });
  });

  describe('integration workflow', () => {
    it('should handle complete notification workflow', async () => {
      // Step 1: Create notifications
      const notificationId1 = await createTestNotification('Workflow Test 1');
      const notificationId2 = await createTestNotification('Workflow Test 2');

      // Step 2: Check initial state
      let notifications = await caller.getNotifications();
      let unreadCount = await caller.getUnreadCount();

      expect(notifications.length).toBe(2);
      expect(unreadCount).toBe(2);

      // Step 3: Mark one as read
      await caller.markAsRead({ notificationId: notificationId1 });

      notifications = await caller.getNotifications();
      unreadCount = await caller.getUnreadCount();

      expect(notifications.length).toBe(2); // Still 2 notifications
      expect(unreadCount).toBe(1); // But only 1 unread

      const readNotification = notifications.find((n) => n.id === notificationId1);
      const unreadNotification = notifications.find((n) => n.id === notificationId2);

      expect(readNotification?.readAt).not.toBeNull();
      expect(unreadNotification?.readAt).toBeNull();

      // Step 4: Mark all as read
      await caller.markAllAsRead();

      notifications = await caller.getNotifications();
      unreadCount = await caller.getUnreadCount();

      expect(notifications.length).toBe(2); // Still 2 notifications
      expect(unreadCount).toBe(0); // No unread notifications

      for (const notification of notifications) {
        expect(notification.readAt).not.toBeNull();
      }
    });

    it('should maintain data consistency with multiple operations', async () => {
      // Create multiple notifications
      const notificationId1 = await createTestNotification('Consistency Test 1');
      const notificationId2 = await createTestNotification('Consistency Test 2');
      const notificationId3 = await createTestNotification('Consistency Test 3');

      // Mark one as read
      await caller.markAsRead({ notificationId: notificationId2 });

      // Verify state
      const notifications = await caller.getNotifications();
      const unreadCount = await caller.getUnreadCount();

      expect(notifications.length).toBe(3);
      expect(unreadCount).toBe(2);

      // Verify specific read states
      const notification1 = notifications.find((n) => n.id === notificationId1);
      const notification2 = notifications.find((n) => n.id === notificationId2);
      const notification3 = notifications.find((n) => n.id === notificationId3);

      expect(notification1?.readAt).toBeNull(); // Unread
      expect(notification2?.readAt).not.toBeNull(); // Read
      expect(notification3?.readAt).toBeNull(); // Unread
    });
  });
});
