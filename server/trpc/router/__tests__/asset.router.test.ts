import type { Asset } from '@shared/types/assets.types';
import { TRPCError } from '@trpc/server';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { assetRouter } from '../asset.router';
import { createAdminUser, createMockContext } from './test-utils';

// Mock the asset services
vi.mock('@server/services/asset.service', () => ({
  getAssets: vi.fn(),
  searchAssetsPublic: vi.fn(),
}));

// Import mocked services for type checking
import { getAssets, searchAssetsPublic } from '@server/services/asset.service';

describe('Asset Router', () => {
  const mockUser = createAdminUser();
  const mockContext = createMockContext(mockUser) as any;
  const nullUserContext = createMockContext(null) as any;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('search procedure', () => {
    it('should return cursor-paginated assets with default parameters', async () => {
      const mockAssets: Asset[] = [
        { id: 'asset-1', name: 'Test Asset 1', description: 'Description 1' },
        { id: 'asset-2', name: 'Test Asset 2', description: 'Description 2' },
      ];

      const mockResponse = {
        noResults: false,
        result: mockAssets,
        nextCursor: undefined,
      };

      vi.mocked(getAssets).mockResolvedValue(mockResponse);

      const caller = assetRouter.createCaller(mockContext);
      const result = await caller.search({});

      expect(getAssets).toHaveBeenCalledWith(
        {
          cursor: 0,
          limit: 10,
          sort: 'createdAt DESC',
        },
        mockContext.req.headers,
      );

      expect(result).toEqual(mockResponse);
    });

    it('should handle search with custom parameters', async () => {
      const mockAssets: Asset[] = [{ id: 'asset-1', name: 'Tool Box', description: 'A tool box' }];

      const mockResponse = {
        noResults: false,
        result: mockAssets,
        nextCursor: 10,
      };

      vi.mocked(getAssets).mockResolvedValue(mockResponse);

      const caller = assetRouter.createCaller(mockContext);
      const result = await caller.search({
        cursor: 5,
        limit: 5,
        search: 'tool',
      });

      expect(getAssets).toHaveBeenCalledWith(
        {
          cursor: 5,
          limit: 5,
          search: 'tool',
          sort: 'createdAt DESC',
        },
        mockContext.req.headers,
      );

      expect(result).toEqual(mockResponse);
    });

    it('should handle locationId parameter and convert to objectLocation array', async () => {
      const mockAssets: Asset[] = [{ id: 'asset-1', name: 'Warehouse Pump', description: 'Industrial pump' }];

      const mockResponse = {
        noResults: false,
        result: mockAssets,
        nextCursor: undefined,
      };

      vi.mocked(getAssets).mockResolvedValue(mockResponse);

      const caller = assetRouter.createCaller(mockContext);
      const result = await caller.search({
        locationId: 'warehouse-a',
        search: 'pump',
      });

      expect(getAssets).toHaveBeenCalledWith(
        {
          cursor: 0,
          limit: 10,
          search: 'pump',
          objectLocation: ['warehouse-a'],
          sort: 'createdAt DESC',
        },
        mockContext.req.headers,
      );

      expect(result).toEqual(mockResponse);
    });

    it('should handle empty search results', async () => {
      const mockResponse = {
        noResults: true,
        result: [],
        nextCursor: undefined,
      };

      vi.mocked(getAssets).mockResolvedValue(mockResponse);

      const caller = assetRouter.createCaller(mockContext);
      const result = await caller.search({ search: 'nonexistent' });

      expect(result).toEqual(mockResponse);
    });

    it('should handle service calls efficiently', async () => {
      const mockAssets: Asset[] = [{ id: 'asset-1', name: 'Asset 1', description: 'Description 1' }];

      const mockResponse = {
        noResults: false,
        result: mockAssets,
        nextCursor: undefined,
      };

      vi.mocked(getAssets).mockResolvedValue(mockResponse);

      const caller = assetRouter.createCaller(mockContext);
      const result = await caller.search({ search: 'test' });

      expect(getAssets).toHaveBeenCalledTimes(1);
      expect(result).toEqual(mockResponse);
    });

    it('should validate input parameters', async () => {
      const caller = assetRouter.createCaller(mockContext);

      // Test invalid cursor (less than 0)
      await expect(caller.search({ cursor: -1 })).rejects.toThrow();

      // Test invalid limit (greater than 100)
      await expect(caller.search({ limit: 101 })).rejects.toThrow();

      // Test invalid limit (less than 1)
      await expect(caller.search({ limit: 0 })).rejects.toThrow();
    });

    it('should handle service errors gracefully', async () => {
      vi.mocked(getAssets).mockRejectedValue(new Error('Service error'));

      const caller = assetRouter.createCaller(mockContext);

      await expect(caller.search({ search: 'test' })).rejects.toThrow('Service error');
    });

    it('should throw UNAUTHORIZED when user is not authenticated', async () => {
      await expect(assetRouter.createCaller(nullUserContext).search({})).rejects.toThrow(TRPCError);
    });

    it('should work without locationId parameter', async () => {
      const mockAssets: Asset[] = [{ id: 'asset-1', name: 'Any Asset', description: 'Asset from any location' }];

      const mockResponse = {
        noResults: false,
        result: mockAssets,
        nextCursor: undefined,
      };

      vi.mocked(getAssets).mockResolvedValue(mockResponse);

      const caller = assetRouter.createCaller(mockContext);
      await caller.search({ search: 'asset' });

      // Verify objectLocation is not included when locationId is not provided
      expect(getAssets).toHaveBeenCalledWith(
        {
          cursor: 0,
          limit: 10,
          search: 'asset',
          sort: 'createdAt DESC',
        },
        mockContext.req.headers,
      );
    });

    it('should handle large cursor values correctly', async () => {
      const mockResponse = {
        noResults: false,
        result: [],
        nextCursor: undefined,
      };

      vi.mocked(getAssets).mockResolvedValue(mockResponse);

      const caller = assetRouter.createCaller(mockContext);
      const result = await caller.search({ cursor: 1000, limit: 20 });

      expect(getAssets).toHaveBeenCalledWith(
        expect.objectContaining({
          cursor: 1000,
          limit: 20,
        }),
        mockContext.req.headers,
      );

      expect(result).toEqual(mockResponse);
    });
  });

  describe('searchPublic procedure', () => {
    it('should search assets with valid upkeepCompanyId', async () => {
      const mockResponse = {
        noResults: false,
        result: [
          {
            id: 'RN9QcEi9O1',
            name: 'Fan Assembly-TRANE HVAC Suite B',
            description: 'Suite B',
          },
          {
            id: 'UJnih6dxAM',
            name: 'TRANE HVAC Suite B',
            description: 'Suite B',
          },
        ],
        nextCursor: undefined,
      };

      vi.mocked(searchAssetsPublic).mockResolvedValue(mockResponse);

      const publicContext = { user: null, req: { headers: { 'x-forwarded-for': '127.0.0.1' } } };
      const caller = assetRouter.createCaller(publicContext as any);
      const result = await caller.searchPublic({
        upkeepCompanyId: 'jTRpZXLexa',
        search: '',
        limit: 100,
      });

      expect(searchAssetsPublic).toHaveBeenCalledWith({
        upkeepCompanyId: 'jTRpZXLexa',
        search: '',
        limit: 100,
      });

      expect(result).toEqual(mockResponse);
    });

    it('should handle locationId parameter in public search', async () => {
      const mockResponse = {
        noResults: false,
        result: [
          {
            id: 'asset-1',
            name: 'Warehouse Asset',
            description: 'Warehouse A',
          },
        ],
        nextCursor: undefined,
      };

      vi.mocked(searchAssetsPublic).mockResolvedValue(mockResponse);

      const publicContext = { user: null, req: { headers: { 'x-forwarded-for': '*********' } } };
      const caller = assetRouter.createCaller(publicContext as any);
      const result = await caller.searchPublic({
        upkeepCompanyId: 'role-123',
        search: 'warehouse',
        limit: 50,
        locationId: 'warehouse-a',
      });

      expect(searchAssetsPublic).toHaveBeenCalledWith({
        upkeepCompanyId: 'role-123',
        search: 'warehouse',
        limit: 50,
        locationId: 'warehouse-a',
      });

      expect(result).toEqual(mockResponse);
    });

    it('should use default values when optional parameters are not provided', async () => {
      const mockResponse = {
        noResults: true,
        result: [],
        nextCursor: undefined,
      };

      vi.mocked(searchAssetsPublic).mockResolvedValue(mockResponse);

      const publicContext = { user: null, req: { headers: { 'x-forwarded-for': '127.0.0.3' } } };
      const caller = assetRouter.createCaller(publicContext as any);
      const result = await caller.searchPublic({
        upkeepCompanyId: 'jTRpZXLexa',
      });

      expect(searchAssetsPublic).toHaveBeenCalledWith({
        upkeepCompanyId: 'jTRpZXLexa',
      });

      expect(result).toEqual(mockResponse);
    });

    it('should validate limit parameter bounds', async () => {
      const publicContext = { user: null, req: { headers: { 'x-forwarded-for': '127.0.0.5' } } };
      const caller = assetRouter.createCaller(publicContext as any);

      // Test limit too low
      await expect(
        caller.searchPublic({
          upkeepCompanyId: 'jTRpZXLexa',
          limit: 0,
        }),
      ).rejects.toThrow();

      // Test limit too high
      await expect(
        caller.searchPublic({
          upkeepCompanyId: 'jTRpZXLexa',
          limit: 101,
        }),
      ).rejects.toThrow();
    });

    it('should handle service errors gracefully', async () => {
      const mockError = new Error('Service connection error');
      vi.mocked(searchAssetsPublic).mockRejectedValue(mockError);

      const publicContext = { user: null, req: { headers: { 'x-forwarded-for': '127.0.0.6' } } };
      const caller = assetRouter.createCaller(publicContext as any);

      await expect(
        caller.searchPublic({
          upkeepCompanyId: 'jTRpZXLexa',
        }),
      ).rejects.toThrow('Service connection error');
    });

    it('should return empty array when no assets found', async () => {
      const mockResponse = {
        noResults: true,
        result: [],
        nextCursor: undefined,
      };

      vi.mocked(searchAssetsPublic).mockResolvedValue(mockResponse);

      const publicContext = { user: null, req: { headers: { 'x-forwarded-for': '127.0.0.7' } } };
      const caller = assetRouter.createCaller(publicContext as any);
      const result = await caller.searchPublic({
        upkeepCompanyId: 'jTRpZXLexa',
        search: 'nonexistent',
      });

      expect(result).toEqual(mockResponse);
    });

    it('should work without user authentication', async () => {
      // This test demonstrates that the public procedure doesn't require user authentication
      const contextWithoutUser = {
        req: {
          headers: { 'x-forwarded-for': '*********' },
        },
        // No user property - this would fail for loggedProcedure
      };

      const mockResponse = {
        noResults: false,
        result: [
          {
            id: 'public-asset',
            name: 'Public Asset',
            description: 'Accessible without auth',
          },
        ],
        nextCursor: undefined,
      };

      vi.mocked(searchAssetsPublic).mockResolvedValue(mockResponse);

      const caller = assetRouter.createCaller(contextWithoutUser as any);
      const result = await caller.searchPublic({
        upkeepCompanyId: 'public-company-123',
      });

      expect(result).toEqual(mockResponse);
      expect(searchAssetsPublic).toHaveBeenCalledWith({
        upkeepCompanyId: 'public-company-123',
      });
    });

    it('should handle search with all parameters', async () => {
      const mockResponse = {
        noResults: false,
        result: [
          {
            id: 'asset-1',
            name: 'Filtered Asset',
            description: 'Matches search criteria',
          },
        ],
        nextCursor: undefined,
      };

      vi.mocked(searchAssetsPublic).mockResolvedValue(mockResponse);

      const publicContext = { user: null, req: { headers: { 'x-forwarded-for': '*********' } } };
      const caller = assetRouter.createCaller(publicContext as any);
      const result = await caller.searchPublic({
        upkeepCompanyId: 'role-123',
        search: 'filtered',
        limit: 100,
        locationId: 'building-1',
      });

      expect(searchAssetsPublic).toHaveBeenCalledWith({
        upkeepCompanyId: 'role-123',
        search: 'filtered',
        limit: 100,
        locationId: 'building-1',
      });

      expect(result).toEqual(mockResponse);
    });
  });
});
