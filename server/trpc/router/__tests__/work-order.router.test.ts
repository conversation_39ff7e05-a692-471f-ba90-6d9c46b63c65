import type { inferProcedureInput } from '@trpc/server';
import { TRPCError } from '@trpc/server';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { createWorkOrderFromCapa, searchWorkOrdersByCapaId, getWorkOrders } from '../../../services/work-order.service';
import type { AppRouter } from '../index';
import { workOrderRouter } from '../work-order.router';
import { createAdminUser, createMockContext } from './test-utils';
import { WorkOrder } from '@shared/types/work-orders.types';

// Mock work order service
vi.mock('../../../services/work-order.service', () => ({
  createWorkOrderFromCapa: vi.fn(),
  searchWorkOrdersByCapaId: vi.fn(),
  getWorkOrdersCountByCapa: vi.fn(),
  getWorkOrders: vi.fn(),
}));

// Type for the createFromCapa input
type CreateFromCapaInput = inferProcedureInput<AppRouter['workOrder']['createFromCapa']>;

describe('Work Order Router', () => {
  const mockUser = createAdminUser();
  const mockContext = createMockContext(mockUser) as any;
  const nullUserContext = createMockContext(null) as any;

  const validCapaInput = {
    id: 'capa-123',
    title: 'Fix hydraulic system leak',
    slug: 'CAPA-0063',
    actionsToAddress: 'Replace the hydraulic seal on Pump 42 immediately',
    priority: 'high' as const,
    dueDate: '2024-12-31T23:59:59.999Z',
    locationId: 'loc-456',
    assetId: 'asset-789',
    userAssignedTo: 'user-123',
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('search procedure', () => {
    it('should return cursor-paginated work orders with default parameters', async () => {
      const mockWorkOrders: WorkOrder[] = [
        {
          id: 'wo-1',
          workOrderNumber: 'WO-001',
          title: 'Fix hydraulic pump',
          currentStatus: 'open',
          priority: 'high' as const,
          dueDate: '2024-12-31T23:59:59.999Z',
          assignedTo: 'user-123',
          locationId: 'loc-456',
          assetId: 'asset-789',
          assetName: 'Hydraulic Pump #1',
        },
        {
          id: 'wo-2',
          workOrderNumber: 'WO-002',
          title: 'Replace air filter',
          currentStatus: 'inProgress',
          priority: 'medium' as const,
          dueDate: '2024-12-25T12:00:00.000Z',
          assignedTo: 'user-456',
          locationId: 'loc-789',
          assetId: 'asset-123',
          assetName: 'HVAC Unit #2',
        },
      ];

      const mockResponse = {
        noResults: false,
        result: mockWorkOrders,
        nextCursor: undefined,
      };

      vi.mocked(getWorkOrders).mockResolvedValue(mockResponse);

      const caller = workOrderRouter.createCaller(mockContext);
      const result = await caller.search({});

      expect(getWorkOrders).toHaveBeenCalledWith(
        {
          sort: 'createdAt DESC',
        },
        mockContext.req.headers,
      );

      expect(result).toEqual(mockResponse);
    });

    it('should handle search with custom parameters', async () => {
      const mockWorkOrders: WorkOrder[] = [
        {
          id: 'wo-1',
          workOrderNumber: 'WO-001',
          title: 'Hydraulic system maintenance',
          currentStatus: 'open',
          priority: 'high' as const,
          dueDate: '2024-12-31T23:59:59.999Z',
          assignedTo: 'user-123',
          locationId: 'loc-456',
          assetId: 'asset-789',
          assetName: 'Hydraulic Pump #1',
        },
      ];

      const mockResponse = {
        noResults: false,
        result: mockWorkOrders,
        nextCursor: 20,
      };

      vi.mocked(getWorkOrders).mockResolvedValue(mockResponse);

      const caller = workOrderRouter.createCaller(mockContext);
      const result = await caller.search({
        cursor: 10,
        limit: 5,
        search: 'hydraulic',
        includes: ['location', 'asset'],
        mode: 'App:Active',
      });

      expect(getWorkOrders).toHaveBeenCalledWith(
        {
          cursor: 10,
          limit: 5,
          search: 'hydraulic',
          includes: ['location', 'asset'],
          mode: 'App:Active',
          sort: 'createdAt DESC',
        },
        mockContext.req.headers,
      );

      expect(result).toEqual(mockResponse);
    });

    it('should handle empty search results', async () => {
      const mockResponse = {
        noResults: true,
        result: [],
        nextCursor: undefined,
      };

      vi.mocked(getWorkOrders).mockResolvedValue(mockResponse);

      const caller = workOrderRouter.createCaller(mockContext);
      const result = await caller.search({ search: 'nonexistent' });

      expect(result).toEqual(mockResponse);
    });

    it('should handle service calls efficiently', async () => {
      const mockWorkOrders: WorkOrder[] = [
        {
          id: 'wo-1',
          workOrderNumber: 'WO-001',
          title: 'Emergency repair',
          currentStatus: 'open',
          priority: 'high' as const,
          dueDate: '2024-12-31T23:59:59.999Z',
          assignedTo: 'user-123',
          locationId: 'loc-456',
          assetId: 'asset-789',
          assetName: 'Critical Equipment',
        },
      ];

      const mockResponse = {
        noResults: false,
        result: mockWorkOrders,
        nextCursor: undefined,
      };

      vi.mocked(getWorkOrders).mockResolvedValue(mockResponse);

      const caller = workOrderRouter.createCaller(mockContext);
      const result = await caller.search({ search: 'emergency' });

      expect(getWorkOrders).toHaveBeenCalledTimes(1);
      expect(result).toEqual(mockResponse);
    });

    it('should validate input parameters', async () => {
      const caller = workOrderRouter.createCaller(mockContext);

      // Test invalid cursor (less than 0)
      await expect(caller.search({ cursor: -1 })).rejects.toThrow();

      // Test invalid limit (greater than 100)
      await expect(caller.search({ limit: 101 })).rejects.toThrow();

      // Test invalid limit (less than 1)
      await expect(caller.search({ limit: 0 })).rejects.toThrow();
    });

    it('should handle service errors gracefully', async () => {
      vi.mocked(getWorkOrders).mockRejectedValue(new Error('Service error'));

      const caller = workOrderRouter.createCaller(mockContext);

      await expect(caller.search({ search: 'test' })).rejects.toThrow('Service error');
    });

    it('should throw UNAUTHORIZED when user is not authenticated', async () => {
      await expect(workOrderRouter.createCaller(nullUserContext).search({})).rejects.toThrow(TRPCError);
    });

    it('should work without search parameter', async () => {
      const mockWorkOrders: WorkOrder[] = [
        {
          id: 'wo-1',
          workOrderNumber: 'WO-001',
          title: 'Any work order',
          currentStatus: 'open',
          priority: 'medium' as const,
          dueDate: '2024-12-31T23:59:59.999Z',
          assignedTo: 'user-123',
          locationId: 'loc-456',
          assetId: 'asset-789',
          assetName: 'Any Asset',
        },
      ];

      const mockResponse = {
        noResults: false,
        result: mockWorkOrders,
        nextCursor: undefined,
      };

      vi.mocked(getWorkOrders).mockResolvedValue(mockResponse);

      const caller = workOrderRouter.createCaller(mockContext);
      await caller.search({ includes: ['asset'] });

      // Verify search is not included when not provided
      expect(getWorkOrders).toHaveBeenCalledWith(
        {
          includes: ['asset'],
          sort: 'createdAt DESC',
        },
        mockContext.req.headers,
      );
    });

    it('should handle large cursor values correctly', async () => {
      const mockResponse = {
        noResults: false,
        result: [],
        nextCursor: undefined,
      };

      vi.mocked(getWorkOrders).mockResolvedValue(mockResponse);

      const caller = workOrderRouter.createCaller(mockContext);
      const result = await caller.search({ cursor: 1000, limit: 20 });

      expect(getWorkOrders).toHaveBeenCalledWith(
        {
          cursor: 1000,
          limit: 20,
          sort: 'createdAt DESC',
        },
        mockContext.req.headers,
      );

      expect(result).toEqual(mockResponse);
    });

    it('should handle custom mode parameters', async () => {
      const mockWorkOrders: WorkOrder[] = [
        {
          id: 'wo-1',
          workOrderNumber: 'WO-001',
          title: 'Sorted work order',
          currentStatus: 'complete',
          priority: 'low' as const,
          dueDate: '2024-12-31T23:59:59.999Z',
          assignedTo: 'user-123',
          locationId: 'loc-456',
          assetId: 'asset-789',
          assetName: 'Test Asset',
        },
      ];

      const mockResponse = {
        noResults: false,
        result: mockWorkOrders,
        nextCursor: undefined,
      };

      vi.mocked(getWorkOrders).mockResolvedValue(mockResponse);

      const caller = workOrderRouter.createCaller(mockContext);
      const result = await caller.search({
        mode: 'App:Completed',
        search: 'sorted',
      });

      expect(getWorkOrders).toHaveBeenCalledWith(
        {
          search: 'sorted',
          mode: 'App:Completed',
          sort: 'createdAt DESC',
        },
        mockContext.req.headers,
      );

      expect(result).toEqual(mockResponse);
    });

    it('should handle pagination with nextCursor', async () => {
      const mockWorkOrders: WorkOrder[] = Array.from(
        { length: 10 },
        (_, i): WorkOrder => ({
          id: `wo-${i + 1}`,
          workOrderNumber: `WO-${String(i + 1).padStart(3, '0')}`,
          title: `Work Order ${i + 1}`,
          currentStatus: 'open',
          priority: 'medium' as const,
          dueDate: '2024-12-31T23:59:59.999Z',
          assignedTo: 'user-123',
          locationId: 'loc-456',
          assetId: 'asset-789',
          assetName: `Asset ${i + 1}`,
        }),
      );

      const mockResponse = {
        noResults: false,
        result: mockWorkOrders,
        nextCursor: 10,
      };

      vi.mocked(getWorkOrders).mockResolvedValue(mockResponse);

      const caller = workOrderRouter.createCaller(mockContext);
      const result = await caller.search({ limit: 10 });

      expect(result).toEqual(mockResponse);
      expect(result.nextCursor).toBe(10);
    });
  });

  describe('createFromCapa procedure', () => {
    it('should successfully create work order from CAPA', async () => {
      vi.mocked(createWorkOrderFromCapa).mockResolvedValue(true);

      const caller = workOrderRouter.createCaller(mockContext);
      const result = await caller.createFromCapa(validCapaInput);

      expect(createWorkOrderFromCapa).toHaveBeenCalledWith(validCapaInput, mockContext.req.headers);
      expect(result).toBe(true);
    });

    it('should handle service errors', async () => {
      vi.mocked(createWorkOrderFromCapa).mockRejectedValue(new Error('Work order creation failed'));

      const caller = workOrderRouter.createCaller(mockContext);

      await expect(caller.createFromCapa(validCapaInput)).rejects.toThrow('Work order creation failed');
    });

    it('should handle service returning false', async () => {
      vi.mocked(createWorkOrderFromCapa).mockResolvedValue(false);

      const caller = workOrderRouter.createCaller(mockContext);
      const result = await caller.createFromCapa(validCapaInput);

      expect(result).toBe(false);
    });

    it('should validate required fields', async () => {
      const caller = workOrderRouter.createCaller(mockContext);

      await expect(caller.createFromCapa({ ...validCapaInput, id: '' })).rejects.toThrow();
      await expect(caller.createFromCapa({ ...validCapaInput, title: undefined as any })).rejects.toThrow();
      await expect(caller.createFromCapa({ ...validCapaInput, priority: 'invalid' as any })).rejects.toThrow();
    });

    it('should throw UNAUTHORIZED when user is not authenticated', async () => {
      await expect(workOrderRouter.createCaller(nullUserContext).createFromCapa(validCapaInput)).rejects.toThrow(
        TRPCError,
      );
    });
  });

  describe('getByCapa procedure', () => {
    it('should get work orders by CAPA ID with cursor pagination', async () => {
      const mockWorkOrders: WorkOrder[] = [
        {
          id: 'wo-1',
          workOrderNumber: 'WO-001',
          title: 'Work Order 1',
          currentStatus: 'open',
          priority: 'medium',
          dueDate: '2024-12-31T23:59:59.999Z',
          assignedTo: 'user-123',
          locationId: 'loc-456',
          assetId: 'asset-789',
          assetName: 'Asset 1',
        },
        {
          id: 'wo-2',
          workOrderNumber: 'WO-002',
          title: 'Work Order 2',
          currentStatus: 'inProgress',
          priority: 'high',
          dueDate: '2024-12-25T12:00:00.000Z',
          assignedTo: 'user-456',
          locationId: 'loc-789',
          assetId: 'asset-123',
          assetName: 'Asset 2',
        },
      ];

      vi.mocked(searchWorkOrdersByCapaId).mockResolvedValue(mockWorkOrders as any);

      const caller = workOrderRouter.createCaller(mockContext);
      const result = await caller.getByCapa({
        capaId: ['capa-123'],
        cursor: 0,
        limit: 10,
      });

      expect(result).toEqual({
        noResults: false,
        result: mockWorkOrders,
        nextCursor: undefined,
      });

      expect(searchWorkOrdersByCapaId).toHaveBeenCalledWith(
        {
          capaId: ['capa-123'],
          limit: 11, // limit + 1 to check for next page
          offset: 0,
          sort: 'createdAt DESC',
        },
        mockContext.req.headers,
      );
    });

    it('should handle empty search results', async () => {
      vi.mocked(searchWorkOrdersByCapaId).mockResolvedValue([]);

      const caller = workOrderRouter.createCaller(mockContext);
      const result = await caller.getByCapa({
        capaId: ['nonexistent'],
        cursor: 0,
        limit: 10,
      });

      expect(result).toEqual({
        noResults: true,
        result: [],
        nextCursor: undefined,
      });
    });

    it('should throw UNAUTHORIZED when user is not authenticated', async () => {
      await expect(
        workOrderRouter.createCaller(nullUserContext).getByCapa({
          capaId: ['capa-123'],
          cursor: 0,
          limit: 10,
        }),
      ).rejects.toThrow(TRPCError);
    });
  });
});
