import { createImageVariants } from '@server/services/image.service';
import { CreateImageVariantsSchema, CreateImageVariantsPublicSchema } from '@shared/types/files.types';
import { privateProcedure, publicProcedure, trpc } from '../trpc';

export const imageRouter = trpc.router({
  createImageVariants: privateProcedure.input(CreateImageVariantsSchema).mutation(async ({ input, ctx }) => {
    return createImageVariants(
      {
        fileData: input.fileData,
        fileName: input.fileName,
        entityType: input.entityType,
        mimeType: input.mimeType,
        entityId: input.entityId,
      },
      ctx.user,
    );
  }),
  createImageVariantsPublic: publicProcedure.input(CreateImageVariantsPublicSchema).mutation(async ({ input }) => {
    return createImageVariants(
      {
        fileData: input.fileData,
        fileName: input.fileName,
        entityType: input.entityType,
        mimeType: input.mimeType,
        entityId: input.entityId,
      },
      { upkeepCompanyId: input.upkeepCompanyId },
    );
  }),
});
