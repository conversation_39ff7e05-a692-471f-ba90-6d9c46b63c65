import { QUEUE_JOB_NAMES } from '@server/queue/job-names';
import { addJobToQueue } from '@server/queue/queue-utils';
import { getAssets, searchAssetsPublic } from '@server/services/asset.service';
import {
  createJha,
  getJhaByInstanceId,
  getJhaVers<PERSON>,
  listJhas,
  toggleArchiveJha,
  update<PERSON>ha,
  updateJhaStatus,
} from '@server/services/jha.service';
import { getLocationById, searchLocationsPublic } from '@server/services/location.service';
import { getUserPublic, getUsersPublic } from '@server/services/user.service';
import { privateProcedure, trpc } from '@server/trpc/trpc';
import { getIpAddress } from '@server/utils/get-ip-address';
import { approvalStatusEnum } from '@shared/schema';
import { Asset, Location } from '@shared/types/assets.types';
import {
  CreateJhaFormSchema,
  GetJhaByInstanceIdSchema,
  ListJhaSchema,
  UpdateJhaFormSchema,
  UpdateJhaStatusSchema,
} from '@shared/types/jha.types';
import { UuidSchema } from '@shared/types/schema.types';
import { UserPublic } from '@shared/types/users.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { TRPCError } from '@trpc/server';

export const jhaRouter = trpc.router({
  create: privateProcedure
    .hasPermission(MODULES.EHS_JHA, ALLOWED_ACTIONS.CREATE)
    .input(CreateJhaFormSchema)
    .mutation(async ({ input, ctx }) => {
      const { isAiGenerated, disclaimerAccepted, ...jhaData } = input;

      // Validate that disclaimer is accepted when content is AI-generated
      if (isAiGenerated && !disclaimerAccepted) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'AI disclaimer must be accepted for AI-generated content',
        });
      }

      const ipAddress = getIpAddress(ctx.req);
      const jha = await createJha(jhaData, ctx.user, ipAddress, disclaimerAccepted);
      if (!jha) {
        throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: 'Failed to create JHA' });
      }

      return jha;
    }),
  update: privateProcedure
    .hasPermission(MODULES.EHS_JHA, ALLOWED_ACTIONS.EDIT)
    .input(UpdateJhaFormSchema)
    .mutation(async ({ input, ctx }) => {
      const updatedJha = await updateJha(input, ctx.user);
      if (!updatedJha) {
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Failed to update JHA' });
      }
      return updatedJha;
    }),
  updateStatus: privateProcedure
    .hasPermission(MODULES.EHS_JHA, ALLOWED_ACTIONS.EDIT)
    .input(UpdateJhaStatusSchema)
    .mutation(async ({ input, ctx }) => {
      const result = await updateJhaStatus(input, ctx.user);

      if (!result) {
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Failed to update JHA status' });
      }

      const notificationMap = {
        [approvalStatusEnum.enumValues[2]]: {
          jobName: QUEUE_JOB_NAMES.JHA_APPROVED_NOTIFICATION,
          payload: {
            updatedJha: result,
            user: ctx.user,
            headers: ctx.req.headers,
          },
        },
        [approvalStatusEnum.enumValues[0]]: {
          jobName: QUEUE_JOB_NAMES.JHA_REJECTED_NOTIFICATION,
          payload: {
            updatedJha: result,
            user: ctx.user,
            headers: ctx.req.headers,
            rejectionReason: input.rejectionReason,
          },
        },
        [approvalStatusEnum.enumValues[1]]: {
          jobName: QUEUE_JOB_NAMES.JHA_REVISED_NOTIFICATION,
          payload: {
            updatedJha: result,
            user: ctx.user,
            headers: ctx.req.headers,
          },
        },
      };

      if (notificationMap[input.status]) {
        addJobToQueue(notificationMap[input.status].jobName, notificationMap[input.status].payload);
      }

      return result;
    }),
  getVersions: privateProcedure
    .hasPermission(MODULES.EHS_JHA, ALLOWED_ACTIONS.VIEW)
    .input(UuidSchema)
    .query(async ({ input, ctx }) => {
      const versions = await getJhaVersions(input.id, ctx.user);

      const allUsers = Array.from(new Set(versions.map((version) => version.createdBy)));

      const users = await getUsersPublic({
        upkeepCompanyId: ctx.user.upkeepCompanyId,
        objectId: allUsers,
      });

      const userMap = users.result.reduce(
        (acc, user) => {
          acc[user.id] = user;
          return acc;
        },
        {} as Record<string, UserPublic>,
      );

      return versions.map((version) => ({
        ...version,
        createdBy: userMap[version.createdBy],
      }));
    }),
  getByInstanceId: privateProcedure
    .hasPermission(MODULES.EHS_JHA, ALLOWED_ACTIONS.VIEW)
    .input(GetJhaByInstanceIdSchema)
    .query(async ({ input, ctx }) => {
      const jha = await getJhaByInstanceId(input, ctx.user, ctx.needPartialCheck);

      if (!jha) {
        throw new TRPCError({ code: 'NOT_FOUND', message: 'JHA not found' });
      }

      // fetch related users information
      const [owner, approver, reviewer, location, assets] = await Promise.all([
        getUserPublic({ id: jha.ownerId, upkeepCompanyId: ctx.user.upkeepCompanyId }),
        getUserPublic({ id: jha.approverId, upkeepCompanyId: ctx.user.upkeepCompanyId }),
        getUserPublic({ id: jha.createdBy, upkeepCompanyId: ctx.user.upkeepCompanyId }),
        jha.locationId ? getLocationById(jha.locationId, ctx.req.headers) : undefined,
        jha.assetIds && jha.assetIds.length > 0
          ? getAssets(
              {
                objectId: jha.assetIds,
                cursor: 0,
                limit: 100,
              },
              ctx.req.headers,
            )
          : undefined,
      ]);

      return {
        ...jha,
        location,
        assets: assets?.result || [],
        owner,
        approver,
        reviewer,
      };
    }),
  getByInstanceIdForEdit: privateProcedure
    .hasPermission(MODULES.EHS_JHA, ALLOWED_ACTIONS.VIEW)
    .input(GetJhaByInstanceIdSchema)
    .query(async ({ input, ctx }) => {
      const jha = await getJhaByInstanceId({ id: input.id }, ctx.user, ctx.needPartialCheck);

      if (!jha) {
        throw new TRPCError({ code: 'NOT_FOUND', message: 'JHA not found' });
      }

      return jha;
    }),
  minimalList: privateProcedure
    .hasPermission(MODULES.EHS_JHA, ALLOWED_ACTIONS.VIEW)
    .input(
      ListJhaSchema.default({
        cursor: 0,
        limit: 10,
      }),
    )
    .query(async ({ input, ctx }) => {
      const paginatedJhas = await listJhas(input, ctx.user, ctx.needPartialCheck);

      // Only fetch locations if includeLocation is true and there are location IDs to search for
      const locationIds = Array.from(
        new Set(paginatedJhas.result.map((jha) => jha.locationId).filter(Boolean)),
      ) as string[];

      const locations =
        input.includeLocation && locationIds.length > 0
          ? await searchLocationsPublic({
              upkeepCompanyId: ctx.user.upkeepCompanyId,
              objectId: locationIds,
              limit: 100,
            })
          : { result: [] };

      return {
        ...paginatedJhas,
        result: paginatedJhas.result.map((jha) => ({
          ...jha,
          location: input.includeLocation
            ? locations.result.find((location) => location.id === jha.locationId)
            : undefined,
        })),
      };
    }),
  list: privateProcedure
    .hasPermission(MODULES.EHS_JHA, ALLOWED_ACTIONS.VIEW)
    .input(
      ListJhaSchema.default({
        cursor: 0,
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc',
      }),
    )
    .query(async ({ ctx, input }) => {
      const paginatedJhas = await listJhas(input, ctx.user, ctx.needPartialCheck);

      // Extract unique owner IDs, location IDs, and asset IDs for batch fetching
      const { ownerIds, locationIds, assetIds } = paginatedJhas.result.reduce(
        (acc, jha) => {
          if (jha.ownerId) acc.ownerIds.add(jha.ownerId);
          if (jha.locationId) acc.locationIds.add(jha.locationId);
          jha.assetIds?.forEach((id) => acc.assetIds.add(id));
          return acc;
        },
        {
          ownerIds: new Set<string>(),
          locationIds: new Set<string>(),
          assetIds: new Set<string>(),
        },
      );

      const ownerIdsArray = Array.from(ownerIds);
      const locationIdsArray = Array.from(locationIds);
      const assetIdsArray = Array.from(assetIds);

      // Batch fetch related data
      const [ownersResponse, locationsResponse, assetsResponse] = await Promise.all([
        ownerIdsArray.length > 0
          ? getUsersPublic({
              upkeepCompanyId: ctx.user.upkeepCompanyId,
              objectId: ownerIdsArray,
              limit: 100,
            })
          : { result: [] },
        locationIdsArray.length > 0
          ? searchLocationsPublic({
              upkeepCompanyId: ctx.user.upkeepCompanyId,
              objectId: locationIdsArray,
              limit: 100,
            })
          : { result: [] },
        assetIdsArray.length > 0
          ? searchAssetsPublic({
              upkeepCompanyId: ctx.user.upkeepCompanyId,
              objectId: assetIdsArray,
              limit: 100,
            })
          : { result: [] },
      ]);

      // Create lookup maps for efficient mapping
      const ownersMap = ownersResponse.result.reduce(
        (acc, owner) => {
          acc[owner.id] = owner;
          return acc;
        },
        {} as Record<string, (typeof ownersResponse.result)[0]>,
      );

      const locationsMap = locationsResponse.result.reduce(
        (acc, location) => {
          acc[location.id] = location;
          return acc;
        },
        {} as Record<string, Location>,
      );

      const assetsMap = assetsResponse.result.reduce(
        (acc, asset) => {
          acc[asset.id] = asset;
          return acc;
        },
        {} as Record<string, Asset>,
      );

      // Map JHAs with related data
      const jhasWithRelatedData = paginatedJhas.result.map((jha) => {
        const owner = ownersMap[jha.ownerId];
        const location = jha.locationId ? locationsMap[jha.locationId] : undefined;
        const assets = jha.assetIds?.map((id) => assetsMap[id]).filter(Boolean) || [];

        return {
          ...jha,
          owner,
          location,
          assets,
        };
      });

      return {
        ...paginatedJhas,
        result: jhasWithRelatedData,
      };
    }),
  toggleArchive: privateProcedure
    .hasPermission(MODULES.EHS_JHA, ALLOWED_ACTIONS.EDIT)
    .input(UuidSchema)
    .mutation(async ({ input, ctx }) => {
      const result = await toggleArchiveJha(input, ctx.user);
      if (!result) {
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Failed to archive/unarchive JHA' });
      }
      return result;
    }),
});
