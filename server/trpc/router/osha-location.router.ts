import { privateProcedure, trpc } from '@server/trpc/trpc';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import {
  CreateOshaLocationSchema,
  ArchiveOshaLocationSchema,
  ListOshaLocationsSchema,
  ExportOshaLocationsSchema,
} from '@shared/types/settings.types';
import {
  createOshaLocation,
  toggleArchiveOshaLocation,
  listOshaLocations,
  exportOshaLocations,
} from '@server/services/osha-location.service';
import { TRPCError } from '@trpc/server';
import { getUsersPublic } from '@server/services/user.service';

export const oshaLocationRouter = trpc.router({
  create: privateProcedure
    .hasPermission(MODULES.OSHA_LOCATIONS, ALLOWED_ACTIONS.CREATE)
    .input(CreateOshaLocationSchema)
    .mutation(async ({ input, ctx }) => {
      const oshaLocation = await createOshaLocation(input, ctx.user);
      if (!oshaLocation) {
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Failed to create OSHA location' });
      }
      return oshaLocation;
    }),
  list: privateProcedure
    .hasPermission(MODULES.OSHA_LOCATIONS, ALLOWED_ACTIONS.VIEW)
    .input(ListOshaLocationsSchema)
    .query(async ({ input, ctx }) => {
      const paginatedOshaLocations = await listOshaLocations(input, ctx.user);

      const userIds = Array.from(
        new Set(paginatedOshaLocations.result.map((gl) => gl.createdBy).filter(Boolean)),
      ) as string[];

      const users = await getUsersPublic({ upkeepCompanyId: ctx.user.upkeepCompanyId, objectId: userIds });

      console.log('users', users);

      const userMap = users.result?.reduce(
        (acc, user) => {
          acc[user.id] = {
            id: user.id,
            fullName: user.fullName ?? 'Unknown',
            username: user.username ?? 'Unknown',
          };

          return acc;
        },
        {} as Record<string, { id: string; fullName: string; username: string }>,
      );

      return {
        ...paginatedOshaLocations,
        result: paginatedOshaLocations.result.map((gl) => ({
          ...gl,
          createdBy: userMap?.[gl.createdBy],
        })),
      };
    }),

  export: privateProcedure
    .hasPermission(MODULES.OSHA_LOCATIONS, ALLOWED_ACTIONS.EXPORT)
    .input(ExportOshaLocationsSchema)
    .mutation(async ({ input, ctx }) => {
      const oshaLocations = await exportOshaLocations(input, ctx.user);

      const userIds = Array.from(new Set(oshaLocations.map((gl) => gl.createdBy).filter(Boolean))) as string[];

      const users = await getUsersPublic({ upkeepCompanyId: ctx.user.upkeepCompanyId, objectId: userIds });

      const userMap = users.result?.reduce(
        (acc, user) => {
          acc[user.id] = {
            id: user.id,
            fullName: user.fullName ?? 'Unknown',
            username: user.username ?? 'Unknown',
            email: user.email ?? '',
          };

          return acc;
        },
        {} as Record<string, { id: string; fullName: string; username: string; email: string }>,
      );

      return oshaLocations.map((gl) => ({
        ...gl,
        createdByUser: userMap?.[gl.createdBy],
      }));
    }),

  minimalList: privateProcedure
    .hasPermission(MODULES.OSHA_LOCATIONS, ALLOWED_ACTIONS.VIEW)
    .input(ListOshaLocationsSchema)
    .query(async ({ input, ctx }) => {
      return await listOshaLocations(input, ctx.user);
    }),

  toggleArchive: privateProcedure
    .hasPermission(MODULES.OSHA_LOCATIONS, ALLOWED_ACTIONS.EDIT)
    .input(ArchiveOshaLocationSchema)
    .mutation(async ({ input, ctx }) => {
      const result = await toggleArchiveOshaLocation(input, ctx.user);
      if (!result) {
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Failed to delete OSHA location' });
      }
      return result;
    }),
});
