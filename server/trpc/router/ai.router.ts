import { z } from 'zod';

import { AnalyzeDocumentSchema, AnalyzeTextSchema } from '@shared/types/ai.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { TRPCError } from '@trpc/server';
import {
  analyzeCapaTranscript,
  analyzeEventTranscript,
  analyzeJhaDocument,
  analyzeJhaTranscript,
  analyzeSopDocument,
  analyzeSopTranscript,
  transcribeAudio,
} from 'server/services/ai.service';
import { privateProcedure, publicProcedure, trpc } from '../trpc';

export const aiRouter = trpc.router({
  transcribePublic: publicProcedure
    .input(
      z.object({
        audioBase64: z.string().min(1),
      }),
    )
    .mutation(async ({ input }) => {
      if (!input.audioBase64) {
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Audio is required' });
      }
      try {
        const audioBuffer = Buffer.from(input.audioBase64, 'base64');
        return await transcribeAudio(audioBuffer);
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to process audio',
          cause: error,
        });
      }
    }),
  analyzePublic: publicProcedure.input(AnalyzeTextSchema).mutation(async ({ input }) => {
    const { text, timezone } = input;

    return await analyzeEventTranscript(text, timezone);
  }),
  transcribe: privateProcedure
    .hasPermission(MODULES.AI_ASSISTANT, ALLOWED_ACTIONS.CREATE)
    .input(
      z.object({
        audioBase64: z.string().min(1),
      }),
    )
    // {ctx, input} both available here, ctx skipped since we're not using it
    .mutation(async ({ input }) => {
      if (!input.audioBase64) {
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Audio is required' });
      }
      try {
        const audioBuffer = Buffer.from(input.audioBase64, 'base64');
        return await transcribeAudio(audioBuffer);
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to process audio',
          cause: error,
        });
      }
    }),

  analyzeEvent: privateProcedure
    .hasPermission(MODULES.AI_ASSISTANT, ALLOWED_ACTIONS.CREATE)
    .input(AnalyzeTextSchema)
    .mutation(async ({ input }) => {
      return await analyzeEventTranscript(input.text, input.timezone);
    }),

  analyzeCapa: privateProcedure
    .hasPermission(MODULES.AI_ASSISTANT, ALLOWED_ACTIONS.CREATE)
    .input(AnalyzeTextSchema)
    .mutation(async ({ input }) => {
      return await analyzeCapaTranscript(input.text, input.timezone);
    }),
  analyzeJha: privateProcedure
    .hasPermission(MODULES.AI_ASSISTANT, ALLOWED_ACTIONS.CREATE)
    .input(AnalyzeTextSchema)
    .mutation(async ({ input, ctx }) => {
      return await analyzeJhaTranscript(input.text, ctx.user);
    }),

  analyzeJhaDocument: privateProcedure
    .hasPermission(MODULES.AI_ASSISTANT, ALLOWED_ACTIONS.CREATE)
    .input(AnalyzeDocumentSchema)
    .mutation(async ({ input, ctx }) => {
      const documentBuffer = Buffer.from(input.documentBase64, 'base64');

      return await analyzeJhaDocument({
        document: documentBuffer,
        user: ctx.user,
      });
    }),

  analyzeSop: privateProcedure
    .hasPermission(MODULES.AI_ASSISTANT, ALLOWED_ACTIONS.CREATE)
    .input(AnalyzeTextSchema)
    .mutation(async ({ input, ctx }) => {
      return await analyzeSopTranscript({
        text: input.text,
        user: ctx.user,
      });
    }),

  analyzeSopDocument: privateProcedure
    .hasPermission(MODULES.AI_ASSISTANT, ALLOWED_ACTIONS.CREATE)
    .input(AnalyzeDocumentSchema)
    .mutation(async ({ input, ctx }) => {
      return await analyzeSopDocument({
        document: Buffer.from(input.documentBase64, 'base64'),
        user: ctx.user,
      });
    }),
});
