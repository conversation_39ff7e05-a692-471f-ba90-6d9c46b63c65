import { sendCapaOverdueNotification } from '@server/services/capa-notification.service';
import { getOverdueCapas } from '@server/services/capa.service';
import { sendJhaOverdueNotification } from '@server/services/jha-notification.service';
import { getOverdueJhas } from '@server/services/jha.service';
import { searchLocationsPublic } from '@server/services/location.service';
import { getUserPublic, getUsersPublic } from '@server/services/user.service';
import { logger } from '@server/utils/logger';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { USER_ACCOUNT_TYPES } from '@shared/user-permissions';
import { differenceInDays } from 'date-fns';
import { env } from 'env';

const locationLookup = new Map();

type OverdueCapa = Awaited<ReturnType<typeof getOverdueCapas>>[number];
type OverdueJha = Awaited<ReturnType<typeof getOverdueJhas>>[number];

const sendCapasNotifications = async (capas: OverdueCapa[]) => {
  for (const capa of capas) {
    try {
      // calculate days overdue
      let daysOverdue = 0;
      if (capa.dueDate) {
        daysOverdue = differenceInDays(new Date(), capa.dueDate);
      }

      // Get location from nested lookup map
      const location = capa.locationId ? locationLookup.get(capa.upkeepCompanyId)?.get(capa.locationId) : undefined;

      const { upkeepCompanyId } = capa;

      const [admins, owner] = await Promise.all([
        getUsersPublic({ upkeepCompanyId, userAccountType: USER_ACCOUNT_TYPES.ADMIN }),
        getUserPublic({ upkeepCompanyId, id: capa.ownerId }),
      ]);

      const toAdmins =
        admins?.result?.map((admin) => ({
          email: admin.email ?? '',
          fullName: admin.fullName ?? '',
          type: 'to' as const,
          id: admin.id,
        })) ?? [];

      if (!capa.ownerId || !owner) {
        logger.error('No owner found for CAPA overdue notification', { capaId: capa.id });
        throw new Error(`No owner found for CAPA ${capa.id} overdue notification`);
      }

      const toOwner = [
        {
          email: owner.email ?? '',
          fullName: owner.fullName ?? '',
          type: 'to' as const,
          id: owner.id,
        },
      ];

      // unique emails
      const toNotified = Array.from(new Set([...toAdmins, ...toOwner].map((item) => item)));
      const toNotifiedIds = toNotified.map((item) => item.id);

      if (toNotified.length === 0) {
        logger.warn('No recipients found for CAPA overdue notification', { capaId: capa.id });
        continue;
      }

      const notifications = [];

      if (toNotified.length > 0) {
        notifications.push(
          sendCapaOverdueNotification({
            capa: {
              ...capa,
              daysOverdue,
              owner,
              location,
              eventSlug: capa.eventSlug,
              eventTitle: capa.eventTitle,
              capaUrl: `${env.EHS_URL}${ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id)}`,
            },
            toUsers: toNotified,
            toUsersIds: toNotifiedIds,
          }),
        );
      }

      await Promise.all(notifications);

      logger.info('Sent overdue notification', { capaId: capa.id, recipients: toNotified.map((r) => r.email) });
    } catch (error) {
      logger.error('Failed to send overdue notification', { error, capaId: capa.id });
    }
  }
};

const sendJhaNotifications = async (jhas: OverdueJha[]) => {
  for (const jha of jhas) {
    try {
      if (!jha.reviewDate) {
        logger.warn('JHA has null reviewDate, skipping notification', { jhaId: jha.id });
        continue;
      }

      const location = jha.locationId ? locationLookup.get(jha.upkeepCompanyId)?.get(jha.locationId) : undefined;

      const { upkeepCompanyId } = jha;

      const [admins, owner, approver, creator] = await Promise.all([
        getUsersPublic({ upkeepCompanyId, userAccountType: USER_ACCOUNT_TYPES.ADMIN }),
        getUserPublic({ upkeepCompanyId, id: jha.ownerId }),
        getUserPublic({ upkeepCompanyId, id: jha.approverId }),
        getUserPublic({ upkeepCompanyId, id: jha.createdBy }),
      ]);

      const toAdmins =
        admins?.result?.map((admin) => ({
          email: admin.email ?? '',
          fullName: admin.fullName ?? '',
          type: 'to' as const,
          id: admin.id,
        })) ?? [];

      if (!jha.ownerId || !owner || !creator) {
        logger.error('No owner found for JHA overdue notification', { jhaId: jha.id });
        throw new Error(`No owner found for JHA ${jha.id} overdue notification`);
      }

      const toOwner = [
        {
          email: owner.email ?? '',
          fullName: owner.fullName ?? '',
          type: 'to' as const,
          id: owner.id,
        },
      ];

      const toApprover = approver
        ? [
            {
              email: approver.email ?? '',
              fullName: approver.fullName ?? '',
              type: 'to' as const,
              id: approver.id,
            },
          ]
        : [];

      const toCreator = [
        {
          email: creator.email ?? '',
          fullName: creator.fullName ?? '',
          type: 'to' as const,
          id: creator.id,
        },
      ];

      const allRecipients = [...toAdmins, ...toOwner, ...toApprover, ...toCreator];
      const toNotified = Array.from(new Map(allRecipients.map((item) => [item.email, item])).values()).filter(
        (item) => item.email && item.fullName,
      );
      const toNotifiedIds = toNotified.map((item) => item.id);

      if (toNotified.length === 0) {
        logger.warn('No recipients found for JHA overdue notification', { jhaId: jha.id });
        continue;
      }

      const notifications: Promise<unknown>[] = [];

      if (toNotified.length > 0) {
        notifications.push(
          sendJhaOverdueNotification({
            jha: {
              ...jha,
              reviewDate: jha.reviewDate,
              owner,
              location,
              jhaUrl: `${env.EHS_URL}${ROUTES.BUILD_JHA_DETAILS_PATH(jha.instanceId!)}`,
            },
            toUsers: toNotified,
            toUsersIds: toNotifiedIds,
          }),
        );
      }

      await Promise.all(notifications);

      logger.info('Sent JHA overdue notification', { jhaId: jha.id, recipients: toNotified.map((r) => r.email) });
    } catch (error) {
      logger.error('Failed to send JHA overdue notification', { error, jhaId: jha.id });
    }
  }
};

const run = async () => {
  logger.info('Running overdue notification job');

  // Fetch both CAPAs and JHAs in parallel for optimal performance
  const [capas, jhas] = await Promise.all([getOverdueCapas(), getOverdueJhas()]);

  // Create location maps for both CAPAs and JHAs
  const capaLocationMap = new Map<string, string[]>();
  const jhaLocationMap = new Map<string, string[]>();

  // Process CAPA locations
  for (const capa of capas) {
    if (!capaLocationMap.has(capa.upkeepCompanyId)) {
      capaLocationMap.set(capa.upkeepCompanyId, []);
    }

    if (capa.locationId) {
      const existingLocations = capaLocationMap.get(capa.upkeepCompanyId) || [];
      capaLocationMap.set(capa.upkeepCompanyId, [...new Set([...existingLocations, capa.locationId])]);
    }
  }

  // CAPA notifications are sent after locations are fetched to populate location from the lookup

  // Process JHA locations
  for (const jha of jhas) {
    if (!jhaLocationMap.has(jha.upkeepCompanyId)) {
      jhaLocationMap.set(jha.upkeepCompanyId, []);
    }

    if (jha.locationId) {
      const existingLocations = jhaLocationMap.get(jha.upkeepCompanyId) || [];
      jhaLocationMap.set(jha.upkeepCompanyId, [...new Set([...existingLocations, jha.locationId])]);
    }
  }

  // Combine all location IDs for efficient fetching
  const allLocationMap = new Map<string, string[]>();

  // Merge CAPA and JHA location maps
  for (const [companyId, locationIds] of capaLocationMap.entries()) {
    allLocationMap.set(companyId, [...(allLocationMap.get(companyId) || []), ...locationIds]);
  }

  for (const [companyId, locationIds] of jhaLocationMap.entries()) {
    const existing = allLocationMap.get(companyId) || [];
    allLocationMap.set(companyId, [...new Set([...existing, ...locationIds])]);
  }

  // Fetch all locations in parallel
  const locationPromises = Array.from(allLocationMap.entries())
    .filter(([_, locationIds]) => locationIds.length > 0)
    .map(([upkeepCompanyId, locationIds]) =>
      searchLocationsPublic({
        upkeepCompanyId,
        objectId: locationIds,
        search: '',
        limit: locationIds.length,
      }).then((locations) => ({ upkeepCompanyId, locations })),
    );

  const locationResults = await Promise.all(locationPromises);

  // Create a nested lookup map for quick access: upkeepCompanyId -> { locationId -> location }
  locationResults.forEach(({ upkeepCompanyId, locations }) => {
    const locationsByIdMap = new Map();
    locations.result.forEach((location) => {
      locationsByIdMap.set(location.id, location);
    });
    locationLookup.set(upkeepCompanyId, locationsByIdMap);
  });

  await Promise.all([sendCapasNotifications(capas), sendJhaNotifications(jhas)]);

  logger.info('Overdue notification job completed', {
    capaCount: capas.length,
    jhaCount: jhas.length,
  });
  process.exit(0);
};

run();
