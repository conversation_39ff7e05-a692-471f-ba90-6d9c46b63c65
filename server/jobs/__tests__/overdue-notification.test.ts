import { beforeEach, describe, expect, it, vi } from 'vitest';

import * as capaNotificationService from '@server/services/capa-notification.service';
import * as capaService from '@server/services/capa.service';
import * as jhaNotificationService from '@server/services/jha-notification.service';
import * as jhaService from '@server/services/jha.service';
import * as locationService from '@server/services/location.service';
import * as userService from '@server/services/user.service';
import { getUserPublic, getUsersPublic } from '@server/services/user.service';
import { capaTagsEnum, rootCauseEnum } from '@shared/schema';
import { USER_ACCOUNT_TYPES } from '@shared/user-permissions';
import { logger } from '../../utils/logger';

vi.mock('@server/services/capa.service', () => ({
  getOverdueCapas: vi.fn(),
}));
vi.mock('@server/services/capa-notification.service', () => ({
  sendCapaOverdueNotification: vi.fn(),
}));
vi.mock('@server/services/jha.service', () => ({
  getOverdueJhas: vi.fn(),
}));
vi.mock('@server/services/jha-notification.service', () => ({
  sendJhaOverdueNotification: vi.fn(),
}));
vi.mock('@server/services/location.service', () => ({
  searchLocationsPublic: vi.fn(),
}));
vi.mock('@server/services/user.service', () => ({
  getUsersPublic: vi.fn(),
  getUserPublic: vi.fn(),
}));
vi.mock('@server/utils/logger', () => ({
  logger: {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  },
}));

// Mock process.exit
const mockExit = vi.fn();
process.exit = mockExit as never;

describe('overdue-notification job', () => {
  const mockCapas = [
    {
      id: 'capa1',
      title: 'Test CAPA',
      dueDate: new Date(Date.now() - 86400000), // 1 day overdue
      status: 'open' as const,
      upkeepCompanyId: 'company1',
      locationId: 'loc1',
      ownerId: 'user1',
      slug: 'capa-1',
      rcaFindings: 'Root cause',
      type: 'corrective' as const,
      priority: 'high' as const,
      eventId: 'event1',
      eventSlug: 'event-1',
      eventTitle: 'Test Event',
      summary: 'Summary',
      assetId: 'asset1',
      rcaMethod: 'other' as const,
      rootCauses: [rootCauseEnum.enumValues[0]],
      otherRootCause: 'Other root cause',
      aiSuggestedAction: 'AI suggested action',
      aiConfidenceScore: 0.9,
      actionsToAddress: 'Actions to address',
      tags: [capaTagsEnum.enumValues[0], capaTagsEnum.enumValues[1]],
      privateToAdmins: true,
      teamMembersToNotify: ['user2', 'user3'],
      archivedAt: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
      createdBy: 'user1',
      triggerWorkOrder: true,
      actionsImplemented: 'Actions implemented',
      implementationDate: new Date(),
      closedAt: new Date(),
      closedBy: 'user1',
      closedReason: 'Closed reason',
      implementedBy: 'user1',
      verificationFindings: 'Verification findings',
      voePerformedBy: 'user1',
      voePerformedAt: new Date(),
      voeStatus: 'Completed',
      voeComments: 'Voe comments',
      voeDate: new Date(),
      effectivenessStatus: 'effective' as const,
    },
  ];

  const mockLocation = {
    noResults: false,
    result: [{ id: 'loc1', name: 'Test Location' }],
    nextCursor: undefined,
  };
  const mockAdmins = {
    noResults: false,
    result: [
      { id: 'admin1', username: 'admin1', firstName: 'Admin', email: '<EMAIL>', fullName: 'Admin User' },
    ],
    nextCursor: undefined,
  };
  const mockOwner = {
    id: 'owner1',
    username: 'owner1',
    firstName: 'Owner',
    email: '<EMAIL>',
    fullName: 'Owner User',
  };

  const mockApprover = {
    id: 'approver1',
    username: 'approver1',
    firstName: 'Approver',
    email: '<EMAIL>',
    fullName: 'Approver User',
  };

  const mockCreator = {
    id: 'creator1',
    username: 'creator1',
    firstName: 'Creator',
    email: '<EMAIL>',
    fullName: 'Creator User',
  };

  const mockJhas = [
    {
      id: 'jha1',
      instanceId: 'jha-instance-1',
      slug: 'test-jha-1',
      title: 'Test JHA',
      status: 'approved' as const,
      reviewDate: new Date(Date.now() - 86400000), // 1 day overdue
      upkeepCompanyId: 'company1',
      locationId: 'loc1',
      ownerId: 'owner1',
      approverId: 'approver1',
      createdBy: 'creator1',
      assetIds: ['asset1', 'asset2'],
      archived: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    vi.resetModules();
    mockExit.mockClear();

    // Set up default mocks
    vi.mocked(capaService.getOverdueCapas).mockResolvedValue([]);
    vi.mocked(jhaService.getOverdueJhas).mockResolvedValue([]);
    vi.mocked(getUsersPublic).mockResolvedValue(mockAdmins);

    // Set up getUserPublic to return different users based on ID
    vi.mocked(getUserPublic).mockImplementation(async ({ id }) => {
      switch (id) {
        case 'owner1':
        case 'user1':
          return mockOwner;
        case 'approver1':
          return mockApprover;
        case 'creator1':
          return mockCreator;
        default:
          return undefined;
      }
    });

    vi.mocked(locationService.searchLocationsPublic).mockResolvedValue(mockLocation);
    vi.mocked(capaNotificationService.sendCapaOverdueNotification).mockResolvedValue(undefined);
    vi.mocked(jhaNotificationService.sendJhaOverdueNotification).mockResolvedValue(undefined);
  });

  it('should send notifications for admins and owner', async () => {
    vi.mocked(capaService.getOverdueCapas).mockResolvedValue(mockCapas);
    vi.mocked(jhaService.getOverdueJhas).mockResolvedValue([]);
    vi.mocked(locationService.searchLocationsPublic).mockResolvedValue(mockLocation);
    vi.mocked(userService.getUsersPublic).mockResolvedValue(mockAdmins);
    vi.mocked(userService.getUserPublic).mockResolvedValue(mockOwner);
    vi.mocked(capaNotificationService.sendCapaOverdueNotification).mockResolvedValue(undefined);

    // Import and wait for execution
    await import('../overdue-notification');

    // Wait a bit for async operations to complete
    await new Promise((resolve) => setTimeout(resolve, 100));

    expect(capaNotificationService.sendCapaOverdueNotification).toHaveBeenCalledTimes(1);

    expect(userService.getUsersPublic).toHaveBeenCalledTimes(1);
    expect(userService.getUserPublic).toHaveBeenCalledTimes(1);

    expect(userService.getUsersPublic).toHaveBeenCalledWith({
      upkeepCompanyId: 'company1',
      userAccountType: USER_ACCOUNT_TYPES.ADMIN,
    });
    expect(userService.getUserPublic).toHaveBeenCalledWith({
      upkeepCompanyId: 'company1',
      id: 'user1',
    });

    expect(capaNotificationService.sendCapaOverdueNotification).toHaveBeenCalledWith({
      capa: {
        ...mockCapas[0],
        daysOverdue: 1,
        location: mockLocation.result[0],
        owner: mockOwner,
        capaUrl: expect.any(String),
      },
      toUsers: [
        { email: '<EMAIL>', fullName: 'Admin User', id: 'admin1', type: 'to' },
        { email: '<EMAIL>', fullName: 'Owner User', id: 'owner1', type: 'to' },
      ],
      toUsersIds: ['admin1', 'owner1'],
    });
  });

  it('should NOT send notification if there is no owner', async () => {
    vi.mocked(capaService.getOverdueCapas).mockResolvedValue(mockCapas);
    vi.mocked(jhaService.getOverdueJhas).mockResolvedValue([]);
    vi.mocked(locationService.searchLocationsPublic).mockResolvedValue(mockLocation);
    vi.mocked(userService.getUsersPublic).mockResolvedValue({ noResults: true, result: [], nextCursor: undefined }); // admins
    vi.mocked(userService.getUserPublic).mockResolvedValue(undefined);
    vi.mocked(capaNotificationService.sendCapaOverdueNotification).mockResolvedValue(undefined);

    await import('../overdue-notification');

    // Wait a bit for async operations to complete
    await new Promise((resolve) => setTimeout(resolve, 100));

    expect(capaService.getOverdueCapas).toHaveBeenCalledTimes(1);
    expect(jhaService.getOverdueJhas).toHaveBeenCalledTimes(1);
    expect(locationService.searchLocationsPublic).toHaveBeenCalledTimes(1);
    expect(userService.getUsersPublic).toHaveBeenCalledTimes(1);
    expect(userService.getUserPublic).toHaveBeenCalledTimes(1);

    expect(capaNotificationService.sendCapaOverdueNotification).not.toHaveBeenCalled();
    expect(logger.error).toHaveBeenCalledWith('No owner found for CAPA overdue notification', {
      capaId: mockCapas[0].id,
    });
  });

  it('should handle CAPA without ownerId (no notification sent)', async () => {
    const capaNoOwner = { ...mockCapas[0], ownerId: '', archivedAt: null };
    vi.mocked(capaService.getOverdueCapas).mockResolvedValue([capaNoOwner]);
    vi.mocked(jhaService.getOverdueJhas).mockResolvedValue([]);
    vi.mocked(locationService.searchLocationsPublic).mockResolvedValue(mockLocation);
    vi.mocked(userService.getUsersPublic).mockResolvedValue(mockAdmins);
    vi.mocked(userService.getUserPublic).mockResolvedValue(mockOwner);
    vi.mocked(capaNotificationService.sendCapaOverdueNotification).mockResolvedValue(undefined);

    await import('../overdue-notification');

    // Wait a bit for async operations to complete
    await new Promise((resolve) => setTimeout(resolve, 100));

    // Should call getUsersPublic for admins and getUserPublic for owner (even though ownerId is undefined)
    expect(userService.getUsersPublic).toHaveBeenCalledTimes(1);
    expect(userService.getUserPublic).toHaveBeenCalledTimes(1);

    // Should not send notification because ownerId is undefined
    expect(capaNotificationService.sendCapaOverdueNotification).not.toHaveBeenCalled();
    expect(logger.error).toHaveBeenCalledWith('No owner found for CAPA overdue notification', {
      capaId: capaNoOwner.id,
    });
  });

  it('should handle CAPA without locationId (location is undefined)', async () => {
    const capaNoLocation = { ...mockCapas[0], locationId: null, archivedAt: null };
    vi.mocked(capaService.getOverdueCapas).mockResolvedValue([capaNoLocation]);
    vi.mocked(jhaService.getOverdueJhas).mockResolvedValue([]);
    vi.mocked(locationService.searchLocationsPublic).mockResolvedValue({
      noResults: true,
      result: [],
      nextCursor: undefined,
    });
    vi.mocked(userService.getUsersPublic).mockResolvedValue(mockAdmins);
    vi.mocked(userService.getUserPublic).mockResolvedValue(mockOwner);
    vi.mocked(capaNotificationService.sendCapaOverdueNotification).mockResolvedValue(undefined);

    await import('../overdue-notification');

    // Wait a bit for async operations to complete
    await new Promise((resolve) => setTimeout(resolve, 100));

    // Should not call searchLocationsPublic because no locationId to search for
    expect(locationService.searchLocationsPublic).not.toHaveBeenCalled();
    expect(capaNotificationService.sendCapaOverdueNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        capa: expect.objectContaining({ location: undefined }),
        toUsersIds: expect.any(Array),
      }),
    );
  });

  it('should log error if sendCAPAOverdueNotification throws', async () => {
    vi.mocked(capaService.getOverdueCapas).mockResolvedValue(mockCapas);
    vi.mocked(jhaService.getOverdueJhas).mockResolvedValue([]);
    vi.mocked(locationService.searchLocationsPublic).mockResolvedValue(mockLocation);
    vi.mocked(userService.getUsersPublic).mockResolvedValue(mockAdmins);
    vi.mocked(userService.getUserPublic).mockResolvedValue(mockOwner);
    vi.mocked(capaNotificationService.sendCapaOverdueNotification).mockRejectedValue(new Error('Email error'));

    await import('../overdue-notification');

    // Wait a bit for async operations to complete
    await new Promise((resolve) => setTimeout(resolve, 100));

    expect(logger.error).toHaveBeenCalledWith(
      'Failed to send overdue notification',
      expect.objectContaining({
        error: expect.any(Error),
        capaId: mockCapas[0].id,
      }),
    );
  });

  // JHA Tests
  describe('JHA overdue notifications', () => {
    it('should send JHA notifications for admins, owner, approver, and creator', async () => {
      vi.mocked(capaService.getOverdueCapas).mockResolvedValue([]);
      vi.mocked(jhaService.getOverdueJhas).mockResolvedValue(mockJhas);
      vi.mocked(locationService.searchLocationsPublic).mockResolvedValue(mockLocation);
      vi.mocked(userService.getUsersPublic).mockResolvedValue(mockAdmins);
      vi.mocked(jhaNotificationService.sendJhaOverdueNotification).mockResolvedValue(undefined);

      await import('../overdue-notification');

      // Wait a bit for async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 100));

      expect(jhaNotificationService.sendJhaOverdueNotification).toHaveBeenCalledTimes(1);

      // Should call getUsersPublic once for admins, and getUserPublic 3 times for owner/approver/creator
      expect(userService.getUsersPublic).toHaveBeenCalledTimes(1);
      expect(userService.getUserPublic).toHaveBeenCalledTimes(3);

      expect(userService.getUsersPublic).toHaveBeenCalledWith({
        upkeepCompanyId: 'company1',
        userAccountType: USER_ACCOUNT_TYPES.ADMIN,
      });
      expect(userService.getUserPublic).toHaveBeenCalledWith({
        upkeepCompanyId: 'company1',
        id: 'owner1',
      });
      expect(userService.getUserPublic).toHaveBeenCalledWith({
        upkeepCompanyId: 'company1',
        id: 'approver1',
      });
      expect(userService.getUserPublic).toHaveBeenCalledWith({
        upkeepCompanyId: 'company1',
        id: 'creator1',
      });

      expect(jhaNotificationService.sendJhaOverdueNotification).toHaveBeenCalledWith({
        jha: {
          ...mockJhas[0],
          reviewDate: mockJhas[0].reviewDate,
          owner: mockOwner,
          location: mockLocation.result[0],
          jhaUrl: expect.any(String),
        },
        toUsers: expect.arrayContaining([
          { email: '<EMAIL>', fullName: 'Admin User', id: 'admin1', type: 'to' },
          { email: '<EMAIL>', fullName: 'Owner User', id: 'owner1', type: 'to' },
          { email: '<EMAIL>', fullName: 'Approver User', id: 'approver1', type: 'to' },
          { email: '<EMAIL>', fullName: 'Creator User', id: 'creator1', type: 'to' },
        ]),
        toUsersIds: expect.arrayContaining(['admin1', 'owner1', 'approver1', 'creator1']),
      });
    });

    it('should NOT send JHA notification if no owner found', async () => {
      vi.mocked(capaService.getOverdueCapas).mockResolvedValue([]);
      vi.mocked(jhaService.getOverdueJhas).mockResolvedValue(mockJhas);
      vi.mocked(locationService.searchLocationsPublic).mockResolvedValue(mockLocation);
      vi.mocked(userService.getUsersPublic).mockResolvedValue(mockAdmins);

      // Override getUserPublic to return undefined for owner
      vi.mocked(getUserPublic).mockImplementation(async ({ id }) => {
        switch (id) {
          case 'owner1':
            return undefined; // No owner found
          case 'approver1':
            return mockApprover;
          case 'creator1':
            return mockCreator;
          default:
            return undefined;
        }
      });

      vi.mocked(jhaNotificationService.sendJhaOverdueNotification).mockResolvedValue(undefined);

      await import('../overdue-notification');

      // Wait a bit for async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 100));

      expect(jhaService.getOverdueJhas).toHaveBeenCalledTimes(1);
      expect(jhaNotificationService.sendJhaOverdueNotification).not.toHaveBeenCalled();
      expect(logger.error).toHaveBeenCalledWith('No owner found for JHA overdue notification', {
        jhaId: mockJhas[0].id,
      });
    });

    it('should NOT send JHA notification if no creator found', async () => {
      vi.mocked(capaService.getOverdueCapas).mockResolvedValue([]);
      vi.mocked(jhaService.getOverdueJhas).mockResolvedValue(mockJhas);
      vi.mocked(locationService.searchLocationsPublic).mockResolvedValue(mockLocation);
      vi.mocked(userService.getUsersPublic).mockResolvedValue(mockAdmins);

      // Override getUserPublic to return undefined for creator
      vi.mocked(getUserPublic).mockImplementation(async ({ id }) => {
        switch (id) {
          case 'owner1':
            return mockOwner;
          case 'approver1':
            return mockApprover;
          case 'creator1':
            return undefined; // No creator found
          default:
            return undefined;
        }
      });

      vi.mocked(jhaNotificationService.sendJhaOverdueNotification).mockResolvedValue(undefined);

      await import('../overdue-notification');

      // Wait a bit for async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 100));

      expect(jhaService.getOverdueJhas).toHaveBeenCalledTimes(1);
      expect(jhaNotificationService.sendJhaOverdueNotification).not.toHaveBeenCalled();
      expect(logger.error).toHaveBeenCalledWith('No owner found for JHA overdue notification', {
        jhaId: mockJhas[0].id,
      });
    });

    it('should handle JHA without ownerId', async () => {
      const jhaNoOwner = { ...mockJhas[0], ownerId: '' };
      vi.mocked(capaService.getOverdueCapas).mockResolvedValue([]);
      vi.mocked(jhaService.getOverdueJhas).mockResolvedValue([jhaNoOwner]);
      vi.mocked(locationService.searchLocationsPublic).mockResolvedValue(mockLocation);
      vi.mocked(userService.getUsersPublic).mockResolvedValue(mockAdmins);
      vi.mocked(jhaNotificationService.sendJhaOverdueNotification).mockResolvedValue(undefined);

      await import('../overdue-notification');

      // Wait a bit for async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 100));

      expect(jhaNotificationService.sendJhaOverdueNotification).not.toHaveBeenCalled();
      expect(logger.error).toHaveBeenCalledWith('No owner found for JHA overdue notification', {
        jhaId: jhaNoOwner.id,
      });
    });

    it('should handle JHA without reviewDate', async () => {
      const jhaNoReviewDate = { ...mockJhas[0], reviewDate: null };
      vi.mocked(capaService.getOverdueCapas).mockResolvedValue([]);
      vi.mocked(jhaService.getOverdueJhas).mockResolvedValue([jhaNoReviewDate]);
      vi.mocked(locationService.searchLocationsPublic).mockResolvedValue(mockLocation);
      vi.mocked(userService.getUsersPublic).mockResolvedValue(mockAdmins);
      vi.mocked(jhaNotificationService.sendJhaOverdueNotification).mockResolvedValue(undefined);

      await import('../overdue-notification');

      // Wait a bit for async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 100));

      expect(jhaNotificationService.sendJhaOverdueNotification).not.toHaveBeenCalled();
      expect(logger.warn).toHaveBeenCalledWith('JHA has null reviewDate, skipping notification', {
        jhaId: jhaNoReviewDate.id,
      });
    });

    it('should handle JHA without locationId (location is undefined)', async () => {
      const jhaNoLocation = { ...mockJhas[0], locationId: null };
      vi.mocked(capaService.getOverdueCapas).mockResolvedValue([]);
      vi.mocked(jhaService.getOverdueJhas).mockResolvedValue([jhaNoLocation]);
      vi.mocked(locationService.searchLocationsPublic).mockResolvedValue({
        noResults: true,
        result: [],
        nextCursor: undefined,
      });
      vi.mocked(userService.getUsersPublic).mockResolvedValue(mockAdmins);
      vi.mocked(jhaNotificationService.sendJhaOverdueNotification).mockResolvedValue(undefined);

      await import('../overdue-notification');

      // Wait a bit for async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Should not call searchLocationsPublic because no locationId to search for
      expect(locationService.searchLocationsPublic).not.toHaveBeenCalled();
      expect(jhaNotificationService.sendJhaOverdueNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          jha: expect.objectContaining({ location: undefined }),
          toUsersIds: expect.any(Array),
        }),
      );
    });

    it('should handle JHA without approver (approver is optional)', async () => {
      const jhaNoApprover = { ...mockJhas[0], approverId: '' };
      vi.mocked(capaService.getOverdueCapas).mockResolvedValue([]);
      vi.mocked(jhaService.getOverdueJhas).mockResolvedValue([jhaNoApprover]);
      vi.mocked(locationService.searchLocationsPublic).mockResolvedValue(mockLocation);
      vi.mocked(userService.getUsersPublic).mockResolvedValue(mockAdmins);

      // Override getUserPublic to return undefined for approver
      vi.mocked(getUserPublic).mockImplementation(async ({ id }) => {
        switch (id) {
          case 'owner1':
            return mockOwner;
          case 'creator1':
            return mockCreator;
          default:
            return undefined; // No approver
        }
      });

      vi.mocked(jhaNotificationService.sendJhaOverdueNotification).mockResolvedValue(undefined);

      await import('../overdue-notification');

      // Wait a bit for async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 100));

      expect(jhaNotificationService.sendJhaOverdueNotification).toHaveBeenCalledTimes(1);
      expect(jhaNotificationService.sendJhaOverdueNotification).toHaveBeenCalledWith({
        jha: {
          ...jhaNoApprover,
          reviewDate: jhaNoApprover.reviewDate,
          owner: mockOwner,
          location: mockLocation.result[0],
          jhaUrl: expect.any(String),
        },
        toUsers: expect.arrayContaining([
          { email: '<EMAIL>', fullName: 'Admin User', id: 'admin1', type: 'to' },
          { email: '<EMAIL>', fullName: 'Owner User', id: 'owner1', type: 'to' },
          { email: '<EMAIL>', fullName: 'Creator User', id: 'creator1', type: 'to' },
        ]),
        toUsersIds: expect.arrayContaining(['admin1', 'owner1', 'creator1']),
      });
    });

    it('should log error if sendJhaOverdueNotification throws', async () => {
      vi.mocked(capaService.getOverdueCapas).mockResolvedValue([]);
      vi.mocked(jhaService.getOverdueJhas).mockResolvedValue(mockJhas);
      vi.mocked(locationService.searchLocationsPublic).mockResolvedValue(mockLocation);
      vi.mocked(userService.getUsersPublic).mockResolvedValue(mockAdmins);
      vi.mocked(jhaNotificationService.sendJhaOverdueNotification).mockRejectedValue(new Error('JHA Email error'));

      await import('../overdue-notification');

      // Wait a bit for async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 100));

      expect(logger.error).toHaveBeenCalledWith(
        'Failed to send JHA overdue notification',
        expect.objectContaining({
          error: expect.any(Error),
          jhaId: mockJhas[0].id,
        }),
      );
    });

    it('should deduplicate email recipients correctly', async () => {
      // Create a JHA where owner and creator have the same email
      const mockOwnerAsCreator = { ...mockOwner, id: 'creator1' };

      vi.mocked(capaService.getOverdueCapas).mockResolvedValue([]);
      vi.mocked(jhaService.getOverdueJhas).mockResolvedValue(mockJhas);
      vi.mocked(locationService.searchLocationsPublic).mockResolvedValue(mockLocation);
      vi.mocked(userService.getUsersPublic).mockResolvedValue(mockAdmins);

      // Override getUserPublic so owner and creator return same email
      vi.mocked(getUserPublic).mockImplementation(async ({ id }) => {
        switch (id) {
          case 'owner1':
            return mockOwner;
          case 'approver1':
            return mockApprover;
          case 'creator1':
            return mockOwnerAsCreator; // Same email as owner
          default:
            return undefined;
        }
      });

      vi.mocked(jhaNotificationService.sendJhaOverdueNotification).mockResolvedValue(undefined);

      await import('../overdue-notification');

      // Wait a bit for async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 100));

      expect(jhaNotificationService.sendJhaOverdueNotification).toHaveBeenCalledTimes(1);

      const call = vi.mocked(jhaNotificationService.sendJhaOverdueNotification).mock.calls[0][0];
      const emailCounts = new Map();
      call.toUsers.forEach((user) => {
        emailCounts.set(user.email, (emailCounts.get(user.email) || 0) + 1);
      });

      // Each email should appear only once
      emailCounts.forEach((count) => {
        expect(count).toBe(1);
      });
    });
  });
});
