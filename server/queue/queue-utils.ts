import { JobsOptions } from 'bullmq';
import { createQueue } from './create-queue';
import { QUEUE_JOB_NAMES } from './job-names';

// Queue instances cache to avoid creating multiple queues with the same name
const queueCache = new Map<string, ReturnType<typeof createQueue>>();

// Default job options per queue type
const DEFAULT_QUEUE_OPTIONS: Record<string, JobsOptions> = {
  [QUEUE_JOB_NAMES.UPDATE_CAPA_NOTIFICATION]: {
    attempts: 13, // Number of retry attempts (~5 hours total with exponential backoff)
    backoff: {
      type: 'exponential',
      delay: 2000, // Start with 2 seconds: 2s, 4s, 8s, 16s, 32s, 64s, 128s, 256s, 512s, 1024s, 2048s, 4096s, 8192s
    },
    removeOnFail: true, // Remove job after all retries fail
    removeOnComplete: true,
  },
  [QUEUE_JOB_NAMES.ASSIGNED_CAPA_NOTIFICATION]: {
    attempts: 13, // Number of retry attempts (~5 hours total with exponential backoff)
    backoff: {
      type: 'exponential',
      delay: 2000, // Start with 2 seconds
    },
    removeOnFail: true, // Remove job after all retries fail
    removeOnComplete: true,
  },
  [QUEUE_JOB_NAMES.COMMENT_MENTION_NOTIFICATION]: {
    attempts: 13, // Number of retry attempts (~5 hours total with exponential backoff)
    backoff: {
      type: 'exponential',
      delay: 2000, // Start with 2 seconds
    },
    removeOnFail: true, // Remove job after all retries fail
    removeOnComplete: true,
  },
  [QUEUE_JOB_NAMES.PUBLIC_EVENT_CREATE_NOTIFICATION]: {
    attempts: 13, // Number of retry attempts (~5 hours total with exponential backoff)
    backoff: {
      type: 'exponential',
      delay: 2000, // Start with 2 seconds
    },
    removeOnFail: true, // Remove job after all retries fail
    removeOnComplete: true,
  },
  [QUEUE_JOB_NAMES.EVENT_CREATE_NOTIFICATION]: {
    attempts: 13, // Number of retry attempts (~5 hours total with exponential backoff)
    backoff: {
      type: 'exponential',
      delay: 2000, // Start with 2 seconds
    },
    removeOnFail: true, // Remove job after all retries fail
    removeOnComplete: true,
  },
  [QUEUE_JOB_NAMES.EVENT_UPDATE_NOTIFICATION]: {
    attempts: 13, // Number of retry attempts (~5 hours total with exponential backoff)
    backoff: {
      type: 'exponential',
      delay: 2000, // Start with 2 seconds
    },
    removeOnFail: true, // Remove job after all retries fail
    removeOnComplete: true,
  },
  [QUEUE_JOB_NAMES.GENERATE_IMAGE]: {
    attempts: 13, // Number of retry attempts (~5 hours total with exponential backoff)
    backoff: {
      type: 'exponential',
      delay: 1000, // Start with 1 seconds
    },
    removeOnFail: true, // Remove job after all retries fail
    removeOnComplete: true,
  },

  [QUEUE_JOB_NAMES.JHA_REVISED_NOTIFICATION]: {
    attempts: 13, // Number of retry attempts (~5 hours total with exponential backoff)
    backoff: {
      type: 'exponential',
      delay: 2000, // Start with 2 seconds
    },
    removeOnFail: true, // Remove job after all retries fail
    removeOnComplete: true,
  },
  [QUEUE_JOB_NAMES.JHA_APPROVED_NOTIFICATION]: {
    attempts: 13, // Number of retry attempts (~5 hours total with exponential backoff)
    backoff: {
      type: 'exponential',
      delay: 2000, // Start with 2 seconds
    },
    removeOnFail: true, // Remove job after all retries fail
    removeOnComplete: true,
  },
  [QUEUE_JOB_NAMES.JHA_REJECTED_NOTIFICATION]: {
    attempts: 13, // Number of retry attempts (~5 hours total with exponential backoff)
    backoff: {
      type: 'exponential',
      delay: 2000, // Start with 2 seconds
    },
    removeOnFail: true, // Remove job after all retries fail
    removeOnComplete: true,
  },
  [QUEUE_JOB_NAMES.SEND_ALERT_NOTIFICATION]: {
    attempts: 13, // Number of retry attempts (~5 hours total with exponential backoff)
    backoff: {
      type: 'exponential',
      delay: 2000, // Start with 2 seconds
    },
  },
  [QUEUE_JOB_NAMES.SEND_EMAIL]: {
    attempts: 13, // Number of retry attempts (~5 hours total with exponential backoff)
    backoff: {
      type: 'exponential',
      delay: 2000, // Start with 2 seconds
    },
  },
  // Add other queue defaults here as needed
};

export const addJobToQueue = <T = Record<string, unknown>>(queueName: string, jobData: T, options?: JobsOptions) => {
  let queue = queueCache.get(queueName);

  if (!queue) {
    queue = createQueue(queueName);
    queueCache.set(queueName, queue);
  }

  // Merge default options with provided options (provided options override defaults)
  const defaultOptions = DEFAULT_QUEUE_OPTIONS[queueName] || {};
  const finalOptions = { ...defaultOptions, ...options };

  queue.add(queueName, jobData, finalOptions);
};
