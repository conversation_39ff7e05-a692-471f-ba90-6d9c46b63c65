/**
 * Queue Job Name Constants
 *
 * Centralizes all queue job names to prevent typos and provide better IDE support.
 * Used across queue workers and job dispatchers.
 */

import { env } from 'env';

const prefix = `ehs-${env.ENVIRONMENT_PREFIX}-`;

export const QUEUE_JOB_NAMES = {
  UPDATE_CAPA_NOTIFICATION: `${prefix}update-capa-notification`,
  ASSIGNED_CAPA_NOTIFICATION: `${prefix}assigned-capa-notification`,
  COMMENT_MENTION_NOTIFICATION: `${prefix}comment-mention-notification`,
  PUBLIC_EVENT_CREATE_NOTIFICATION: `${prefix}public-event-create-notification`,
  EVENT_CREATE_NOTIFICATION: `${prefix}event-create-notification`,
  EVENT_UPDATE_NOTIFICATION: `${prefix}event-update-notification`,
  GENERATE_IMAGE: `${prefix}generate-image`,
  JHA_REVISED_NOTIFICATION: `${prefix}jha-revised-notification`,
  <PERSON><PERSON>_APPROVED_NOTIFICATION: `${prefix}jha-approved-notification`,
  JHA_REJECTED_NOTIFICATION: `${prefix}jha-rejected-notification`,
  SEND_ALERT_NOTIFICATION: `${prefix}send-alert-notification`,
  SEND_EMAIL: `${prefix}send-email`,
  // Add other job names here as needed
} as const;

export type QueueJobName = (typeof QUEUE_JOB_NAMES)[keyof typeof QUEUE_JOB_NAMES];
