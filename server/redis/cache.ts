import { redisClient } from './client';
import { logger } from '@server/utils/logger';
import { env } from '../../env';

const DEFAULT_TTL = 60 * 60 * 24; // 24h

const DEFAULT_PREFIX = `ehs:${env.BUILD_TAG}:${env.COMMIT_SHA}:`;

export const getCache = async <T>(key: string): Promise<T | null> => {
  try {
    const data = await redisClient.get(DEFAULT_PREFIX + key);
    return data ? JSON.parse(data) : null;
  } catch (err) {
    logger.error(`[Redis] getCache error for key ${key}`, err);
    return null;
  }
};

export const setCache = async <T>(key: string, value: T, ttl = DEFAULT_TTL): Promise<void> => {
  try {
    await redisClient.set(DEFAULT_PREFIX + key, JSON.stringify(value), 'EX', ttl);
  } catch (err) {
    logger.error(`[Redis] setCache error for key ${key}`, err);
  }
};

export const deleteCache = async (key: string): Promise<void> => {
  try {
    await redisClient.del(DEFAULT_PREFIX + key);
  } catch (err) {
    logger.error(`[Redis] deleteCache error for key ${key}`, err);
  }
};

export const clearCacheByPattern = async (pattern: string): Promise<void> => {
  try {
    const keys = await redisClient.keys(DEFAULT_PREFIX + pattern);
    if (keys.length > 0) await redisClient.del(...keys);
  } catch (err) {
    logger.error(`[Redis] clearCacheByPattern error for pattern ${pattern}`, err);
  }
};
