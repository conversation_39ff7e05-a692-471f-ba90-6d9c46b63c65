import { db } from '@server/db';
import { controlMeasures } from '@shared/schema';
import { CONTROL_MEASURES } from '@shared/seeds/control-measures';

export const seed = async ({ upkeepCompanyId, userId }: { upkeepCompanyId: string; userId: string }) => {
  await db
    .insert(controlMeasures)
    .values(
      CONTROL_MEASURES.map((controlMeasure) => ({
        name: controlMeasure.name,
        type: controlMeasure.type,
        upkeepCompanyId,
        createdBy: userId,
      })),
    )
    .execute();
};
