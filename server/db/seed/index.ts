import { seed as seedHazards } from './hazards';
import { seed as seedControlMeasures } from './control-measures';

type SeedArgs = {
  upkeepCompanyId: string;
  userId: string;
};

const seedFunctions = [
  { name: 'hazards', fn: seedHazards },
  { name: 'control-measures', fn: seedControlMeasures },
];

const runAllSeeds = async (args: SeedArgs) => {
  console.log('Starting database seeding...');
  console.log(`Company ID: ${args.upkeepCompanyId}`);
  console.log(`User ID: ${args.userId}`);

  for (const { name, fn } of seedFunctions) {
    try {
      console.log(`Seeding ${name}...`);
      await fn(args);
      console.log(`✓ Successfully seeded ${name}`);
    } catch (error) {
      console.error(`✗ Failed to seed ${name}:`, error);
      throw error;
    }
  }

  console.log('All seeds completed successfully!');
};

// CLI execution support
if (import.meta.url === new URL(import.meta.url).href) {
  const args = process.argv.slice(2);
 
  // Parse named arguments
  const parseArgs = (args: string[]) => {
    const parsed: { upkeepCompanyId?: string; userId?: string } = {};
    
    for (let i = 0; i < args.length; i++) {
      const arg = args[i];
      const nextArg = args[i + 1];
      
      if (arg === '--upkeepCompanyId' && nextArg) {
        parsed.upkeepCompanyId = nextArg;
        i++; // Skip the next argument since we consumed it
      } else if (arg === '--userId' && nextArg) {
        parsed.userId = nextArg;
        i++; // Skip the next argument since we consumed it
      } else if (arg.startsWith('--upkeepCompanyId=')) {
        parsed.upkeepCompanyId = arg.split('=')[1];
      } else if (arg.startsWith('--userId=')) {
        parsed.userId = arg.split('=')[1];
      }
    }
    
    return parsed;
  };

  const { upkeepCompanyId, userId } = parseArgs(args);
  
  if (!upkeepCompanyId || !userId) {
    console.error('Usage: node index.ts --upkeepCompanyId <companyId> --userId <userId>');
    console.error('   or: node index.ts --upkeepCompanyId=<companyId> --userId=<userId>');
    console.error('');
    console.error('Required arguments:');
    console.error('  --upkeepCompanyId  The UpKeep company ID');
    console.error('  --userId          The user ID');
    process.exit(1);
  }
 
  runAllSeeds({ upkeepCompanyId, userId })
    .then(() => {
      console.log('Seeding process completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Seeding process failed:', error);
      process.exit(1);
    });
}
