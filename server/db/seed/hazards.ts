import { db } from '@server/db';
import { hazards } from '@shared/schema';
import { HAZARDS } from '@shared/seeds/hazards';

export const seed = async ({ upkeepCompanyId, userId }: { upkeepCompanyId: string; userId: string }) => {
  await db
    .insert(hazards)
    .values(
      HAZARDS.map((hazard) => ({
        name: hazard.name,
        type: hazard.type,
        upkeepCompanyId,
        createdBy: userId,
      })),
    )
    .execute();
};
