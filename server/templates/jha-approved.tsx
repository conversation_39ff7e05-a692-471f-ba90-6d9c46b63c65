import { formatDate } from '@shared/date-utils';
import { RouterOutputs } from '@shared/types/router.types';
import React from 'react';

export type JhaApprovedTemplateParams = RouterOutputs['jha']['updateStatus'] & {
  approvedAt: Date;
  location?: { id: string; name: string };
  owner?: Partial<{
    id: string;
    username: string;
    firstName: string;
    email?: string;
    lastName?: string;
    fullName?: string;
  }>;
  approver?: Partial<{
    id: string;
    username: string;
    firstName: string;
    email?: string;
    lastName?: string;
    fullName?: string;
  }>;
  jhaUrl: string;
};

export default function JhaApprovedTemplate({
  slug,
  title,
  version: _version,
  reviewDate,
  approvedAt,
  location,
  owner,
  approver,
  jhaUrl,
}: JhaApprovedTemplateParams) {
  const getStatusBadgeStyle = () => {
    return {
      display: 'inline-block',
      padding: '2px 10px',
      borderRadius: '8px',
      fontSize: '13px',
      fontWeight: 600,
      marginRight: '6px',
      backgroundColor: '#d1fae5',
      color: '#065f46',
    };
  };

  const styles = {
    body: {
      fontFamily: 'Arial, sans-serif',
      background: '#fff',
      color: '#222',
      margin: 0,
      padding: 0,
    },
    container: {
      maxWidth: '700px',
      margin: '32px auto',
      background: '#fff',
      borderRadius: '4px',
      border: '1px solid #eee',
    },
    content: {
      padding: '0 32px 20px',
    },
    header: {
      fontSize: '18px',
      fontWeight: 'bold',
      marginBottom: '20px',
      padding: '10px',
      background: '#d1fae5',
      color: '#065f46',
      borderBottom: '1px solid #10b981',
      borderRadius: '4px 4px 0 0',
    },
    sectionTitle: {
      fontSize: '18px',
      fontWeight: 600,
      marginTop: '32px',
      marginBottom: '20px',
    },
    infoTable: {
      width: '100%',
      borderCollapse: 'collapse' as const,
      marginBottom: '24px',
    },
    fieldBlock: {
      marginBottom: '18px',
    },
    label: {
      color: '#222',
      display: 'block',
      fontWeight: 600,
      textAlign: 'left' as const,
    },
    value: {
      display: 'block',
      fontWeight: 500,
    },
    badgeJha: {
      background: '#7c3aed',
      color: '#fff',
    },
    ctaBtn: {
      display: 'inline-block',
      background: '#7c3aed',
      color: '#fff',
      padding: '10px 28px',
      borderRadius: '6px',
      fontWeight: 600,
      textDecoration: 'none',
      margin: '16px 0 0',
    },
    footer: {
      color: '#888',
      fontSize: '13px',
      marginTop: '32px',
      textAlign: 'left' as const,
    },
    card: {
      border: '1px solid #eee',
      borderRadius: '10px',
      padding: '24px',
      marginBottom: '24px',
      background: '#fff',
    },
    ctaCenter: {
      textAlign: 'center' as const,
    },
    divider: {
      border: 'none',
      borderTop: '1px solid #e5e7eb',
      margin: '24px 0 28px 0',
    },
    reviewPrompt: {
      background: '#fef3c7',
      border: '1px solid #f59e0b',
      borderRadius: '8px',
      padding: '16px',
      marginBottom: '24px',
    },
    reviewPromptText: {
      color: '#92400e',
      fontWeight: 600,
      fontSize: '14px',
      margin: 0,
    },
  };

  return (
    <React.Fragment>
      <html lang="en">
        <head>
          <meta charSet="UTF-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1.0" />
          <title>JHA Approved</title>
          <style>{`
          @media only screen and (max-width: 600px) {
            .responsive-container {
              margin: 16px !important;
              border-radius: 0 !important;
            }
            
            .responsive-content {
              padding: 0 16px 20px !important;
            }
            
            .responsive-card {
              padding: 16px !important;
              margin-bottom: 16px !important;
            }
            
            .responsive-table {
              display: block !important;
              width: 100% !important;
            }
            
            .responsive-table tbody,
            .responsive-table tr,
            .responsive-table td {
              display: block !important;
              width: 100% !important;
            }
            
            .responsive-table td {
              display: block !important;
              width: 100% !important;
              padding-bottom: 24px !important;
              border: none !important;
              box-sizing: border-box !important;
            }
            
            .responsive-table td:last-child {
              padding-bottom: 0 !important;
            }
            
            .mobile-column {
              width: 100% !important;
              display: block !important;
              vertical-align: top !important;
            }
            
            .mobile-section-title {
              font-size: 16px !important;
              font-weight: 600 !important;
              margin: 0 0 20px 0 !important;
              padding-bottom: 8px !important;
              border-bottom: 1px solid #e5e7eb !important;
            }
            
            .mobile-divider {
              display: block !important;
              border: none !important;
              border-top: 1px solid #e5e7eb !important;
              margin: 24px 0 !important;
            }
          }
          
          /* Fallback for email clients */
          @media screen and (max-device-width: 600px) {
            .responsive-table td {
              display: block !important;
              width: 100% !important;
            }
            
            .mobile-column {
              width: 100% !important;
              display: block !important;
            }
          }
        `}</style>
        </head>
        <body style={styles.body}>
          <div style={styles.container} className="responsive-container">
            <div style={styles.header}>Job Hazard Analysis Approved</div>
            <div style={styles.content} className="responsive-content">
              <div style={{ fontSize: '20px', fontWeight: 'bold', marginBottom: '4px' }}>{title}</div>
              <div style={{ marginBottom: '24px' }}>
                Your Job Hazard Analysis has been approved and is now officially active. The JHA is ready for
                implementation and use.
              </div>

              <div style={styles.card} className="responsive-card">
                <table style={styles.infoTable} className="responsive-table" width="100%">
                  <tbody>
                    <tr>
                      <td style={{ width: '50%', verticalAlign: 'top' }} className="mobile-column">
                        <div
                          style={{ ...styles.sectionTitle, fontSize: '16px', marginTop: 0 }}
                          className="mobile-section-title"
                        >
                          JHA Information
                        </div>
                        <div style={styles.fieldBlock}>
                          <span style={styles.label}>JHA ID</span>
                          <span style={styles.value}>{slug || 'Pending'}</span>
                        </div>
                        <div style={styles.fieldBlock}>
                          <span style={styles.label}>Type</span>
                          <div style={styles.value}>
                            <span
                              style={{
                                display: 'inline-block',
                                padding: '2px 10px',
                                borderRadius: '8px',
                                fontSize: '13px',
                                fontWeight: 600,
                                marginRight: '6px',
                                ...styles.badgeJha,
                              }}
                            >
                              Job Hazard Analysis
                            </span>
                          </div>
                        </div>
                        <div style={styles.fieldBlock}>
                          <span style={styles.label}>Status</span>
                          <div style={styles.value}>
                            <span style={getStatusBadgeStyle()}>Approved</span>
                          </div>
                        </div>
                      </td>
                      <td style={{ width: '50%', verticalAlign: 'top' }} className="mobile-column">
                        <hr className="mobile-divider" style={{ display: 'none' }} />
                        <div
                          style={{ ...styles.sectionTitle, fontSize: '16px', marginTop: 0 }}
                          className="mobile-section-title"
                        >
                          Details
                        </div>
                        <div style={styles.fieldBlock}>
                          <span style={styles.label}>Approved Date</span>
                          <span style={styles.value}>{formatDate(approvedAt, true)}</span>
                        </div>
                        {approver && (
                          <div style={styles.fieldBlock}>
                            <span style={styles.label}>Approved By</span>
                            <span style={styles.value}>{approver.fullName || approver.email}</span>
                          </div>
                        )}
                        {reviewDate && (
                          <div style={styles.fieldBlock}>
                            <span style={styles.label}>Next Review Due</span>
                            <span style={styles.value}>{formatDate(reviewDate, true)}</span>
                          </div>
                        )}
                        <div style={styles.fieldBlock}>
                          <span style={styles.label}>Location</span>
                          <span style={styles.value}>{location?.name || 'Not specified'}</span>
                        </div>
                        <div style={styles.fieldBlock}>
                          <span style={styles.label}>Owner</span>
                          {owner?.fullName && owner?.email && (
                            <span style={styles.value}>
                              {owner?.fullName} ({owner?.email})
                            </span>
                          )}
                          {owner?.fullName && !owner?.email && <span style={styles.value}>{owner?.fullName}</span>}
                          {!owner?.fullName && owner?.email && <span style={styles.value}>{owner?.email}</span>}
                          {!owner?.fullName && !owner?.email && <span style={styles.value}>Not specified</span>}
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <div style={styles.ctaCenter}>
                <a href={jhaUrl} style={styles.ctaBtn}>
                  View JHA
                </a>
              </div>

              <div style={styles.footer}>
                This is an automated notification from your UpKeep EHS system. Please do not reply to this email.
              </div>
            </div>
          </div>
        </body>
      </html>
    </React.Fragment>
  );
}
