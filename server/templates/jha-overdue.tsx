import { EmailUser } from '@server/services/email.service';
import { formatDate } from '@shared/date-utils';
import React from 'react';

export type JhaOverdueTemplateParams = {
  id: string;
  upkeepCompanyId: string;
  slug: string | null;
  instanceId: string | null;
  title: string;
  ownerId: string;
  approverId: string | null;
  status: 'draft' | 'review' | 'approved';
  reviewDate: Date | null;
  locationId: string | null;
  createdBy: string;
  owner?: Partial<{
    id: string;
    username: string;
    firstName: string;
    email?: string;
    lastName?: string;
    fullName?: string;
  }>;
  location?: { id: string; name: string };
  jhaUrl: string;
};

export type JhaOverdueNotificationParams = {
  jha: JhaOverdueTemplateParams;
  toUsers: EmailUser[];
  toUsersIds: string[];
};

const JhaOverdueTemplate: React.FC<JhaOverdueTemplateParams> = ({
  slug,
  title,
  reviewDate,
  location,
  owner,
  jhaUrl,
}) => {
  const styles = {
    body: {
      fontFamily: 'Arial, sans-serif',
      background: '#fff',
      color: '#222',
      margin: 0,
      padding: 0,
    },
    container: {
      maxWidth: '700px',
      margin: '32px auto',
      background: '#fff',
      borderRadius: '4px',
      border: '1px solid #eee',
    },
    content: {
      padding: '0 32px 20px',
    },
    header: {
      fontSize: '18px',
      fontWeight: 'bold',
      marginBottom: '20px',
      padding: '10px',
      background: '#f3f4f6',
      borderBottom: '1px solid #e5e7eb',
      borderRadius: '4px 4px 0 0',
    },
    alertSection: {
      backgroundColor: '#fef3c7',
      border: '1px solid #f59e0b',
      borderRadius: '8px',
      padding: '16px',
      marginBottom: '24px',
    },
    alertText: {
      color: '#92400e',
      fontWeight: 600,
      fontSize: '14px',
      margin: 0,
    },
    sectionTitle: {
      fontSize: '18px',
      fontWeight: 600,
      marginTop: '32px',
      marginBottom: '20px',
    },
    fieldBlock: {
      marginBottom: '18px',
    },
    label: {
      color: '#222',
      display: 'block',
      fontWeight: 600,
      textAlign: 'left' as const,
    },
    value: {
      display: 'block',
      fontWeight: 500,
    },
    ctaBtn: {
      display: 'inline-block',
      background: '#7c3aed',
      color: '#fff',
      padding: '10px 28px',
      borderRadius: '6px',
      fontWeight: 600,
      textDecoration: 'none',
      margin: '16px 0 0',
    },
    footer: {
      color: '#888',
      fontSize: '13px',
      marginTop: '32px',
      textAlign: 'left' as const,
    },
    card: {
      border: '1px solid #eee',
      borderRadius: '10px',
      padding: '24px',
      marginBottom: '24px',
      background: '#fff',
    },
    ctaCenter: {
      textAlign: 'center' as const,
    },
    divider: {
      border: 'none',
      borderTop: '1px solid #e5e7eb',
      margin: '24px 0 28px 0',
    },
    statusPrompt: {
      backgroundColor: '#ede9fe',
      border: '1px solid #7c3aed',
      borderRadius: '8px',
      padding: '16px',
      marginTop: '24px',
    },
    statusText: {
      color: '#5b21b6',
      fontWeight: 600,
      fontSize: '14px',
      margin: 0,
    },
  };

  return (
    <React.Fragment>
      <html lang="en">
        <head>
          <meta charSet="UTF-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1.0" />
          <title>JHA Review Due</title>
        </head>
        <body style={styles.body}>
          <div style={styles.container}>
            <div style={styles.header}>Job Hazard Analysis Review Due</div>
            <div style={styles.content}>
              <div style={{ fontSize: '20px', fontWeight: 'bold', marginBottom: '4px' }}>{title}</div>
              <div style={{ marginBottom: '24px' }}>
                This Job Hazard Analysis is due for periodic review. Please review and update as necessary to maintain
                compliance and relevance.
              </div>

              <div style={styles.alertSection}>
                <p style={styles.alertText}>
                  Review required by {formatDate(reviewDate, true)} to maintain compliance.
                </p>
              </div>

              <div style={styles.card}>
                <div style={styles.sectionTitle}>JHA Information</div>
                <div style={styles.fieldBlock}>
                  <span style={styles.label}>JHA ID</span>
                  <span style={styles.value}>{slug || 'Pending'}</span>
                </div>
                <div style={styles.fieldBlock}>
                  <span style={styles.label}>Title</span>
                  <span style={styles.value}>{title}</span>
                </div>
                <div style={styles.fieldBlock}>
                  <span style={styles.label}>Review Due Date</span>
                  <span style={styles.value}>{formatDate(reviewDate, true)}</span>
                </div>

                <hr style={styles.divider} />

                <div style={styles.sectionTitle}>Assignment Details</div>
                {owner && (
                  <div style={styles.fieldBlock}>
                    <span style={styles.label}>Owner</span>
                    {owner.fullName && owner.email && (
                      <span style={styles.value}>
                        {owner.fullName} ({owner.email})
                      </span>
                    )}
                    {owner.fullName && !owner.email && <span style={styles.value}>{owner.fullName}</span>}
                    {!owner.fullName && owner.email && <span style={styles.value}>{owner.email}</span>}
                    {!owner.fullName && !owner.email && <span style={styles.value}>Not specified</span>}
                  </div>
                )}
                {location && (
                  <div style={styles.fieldBlock}>
                    <span style={styles.label}>Location</span>
                    <span style={styles.value}>{location.name}</span>
                  </div>
                )}

                <hr style={styles.divider} />

                <div style={styles.sectionTitle}>Next Steps</div>
                <div style={{ marginBottom: '16px' }}>
                  Please review this JHA and initiate a revision if any changes are needed to maintain compliance and
                  relevance. Click the link below to access the JHA and begin your review process.
                </div>
              </div>

              <div style={styles.ctaCenter}>
                <a href={jhaUrl} style={styles.ctaBtn}>
                  Review JHA
                </a>
              </div>

              <div style={styles.statusPrompt}>
                <p style={styles.statusText}>Review required to maintain compliance and relevance</p>
              </div>

              <div style={styles.footer}>
                This is an automated notification from your UpKeep EHS system. Please do not reply to this email.
              </div>
            </div>
          </div>
        </body>
      </html>
    </React.Fragment>
  );
};

export default JhaOverdueTemplate;
