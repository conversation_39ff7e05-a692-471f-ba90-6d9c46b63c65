import { severityEnum, statusEnum } from '@shared/schema';
import { SEVERITY_STYLES, STATUS_STYLES, StatusConfig } from '@shared/types/schema.types';

export const getStatusStyle = (status: (typeof statusEnum.enumValues)[number]): StatusConfig => {
  return STATUS_STYLES[status] || STATUS_STYLES[statusEnum.enumValues[0]];
};

export const getStatusColors = (
  status: (typeof statusEnum.enumValues)[number],
): { background: string; color: string } => {
  const config = getStatusStyle(status);
  return {
    background: config.backgroundColor,
    color: config.color,
  };
};

export const getStatusBadgeStyle = (
  status: (typeof statusEnum.enumValues)[number],
  styles: Record<string, Record<string, string | number>>,
) => {
  const colors = getStatusColors(status);
  return { ...styles.badge, ...colors };
};

export const getSeverityStyle = (severity: (typeof severityEnum.enumValues)[number]): StatusConfig => {
  return SEVERITY_STYLES[severity] || SEVERITY_STYLES[severityEnum.enumValues[2]];
};

export const getSeverityColors = (
  severity: (typeof severityEnum.enumValues)[number],
): { background: string; color: string } => {
  const config = getSeverityStyle(severity);
  return {
    background: config.backgroundColor,
    color: config.color,
  };
};
