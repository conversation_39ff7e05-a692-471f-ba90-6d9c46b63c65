import { Worker, Job } from 'bullmq';
import { bullMqRedisClient } from '../redis/queue-client';
import { QUEUE_JOB_NAMES } from '../queue/job-names';
import { sendCapaUpdateNotification, sendCapaAssignedNotification } from '../services/capa-notification.service';
import { sendCommentMentionNotification } from '../services/comment-notification.service';
import {
  sendPublicEventCreateNotification,
  sendEventCreateNotification,
  sendEventUpdateNotificationWithUser,
} from '../services/event-notification.service';
import {
  sendJhaRevisedNotification,
  sendJhaApprovedNotification,
  sendJhaRejectedNotification,
} from '../services/jha-notification.service';
import { logger } from '../utils/logger';
import { createImageVariant } from '@server/services/image.service';
import { createNotificationForUsers } from '@server/services/notification.service';
import { sendEmail } from '@server/services/email.service';
import { EmailJobPayload } from '@shared/types/queues.types';

// Email template imports
import CapaAssignedTemplate, { CapaAssignedTemplateParams } from '@server/templates/capa-assigned';
import CapaOverdueTemplate, { CapaOverdueTemplateParams } from '@server/templates/capa-overdue';
import CapaUpdateTemplate, { CapaUpdateTemplateParams } from '@server/templates/capa-update';
import CommentMentionTemplate, { CommentMentionTemplateParams } from '@server/templates/comment-mention';
import EventCreateTemplate, { EventCreateTemplateParams } from '@server/templates/event-create';
import EventUpdateTemplate, { EventUpdateTemplateParams } from '@server/templates/event-update';
import JhaApprovedTemplate, { JhaApprovedTemplateParams } from '@server/templates/jha-approved';
import JhaRevisedTemplate, { JhaRevisedTemplateParams } from '@server/templates/jha-revised';
import JhaOverdueTemplate, { JhaOverdueTemplateParams } from '@server/templates/jha-overdue';
import JhaRejectedTemplate, { JhaRejectedTemplateParams } from '@server/templates/jha-rejected';
import React from 'react';

const workers: Worker[] = [];

/**
 * IMPORTANT: Why we maintain template mapping in the worker
 *
 * This template registry exists because of BullMQ queue serialization limitations:
 *
 * 1. **React Elements Can't Be Serialized**: React.createElement() returns objects with
 *    functions and complex references that can't be converted to JSON for queue storage.
 *
 * 2. **Queue Boundary**: When notification services add jobs to the queue, they can only
 *    send JSON-serializable data (templateType + templateProps). The worker must
 *    reconstruct the actual React elements from this data.
 *
 * 3. **Template Import Centralization**: Rather than importing templates in every
 *    notification service (where they can't be used anyway due to serialization),
 *    we centralize all template imports here at the "queue processing boundary".
 *
 * 4. **Type Safety Bridge**: The casting functions bridge the gap between the generic
 *    Record<string, unknown> from the queue and the specific template param types,
 *    ensuring type safety without 'as any' usage.
 */

/**
 * Type-safe template prop casting functions
 * These functions cast the queue data to the correct template param types
 */
const castTemplateProps = {
  'capa-assigned': (props: Record<string, unknown>) => props as CapaAssignedTemplateParams,
  'capa-overdue': (props: Record<string, unknown>) => props as CapaOverdueTemplateParams,
  'capa-update': (props: Record<string, unknown>) => props as CapaUpdateTemplateParams,
  'comment-mention': (props: Record<string, unknown>) => props as CommentMentionTemplateParams,
  'event-create': (props: Record<string, unknown>) => props as EventCreateTemplateParams,
  'event-update': (props: Record<string, unknown>) => props as EventUpdateTemplateParams,
  'jha-approved': (props: Record<string, unknown>) => props as JhaApprovedTemplateParams,
  'jha-revised': (props: Record<string, unknown>) => props as JhaRevisedTemplateParams,
  'jha-overdue': (props: Record<string, unknown>) => props as JhaOverdueTemplateParams,
  'jha-rejected': (props: Record<string, unknown>) => props as JhaRejectedTemplateParams,
} as const;

/**
 * Processes email jobs by reconstructing React templates and sending emails
 */
const processEmailJob = async (emailJobPayload: EmailJobPayload) => {
  const { templateType, templateProps, options } = emailJobPayload;

  let template: React.ReactElement;

  // Create template based on type with proper type casting
  switch (templateType) {
    case 'capa-assigned':
      template = React.createElement(CapaAssignedTemplate, castTemplateProps['capa-assigned'](templateProps));
      break;
    case 'capa-overdue':
      template = React.createElement(CapaOverdueTemplate, castTemplateProps['capa-overdue'](templateProps));
      break;
    case 'capa-update':
      template = React.createElement(CapaUpdateTemplate, castTemplateProps['capa-update'](templateProps));
      break;
    case 'comment-mention':
      template = React.createElement(CommentMentionTemplate, castTemplateProps['comment-mention'](templateProps));
      break;
    case 'event-create':
      template = React.createElement(EventCreateTemplate, castTemplateProps['event-create'](templateProps));
      break;
    case 'event-update':
      template = React.createElement(EventUpdateTemplate, castTemplateProps['event-update'](templateProps));
      break;
    case 'jha-approved':
      template = React.createElement(JhaApprovedTemplate, castTemplateProps['jha-approved'](templateProps));
      break;
    case 'jha-revised':
      template = React.createElement(JhaRevisedTemplate, castTemplateProps['jha-revised'](templateProps));
      break;
    case 'jha-overdue':
      template = React.createElement(JhaOverdueTemplate, castTemplateProps['jha-overdue'](templateProps));
      break;
    case 'jha-rejected':
      template = React.createElement(JhaRejectedTemplate, castTemplateProps['jha-rejected'](templateProps));
      break;
    default:
      throw new Error(`Unknown email template type: ${templateType}`);
  }

  await sendEmail(template, options);
};

/**
 * Generic worker job handler that abstracts common try-catch boilerplate
 * @param serviceFunction - The service function to execute
 * @param jobType - Human-readable job type for logging
 * @param getJobContext - Function to extract context data for logging
 */
const createJobHandler = <T>(
  serviceFunction: (data: T) => Promise<void>,
  jobType: string,
  getJobContext: (data: T) => Record<string, unknown>,
) => {
  return async (job: Job<T>) => {
    const context = getJobContext(job.data);

    try {
      logger.info(`Processing ${jobType} job: ${job.id}`, {
        jobId: job.id,
        attempts: job.attemptsMade,
        ...context,
      });

      await serviceFunction(job.data);

      logger.info(`Successfully processed ${jobType} job: ${job.id}`, {
        jobId: job.id,
        ...context,
      });
    } catch (error) {
      logger.error(`Failed to process ${jobType} job: ${job.id}`, {
        jobId: job.id,
        error: error instanceof Error ? error.message : 'Unknown error',
        attempts: job.attemptsMade,
        ...context,
      });
      throw error; // Re-throw to trigger retry
    }
  };
};

export const registerWorkers = () => {
  // CAPA Update Notification Worker
  workers.push(
    new Worker(
      QUEUE_JOB_NAMES.UPDATE_CAPA_NOTIFICATION,
      createJobHandler(sendCapaUpdateNotification, 'CAPA update notification', (data) => ({ capaId: data.capa?.id })),
      {
        connection: bullMqRedisClient,
        concurrency: 1, // No concurrency - process one job at a time
      },
    ),
  );

  // CAPA Assigned Notification Worker
  workers.push(
    new Worker(
      QUEUE_JOB_NAMES.ASSIGNED_CAPA_NOTIFICATION,
      createJobHandler(sendCapaAssignedNotification, 'CAPA assigned notification', (data) => ({
        capaId: data.capa?.id,
      })),
      {
        connection: bullMqRedisClient,
        concurrency: 1, // No concurrency - process one job at a time
      },
    ),
  );

  // Comment Mention Notification Worker
  workers.push(
    new Worker(
      QUEUE_JOB_NAMES.COMMENT_MENTION_NOTIFICATION,
      createJobHandler(sendCommentMentionNotification, 'comment mention notification', (data) => ({
        commentId: data.newComment?.id,
        entityType: data.input?.entityType,
        entityId: data.input?.entityId,
      })),
      {
        connection: bullMqRedisClient,
        concurrency: 1, // No concurrency - process one job at a time
      },
    ),
  );

  // Public Event Create Notification Worker
  workers.push(
    new Worker(
      QUEUE_JOB_NAMES.PUBLIC_EVENT_CREATE_NOTIFICATION,
      createJobHandler(sendPublicEventCreateNotification, 'public event create notification', (data) => ({
        eventId: data.createdEvent?.id,
        upkeepCompanyId: data.upkeepCompanyId,
      })),
      {
        connection: bullMqRedisClient,
        concurrency: 1, // No concurrency - process one job at a time
      },
    ),
  );

  // Event Create Notification Worker
  workers.push(
    new Worker(
      QUEUE_JOB_NAMES.EVENT_CREATE_NOTIFICATION,
      createJobHandler(sendEventCreateNotification, 'event create notification', (data) => ({
        eventId: data.createdEvent?.id,
        userId: data.user?.id,
      })),
      {
        connection: bullMqRedisClient,
        concurrency: 1, // No concurrency - process one job at a time
      },
    ),
  );

  // Event Update Notification Worker
  workers.push(
    new Worker(
      QUEUE_JOB_NAMES.EVENT_UPDATE_NOTIFICATION,
      createJobHandler(sendEventUpdateNotificationWithUser, 'event update notification', (data) => ({
        eventId: data.updatedEvent?.id,
        userId: data.user?.id,
        actionPrefix: data.actionPrefix,
      })),
      {
        connection: bullMqRedisClient,
        concurrency: 1, // No concurrency - process one job at a time
      },
    ),
  );

  workers.push(
    new Worker(
      QUEUE_JOB_NAMES.GENERATE_IMAGE,
      createJobHandler(
        async (jobData) => {
          createImageVariant(jobData);
        },
        'generate image',
        (data) => ({
          user: data.user,
          entityType: data.input?.entityType,
          entityId: data.input?.entityId,
          fileName: data.input?.fileName,
          variantType: data.variantType,
          originalS3Key: data.originalS3Key,
          presignedUrl: data.presignedUrl,
        }),
      ),
      {
        connection: bullMqRedisClient,
        concurrency: 3,
      },
    ),
  );

  // JHA Create Notification Worker
  workers.push(
    new Worker(
      QUEUE_JOB_NAMES.JHA_REVISED_NOTIFICATION,
      createJobHandler(sendJhaRevisedNotification, 'JHA revised notification', (data) => ({
        jhaId: data.updatedJha?.id,
        userId: data.user?.id,
      })),
      {
        connection: bullMqRedisClient,
        concurrency: 1, // No concurrency - process one job at a time
      },
    ),
  );

  // JHA Approved Notification Worker
  workers.push(
    new Worker(
      QUEUE_JOB_NAMES.JHA_APPROVED_NOTIFICATION,
      createJobHandler(sendJhaApprovedNotification, 'JHA approved notification', (data) => ({
        jhaId: data.updatedJha?.id,
        userId: data.user?.id,
      })),
      {
        connection: bullMqRedisClient,
        concurrency: 1, // No concurrency - process one job at a time
      },
    ),
  );

  // JHA Rejected Notification Worker
  workers.push(
    new Worker(
      QUEUE_JOB_NAMES.JHA_REJECTED_NOTIFICATION,
      createJobHandler(sendJhaRejectedNotification, 'JHA rejected notification', (data) => ({
        jhaId: data.updatedJha?.id,
        userId: data.user?.id,
        rejectionReason: data.rejectionReason,
      })),
      {
        connection: bullMqRedisClient,
        concurrency: 1, // No concurrency - process one job at a time
      },
    ),
  );

  // Send Alert Notification Worker
  workers.push(
    new Worker(
      QUEUE_JOB_NAMES.SEND_ALERT_NOTIFICATION,
      createJobHandler(
        async (data) => {
          await createNotificationForUsers(data);
        },
        'send alert notification',
        (data) => ({
          upkeepCompanyId: data.upkeepCompanyId,
          userCount: data.userIds?.length || 0,
          title: data.title,
        }),
      ),
      {
        connection: bullMqRedisClient,
        concurrency: 3, // Can handle multiple notification jobs concurrently
      },
    ),
  );

  // Send Email Worker
  workers.push(
    new Worker(
      QUEUE_JOB_NAMES.SEND_EMAIL,
      createJobHandler(processEmailJob, 'send email', (data: EmailJobPayload) => ({
        templateType: data.templateType,
        subject: data.options.subject,
        recipientCount: data.options.to.length,
      })),
      {
        connection: bullMqRedisClient,
        concurrency: 3, // Can handle multiple email jobs concurrently
      },
    ),
  );

  logger.info('[BullMQ] Workers registered successfully');
};

export const shutdownWorkers = async () => {
  await Promise.all(workers.map((w) => w.close()));
  logger.info('[BullMQ] All workers shut down');
};
