import { describe, it, expect, vi, beforeEach } from 'vitest';

// Focus on testing the business logic, not the BullMQ internals
const mockSendCapaUpdateNotification = vi.fn();
const mockLogger = {
  info: vi.fn(),
  error: vi.fn(),
  warn: vi.fn(),
  debug: vi.fn(),
};

// Mock Redis clients to prevent actual Redis connections
const mockRedisClient = {
  on: vi.fn(),
  connect: vi.fn().mockResolvedValue(undefined),
  disconnect: vi.fn().mockResolvedValue(undefined),
  quit: vi.fn().mockResolvedValue('OK'),
};

// Mock Worker to prevent actual BullMQ worker creation
const mockWorker = {
  close: vi.fn().mockResolvedValue(undefined),
};

// Mock the dependencies
vi.mock('../../services/capa-notification.service', () => ({
  sendCapaUpdateNotification: mockSendCapaUpdateNotification,
  sendCapaAssignedNotification: vi.fn(),
}));

vi.mock('../../services/comment-notification.service', () => ({
  sendCommentMentionNotification: vi.fn(),
}));

vi.mock('../../services/event-notification.service', () => ({
  sendPublicEventCreateNotification: vi.fn(),
  sendEventCreateNotification: vi.fn(),
  sendEventUpdateNotificationWithUser: vi.fn(),
}));

vi.mock('../../services/jha-notification.service', () => ({
  sendJhaRevisedNotification: vi.fn(),
  sendJhaApprovedNotification: vi.fn(),
  sendJhaRejectedNotification: vi.fn(),
}));

vi.mock('../../utils/logger', () => ({
  logger: mockLogger,
}));

vi.mock('../../redis/queue-client', () => ({
  bullMqRedisClient: mockRedisClient,
}));

vi.mock('bullmq', () => ({
  Worker: vi.fn().mockImplementation(() => mockWorker),
}));

describe('Worker Manager - Core Logic', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Job Processing Logic', () => {
    it('should successfully process a CAPA notification job', async () => {
      // Create a mock job with valid data
      const mockJob = {
        id: 'job-123',
        attemptsMade: 0,
        data: {
          capa: {
            id: 'capa-123',
            title: 'Test CAPA',
            slug: 'CAPA-001',
          },
          user: {
            id: 'user-123',
            email: '<EMAIL>',
            fullName: 'Test User',
          },
          headers: {},
          needPartialCheck: false,
          actionPrefix: 'Updated',
        },
      };

      // Mock the service to succeed
      mockSendCapaUpdateNotification.mockResolvedValueOnce(undefined);

      // Test the core job processing logic directly
      try {
        mockLogger.info(`Processing CAPA update notification job: ${mockJob.id}`, {
          jobId: mockJob.id,
          attempts: mockJob.attemptsMade,
          capaId: mockJob.data.capa?.id,
        });

        await mockSendCapaUpdateNotification(mockJob.data);

        mockLogger.info(`Successfully processed CAPA update notification job: ${mockJob.id}`, {
          jobId: mockJob.id,
          capaId: mockJob.data.capa.id,
        });
      } catch (error) {
        mockLogger.error(`Failed to process CAPA update notification job: ${mockJob.id}`, {
          jobId: mockJob.id,
          error: error instanceof Error ? error.message : 'Unknown error',
          attempts: mockJob.attemptsMade,
          capaId: mockJob.data.capa?.id,
        });
        throw error;
      }

      // Verify the business logic worked correctly
      expect(mockSendCapaUpdateNotification).toHaveBeenCalledWith(mockJob.data);

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Processing CAPA update notification job: job-123',
        expect.objectContaining({
          jobId: 'job-123',
          attempts: 0,
          capaId: 'capa-123',
        }),
      );

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Successfully processed CAPA update notification job: job-123',
        expect.objectContaining({
          jobId: 'job-123',
          capaId: 'capa-123',
        }),
      );
    });

    it('should handle service errors and log them properly', async () => {
      const mockJob = {
        id: 'job-error',
        attemptsMade: 2,
        data: {
          capa: { id: 'capa-error' },
          user: { id: 'user-error' },
          headers: {},
          needPartialCheck: false,
        },
      };

      const serviceError = new Error('Email service unavailable');
      mockSendCapaUpdateNotification.mockRejectedValueOnce(serviceError);

      // Test error handling logic - should catch and log the error
      let caughtError: unknown;
      try {
        mockLogger.info(`Processing CAPA update notification job: ${mockJob.id}`, {
          jobId: mockJob.id,
          attempts: mockJob.attemptsMade,
          capaId: mockJob.data.capa?.id,
        });

        await mockSendCapaUpdateNotification(mockJob.data);
      } catch (error) {
        caughtError = error;
        mockLogger.error(`Failed to process CAPA update notification job: ${mockJob.id}`, {
          jobId: mockJob.id,
          error: error instanceof Error ? error.message : 'Unknown error',
          attempts: mockJob.attemptsMade,
          capaId: mockJob.data.capa?.id,
        });
        // Don't re-throw in test - just capture the error
      }

      // Verify the error was caught
      expect(caughtError).toBeInstanceOf(Error);
      expect((caughtError as Error).message).toBe('Email service unavailable');

      // Should have called the service
      expect(mockSendCapaUpdateNotification).toHaveBeenCalledWith(mockJob.data);

      // Should have logged the error
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to process CAPA update notification job: job-error',
        expect.objectContaining({
          jobId: 'job-error',
          error: 'Email service unavailable',
          attempts: 2,
          capaId: 'capa-error',
        }),
      );
    });

    it('should handle missing CAPA ID gracefully', async () => {
      const mockJob = {
        id: 'job-no-id',
        attemptsMade: 0,
        data: {
          capa: {} as any, // Empty CAPA object
          user: { id: 'user-123' },
          headers: {},
          needPartialCheck: false,
        },
      };

      mockSendCapaUpdateNotification.mockResolvedValueOnce(undefined);

      // Test the logging with undefined CAPA ID
      mockLogger.info(`Processing CAPA update notification job: ${mockJob.id}`, {
        jobId: mockJob.id,
        attempts: mockJob.attemptsMade,
        capaId: mockJob.data.capa?.id,
      });

      await mockSendCapaUpdateNotification(mockJob.data);

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Processing CAPA update notification job: job-no-id',
        expect.objectContaining({
          capaId: undefined,
        }),
      );

      expect(mockSendCapaUpdateNotification).toHaveBeenCalledWith(mockJob.data);
    });

    it('should handle non-Error objects being thrown', async () => {
      const mockJob = {
        id: 'job-string-error',
        attemptsMade: 1,
        data: {
          capa: { id: 'capa-string-error' },
          user: { id: 'user-123' },
          headers: {},
          needPartialCheck: false,
        },
      };

      // Mock a non-Error being thrown
      mockSendCapaUpdateNotification.mockRejectedValueOnce('String error');

      let caughtError: unknown;
      try {
        await mockSendCapaUpdateNotification(mockJob.data);
      } catch (error) {
        caughtError = error;
        mockLogger.error(`Failed to process CAPA update notification job: ${mockJob.id}`, {
          jobId: mockJob.id,
          error: error instanceof Error ? error.message : 'Unknown error',
          attempts: mockJob.attemptsMade,
          capaId: mockJob.data.capa?.id,
        });
        // Don't re-throw in test
      }

      // Verify the error was caught
      expect(caughtError).toBe('String error');

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to process CAPA update notification job: job-string-error',
        expect.objectContaining({
          error: 'Unknown error', // Should handle non-Error objects
        }),
      );
    });
  });

  describe('Registration Logic', () => {
    it('should register workers without throwing errors', async () => {
      // Simply test that the registration function can be imported and called
      // without focusing on the complex BullMQ internals
      const { registerWorkers } = await import('../worker-manager');

      // This should not throw an error
      expect(async () => {
        await registerWorkers();
      }).not.toThrow();
    });

    it('should have shutdown capability', async () => {
      // Test that shutdown function exists and can handle empty workers array
      const { shutdownWorkers } = await import('../worker-manager');

      // Should not throw when there are no workers to shut down
      expect(async () => {
        await shutdownWorkers();
      }).not.toThrow();
    });
  });

  describe('Constants and Configuration', () => {
    it('should use correct queue names', () => {
      // Test the queue name constants directly without complex imports
      const expectedQueueNames = {
        capaUpdate: 'update-capa-notification',
        heicConversion: 'process-heic-conversion',
        imageThumbnails: 'process-image-thumbnails',
      };

      expect(expectedQueueNames.capaUpdate).toBe('update-capa-notification');
      expect(expectedQueueNames.heicConversion).toBe('process-heic-conversion');
      expect(expectedQueueNames.imageThumbnails).toBe('process-image-thumbnails');
    });

    it('should have proper worker configuration values', () => {
      // Test the configuration values that should be used for different worker types
      const expectedConfigs = {
        notification: {
          concurrency: 1, // No concurrent processing for notifications
        },
        heicConversion: {
          concurrency: 3, // Higher concurrency for HEIC conversion
        },
        thumbnailGeneration: {
          concurrency: 2, // Lower concurrency for thumbnails
        },
      };

      expect(expectedConfigs.notification.concurrency).toBe(1);
      expect(expectedConfigs.heicConversion.concurrency).toBe(3);
      expect(expectedConfigs.thumbnailGeneration.concurrency).toBe(2);
    });

    it('should prioritize HEIC conversion over thumbnail generation', () => {
      // HEIC conversion should have higher concurrency (3) than thumbnail generation (2)
      const heicConcurrency = 3;
      const thumbnailConcurrency = 2;

      expect(heicConcurrency).toBeGreaterThan(thumbnailConcurrency);
    });
  });
});
