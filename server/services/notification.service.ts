import { db } from '@server/db';
import { emitNotificationChange } from '@server/services/notification-pubsub';
import { notifications, userNotifications } from '@shared/schema';
import { AlertNotificationJobPayload } from '@shared/types/queues.types';
import { User } from '@shared/types/users.types';
import { and, count, desc, eq, isNull, sql } from 'drizzle-orm';

export const getUserNotifications = async (user: User) => {
  return db
    .select({
      id: notifications.id,
      upkeepCompanyId: notifications.upkeepCompanyId,
      title: notifications.title,
      description: notifications.description,
      url: notifications.url,
      createdAt: notifications.createdAt,
      updatedAt: notifications.updatedAt,
      archivedAt: notifications.archivedAt,
      readAt: userNotifications.readAt,
      userNotificationId: userNotifications.id,
    })
    .from(notifications)
    .innerJoin(userNotifications, eq(notifications.id, userNotifications.notificationId))
    .where(
      and(
        eq(notifications.upkeepCompanyId, user.upkeepCompanyId),
        eq(userNotifications.userId, user.id),
        isNull(notifications.archivedAt),
        isNull(userNotifications.archivedAt),
      ),
    )
    .orderBy(sql`CASE WHEN ${userNotifications.readAt} IS NULL THEN 0 ELSE 1 END`, desc(notifications.createdAt))
    .limit(10);
};

export const getUnreadNotificationCount = async (user: User) => {
  const result = await db
    .select({ count: count() })
    .from(notifications)
    .innerJoin(userNotifications, eq(notifications.id, userNotifications.notificationId))
    .where(
      and(
        eq(notifications.upkeepCompanyId, user.upkeepCompanyId),
        eq(userNotifications.userId, user.id),
        isNull(notifications.archivedAt),
        isNull(userNotifications.archivedAt),
        isNull(userNotifications.readAt),
      ),
    );

  return result[0]?.count || 0;
};

export const markNotificationAsRead = async (user: User, notificationId: string) => {
  await db
    .update(userNotifications)
    .set({
      readAt: new Date(),
      updatedAt: new Date(),
    })
    .where(and(eq(userNotifications.notificationId, notificationId), eq(userNotifications.userId, user.id)));
};

export const markAllNotificationsAsRead = async (user: User) => {
  await db
    .update(userNotifications)
    .set({
      readAt: new Date(),
      updatedAt: new Date(),
    })
    .where(
      and(
        eq(userNotifications.userId, user.id),
        isNull(userNotifications.archivedAt),
        isNull(userNotifications.readAt),
      ),
    );
};

// Queue-based notification function - adds jobs to the queue for processing
export const sendAlertNotification = async (payload: AlertNotificationJobPayload) => {
  const { addJobToQueue } = await import('@server/queue/queue-utils');
  const { QUEUE_JOB_NAMES } = await import('@server/queue/job-names');

  addJobToQueue(QUEUE_JOB_NAMES.SEND_ALERT_NOTIFICATION, payload, {
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 2000,
    },
    removeOnComplete: 100,
    removeOnFail: 50,
  });
};

// Direct notification function - used by the worker for actual processing
export const createNotificationForUsers = async ({
  upkeepCompanyId,
  userIds,
  title,
  description,
  url,
}: AlertNotificationJobPayload) => {
  return db.transaction(async (tx) => {
    // Create the notification
    const notification = {
      upkeepCompanyId,
      title,
      description: description || null,
      url: url || null,
      createdAt: new Date(),
      updatedAt: null,
      archivedAt: null,
    };

    const [createdNotification] = await tx
      .insert(notifications)
      .values(notification)
      .returning({ id: notifications.id });

    // Create user notifications for each user
    const userNotificationRecords = userIds.map((userId) => ({
      userId,
      notificationId: createdNotification.id,
      readAt: null,
      archivedAt: null,
      createdAt: new Date(),
      updatedAt: null,
    }));

    await tx
      .insert(userNotifications)
      .values(userNotificationRecords)
      .onConflictDoUpdate({
        target: [userNotifications.userId, userNotifications.notificationId],
        set: {
          archivedAt: null,
          updatedAt: new Date(),
        },
      });

    emitNotificationChange(upkeepCompanyId, 'new');

    return notification;
  });
};
