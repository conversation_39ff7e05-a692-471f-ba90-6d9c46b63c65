import sharp from 'sharp';
import heicConvert from 'heic-convert';
import axios from 'axios';
import { TRPCError } from '@trpc/server';
import { isHEIC, CreateImageVariantsSchemaOptions } from '@shared/types/files.types';
import { getPresignedUrl, updateFile } from './file.service';
import { addJobToQueue } from '@server/queue/queue-utils';
import { QUEUE_JOB_NAMES } from '@server/queue/job-names';
import { logger } from '@server/utils/logger';
import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { env } from 'env';
import { User } from '@shared/types/users.types';
import { UpkeepCompanyIdSchema } from '@shared/types/schema.types';
import z from 'zod';

interface ImageProcessingResult {
  buffer: Buffer;
  format: string;
  size: number;
  width: number;
  height: number;
}

const DEFAULT_QUALITY = 85;
const DEFAULT_MAX_DIMENSION = 1920;
const SMALL_VARIANT_DIMENSIONS = 576;

const s3Client = new S3Client({
  region: env.AWS_REGION,
  credentials: {
    accessKeyId: env.AWS_ACCESS_KEY_ID,
    secretAccessKey: env.AWS_SECRET_ACCESS_KEY,
  },
});

const getVariantPresignedUrl = async (originalS3Key: string, suffix: 'small' | 'original', mimeType: string) => {
  // Extract file extension and add suffix to key
  const keyParts = originalS3Key.split('.');
  const extension = keyParts.pop();
  const baseKey = keyParts.join('.');
  const variantKey = `${baseKey}_${suffix}.${extension}`;

  const command = new PutObjectCommand({
    Bucket: env.S3_BUCKET_NAME,
    Key: variantKey,
    ContentType: mimeType,
    ACL: 'public-read',
  });

  return await getSignedUrl(s3Client, command, {
    expiresIn: 3600, // URL expires in 1 hour
    signableHeaders: new Set(['host']),
  });
};

const convertHeicToJpg = async (buffer: Buffer) => {
  const convertedBuffer = await heicConvert({
    buffer: new Uint8Array(buffer) as unknown as ArrayBuffer, // unable to handle without casting
    format: 'JPEG',
    quality: DEFAULT_QUALITY,
  });

  return Buffer.from(convertedBuffer);
};

/**
 * Maps MIME types to Sharp-compatible output formats
 */
const MIME_TO_OUTPUT_FORMAT_MAP: Record<string, 'jpeg' | 'png' | 'webp'> = {
  'image/jpeg': 'jpeg',
  'image/jpg': 'jpeg',
  'image/png': 'png',
  'image/webp': 'webp',
} as const;

/**
 * Determines the best output format based on input format and preferences
 *
 * @param inputMimeType - Original MIME type of the input image
 * @param preferredFormat - Preferred output format (overrides auto-detection)
 * @returns Optimal output format for the image
 */
const determineOutputFormat = (
  inputMimeType?: CreateImageVariantsSchemaOptions['mimeType'],
  preferredFormat?: 'jpeg' | 'png' | 'webp',
): 'jpeg' | 'png' | 'webp' => {
  // If preferred format is specified, use it
  if (preferredFormat) {
    return preferredFormat;
  }

  // If input format is supported as output format, use it
  if (inputMimeType && MIME_TO_OUTPUT_FORMAT_MAP[inputMimeType.toLowerCase()]) {
    const outputFormat = MIME_TO_OUTPUT_FORMAT_MAP[inputMimeType.toLowerCase()];
    return outputFormat;
  }

  // Default fallback to JPEG for unsupported formats (like HEIC/HEIF)
  return 'jpeg';
};

const uploadBufferToS3 = async (
  presignedUrl: string,
  buffer: Buffer,
  mimeType: CreateImageVariantsSchemaOptions['mimeType'],
) => {
  return await axios.put(presignedUrl, buffer, {
    headers: {
      'Content-Type': mimeType,
    },
  });
};

const fromBase64ToBuffer = (data: string) => {
  return Buffer.from(data, 'base64');
};

/**
 * Processes images using Sharp library with comprehensive optimization
 *
 * @param options - Processing configuration options
 * @returns Promise resolving to processed image data with metadata
 */
export const processWithSharp = async (options: CreateImageVariantsSchemaOptions): Promise<ImageProcessingResult> => {
  const operation = 'processWithSharp';
  const { quality = DEFAULT_QUALITY, maxWidth, maxHeight, kernel, outputFormat = 'jpeg', fileBuffer } = options;

  try {
    // Initialize Sharp pipeline with optimizations
    let pipeline = sharp(fileBuffer, {
      unlimited: true,
      sequentialRead: true, // Optimize for streaming
    });

    // Strip metadata for privacy and file size optimization
    pipeline = pipeline.withMetadata({});

    // Apply resizing if dimensions are specified
    if (maxWidth || maxHeight) {
      pipeline = pipeline.resize(maxWidth, maxHeight, {
        fit: 'inside',
        withoutEnlargement: true,
        kernel: kernel ?? 'lanczos3',
      });
    }

    // Apply format-specific optimizations
    switch (outputFormat) {
      case 'jpeg':
        pipeline = pipeline.jpeg({
          quality,
          progressive: true,
          mozjpeg: true, // 10-15% better compression
          optimizeScans: true, // Progressive scan optimization
          trellisQuantisation: true, // 2-5% better compression
          overshootDeringing: true,
        });
        break;
      case 'png':
        pipeline = pipeline.png({
          quality,
          progressive: true,
          compressionLevel: 9, // Maximum compression (0-9)
          adaptiveFiltering: true, // Better compression
        });
        break;
      case 'webp':
        pipeline = pipeline.webp({
          quality,
          smartSubsample: true, // Better chroma subsampling
          alphaQuality: quality, // Match alpha channel quality
        });
        break;
      default:
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: `Unsupported output format: ${outputFormat}`,
        });
    }

    // Process the image
    const processedBuffer = await pipeline.toBuffer({ resolveWithObject: true });

    const result: ImageProcessingResult = {
      buffer: processedBuffer.data,
      format: processedBuffer.info.format,
      size: processedBuffer.info.size,
      width: processedBuffer.info.width,
      height: processedBuffer.info.height,
    };

    return result;
  } catch (error) {
    // Re-throw TRPCErrors as-is
    if (error instanceof TRPCError) {
      throw error;
    }

    logger.error('Sharp image processing failed', {
      operation,
      quality,
      maxWidth,
      maxHeight,
      outputFormat,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });

    throw new TRPCError({
      code: 'UNPROCESSABLE_CONTENT',
      message: 'Failed to process image. Please check the image format and try again.',
      cause: error,
    });
  }
};

/**
 * Creates multiple image variants (original, medium, small) with comprehensive processing
 * For non-HEIC images, all processing is queued to avoid blocking the UI
 * For HEIC images, conversion happens synchronously since browsers can't preview HEIC
 */
export const createImageVariants = async (
  input: CreateImageVariantsSchemaOptions,
  user: z.infer<typeof UpkeepCompanyIdSchema> & { id?: User['id'] },
) => {
  try {
    const fileBuffer = fromBase64ToBuffer(input.fileData);

    if (isHEIC(input.mimeType)) {
      // Convert HEIC to JPEG synchronously (required for browser compatibility)
      const processedJpegBuffer = await convertHeicToJpg(fileBuffer);
      const mimeType = 'image/jpeg' as const;
      const outputFormat = 'jpeg' as const;

      // Process the main image synchronously for HEIC
      const converted = await processWithSharp({
        ...input,
        quality: input.quality ?? DEFAULT_QUALITY,
        maxWidth: input.maxWidth ?? DEFAULT_MAX_DIMENSION,
        maxHeight: input.maxHeight ?? DEFAULT_MAX_DIMENSION,
        outputFormat,
        mimeType: mimeType,
        fileBuffer: processedJpegBuffer,
      });

      // Get presigned URL for the main variant
      const presignedData = await getPresignedUrl(
        {
          fileName: input.fileName,
          fileSize: converted.size,
          mimeType: mimeType,
          entityType: input.entityType,
          entityId: input.entityId,
        },
        user,
      );

      // Upload main image to S3 synchronously for HEIC
      await uploadBufferToS3(presignedData.presignedUrl, converted.buffer, `image/${outputFormat}`);

      // Mark file as completed after successful upload
      if (presignedData.file?.id && presignedData.file?.s3Key) {
        await updateFile({ id: presignedData.file.id, s3Key: presignedData.file.s3Key, status: 'completed' }, user);
      }

      // Queue variants asynchronously even for HEIC
      const processedJpegBufferBase64 = processedJpegBuffer.toString('base64');

      // Queue small variant generation
      const payloadSmall = {
        input: {
          ...input,
          fileData: processedJpegBufferBase64,
          fileBuffer: undefined,
          maxWidth: SMALL_VARIANT_DIMENSIONS,
          maxHeight: SMALL_VARIANT_DIMENSIONS,
          quality: 80,
          mimeType,
          outputFormat,
        },
        user,
        originalS3Key: presignedData.file?.s3Key,
        variantType: 'small' as const,
      };

      addJobToQueue<CreateImageVariantJobData>(QUEUE_JOB_NAMES.GENERATE_IMAGE, payloadSmall);

      return presignedData;
    }

    // For non-HEIC images, queue ALL processing to avoid blocking UI
    else {
      // Create placeholder presigned URL for immediate response (no upload yet)
      const presignedData = await getPresignedUrl(
        {
          fileName: input.fileName,
          fileSize: fileBuffer.length, // Use original size as estimate
          mimeType: input.mimeType,
          entityType: input.entityType,
          entityId: input.entityId,
        },
        user,
      );

      // Queue original image processing
      const payloadOriginal = {
        input: {
          ...input,
          fileBuffer: undefined,
          maxWidth: input.maxWidth ?? DEFAULT_MAX_DIMENSION,
          maxHeight: input.maxHeight ?? DEFAULT_MAX_DIMENSION,
          quality: input.quality ?? DEFAULT_QUALITY,
          outputFormat: 'jpeg' as const,
          kernel: 'lanczos3' as const,
        },
        user,
        originalS3Key: presignedData.file?.s3Key,
        variantType: 'original' as const,
        presignedUrl: presignedData.presignedUrl, // Pass presigned URL for direct upload
        fileId: presignedData.file?.id,
      };

      addJobToQueue<CreateImageVariantJobData>(QUEUE_JOB_NAMES.GENERATE_IMAGE, payloadOriginal);

      // Queue small variant generation
      const payloadSmall = {
        input: {
          ...input,
          fileBuffer: undefined,
          maxWidth: SMALL_VARIANT_DIMENSIONS,
          maxHeight: SMALL_VARIANT_DIMENSIONS,
          quality: 80,
          outputFormat: 'jpeg' as const,
          kernel: 'cubic' as const,
        },
        user,
        originalS3Key: presignedData.file?.s3Key,
        variantType: 'small' as const,
      };

      addJobToQueue<CreateImageVariantJobData>(QUEUE_JOB_NAMES.GENERATE_IMAGE, payloadSmall);

      return presignedData;
    }
  } catch (error) {
    // Re-throw TRPCErrors as-is
    if (error instanceof TRPCError) {
      throw error;
    }

    logger.error('Image variant creation failed', {
      fileName: input.fileName,
      mimeType: input.mimeType,
      dimensions: `${input.maxWidth}x${input.maxHeight}`,
      userId: user.id,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });

    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: `Failed to process image. Please try again.`,
      cause: error,
    });
  }
};

type CreateImageVariantJobData = {
  input: CreateImageVariantsSchemaOptions;
  user: { upkeepCompanyId: string; id?: string };
  originalS3Key?: string;
  variantType: 'small' | 'original';
  presignedUrl?: string;
  fileId?: string;
};

export const createImageVariant = async (jobData: CreateImageVariantJobData) => {
  const { input, user, originalS3Key, variantType, presignedUrl, fileId } = jobData;

  const operation = 'createImageVariant';

  try {
    let processBuffer: Buffer | undefined = input.fileBuffer;
    let outputFormat = determineOutputFormat(input.mimeType);
    let mimeType = input.mimeType;

    // Decode file data if buffer is not provided
    if (!processBuffer) {
      if (!input.fileData) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Either fileBuffer or fileData must be provided',
        });
      }

      processBuffer = fromBase64ToBuffer(input.fileData);
    }

    // Handle HEIC conversion if needed
    if (isHEIC(input.mimeType) && processBuffer) {
      processBuffer = await convertHeicToJpg(processBuffer);
      mimeType = 'image/jpeg';
      outputFormat = 'jpeg'; // Ensure output format matches converted type
    }

    if (!processBuffer) {
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to obtain image buffer for processing',
      });
    }

    // Process the image variant
    const converted = await processWithSharp({
      ...input,
      quality: input.quality ?? DEFAULT_QUALITY,
      maxWidth: input.maxWidth ?? DEFAULT_MAX_DIMENSION,
      maxHeight: input.maxHeight ?? DEFAULT_MAX_DIMENSION,
      outputFormat,
      mimeType,
      fileBuffer: processBuffer,
    });

    // Handle upload based on variant type
    if (variantType === 'original') {
      // For original images, use the provided presigned URL
      if (!presignedUrl) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Presigned URL is required for original image processing',
        });
      }

      await uploadBufferToS3(presignedUrl, converted.buffer, `image/${outputFormat}`);

      // Mark file as completed once original upload finishes
      if (fileId && originalS3Key) {
        await updateFile({ id: fileId, s3Key: originalS3Key, status: 'completed' }, user);
      }
    } else {
      // For variants, upload to S3 with suffix - NO separate DB record
      if (!originalS3Key) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Original S3 key is required for variant generation',
        });
      }

      // Get presigned URL for variant upload (no DB record created)
      const variantPresignedUrl = await getVariantPresignedUrl(originalS3Key, variantType, `image/${outputFormat}`);

      // Upload variant to S3 using the suffixed S3 key
      await uploadBufferToS3(variantPresignedUrl, converted.buffer, `image/${outputFormat}`);
    }

    // For variants, we don't return a DB record since we didn't create one
    // The original image DB record serves as the source of truth

    logger.info('Image variant creation completed successfully', {
      operation,
      variantType,
      fileName: input.fileName,
      dimensions: `${converted.width}x${converted.height}`,
      userId: user.id,
      originalS3Key,
    });

    // No return value for variants since no DB record was created
  } catch (error) {
    logger.error('Image variant creation failed', {
      operation,
      variantType,
      fileName: input.fileName,
      mimeType: input.mimeType,
      dimensions: `${input.maxWidth}x${input.maxHeight}`,
      userId: user.id,
      originalS3Key,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });

    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: `Failed to create ${variantType} image variant. Please try again.`,
      cause: error,
    });
  }
};
