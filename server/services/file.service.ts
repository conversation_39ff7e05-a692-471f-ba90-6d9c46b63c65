import {
  CopyObjectCommand,
  DeleteO<PERSON>sCommand,
  GetO<PERSON><PERSON>ommand,
  PutO<PERSON>Command,
  S3Client,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { createId } from '@paralleldrive/cuid2';
import { db } from '@server/db';
import { logger } from '@server/utils/logger';
import { getFileSizeErrorMessage, MAX_FILE_SIZE_BYTES } from '@shared/files-utils';
import { files } from '@shared/schema';
import {
  CreateFileSchema,
  GetPresignedUrlInputSchema,
  ListFilesSchema,
  SUPPORTED_MIME_TYPES,
  UpdateFileSchema,
} from '@shared/types/files.types';
import { IdArraySchema, UpkeepCompanyIdSchema } from '@shared/types/schema.types';
import { User } from '@shared/types/users.types';
import { and, eq, inArray } from 'drizzle-orm';
import { env } from 'env';
import { z } from 'zod';

const s3Client = new S3Client({
  region: env.AWS_REGION,
  credentials: {
    accessKeyId: env.AWS_ACCESS_KEY_ID,
    secretAccessKey: env.AWS_SECRET_ACCESS_KEY,
  },
});

export const getPresignedUrl = async (
  params: z.infer<typeof GetPresignedUrlInputSchema>,
  user: z.infer<typeof UpkeepCompanyIdSchema> & { id?: User['id'] },
) => {
  // Validate MIME type
  if (!SUPPORTED_MIME_TYPES[params.mimeType as keyof typeof SUPPORTED_MIME_TYPES]) {
    const supportedFormats = Object.keys(SUPPORTED_MIME_TYPES).flat().join(', ');
    throw new Error(`Unsupported file type: ${params.mimeType}. Supported formats: ${supportedFormats}`);
  }

  if (params.fileSize > MAX_FILE_SIZE_BYTES) {
    throw new Error(getFileSizeErrorMessage(params.fileName, params.fileSize));
  }

  const fileExtension = params.fileName.split('.').pop();

  const uniqueId = createId();

  // Build S3 key with suffix in the key path, not the filename
  const suffix = params.suffix ? `_${params.suffix}` : '';
  const key = `ehs-files/${user.upkeepCompanyId}/${params.entityType}/${uniqueId}${suffix}.${fileExtension}`;

  const command = new PutObjectCommand({
    Bucket: env.S3_BUCKET_NAME,
    Key: key,
    ContentType: params.mimeType,
    ACL: 'public-read',
  });

  const presignedUrl = await getSignedUrl(s3Client, command, {
    expiresIn: 3600, // URL expires in 1 hour
    signableHeaders: new Set(['host']),
  });

  const url = presignedUrl.split('?')[0];

  // Create file record
  const file = await createFile({
    fileName: params.fileName,
    fileSize: params.fileSize,
    mimeType: params.mimeType,
    presignedUrl: url,
    s3Key: key,
    s3Bucket: env.S3_BUCKET_NAME,
    expiresAt: new Date(Date.now() + 3600 * 1000), // 1 hour from now
    uploadedBy: user?.id,
    upkeepCompanyId: user?.upkeepCompanyId,
    entityId: params.entityId,
    entityType: params.entityType,
  });

  return {
    presignedUrl,
    file,
  };
};

export const createFiles = async (
  fileData: z.infer<typeof CreateFileSchema>[],
  tx?: Parameters<Parameters<typeof db.transaction>[0]>[0],
) => {
  if (fileData.length === 0) {
    return [];
  }

  const createFilesOperation = async (transaction: typeof tx) => {
    const createdFiles = await transaction!
      .insert(files)
      .values(
        fileData.map((data) => ({
          ...data,
          status: 'pending' as const,
        })),
      )
      .returning();

    return createdFiles;
  };

  if (tx) {
    return createFilesOperation(tx);
  }

  return db.transaction(createFilesOperation);
};

export const createFile = async (params: z.infer<typeof CreateFileSchema>) => {
  const createdFiles = await createFiles([params]);

  const createdFile = createdFiles.at(0);
  if (!createdFile) {
    throw new Error(`Failed to create file record for ${params.fileName}`);
  }

  return createdFile;
};

export const getFiles = async (params: z.infer<typeof ListFilesSchema>, user: User) => {
  const conditions = [eq(files.upkeepCompanyId, user.upkeepCompanyId)];

  if (params.entityType) {
    conditions.push(eq(files.entityType, params.entityType));
  }

  if (params.entityId) {
    conditions.push(eq(files.entityId, params.entityId));
  }

  if (params.status) {
    conditions.push(eq(files.status, params.status));
  }

  return await db
    .select()
    .from(files)
    .where(and(...conditions))
    .orderBy(files.createdAt);
};

export const duplicateFiles = async (params: z.infer<typeof ListFilesSchema>, user: User) => {
  const sourceFiles = await getFiles(params, user);

  if (sourceFiles.length === 0) {
    return [];
  }

  return await db.transaction(async (tx) => {
    const copiedS3Keys: string[] = [];
    const pendingDbFilesInsertions: Array<z.infer<typeof CreateFileSchema>> = [];

    try {
      // First, copy all S3 objects and collect the file data for batch insert
      for (const sourceFile of sourceFiles) {
        const fileExtension = sourceFile.fileName.split('.').pop();
        const uniqueId = createId();
        const newS3Key = `ehs-files/${user.upkeepCompanyId}/${params.entityType}/${uniqueId}.${fileExtension}`;

        const copyCommand = new CopyObjectCommand({
          Bucket: env.S3_BUCKET_NAME,
          Key: newS3Key,
          CopySource: `${env.S3_BUCKET_NAME}/${sourceFile.s3Key}`,
          ACL: 'public-read',
          MetadataDirective: 'COPY',
        });

        await s3Client.send(copyCommand);
        copiedS3Keys.push(newS3Key);

        const newPresignedUrl = await getPresignedReadUrl(newS3Key);
        const url = newPresignedUrl.split('?')[0];

        pendingDbFilesInsertions.push({
          fileName: sourceFile.fileName,
          fileSize: sourceFile.fileSize,
          mimeType: sourceFile.mimeType,
          s3Bucket: sourceFile.s3Bucket,
          presignedUrl: url,
          s3Key: newS3Key,
          upkeepCompanyId: user.upkeepCompanyId,
          uploadedBy: user.id,
          entityType: params.entityType,
          entityId: null, // will be associated after
          expiresAt: new Date(Date.now() + 3600 * 1000), // 1 hour from now
        });
      }

      const duplicatedFiles = await createFiles(pendingDbFilesInsertions, tx);

      return duplicatedFiles;
    } catch (error) {
      // Transaction will auto-rollback database changes
      // But we need to manually clean up S3 objects
      if (copiedS3Keys.length > 0) {
        const deleteCommand = new DeleteObjectsCommand({
          Bucket: env.S3_BUCKET_NAME,
          Delete: {
            Objects: copiedS3Keys.map((key) => ({ Key: key })),
          },
        });

        try {
          await s3Client.send(deleteCommand);
        } catch (rollbackError) {
          logger.error('Failed to rollback S3 objects during duplicate operation:', rollbackError);
        }
      }

      throw error;
    }
  });
};

export const updateFile = async (
  params: z.infer<typeof UpdateFileSchema>,
  user: z.infer<typeof UpkeepCompanyIdSchema>,
) => {
  const file = await db
    .update(files)
    .set({
      ...params,
      updatedAt: new Date(),
    })
    .where(and(eq(files.id, params.id), eq(files.upkeepCompanyId, user.upkeepCompanyId)))
    .returning();

  return file.at(0);
};

export const getPresignedReadUrl = async (s3Key: string) => {
  const command = new GetObjectCommand({
    Bucket: env.S3_BUCKET_NAME,
    Key: s3Key,
  });

  const presignedUrl = await getSignedUrl(s3Client, command);

  return presignedUrl;
};

export const removeFiles = async (ids: z.infer<typeof IdArraySchema>) => {
  const deletedFiles = await db.delete(files).where(inArray(files.id, ids)).returning();

  const command = new DeleteObjectsCommand({
    Bucket: env.S3_BUCKET_NAME,
    Delete: {
      Objects: deletedFiles?.map((file) => ({ Key: file.s3Key })),
    },
  });

  await s3Client.send(command);

  return deletedFiles;
};
