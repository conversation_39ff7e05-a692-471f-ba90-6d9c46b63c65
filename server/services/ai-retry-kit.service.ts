import { openai } from '@ai-sdk/openai';
import { logger } from '@server/utils/logger';
import { OpenAIModel } from 'env';
import { generateObject } from 'ai';
import { z, ZodError } from 'zod';
import { getSchemaFixingPrompts } from './prompts.service';

/**
 * AI Retry Kit Service
 *
 * This service provides intelligent retry mechanisms for AI-generated objects that fail
 * Zod schema validation. It includes two main retry strategies:
 * 1. Schema-based fixing: Uses AI to fix validation errors in generated objects
 * 2. Original retry: Retries the original prompt with optional error context
 */

/**
 * Schema that defines the structure for AI-fixed objects
 * Used when asking AI to correct validation errors in generated objects
 */
const FixedObjectSchema = z.object({
  fixedObject: z.record(z.string(), z.unknown()), // The corrected object as key-value pairs
  explanation: z.string().optional(), // Optional explanation of what was fixed
});

/**
 * Extracts Zod validation error details for the retry prompt
 *
 * Takes a ZodError and formats it into a human-readable string that can be
 * included in AI prompts to help fix validation issues.
 *
 * @param error - The ZodError containing validation issues
 * @returns Formatted string listing all validation errors with their paths
 */
const extractZodErrorDetails = (error: ZodError): string => {
  return error.issues
    .map((issue) => {
      // Build a readable path string (e.g., "users.0.name" or "at root")
      const path = issue.path.length > 0 ? `at path "${issue.path.join('.')}"` : 'at root';
      return `- ${issue.message} ${path}`;
    })
    .join('\n');
};

/**
 * Recursively extracts ZodError from nested AI error structures
 *
 * AI SDK errors often wrap ZodErrors in multiple layers of cause chains.
 * This recursive function navigates through the nested error structure to find
 * the actual ZodError that contains the validation details we need.
 *
 * @param error - The current error or cause to check
 * @returns The nested ZodError if found, null otherwise
 */
const extractZodError = (error: unknown): ZodError | null => {
  // Base case: if this is already a ZodError, return it
  if (error instanceof ZodError) {
    return error;
  }

  // If it's not an object or doesn't have a cause, we can't go deeper
  if (!error || typeof error !== 'object' || !('cause' in error)) {
    return null;
  }

  // Recursively check the cause
  return extractZodError((error as { cause: unknown }).cause);
};

/**
 * Extracts original value from AI error structures
 *
 * When AI generation fails validation, the original generated value is often
 * buried in the error structure. This function extracts that value so we can
 * attempt to fix it rather than regenerating from scratch.
 *
 * @param error - The error containing the original generated value
 * @returns The original value that failed validation
 */
const extractOriginalValue = (error: Error): unknown => {
  // Check if error has a cause with the value
  if (!error.cause || typeof error.cause !== 'object') {
    return (error as unknown as Record<string, unknown>).value;
  }

  const cause = error.cause as Record<string, unknown>;
  // Try to find value in cause first, then fallback to error itself
  return cause.value ?? (error as unknown as Record<string, unknown>).value;
};

/**
 * Builds parameters for original retry with optional error context
 *
 * Modifies the original generateObject parameters to include information about
 * the previous failure. This helps the AI understand what went wrong and
 * potentially avoid the same error on retry.
 *
 * @param options - Configuration object containing all parameters
 * @returns Modified parameters with error context added
 */
function buildParamsForOriginalRetry(options: {
  baseParams: Parameters<typeof generateObject>[0];
  error: Error;
  attachErrorInfoToOriginalRetry?: boolean;
  context?: string;
}): Parameters<typeof generateObject>[0] {
  const { baseParams, error: err, attachErrorInfoToOriginalRetry, context } = options;
  // If error attachment is disabled, return original parameters unchanged
  if (!(attachErrorInfoToOriginalRetry ?? true)) {
    return baseParams;
  }

  // Build a helpful error context message for the AI
  const errorNote =
    `Previous attempt${context ? ` during ${context}` : ''} failed with ${err.name}: ${err.message}. ` +
    `Produce a valid JSON object strictly following the provided schema; do not include extraneous fields.`;

  const params = baseParams as Record<string, unknown>;

  // Strategy 1: Append to existing prompt string
  if (typeof params.prompt === 'string') {
    return {
      ...baseParams,
      prompt: `${params.prompt}\n\nNote: ${errorNote}`,
    } as Parameters<typeof generateObject>[0];
  }

  // Strategy 2: Add system message to message array
  if (Array.isArray(params.messages)) {
    return {
      ...baseParams,
      messages: [{ role: 'system', content: `Retry context: ${errorNote}` }, ...params.messages],
    } as Parameters<typeof generateObject>[0];
  }

  // If neither prompt nor messages format, return unchanged
  return baseParams;
}

/**
 * Generic retry mechanism for fixing Zod schema validation errors
 *
 * This is the core "schema fixing" strategy. When an AI-generated object fails
 * Zod validation, this function uses a separate AI call specifically designed
 * to fix validation errors while preserving the original content intent.
 *
 * @param options - Configuration object containing all parameters
 * @returns Promise<T> - The fixed object conforming to the schema
 */
export async function retryWithSchemaFix<T>(options: {
  originalObject: unknown;
  zodError: ZodError;
  expectedSchema: z.ZodType<T>;
  context?: string;
  schemaDescription?: string;
}): Promise<T> {
  const { originalObject, zodError, expectedSchema, context } = options;
  try {
    // Log the attempt with error details for debugging
    logger.info('Attempting to fix schema validation error with AI', {
      context,
      errorCount: zodError.issues.length,
      zodErrors: extractZodErrorDetails(zodError),
    });

    // Get centralized schema fixing prompts from prompts service
    const { systemPrompt, generateUserPrompt } = getSchemaFixingPrompts();

    const userPrompt = generateUserPrompt(extractZodErrorDetails(zodError), originalObject);

    // Call AI to fix the object - using GPT-5 Nano for speed and cost efficiency
    const result = await generateObject({
      model: openai(OpenAIModel.GPT_5_NANO),
      schema: FixedObjectSchema,
      system: systemPrompt,
      prompt: userPrompt,
      providerOptions: {
        openai: {
          reasoningEffort: 'low', // Fast generation for fixing
          textVerbosity: 'low', // Concise responses
        },
      },
    });

    // Validate the AI-fixed object against the original expected schema
    // This ensures the fix actually resolves the validation issues
    const parsedResult = expectedSchema.parse(result.object.fixedObject);

    logger.info('Successfully fixed schema validation error', {
      context,
      explanation: result.object.explanation,
      originalErrorCount: zodError.issues.length,
    });

    return parsedResult;
  } catch (retryError) {
    // If the fix attempt fails, log the error and re-throw the original
    logger.error('Failed to fix schema validation error with AI retry', {
      context,
      originalError: zodError.message,
      retryError: retryError instanceof Error ? retryError.message : 'Unknown error',
    });

    // Re-throw the original error since the retry failed
    throw zodError;
  }
}

/**
 * Wrapper for generateObject that automatically retries with schema fixing on Zod errors
 *
 * This is the main entry point for the retry system. It attempts the original generation,
 * and if that fails with validation errors, it automatically tries to fix them using
 * the appropriate retry strategy.
 *
 * Retry strategies:
 * 1. Schema fixing: For Zod validation errors, extract the problematic object and fix it
 * 2. Original retry: For other errors, retry with the original prompt plus error context
 *
 * @param options - Configuration object containing all parameters
 * @returns Promise<{ object: T }> - The generated and validated object
 */
export async function generateObjectWithRetry<T>(options: {
  params: Parameters<typeof generateObject>[0] & { schema: z.ZodType<T> };
  context?: string;
  maxRetries?: number; // Maximum number of schema-fix retry attempts (default: 3)
  allowOriginalRetryOnUnknownError?: boolean; // Enable original retry for non-Zod errors (default: false)
  originalRetryAttempts?: number; // How many original retries to attempt (default: 1)
  attachErrorInfoToOriginalRetry?: boolean; // Add error context to retry prompts (default: true)
}): Promise<{ object: T }> {
  const {
    params,
    context,
    maxRetries = 3,
    allowOriginalRetryOnUnknownError = false,
    originalRetryAttempts = 1,
    attachErrorInfoToOriginalRetry = true,
  } = options;

  // Strategy 1: Try original generation once
  try {
    const result = await generateObject(params);
    return { object: result.object as T };
  } catch (error) {
    // Strategy 2: If Zod error, try schema fixing maxRetries times
    if (error instanceof Error && error.name === 'AI_NoObjectGeneratedError') {
      const zodError = extractZodError(error);
      const originalValue = extractOriginalValue(error);

      if (zodError) {
        logger.warn('Schema validation failed, attempting schema fixing', {
          context,
          errorCount: zodError.issues.length,
          maxRetries,
        });

        // Try schema fixing up to maxRetries times
        for (let attempt = 0; attempt < maxRetries; attempt++) {
          try {
            logger.warn(`Schema fix attempt ${attempt + 1}/${maxRetries}`, {
              context,
              errorCount: zodError.issues.length,
            });

            const fixedObject = await retryWithSchemaFix({
              originalObject: originalValue,
              zodError,
              expectedSchema: params.schema,
              context,
            });

            logger.info(`Schema fixing succeeded on attempt ${attempt + 1}`, { context });
            return { object: fixedObject };
          } catch (retryError) {
            logger.warn(`Schema fix attempt ${attempt + 1} failed`, {
              context,
              retryError: retryError instanceof Error ? retryError.message : 'Unknown error',
            });
            // Continue to next schema fix attempt
          }
        }
      }
    }

    // Strategy 3: Try original retry for non-Zod errors or when schema fixing is not applicable
    if (allowOriginalRetryOnUnknownError && originalRetryAttempts > 0) {
      logger.warn('Attempting original retry strategy', {
        context,
        attempts: originalRetryAttempts,
        errorName: (error as Error).name,
        errorMessage: (error as Error).message,
      });

      for (let attempt = 0; attempt < originalRetryAttempts; attempt++) {
        try {
          logger.warn(`Original retry attempt ${attempt + 1}/${originalRetryAttempts}`, {
            context,
            errorName: (error as Error).name,
          });

          // Build retry parameters with optional error context
          const retryParams = buildParamsForOriginalRetry({
            baseParams: params,
            error: error as Error,
            attachErrorInfoToOriginalRetry,
            context,
          });

          const result = await generateObject(retryParams);
          logger.info(`Original retry succeeded on attempt ${attempt + 1}`, { context });
          return { object: result.object as T };
        } catch (retryError) {
          logger.warn(`Original retry attempt ${attempt + 1} failed`, {
            context,
            retryError: retryError instanceof Error ? retryError.message : 'Unknown error',
          });
          // Continue to next original retry attempt
        }
      }
    } else {
      logger.info('Original retry disabled or no attempts configured', {
        context,
        allowOriginalRetryOnUnknownError,
        originalRetryAttempts,
      });
    }

    // All strategies exhausted, throw the original error
    throw error;
  }
}
