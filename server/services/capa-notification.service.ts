import { TZDate } from '@date-fns/tz';
import { getCompany } from '@server/services/company.service';
import { EmailUser, sendEmail } from '@server/services/email.service';
import { createNotificationForUsers } from '@server/services/notification.service';
import { addJobToQueue } from '@server/queue/queue-utils';
import { QUEUE_JOB_NAMES } from '@server/queue/job-names';
import { getEventById } from '@server/services/event.service';
import { getLocationById } from '@server/services/location.service';
import { getUsersPublic } from '@server/services/user.service';
import { CapaAssignedTemplateParams } from '@server/templates/capa-assigned';
import CapaOverdueTemplate, { CapaOverdueTemplateParams } from '@server/templates/capa-overdue';
import { CapaUpdateTemplateParams } from '@server/templates/capa-update';
import { ROUTES } from '@shared/ROUTE_PATHS';
import {
  AlertNotificationJobPayload,
  CapaAssignedNotificationJobPayload,
  CapaUpdateNotificationJobPayload,
  EmailJobPayload,
} from '@shared/types/queues.types';
import { env } from 'env';
import React from 'react';
import { formatDate } from '@shared/date-utils';

export const sendCapaOverdueNotification = async ({
  capa,
  toUsers,
  toUsersIds,
}: {
  capa: CapaOverdueTemplateParams;
  toUsers: EmailUser[];
  toUsersIds: string[];
}) => {
  const template = React.createElement(CapaOverdueTemplate, {
    ...capa,
    capaUrl: `${env.EHS_URL}${ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id)}`,
  });

  await sendEmail(template, {
    to: toUsers,
    subject: `CAPA Overdue: ${capa.slug} - ${capa.title}`,
  });

  // Create notification record for all users who were notified
  if (toUsersIds.length > 0) {
    await createNotificationForUsers({
      upkeepCompanyId: capa.upkeepCompanyId,
      userIds: toUsersIds,
      title: 'CAPA Overdue',
      description: `${capa.slug} is overdue since ${formatDate(capa.dueDate, true)}.`,
      url: ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id),
    });
  }
};

export const sendCapaAssignedNotification = async ({
  capa,
  user,
  headers,
  needPartialCheck,
}: CapaAssignedNotificationJobPayload) => {
  const teamMembers =
    capa?.teamMembersToNotify && capa?.teamMembersToNotify?.length > 0 ? capa?.teamMembersToNotify : [];

  const allUserIds = teamMembers.concat(capa.ownerId!);
  const toNotifyUserIds = [...new Set(allUserIds)];

  const [location, event, usersToNotify, { clientTimezone }] = await Promise.all([
    capa?.locationId ? getLocationById(capa?.locationId, headers) : undefined,
    capa?.eventId ? getEventById(capa?.eventId, user, needPartialCheck) : undefined,
    getUsersPublic({ upkeepCompanyId: user.upkeepCompanyId, objectId: toNotifyUserIds }),
    getCompany(headers),
  ]);

  const owner = usersToNotify.result.find((u) => u.id === capa?.ownerId);
  const timestamp = clientTimezone && capa.dueDate ? new TZDate(capa.dueDate, clientTimezone) : null;

  await sendCapaAssignedNotificationEmail({
    capa: {
      ...capa,
      dueDate: timestamp,
      linkedEvent: event
        ? {
            title: event?.title,
            slug: event?.slug!,
            url: `${env.EHS_URL}${ROUTES.BUILD_EVENT_DETAILS_PATH(event?.id)}`,
          }
        : undefined,
      owner: {
        email: owner?.email,
        fullName: owner?.fullName,
      },
      location: location,
      capaUrl: `${env.EHS_URL}${ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id!)}`,
    },
    toUsers: [
      { email: user.email, fullName: user.fullName, type: 'to' },
      ...usersToNotify.result
        .filter((u) => u.email && u.fullName)
        .map((u) => ({
          email: u.email!,
          fullName: u.fullName!,
          type: 'to' as const,
        })),
    ],
  });

  // Create notification record for all users who were notified
  const allUsersToNotify = [user, ...usersToNotify.result];
  const uniqueUsersToNotify = [...new Map(allUsersToNotify.map((u) => [u.id, u])).values()];
  const userIdsToNotify = uniqueUsersToNotify.map((u) => u.id);

  if (userIdsToNotify.length > 0) {
    // Create alert notification job payload
    const alertJobPayload: AlertNotificationJobPayload = {
      upkeepCompanyId: user.upkeepCompanyId,
      userIds: userIdsToNotify,
      title: 'CAPA Assigned',
      description: `CAPA "${capa.title}" (${capa.slug}) has been assigned${location ? ` at ${location.name}` : ''}.`,
      url: ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id),
    };

    // Generate unique job ID based on CAPA ID and due date
    const jobId = `capa-${capa.id}-assigned-notification-${Date.now()}`;

    addJobToQueue(QUEUE_JOB_NAMES.SEND_ALERT_NOTIFICATION, alertJobPayload, { jobId });
  }
};

export const sendCapaAssignedNotificationEmail = async ({
  capa,
  toUsers,
}: {
  capa: CapaAssignedTemplateParams;
  toUsers: EmailUser[];
}) => {
  // Queue email sending
  const emailJobPayload: EmailJobPayload = {
    templateType: 'capa-assigned',
    templateProps: {
      ...capa,
      capaUrl: `${env.EHS_URL}${ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id)}`,
    },
    options: {
      to: toUsers,
      subject: `CAPA Assigned: ${capa.slug} - ${capa.title}`,
    },
  };

  const emailJobId = `capa-${capa.id}-assigned-email-${Date.now()}`;
  addJobToQueue(QUEUE_JOB_NAMES.SEND_EMAIL, emailJobPayload, { jobId: emailJobId });
};

export const sendCapaUpdatedNotification = async ({
  capa,
  toUsers,
  actionPrefix = 'Updated',
}: {
  capa: CapaUpdateTemplateParams;
  toUsers: EmailUser[];
  actionPrefix?: string;
}) => {
  // Queue email sending
  const emailJobPayload: EmailJobPayload = {
    templateType: 'capa-update',
    templateProps: {
      ...capa,
      capaUrl: `${env.EHS_URL}${ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id)}`,
    },
    options: {
      to: toUsers,
      subject: `CAPA ${actionPrefix}: ${capa.slug} - ${capa.title}`,
    },
  };

  const emailJobId = `capa-${capa.id}-update-email-${Date.now()}`;
  addJobToQueue(QUEUE_JOB_NAMES.SEND_EMAIL, emailJobPayload, { jobId: emailJobId });
};

export const sendCapaUpdateNotification = async ({
  capa,
  user,
  headers,
  needPartialCheck,
  actionPrefix,
}: CapaUpdateNotificationJobPayload) => {
  const teamMembers =
    capa?.teamMembersToNotify && capa?.teamMembersToNotify?.length > 0 ? capa?.teamMembersToNotify : [];

  const allUserIds = teamMembers.concat(capa.ownerId!);
  const toNotifyUserIds = [...new Set(allUserIds)];

  const [location, event, usersToNotify, { clientTimezone }] = await Promise.all([
    capa?.locationId ? getLocationById(capa?.locationId, headers) : undefined,
    capa?.eventId ? getEventById(capa?.eventId, user, needPartialCheck) : undefined,
    getUsersPublic({ upkeepCompanyId: user.upkeepCompanyId, objectId: toNotifyUserIds }),
    getCompany(headers),
  ]);

  const owner = usersToNotify.result.find((user) => user.id === capa?.ownerId);
  const timestamp = clientTimezone && capa.dueDate ? new TZDate(capa.dueDate, clientTimezone) : null;

  await sendCapaUpdatedNotification({
    capa: {
      ...capa,
      dueDate: timestamp,
      linkedEvent: event
        ? {
            title: event?.title,
            slug: event?.slug!,
            url: `${env.EHS_URL}${ROUTES.BUILD_EVENT_DETAILS_PATH(event?.id)}`,
          }
        : undefined,
      owner: {
        email: owner?.email,
        fullName: owner?.fullName,
      },
      updatedBy: {
        email: user.email,
        fullName: user.fullName,
      },
      location: location,
      capaUrl: `${env.EHS_URL}${ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id!)}`,
    },
    toUsers: [
      { email: user.email, fullName: user.fullName, type: 'to' },
      ...usersToNotify.result
        .filter((user) => user.email && user.fullName)
        .map((user) => ({
          email: user.email!,
          fullName: user.fullName!,
          type: 'to' as const,
        })),
    ],
    actionPrefix,
  });

  // Create notification record for all users who were notified
  const allUpdateNotificationUsers = [user, ...usersToNotify.result];
  const uniqueUpdateNotificationUsers = [...new Map(allUpdateNotificationUsers.map((u) => [u.id, u])).values()];
  const updateNotificationUserIds = uniqueUpdateNotificationUsers.map((u) => u.id);

  if (updateNotificationUserIds.length > 0) {
    // Create alert notification job payload
    const alertJobPayload: AlertNotificationJobPayload = {
      upkeepCompanyId: user.upkeepCompanyId,
      userIds: updateNotificationUserIds,
      title: `CAPA ${actionPrefix || 'Updated'}`,
      description: `CAPA "${capa.title}" (${capa.slug}) has been ${(actionPrefix || 'Updated').toLowerCase()}${location ? ` at ${location.name}` : ''}.`,
      url: ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id),
    };

    // Generate unique job ID based on CAPA ID and due date
    const jobId = `capa-${capa.id}-${actionPrefix || 'update'}-notification-${Date.now()}`;

    addJobToQueue(QUEUE_JOB_NAMES.SEND_ALERT_NOTIFICATION, alertJobPayload, { jobId });
  }
};
