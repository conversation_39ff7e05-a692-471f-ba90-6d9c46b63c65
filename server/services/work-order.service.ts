import api, { buildDefaultHeaders, type Headers } from '@server/utils/api';
import { UpkeepAsset } from '@shared/types/assets.types';
import { PaginatedResponse, RawParseObject } from '@shared/types/schema.types';
import {
  type CountWorkOrdersByCapaIdInput,
  type CreateWorkOrderFromCapaInput,
  type CreateWorkOrderParams,
  type WorkOrderSearchInput,
  type WorkOrder,
  convertNumberToPriority,
  convertPriorityToNumber,
} from '@shared/types/work-orders.types';
import { TRPCError } from '@trpc/server';

type RawWorkOrderFromApi = {
  id: string;
  workOrderNumber: string;
  mainDescription: string;
  currentStatus: string;
  priorityNumber: number;
  dueDate: string;
  objectLocationForWorkOrder: RawParseObject;
  objectAsset: UpkeepAsset;
  userAssignedTo: RawParseObject;
  capaId?: string;
};

export const createWorkOrder = async (params: CreateWorkOrderParams, headers: Headers): Promise<boolean> => {
  const response = await api.post('/api/v1/work-orders/create/wrapper', params, {
    headers: buildDefaultHeaders(headers),
  });

  return response.data.success;
};

export const searchWorkOrdersByCapaId = async (
  params: { capaId: string[]; limit: number; offset: number; sort?: string },
  headers: Headers,
): Promise<WorkOrder[]> => {
  const requestBody = {
    limit: params.limit,
    offset: params.offset,
    sort: params.sort || 'createdAt DESC',
    capaId: params.capaId,
  };

  const response = await api.post('/api/v1/work-orders/search', requestBody, {
    headers: buildDefaultHeaders(headers),
  });

  if (!response.data?.success || !response.data?.results) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: 'Failed to search work orders',
    });
  }

  const apiData = response.data.results;

  // Transform the response to match our simplified WorkOrder type
  return apiData.map((workOrder: RawWorkOrderFromApi) => ({
    id: workOrder.id,
    workOrderNumber: workOrder.workOrderNumber,
    title: workOrder.mainDescription,
    currentStatus: workOrder.currentStatus,
    priority: convertNumberToPriority(workOrder.priorityNumber),
    dueDate: workOrder.dueDate,
    assignedTo: workOrder?.userAssignedTo?.objectId,
    locationId: workOrder?.objectLocationForWorkOrder?.objectId,
    assetId: workOrder?.objectAsset?.id,
    assetName: workOrder?.objectAsset?.Name,
  }));
};

/**
 * Fetch all work orders related to a CAPA with automatic pagination
 * @param capaId The CAPA ID to fetch work orders for
 * @param headers Request headers
 * @param sort Optional sort parameter (defaults to 'createdAt DESC')
 * @returns Array of all work orders related to the CAPA
 */
export const getAllWorkOrdersByCapaId = async (
  capaId: string,
  headers: Headers,
  sort?: string,
): Promise<WorkOrder[]> => {
  const allWorkOrders: WorkOrder[] = [];
  let offset = 0;
  const limit = 50;
  let hasMore = true;

  while (hasMore) {
    const workOrdersBatch = await searchWorkOrdersByCapaId(
      { capaId: [capaId], limit: limit + 1, offset, sort }, // +1 to check if there are more
      headers,
    );

    // Check if we got more results than requested (indicating more pages available)
    hasMore = workOrdersBatch.length > limit;
    const actualResults = hasMore ? workOrdersBatch.slice(0, limit) : workOrdersBatch;

    allWorkOrders.push(...actualResults);
    offset += limit;
  }

  return allWorkOrders;
};

export const getWorkOrdersCountByCapa = async (
  params: CountWorkOrdersByCapaIdInput,
  headers: Headers,
): Promise<number> => {
  const requestBody = {
    capaId: params.capaId,
  };

  const response = await api.post('/api/v1/work-orders/search/count', requestBody, {
    headers: buildDefaultHeaders(headers),
  });

  if (!response.data?.success) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: 'Failed to count work orders',
    });
  }

  return response.data.result as number;
};

export const createWorkOrderFromCapa = async (
  capaData: CreateWorkOrderFromCapaInput,
  headers: Headers,
): Promise<boolean> => {
  // Validate required fields
  if (
    !capaData.id ||
    !capaData.title ||
    !capaData.slug ||
    !capaData.actionsToAddress ||
    !capaData.priority ||
    !capaData.dueDate
  ) {
    throw new TRPCError({
      code: 'BAD_REQUEST',
      message: 'Missing required fields: id, title, slug, actionsToAddress, priority, and dueDate are mandatory',
    });
  }

  // Map CAPA fields to work order parameters
  const mainDescription = capaData.title;

  const note = `This Work Order was created from ${capaData.slug}.
${capaData.actionsToAddress}`;

  const priorityNumber = convertPriorityToNumber(capaData.priority);

  const workOrderParams: CreateWorkOrderParams = {
    mainDescription,
    note,
    priorityNumber,
    dueDate: capaData.dueDate,
    objectLocationForWorkOrder: capaData.locationId ?? undefined,
    objectAsset: capaData.assetId ?? undefined,
    userAssignedTo: capaData.userAssignedTo,
    capaId: capaData.id,
  };

  // Create the work order
  return await createWorkOrder(workOrderParams, headers);
};

export const getWorkOrders = async (
  params: WorkOrderSearchInput,
  headers: Headers,
): Promise<PaginatedResponse<WorkOrder>> => {
  const { cursor = 0, limit = 50, search, includes, mode, sort } = params;
  const offset = cursor;

  // Request one extra item to determine if there are more results
  const requestBody = {
    includes: includes ?? [],
    limit: limit + 1,
    offset,
    sort: sort,
    mode: mode ?? 'App:All',
    ...(search && { title: search }),
  };

  const response = await api.post('/api/v1/work-orders/search', requestBody, {
    headers: buildDefaultHeaders(headers),
  });

  if (!response.data?.success || !response.data?.results) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: 'Failed to search work orders',
    });
  }

  const apiData = response.data.results;

  // Transform the response to match our WorkOrder type
  const allWorkOrders = apiData.map((workOrder: RawWorkOrderFromApi) => ({
    id: workOrder.id,
    workOrderNumber: workOrder.workOrderNumber,
    title: workOrder.mainDescription,
    currentStatus: workOrder.currentStatus,
    priority: convertNumberToPriority(workOrder.priorityNumber),
    dueDate: workOrder.dueDate,
    assignedTo: workOrder?.userAssignedTo?.objectId,
    locationId: workOrder?.objectLocationForWorkOrder?.objectId,
    assetId: workOrder?.objectAsset?.id,
    assetName: workOrder?.objectAsset?.Name,
    capaId: workOrder?.capaId,
  }));

  // Check if we got more results than requested (indicating more pages available)
  const hasMore = allWorkOrders.length > limit;
  const workOrders = hasMore ? allWorkOrders.slice(0, limit) : allWorkOrders;

  return {
    noResults: workOrders.length === 0,
    result: workOrders,
    nextCursor: hasMore ? cursor + limit : undefined,
  };
};

export const assignCapa = async (workOrderId: string, capaId: string, headers: Headers): Promise<boolean> => {
  const requestBody = {
    capaId: capaId || '',
    sendNotification: false,
  };

  const response = await api.patch(`/api/v1/work-orders/${workOrderId}`, requestBody, {
    headers: buildDefaultHeaders(headers),
  });

  return response.data.success;
};
