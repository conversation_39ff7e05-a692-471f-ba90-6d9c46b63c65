import { bulkCreateControlMeasures } from '@server/services/control-measures.service';
import { bulkCreateHazards } from '@server/services/hazards.service';
import { CreateJhaType, UpdateJhaType } from '@shared/types/jha.types';
import { ControlMeasuresCreateSchema, HazardsCreateSchema } from '@shared/types/settings.types';
import { User } from '@shared/types/users.types';
import { z } from 'zod';

import { db } from '@server/db';

export const processJhaResult = async <T extends CreateJhaType | UpdateJhaType>(
  result: T,
  user: User,
  tx?: Parameters<Parameters<typeof db.transaction>[0]>[0],
) => {
  // Collect unique hazards and control measures to create
  const hazardsToCreate = new Map<string, z.infer<typeof HazardsCreateSchema>>();
  const controlMeasuresToCreate = new Map<string, z.infer<typeof ControlMeasuresCreateSchema>>();

  for (const step of result.steps) {
    step.hazardsToCreate?.forEach((hazard) => {
      const parsed = HazardsCreateSchema.parse(hazard);
      hazardsToCreate.set(`${parsed.name}|${parsed.type}`, parsed);
    });
    step.controlMeasuresToCreate?.forEach((controlMeasure) => {
      const parsed = ControlMeasuresCreateSchema.parse(controlMeasure);
      controlMeasuresToCreate.set(`${parsed.name}|${parsed.type}`, parsed);
    });
  }

  // Bulk create unique hazards and control measures
  const [createdHazards, createdControlMeasures] = await Promise.all([
    hazardsToCreate.size > 0 ? bulkCreateHazards(Array.from(hazardsToCreate.values()), user, tx) : [],
    controlMeasuresToCreate.size > 0
      ? bulkCreateControlMeasures(Array.from(controlMeasuresToCreate.values()), user, tx)
      : [],
  ]);

  // Index created entities for quick lookup
  const hazardIndex = new Map<string, string>();
  for (const hazard of createdHazards) {
    hazardIndex.set(`${hazard.name}|${hazard.type}`, hazard.id);
  }

  const controlMeasureIndex = new Map<string, string>();
  for (const controlMeasure of createdControlMeasures) {
    controlMeasureIndex.set(`${controlMeasure.name}|${controlMeasure.type}`, controlMeasure.id);
  }

  // Map created IDs back into the steps and clear creation arrays
  const steps = result.steps.map((step) => {
    const createdHazardIds =
      step.hazardsToCreate
        ?.map((hazard) => hazardIndex.get(`${hazard.name}|${hazard.type}`))
        .filter((id): id is string => Boolean(id)) ?? [];

    const createdControlMeasureIds =
      step.controlMeasuresToCreate
        ?.map((controlMeasure) => controlMeasureIndex.get(`${controlMeasure.name}|${controlMeasure.type}`))
        .filter((id): id is string => Boolean(id)) ?? [];

    return {
      ...step,
      hazardIds: [...(step.hazardIds ?? []), ...createdHazardIds],
      controlMeasureIds: [...(step.controlMeasureIds ?? []), ...createdControlMeasureIds],
      hazardsToCreate: undefined,
      controlMeasuresToCreate: undefined,
    };
  });

  return {
    ...result,
    steps,
  };
};
