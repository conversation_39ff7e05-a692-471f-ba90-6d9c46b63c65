import { TZDate } from '@date-fns/tz';
import { getCompany } from '@server/services/company.service';
import { EmailUser } from '@server/services/email.service';
import { addJobToQueue } from '@server/queue/queue-utils';
import { QUEUE_JOB_NAMES } from '@server/queue/job-names';
import { getUsersPublic } from '@server/services/user.service';
import { CommentMentionTemplateParams } from '@server/templates/comment-mention';
import { ROUTES } from '@shared/ROUTE_PATHS';
import {
  AlertNotificationJobPayload,
  CommentMentionNotificationJobPayload,
  EmailJobPayload,
} from '@shared/types/queues.types';
import { env } from 'env';

export const sendCommentMentionNotification = async ({
  newComment,
  savedMentions,
  user,
  headers,
  input,
}: CommentMentionNotificationJobPayload) => {
  const routeBuilder = {
    event: ROUTES.BUILD_EVENT_DETAILS_PATH,
    capa: ROUTES.BUILD_CAPA_DETAILS_PATH,
  };

  const toUserIds = savedMentions?.map((mention) => mention.userId) || [];

  const [users, { clientTimezone }] = await Promise.all([
    getUsersPublic({
      upkeepCompanyId: newComment.upkeepCompanyId,
      objectId: toUserIds,
    }),
    getCompany(headers),
  ]);

  const timestamp = clientTimezone ? new TZDate(newComment.createdAt, clientTimezone) : newComment.createdAt;

  const updatedContent = newComment.content.replace(/@(\w+)/g, (_, userId) => {
    const foundUser = users.result.find((u) => u.id === userId);
    return foundUser ? `@${foundUser.fullName}` : `@${userId}`; // fallback to original if not found
  });

  const data: CommentMentionTemplateParams = {
    ...input,
    entityUrl: `${env.EHS_URL}${routeBuilder[input.entityType](input.entityId)}`,
    timestamp,
    reporter: {
      fullName: user.fullName,
      email: user.email,
    },
    content: updatedContent,
    users: users.result,
  };

  const toUsers: EmailUser[] = users.result
    .filter((user) => user.email && user.fullName)
    .map((user) => ({
      email: user.email!,
      fullName: user.fullName!,
      type: 'to',
    }));

  let contentWithNames = data.content;
  if (toUsers && toUsers.length > 0) {
    for (const user of toUsers) {
      if (user && user.fullName) {
        contentWithNames = contentWithNames.replace(`@${user.id}`, `@${user.fullName}`);
      }
    }
  }

  // Queue email sending
  const emailJobPayload: EmailJobPayload = {
    templateType: 'comment-mention',
    templateProps: {
      ...data,
      content: contentWithNames,
      taggedUser: toUsers.map((user) => user.fullName).join(', '),
    },
    options: {
      to: toUsers,
      subject: `You've been tagged in a comment: ${data?.entitySlug} – ${data?.entityTitle}`,
    },
  };

  const emailJobId = `comment-${newComment.id}-mention-email-${Date.now()}`;
  addJobToQueue(QUEUE_JOB_NAMES.SEND_EMAIL, emailJobPayload, { jobId: emailJobId });

  // Create notification record for all users who were notified
  const userIdsToNotify = users.result.map((u: { id: string }) => u.id);

  if (userIdsToNotify.length > 0) {
    // Create alert notification job payload
    const alertJobPayload: AlertNotificationJobPayload = {
      upkeepCompanyId: user.upkeepCompanyId,
      userIds: userIdsToNotify,
      title: 'Comment Mention',
      description: `You've been mentioned in a comment on "${data?.entityTitle}" (${data?.entitySlug}).`,
      url: data.entityUrl,
    };

    // Generate unique job ID based on comment ID and created date
    const jobId = `comment-${newComment.id}-mention-notification-${Date.now()}`;

    addJobToQueue(QUEUE_JOB_NAMES.SEND_ALERT_NOTIFICATION, alertJobPayload, { jobId });
  }
};
