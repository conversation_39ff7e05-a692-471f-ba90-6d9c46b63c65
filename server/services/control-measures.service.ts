import { db } from '@server/db';
import { createPaginatedResponse } from '@server/utils/pagination';
import { auditTrail, controlMeasures } from '@shared/schema';
import {
  BulkControlMeasuresCreateSchema,
  ControlMeasuresCreateSchema,
  ControlMeasuresUpdateSchema,
  ExportControlMeasuresSchema,
  ListControlMeasuresSchema,
} from '@shared/types/settings.types';
import { User } from '@shared/types/users.types';
import { endOfDay, startOfDay } from 'date-fns';
import { and, desc, eq, gte, ilike, inArray, isNull, lte, sql } from 'drizzle-orm';
import { z } from 'zod';

export const createControlMeasure = async (input: z.infer<typeof ControlMeasuresCreateSchema>, user: User) => {
  return db.transaction(async (tx) => {
    const [controlMeasure] = await tx
      .insert(controlMeasures)
      .values({
        ...input,
        upkeepCompanyId: user.upkeepCompanyId,
        createdBy: user.id,
      })
      .returning();

    if (!controlMeasure) {
      return;
    }

    return controlMeasure;
  });
};

export const updateControlMeasure = async (input: z.infer<typeof ControlMeasuresUpdateSchema>, _: User) => {
  return db.transaction(async (tx) => {
    const [controlMeasure] = await tx
      .update(controlMeasures)
      .set({ ...input, updatedAt: new Date() })
      .where(eq(controlMeasures.id, input.id!))
      .returning();
    return controlMeasure;
  });
};

export const getControlMeasures = async (user: User) => {
  return db
    .select()
    .from(controlMeasures)
    .where(and(eq(controlMeasures.upkeepCompanyId, user.upkeepCompanyId), isNull(controlMeasures.archivedAt)));
};

export const listControlMeasures = async (input: z.infer<typeof ListControlMeasuresSchema>, user: User) => {
  const {
    cursor = 0,
    limit = 10,
    search,
    type = [],
    createdBy = [],
    includeArchived = false,
    createdDateRange,
    mustIncludeObjectIds,
  } = input;

  let query = db
    .select({
      id: controlMeasures.id,
      name: controlMeasures.name,
      type: controlMeasures.type,
      upkeepCompanyId: controlMeasures.upkeepCompanyId,
      createdBy: controlMeasures.createdBy,
      createdAt: controlMeasures.createdAt,
      updatedAt: controlMeasures.updatedAt,
      archivedAt: controlMeasures.archivedAt,
    })
    .from(controlMeasures)
    .where(
      and(
        eq(controlMeasures.upkeepCompanyId, user.upkeepCompanyId),
        includeArchived ? undefined : isNull(controlMeasures.archivedAt),
        search ? ilike(controlMeasures.name, `%${search}%`) : undefined,
        type.length > 0 ? inArray(controlMeasures.type, type) : undefined,
        createdBy.length > 0 ? inArray(controlMeasures.createdBy, createdBy) : undefined,
        createdDateRange?.from ? gte(controlMeasures.createdAt, startOfDay(createdDateRange.from)) : undefined,
        createdDateRange?.to ? lte(controlMeasures.createdAt, endOfDay(createdDateRange.to)) : undefined,
      ),
    )
    .$dynamic();

  if (mustIncludeObjectIds && mustIncludeObjectIds.length > 0) {
    query = query.orderBy(
      sql`CASE WHEN ${controlMeasures.id} IN (${mustIncludeObjectIds}) THEN 0 ELSE 1 END`,
      desc(controlMeasures.createdAt),
    );
  } else {
    query = query.orderBy(desc(controlMeasures.createdAt));
  }

  const filtered = await query.limit(limit).offset(cursor);

  return createPaginatedResponse(filtered, { cursor, limit });
};

export const toggleArchive = async (id: string, user: User) => {
  return db.transaction(async (tx) => {
    const [controlMeasure] = await tx
      .update(controlMeasures)
      .set({ archivedAt: sql`CASE WHEN ${controlMeasures.archivedAt} IS NULL THEN NOW() ELSE NULL END` })
      .where(eq(controlMeasures.id, id))
      .returning({ archivedAt: controlMeasures.archivedAt });

    if (!controlMeasure) {
      return;
    }

    const archivedAt = controlMeasure.archivedAt;
    const action = archivedAt ? 'archived' : 'unarchived';

    await tx.insert(auditTrail).values({
      entityType: 'control_measure',
      entityId: id,
      action,
      details: JSON.stringify(controlMeasure),
      upkeepCompanyId: user.upkeepCompanyId,
    });
  });
};

export const bulkCreateControlMeasures = async (
  input: z.infer<typeof BulkControlMeasuresCreateSchema>,
  user: User,
  tx?: Parameters<Parameters<typeof db.transaction>[0]>[0],
) => {
  const createControlMeasures = async (transaction: typeof tx) => {
    const controlMeasureToCreate = input.map((controlMeasure) => ({
      ...controlMeasure,
      upkeepCompanyId: user.upkeepCompanyId,
      createdBy: user.id,
    }));

    const results = await transaction!
      .insert(controlMeasures)
      .values(controlMeasureToCreate)
      .onConflictDoUpdate({
        target: [controlMeasures.name, controlMeasures.type, controlMeasures.upkeepCompanyId],
        set: {
          updatedAt: new Date(),
          archivedAt: null, // Unarchive if it was archived
        },
      })
      .returning();

    return results;
  };

  if (tx) {
    return createControlMeasures(tx);
  }

  return db.transaction(createControlMeasures);
};

export const exportControlMeasures = async (input: z.infer<typeof ExportControlMeasuresSchema>, user: User) => {
  const { search, includeArchived, type = [], createdBy = [], createdDateRange } = input;

  return await db
    .select()
    .from(controlMeasures)
    .where(
      and(
        eq(controlMeasures.upkeepCompanyId, user.upkeepCompanyId),
        includeArchived ? undefined : isNull(controlMeasures.archivedAt),
        search ? ilike(controlMeasures.name, `%${search}%`) : undefined,
        type.length > 0 ? inArray(controlMeasures.type, type) : undefined,
        createdBy.length > 0 ? inArray(controlMeasures.createdBy, createdBy) : undefined,
        createdDateRange?.from ? gte(controlMeasures.createdAt, startOfDay(createdDateRange.from)) : undefined,
        createdDateRange?.to ? lte(controlMeasures.createdAt, endOfDay(createdDateRange.to)) : undefined,
      ),
    )
    .limit(500);
};
