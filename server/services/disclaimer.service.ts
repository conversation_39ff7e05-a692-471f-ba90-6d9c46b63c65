import { db } from '@server/db';
import { disclaimerTrail } from '@shared/schema';
import type { User } from '@shared/types/users.types';

export const logAiDisclaimerAcceptance = async ({
  entityType,
  entityId,
  user,
  ipAddress,
}: {
  entityType: string;
  entityId: string;
  user: User;
  ipAddress: string;
}) => {
  const data = {
    accepted: true,
    accepted_at: new Date().toISOString(),
    version: 'v1.0',
    ip_address: ipAddress,
  };

  await db.insert(disclaimerTrail).values({
    upkeepCompanyId: user.upkeepCompanyId,
    entityType,
    entityId,
    type: 'ai_content_disclaimer',
    data,
    acceptedBy: user.id,
  });
};
