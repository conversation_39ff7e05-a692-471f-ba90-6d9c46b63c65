import { db } from '@server/db';
import { processJhaResult } from '@server/services/jha-processing.service';
import { createPaginatedResponse } from '@server/utils/pagination';
import { logAiDisclaimerAcceptance } from './disclaimer.service';
import {
  auditTrail,
  controlMeasureCategoryEnum,
  hazardCategoryEnum,
  jha,
  approvalStatusEnum,
  auditTrailActionEnum,
  jhaSteps,
} from '@shared/schema';
import {
  CreateJhaFormSchema,
  CreateJhaType,
  GetJhaByInstanceIdSchema,
  ListJhaSchema,
  UpdateJhaFormSchema,
  UpdateJhaStatus,
  UpdateJhaType,
} from '@shared/types/jha.types';
import { UuidSchema } from '@shared/types/schema.types';
import { and, asc, desc, eq, gte, ilike, inArray, isNotNull, isNull, lt, lte, or, sql } from 'drizzle-orm';
import { User } from '@shared/types/users.types';
import { z } from 'zod';
import { formatDate } from '@shared/date-utils';

export type JhaStep = {
  id: string;
  upkeepCompanyId: string;
  jhaId: string;
  serial: number;
  title: string;
  description: string | null;
  hazardIds: string[] | null;
  controlMeasureIds: string[] | null;
  severity: number;
  likelihood: number;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date | null;
  archivedAt: Date | null;
  hazards: Array<{
    id: string;
    upkeepCompanyId: string;
    name: string;
    type: (typeof hazardCategoryEnum.enumValues)[number];
    createdBy: string;
    createdAt: Date;
    updatedAt: Date | null;
    archivedAt: Date | null;
  }>;
  controlMeasures: Array<{
    id: string;
    upkeepCompanyId: string;
    name: string;
    type: (typeof controlMeasureCategoryEnum.enumValues)[number];
    createdBy: string;
    createdAt: Date;
    updatedAt: Date | null;
    archivedAt: Date | null;
  }>;
};

export const createJha = async (
  input: z.infer<typeof CreateJhaFormSchema>,
  user: User,
  ipAddress: string,
  disclaimerAccepted?: boolean,
) => {
  return db.transaction(async (tx) => {
    // Process the JHA result to bulk create hazards and control measures within the transaction
    const processedJhaResult = await processJhaResult<CreateJhaType>(input, user, tx);

    const { jha: jhaInput, steps } = processedJhaResult;

    // calculate highestSeverity based on the steps
    const highestSeverity = steps.reduce((max, step) => {
      return Math.max(max, (step.severity ?? 0) * (step.likelihood ?? 0));
    }, 0);

    // create the jha
    const [jhaData] = await tx
      .insert(jha)
      .values({
        ...jhaInput,
        upkeepCompanyId: user.upkeepCompanyId,
        createdBy: user.id,
        highestSeverity,
      })
      .returning({
        id: jha.id,
        instanceId: jha.instanceId,
        slug: jha.slug,
        version: jha.version,
        status: jha.status,
      });

    if (!jhaData?.id) {
      return;
    }

    // create the steps
    const stepsData = steps.map((step) => ({
      ...step,
      jhaId: jhaData.id,
      upkeepCompanyId: user.upkeepCompanyId,
      createdBy: user.id,
    }));
    await tx.insert(jhaSteps).values(stepsData);

    const action = jhaData.status === approvalStatusEnum.enumValues[1] ? 'in_review' : 'drafted';

    // create audit trail with the data created
    await tx.insert(auditTrail).values({
      entityType: 'jha',
      entityId: jhaData.id,
      details: JSON.stringify(processedJhaResult),
      action,
      userId: user.id,
      upkeepCompanyId: user.upkeepCompanyId,
    });

    if (disclaimerAccepted && jhaData.id) {
      await logAiDisclaimerAcceptance({ entityType: 'jha', entityId: jhaData.id, user, ipAddress });
    }

    return jhaData;
  });
};

export const updateJha = async (input: z.infer<typeof UpdateJhaFormSchema>, user: User) => {
  return db.transaction(async (tx) => {
    // Process the JHA result to bulk create hazards and control measures within the transaction
    const processedJhaResult = await processJhaResult<UpdateJhaType>(input, user, tx);

    const { jha: updatedJha, steps } = processedJhaResult;

    // calculate highestSeverity based on the steps
    const highestSeverity = steps.reduce((max, step) => {
      return Math.max(max, (step.severity ?? 0) * (step.likelihood ?? 0));
    }, 0);

    const updated = await tx
      .update(jha)
      .set({
        ...updatedJha,
        highestSeverity,
      })
      .where(and(eq(jha.id, input.jha.id), eq(jha.upkeepCompanyId, user.upkeepCompanyId)))
      .returning({
        id: jha.id,
        status: jha.status,
      });

    const jhaRecord = updated.at(0);

    if (!jhaRecord) {
      return;
    }

    for (const step of steps) {
      if (step?.id) {
        await tx
          .update(jhaSteps)
          .set({
            ...step,
            jhaId: jhaRecord.id,
          })
          .where(eq(jhaSteps.id, step.id));
      } else {
        await tx.insert(jhaSteps).values({
          ...step,
          jhaId: jhaRecord.id,
          upkeepCompanyId: user.upkeepCompanyId,
          createdBy: user.id,
        });
      }
    }

    await tx.insert(auditTrail).values({
      entityType: 'jha',
      entityId: jhaRecord.id,
      action: 'updated',
      userId: user.id,
      upkeepCompanyId: user.upkeepCompanyId,
      details: JSON.stringify(jhaRecord),
    });

    return jhaRecord;
  });
};

export const getJhaVersions = async (instanceId: string, user: User) => {
  return db
    .select({
      id: jha.id,
      instanceId: jha.instanceId,
      slug: jha.slug,
      version: jha.version,
      title: jha.title,
      status: jha.status,
      reviewDate: jha.reviewDate,
      reasonForRevision: jha.reasonForRevision,
      createdBy: jha.createdBy,
      updatedAt: jha.updatedAt,
    })
    .from(jha)
    .where(and(eq(jha.instanceId, instanceId), eq(jha.upkeepCompanyId, user.upkeepCompanyId)))
    .orderBy(desc(jha.createdAt));
};

export const updateJhaStatus = async (input: UpdateJhaStatus, user: User) => {
  return await db.transaction(async (tx) => {
    const currentDate = formatDate(new Date(), true);
    const updated = await tx
      .update(jha)
      .set({
        status: input.status,
        updatedAt: new Date(),
        notes: input.rejectionReason
          ? sql`COALESCE(${jha.notes}, '') || CASE WHEN COALESCE(${jha.notes}, '') != '' THEN '\n\n' ELSE '' END || '--- REVIEWER COMMENTS --- ' || ${currentDate} || '\n' || ${input.rejectionReason}`
          : undefined,
      })
      .where(and(eq(jha.id, input.id), eq(jha.upkeepCompanyId, user.upkeepCompanyId)))
      .returning({
        id: jha.id,
        instanceId: jha.instanceId,
        slug: jha.slug,
        version: jha.version,
        title: jha.title,
        status: jha.status,
        locationId: jha.locationId,
        assetIds: jha.assetIds,
        createdAt: jha.createdAt,
        updatedAt: jha.updatedAt,
        createdBy: jha.createdBy,
        ownerId: jha.ownerId,
        approverId: jha.approverId,
        upkeepCompanyId: jha.upkeepCompanyId,
        reviewDate: jha.reviewDate,
        notes: jha.notes,
      });

    const jhaRecord = updated.at(0);

    if (!jhaRecord) {
      return;
    }

    const statusMap: Record<string, (typeof auditTrailActionEnum.enumValues)[number]> = {
      [approvalStatusEnum.enumValues[2]]: 'approved',
      [approvalStatusEnum.enumValues[0]]: 'rejected',
      [approvalStatusEnum.enumValues[1]]: 'in_review',
    };

    const action = statusMap[jhaRecord.status] || 'updated';

    await tx.insert(auditTrail).values({
      entityType: 'jha',
      entityId: jhaRecord.id,
      action,
      userId: user.id,
      upkeepCompanyId: user.upkeepCompanyId,
      details: JSON.stringify(jhaRecord),
    });

    return jhaRecord;
  });
};

export const getJhaByInstanceId = async (
  input: z.infer<typeof GetJhaByInstanceIdSchema>,
  user: User,
  needPartialCheck = false,
) => {
  const result = await db
    .select({
      // JHA fields
      id: jha.id,
      upkeepCompanyId: jha.upkeepCompanyId,
      slug: jha.slug,
      version: jha.version,
      instanceId: jha.instanceId,
      title: jha.title,
      ownerId: jha.ownerId,
      approverId: jha.approverId,
      status: jha.status,
      reviewDate: jha.reviewDate,
      locationId: jha.locationId,
      assetIds: jha.assetIds,
      highestSeverity: jha.highestSeverity,
      notes: jha.notes,
      isPublic: jha.isPublic,
      workOrderId: jha.workOrderId,
      createdBy: jha.createdBy,
      createdAt: jha.createdAt,
      updatedAt: jha.updatedAt,
      archivedAt: jha.archivedAt,
      // Aggregated steps with hazards and control measures
      steps: sql<JhaStep[]>`
        COALESCE(
          JSON_AGG(
            CASE 
              WHEN ${jhaSteps.id} IS NOT NULL 
              THEN JSON_BUILD_OBJECT(
                'id', ${jhaSteps.id},
                'upkeepCompanyId', ${jhaSteps.upkeepCompanyId},
                'jhaId', ${jhaSteps.jhaId},
                'serial', ${jhaSteps.serial},
                'title', ${jhaSteps.title},
                'hazardIds', ${jhaSteps.hazardIds},
                'controlMeasureIds', ${jhaSteps.controlMeasureIds},
                'severity', ${jhaSteps.severity},
                'likelihood', ${jhaSteps.likelihood},
                'description', ${jhaSteps.description},
                'createdBy', ${jhaSteps.createdBy},
                'createdAt', ${jhaSteps.createdAt},
                'updatedAt', ${jhaSteps.updatedAt},
                'archivedAt', ${jhaSteps.archivedAt},
                'hazards', COALESCE(
                  (
                    SELECT JSON_AGG(
                      JSON_BUILD_OBJECT(
                        'id', h.id,
                        'upkeepCompanyId', h.upkeep_company_id,
                        'name', h.name,
                        'type', h.type,
                        'createdBy', h.created_by,
                        'createdAt', h.created_at,
                        'updatedAt', h.updated_at,
                        'archivedAt', h.archived_at
                      )
                    )
                    FROM hazards h
                    WHERE h.id = ANY(${jhaSteps.hazardIds})
                      AND h.upkeep_company_id = ${user.upkeepCompanyId}
                      AND h.archived_at IS NULL
                  ),
                  '[]'::json
                ),
                'controlMeasures', COALESCE(
                  (
                    SELECT JSON_AGG(
                      JSON_BUILD_OBJECT(
                        'id', cm.id,
                        'upkeepCompanyId', cm.upkeep_company_id,
                        'name', cm.name,
                        'type', cm.type,
                        'createdBy', cm.created_by,
                        'createdAt', cm.created_at,
                        'updatedAt', cm.updated_at,
                        'archivedAt', cm.archived_at
                      )
                    )
                    FROM control_measures cm
                    WHERE cm.id = ANY(${jhaSteps.controlMeasureIds})
                      AND cm.upkeep_company_id = ${user.upkeepCompanyId}
                      AND cm.archived_at IS NULL
                  ),
                  '[]'::json
                )
              )
              ELSE NULL 
            END 
            ORDER BY ${jhaSteps.serial}
          ) FILTER (WHERE ${jhaSteps.id} IS NOT NULL),
          '[]'::json
        )
      `.as('steps'),
    })
    .from(jha)
    .leftJoin(jhaSteps, and(eq(jha.id, jhaSteps.jhaId), eq(jhaSteps.upkeepCompanyId, user.upkeepCompanyId)))
    .where(
      and(
        eq(jha.upkeepCompanyId, user.upkeepCompanyId),
        needPartialCheck
          ? or(
              eq(jha.ownerId, user.id),
              eq(jha.approverId, user.id),
              eq(jha.createdBy, user.id),
              eq(jha.isPublic, true),
            )
          : undefined,
        eq(jha.instanceId, input.id),
        eq(jha.upkeepCompanyId, user.upkeepCompanyId),
        input.versionId ? eq(jha.id, input.versionId) : undefined,
      ),
    )
    .groupBy(
      jha.id,
      jha.upkeepCompanyId,
      jha.slug,
      jha.version,
      jha.instanceId,
      jha.title,
      jha.ownerId,
      jha.approverId,
      jha.status,
      jha.reviewDate,
      jha.locationId,
      jha.assetIds,
      jha.highestSeverity,
      jha.notes,
      jha.isPublic,
      jha.workOrderId,
      jha.createdBy,
      jha.createdAt,
      jha.updatedAt,
      jha.archivedAt,
    )
    .orderBy(desc(jha.createdAt))
    .limit(1);

  return result.at(0);
};

export const listJhas = async (input: z.infer<typeof ListJhaSchema>, user: User, needPartialCheck = false) => {
  const {
    cursor = 0,
    limit = 10,
    sortBy = 'createdAt',
    sortOrder = 'desc',
    search,
    status = [],
    ownerId = [],
    riskLevel,
    reviewStatus,
    includeArchived,
    locationIds = [],
    mustIncludeObjectIds,
  } = input;

  let query = db
    .select({
      id: jha.id,
      instanceId: jha.instanceId,
      slug: jha.slug,
      title: jha.title,
      ownerId: jha.ownerId,
      status: jha.status,
      reviewDate: jha.reviewDate,
      locationId: jha.locationId,
      assetIds: jha.assetIds,
      highestSeverity: jha.highestSeverity,
      version: jha.version,
      createdAt: jha.createdAt,
      archivedAt: jha.archivedAt,
      stepCount: sql<number>`(
        SELECT COUNT(*)::int 
        FROM jha_steps 
        WHERE jha_steps.jha_id = jha.id 
        AND jha_steps.upkeep_company_id = ${user.upkeepCompanyId}
      )`.as('stepCount'),
      lastRevised: jha.updatedAt,
    })
    .from(jha)
    .where(
      and(
        eq(jha.upkeepCompanyId, user.upkeepCompanyId),
        needPartialCheck
          ? or(
              eq(jha.ownerId, user.id),
              eq(jha.approverId, user.id),
              eq(jha.createdBy, user.id),
              eq(jha.isPublic, true),
            )
          : undefined,
        !includeArchived ? isNull(jha.archivedAt) : undefined,
        search ? or(ilike(jha.title, `%${search}%`), ilike(jha.slug, `%${search}%`)) : undefined,
        status.length > 0 ? inArray(jha.status, status) : undefined,
        ownerId.length > 0 ? inArray(jha.ownerId, ownerId) : undefined,
        locationIds.length > 0 ? inArray(jha.locationId, locationIds) : undefined,
        // Risk level filter based on highestSeverity
        riskLevel === 'high' ? gte(jha.highestSeverity, 15) : undefined,
        riskLevel === 'medium' ? and(gte(jha.highestSeverity, 6), lt(jha.highestSeverity, 15)) : undefined,
        riskLevel === 'low' ? and(gte(jha.highestSeverity, 1), lt(jha.highestSeverity, 6)) : undefined,
        // Review status filter
        reviewStatus === 'overdue' ? lt(jha.reviewDate, new Date()) : undefined,
        reviewStatus === 'due_soon'
          ? and(gte(jha.reviewDate, new Date()), lt(jha.reviewDate, new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)))
          : undefined,
        reviewStatus === 'no_review_date' ? isNull(jha.reviewDate) : undefined,
      ),
    )
    .$dynamic();

  // Handle sorting
  if (mustIncludeObjectIds && mustIncludeObjectIds.length > 0) {
    query = query.orderBy(
      sql`CASE WHEN ${jha.id} IN (${mustIncludeObjectIds}) THEN 0 ELSE 1 END`,
      sortOrder === 'desc' ? desc(jha.createdAt) : asc(jha.createdAt),
    );
  } else {
    switch (sortBy) {
      case 'title':
        query = query.orderBy(sortOrder === 'desc' ? desc(jha.title) : asc(jha.title));
        break;
      case 'highestSeverity':
        query = query.orderBy(sortOrder === 'desc' ? desc(jha.highestSeverity) : asc(jha.highestSeverity));
        break;
      case 'reviewDate':
        query = query.orderBy(
          sortOrder === 'desc' ? desc(jha.reviewDate) : asc(jha.reviewDate),
          desc(jha.createdAt), // Secondary sort
        );
        break;
      case 'createdAt':
      default:
        query = query.orderBy(sortOrder === 'desc' ? desc(jha.createdAt) : asc(jha.createdAt));
        break;
    }
  }

  const filtered = await query.limit(limit).offset(cursor);

  return createPaginatedResponse(filtered, { cursor, limit });
};

export const toggleArchiveJha = async (input: z.infer<typeof UuidSchema>, user: User) => {
  return await db.transaction(async (tx) => {
    const now = new Date().toISOString();
    const updated = await tx
      .update(jha)
      .set({
        archivedAt: sql`CASE WHEN ${jha.archivedAt} IS NULL THEN CAST(${now} AS TIMESTAMP) ELSE NULL END`,
        updatedAt: sql`CAST(${now} AS TIMESTAMP)`,
      })
      .where(and(eq(jha.instanceId, input.id), eq(jha.upkeepCompanyId, user.upkeepCompanyId)))
      .returning({
        id: jha.id,
        title: jha.title,
        archivedAt: jha.archivedAt,
        updatedAt: jha.updatedAt,
      });

    const jhaRecord = updated.at(0);

    if (!jhaRecord) {
      return null;
    }

    const action = jhaRecord.archivedAt ? 'archived' : 'unarchived';
    await tx.insert(auditTrail).values({
      entityType: 'jha',
      entityId: jhaRecord.id,
      action,
      userId: user.id,
      upkeepCompanyId: user.upkeepCompanyId,
      details: JSON.stringify(jhaRecord),
    });

    return jhaRecord;
  });
};

export const getOverdueJhas = () => {
  // Calculate specific dates: today and exactly 7 days from now
  const today = new Date();
  const todayStart = new Date(today);
  todayStart.setHours(0, 0, 0, 0); // Start of today
  const todayEnd = new Date(today);
  todayEnd.setHours(23, 59, 59, 999); // End of today

  const sevenDaysFromNow = new Date();
  sevenDaysFromNow.setDate(sevenDaysFromNow.getDate() + 7);
  const sevenDaysFromNowStart = new Date(sevenDaysFromNow);
  sevenDaysFromNowStart.setHours(0, 0, 0, 0); // Start of 7 days from now
  const sevenDaysFromNowEnd = new Date(sevenDaysFromNow);
  sevenDaysFromNowEnd.setHours(23, 59, 59, 999); // End of 7 days from now

  return db
    .select({
      id: jha.id,
      upkeepCompanyId: jha.upkeepCompanyId,
      slug: jha.slug,
      instanceId: jha.instanceId,
      title: jha.title,
      ownerId: jha.ownerId,
      approverId: jha.approverId,
      status: jha.status,
      reviewDate: jha.reviewDate,
      locationId: jha.locationId,
      createdBy: jha.createdBy,
    })
    .from(jha)
    .where(
      and(
        // Active JHAs only (not archived)
        isNull(jha.archivedAt),
        // Active status (approved)
        eq(jha.status, approvalStatusEnum.enumValues[2]),
        // Review date is not null
        isNotNull(jha.reviewDate),
        // Review date is either today OR exactly 7 days from now
        or(
          and(gte(jha.reviewDate, todayStart), lte(jha.reviewDate, todayEnd)),
          and(gte(jha.reviewDate, sevenDaysFromNowStart), lte(jha.reviewDate, sevenDaysFromNowEnd)),
        ),
      ),
    )
    .orderBy(desc(jha.reviewDate));
};
