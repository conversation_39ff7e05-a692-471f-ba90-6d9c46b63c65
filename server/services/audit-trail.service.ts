import { db } from '@server/db';
import { auditTrail } from '@shared/schema';
import { User } from '@shared/types/users.types';
import { and, desc, eq } from 'drizzle-orm';

export const getAuditTrail = async (entityId: string, user: User) => {
  return await db
    .select()
    .from(auditTrail)
    .where(and(eq(auditTrail.entityId, entityId), eq(auditTrail.upkeepCompanyId, user.upkeepCompanyId)))
    .orderBy(desc(auditTrail.timestamp));
};
