import api, { buildDefaultHeaders, type Headers } from '@server/utils/api';
import { Location } from '@shared/types/assets.types';
import { PaginatedResponse, PublicSearchInput } from '@shared/types/schema.types';

type UpstreamLocation = {
  id: string;
  stringName: string;
  arrayOfSublocations?: Array<{
    id: string;
    stringName: string;
    objectId: string;
  }>;
};

type GetLocationsCountParams = {
  search?: string;
};

// Map upstream response to our Location type
const mapLocationResponse = (upstreamLocation: { id: string; stringName: string }): Location => ({
  id: upstreamLocation.id,
  name: upstreamLocation.stringName,
});

// Map public API response to our Location type
// Based on the API response structure with main locations and sublocations
const mapPublicLocationResponse = (upstreamLocation: UpstreamLocation): Location[] => {
  const locations: Location[] = [];

  // Add the main location
  locations.push({
    id: upstreamLocation.id,
    name: upstreamLocation.stringName,
  });

  // Add sublocations if they exist
  if (upstreamLocation.arrayOfSublocations) {
    upstreamLocation.arrayOfSublocations.forEach((subLocation) => {
      locations.push({
        id: subLocation.id,
        name: subLocation.stringName,
      });
    });
  }

  return locations;
};

export const getLocations = async (
  params: {
    cursor?: number;
    limit: number;
    search?: string;
    sort?: string;
    objectId?: string[];
    mustIncludeObjectIds?: string[];
  },
  headers: Headers,
): Promise<PaginatedResponse<Location>> => {
  const { cursor = 0, limit, search, sort, objectId, mustIncludeObjectIds } = params;
  const offset = cursor;

  // Request one extra item to determine if there are more results
  const requestBody = {
    limit: limit + 1,
    offset,
    sort: sort || 'createdAt DESC',
    ...(search && { search }),
    ...(objectId && { objectId }),
  };

  const response = await api.post('/api/v1/locations/search', requestBody, {
    headers: buildDefaultHeaders(headers),
  });

  const allLocations = response.data.results.map(mapLocationResponse);

  // Check if we got more results than requested (indicating more pages available)
  const hasMore = allLocations.length > limit;
  const locations = hasMore ? allLocations.slice(0, limit) : allLocations;

  const nextCursor = hasMore ? cursor + limit : undefined;

  let isNotIncluded = [];

  // If the mustIncludeObjectIds is provided, we need to add it to the final results
  if (mustIncludeObjectIds && mustIncludeObjectIds.length > 0 && cursor === 0) {
    isNotIncluded = mustIncludeObjectIds.filter(
      (location) => !allLocations.map((location: Location) => location.id).includes(location),
    );

    if (isNotIncluded.length > 0) {
      const mustIncludeLocation = await api.post(
        '/api/v1/locations/search',
        {
          ...requestBody,
          objectId: isNotIncluded,
        },
        {
          headers: buildDefaultHeaders(headers),
        },
      );

      if (mustIncludeLocation.data.success && mustIncludeLocation.data.results) {
        const locationsToAdd = mustIncludeLocation.data.results.map(mapLocationResponse);
        locations.unshift(...locationsToAdd);
      }
    }
  }

  return {
    noResults: locations.length === 0,
    result: locations,
    nextCursor,
  };
};

export const getAllLocations = async (roleId: string) => {
  const limit = 1000;
  let offset = 0;
  let allLocations: Location[] = [];
  let hasMore = true;

  while (hasMore) {
    const response = await api.post('/api/app/search/locations', {
      limit: limit + 1,
      offset,
      roleId,
    });

    if (response.data.success && response.data.results) {
      const locations = response.data.results.map(mapLocationResponse);
      allLocations.push(...locations.slice(0, limit));

      hasMore = locations.length > limit;
      offset += limit;
    }
  }

  return allLocations;
};

export const getLocationsCount = async (params: GetLocationsCountParams, headers: Headers): Promise<number> => {
  const requestBody = {
    ...(params.search && { search: params.search }),
  };

  const response = await api.post('/api/v1/locations/search/count', requestBody, {
    headers: buildDefaultHeaders(headers),
  });
  return response.data.result as number;
};

export const getLocationById = async (id: string, headers: Headers) => {
  const response = await api.get(`/api/v1/locations/${id}`, {
    headers: buildDefaultHeaders(headers),
  });

  // Map the response to only return needed properties
  return mapLocationResponse(response.data.result);
};

/**
 * Public location search - calls the UpKeep API without authentication requirements
 * Uses the search endpoint that requires only roleId with cursor-based pagination
 */
export const searchLocationsPublic = async (params: PublicSearchInput): Promise<PaginatedResponse<Location>> => {
  const { cursor = 0, limit = 50, mustIncludeObjectIds } = params;
  const offset = cursor;

  // Request one extra item to determine if there are more results
  const requestBody = {
    roleId: params.upkeepCompanyId,
    limit: limit + 1,
    offset,
    ...(params.search && { search: params.search }),
    ...(params.objectId && { objectId: params.objectId }),
  };

  const response = await api.post('/api/app/search/locations', requestBody);

  // Check if response has expected structure
  if (!response.data.success || !response.data.results) {
    return {
      noResults: true,
      result: [],
      nextCursor: undefined,
    };
  }

  // Map the response to be consistent with our Location type
  // Flatten the main locations and sublocations into a single array
  const allLocations: Location[] = [];

  response.data.results.forEach((location: UpstreamLocation) => {
    const mappedLocations = mapPublicLocationResponse(location);
    allLocations.push(...mappedLocations);
  });

  // Check if there are more results by seeing if we got the extra item
  const hasMore = allLocations.length > limit;
  const finalResults = hasMore ? allLocations.slice(0, limit) : allLocations;

  if (!mustIncludeObjectIds || mustIncludeObjectIds.length === 0) {
    return {
      noResults: finalResults.length === 0,
      result: finalResults,
      nextCursor: hasMore ? cursor + limit : undefined,
    };
  }

  // If the mustIncludeObjectId is provided, we need to add it to the final results
  const isNotIncluded = mustIncludeObjectIds.filter(
    (locationId) => !finalResults.map((location: Location) => location.id).includes(locationId),
  );

  if (isNotIncluded.length === 0) {
    return {
      noResults: finalResults.length === 0,
      result: finalResults,
      nextCursor: hasMore ? cursor + limit : undefined,
    };
  }

  const mustIncludeLocation = await api.post('/api/app/search/locations', {
    ...requestBody,
    objectId: isNotIncluded,
  });

  let hasAllMustIncludeLocations = false;

  mustIncludeLocation.data.results.forEach((location: UpstreamLocation) => {
    const mappedLocations = mapPublicLocationResponse(location);
    hasAllMustIncludeLocations = mappedLocations.every((location) => mustIncludeObjectIds.includes(location.id));
    finalResults.unshift(...mappedLocations);
  });

  if (hasAllMustIncludeLocations) {
    return {
      noResults: finalResults.length === 0,
      result: finalResults,
      nextCursor: hasMore ? cursor + limit : undefined,
    };
  }

  const mustIncludeSubLocation = await api.post('/api/app/search/locations', {
    ...requestBody,
    arrayOfSublocations: isNotIncluded,
  });

  mustIncludeSubLocation.data.results.forEach((location: UpstreamLocation) => {
    const mappedLocations = mapPublicLocationResponse(location);
    finalResults.unshift(...mappedLocations);
  });

  return {
    noResults: finalResults.length === 0,
    result: finalResults,
    nextCursor: hasMore ? cursor + limit : undefined,
  };
};
