import { db } from '@server/db';
import { oshaAuditTrail, oshaCompanyInformation, oshaLocations } from '@shared/schema';
import {
  GetEstablishInformation,
  OshaSummaryExecutiveCertification,
  UpsertOshaCompanyInformation,
} from '@shared/types/osha.types';
import { User } from '@shared/types/users.types';
import { and, eq, getTableColumns, isNotNull, sql } from 'drizzle-orm';

export const upsertOshaSummary = async (input: UpsertOshaCompanyInformation, user: User) => {
  return db.transaction(async (tx) => {
    const values = {
      ...input,
      createdBy: user!.id,
      upkeepCompanyId: user!.upkeepCompanyId,
    };

    const inserted = await tx
      .insert(oshaCompanyInformation)
      .values({ ...values, createdAt: new Date() })
      .onConflictDoUpdate({
        target: [
          oshaCompanyInformation.upkeepCompanyId,
          oshaCompanyInformation.year,
          oshaCompanyInformation.oshaLocationId,
        ],
        set: { ...values, updatedAt: new Date() },
      })
      .returning({
        id: oshaCompanyInformation.id,
        createdAt: oshaCompanyInformation.createdAt,
        updatedAt: oshaCompanyInformation.updatedAt,
      });

    const summary = inserted.at(0);
    if (!summary) {
      return;
    }

    const action = summary?.updatedAt ? 'updated' : 'created';

    await tx.insert(oshaAuditTrail).values({
      upkeepCompanyId: user.upkeepCompanyId,
      entityId: summary.id,
      entityType: 'osha_company_information',
      action,
      createdBy: user.id!,
      ipAddress: input.ipAddress || 'unknown',
      userAgent: input.userAgent || 'unknown',
      details: JSON.stringify(values),
    });

    return summary;
  });
};

export const upsertOshaExecutiveCertification = async (input: OshaSummaryExecutiveCertification, user: User) => {
  return db.transaction(async (tx) => {
    const { id, ...summary } = input;

    const values = {
      ...summary,
      createdBy: user!.id,
    };

    await tx.update(oshaCompanyInformation).set(values).where(eq(oshaCompanyInformation.id, id));

    await tx.insert(oshaAuditTrail).values({
      upkeepCompanyId: user.upkeepCompanyId,
      entityId: id,
      entityType: 'osha_company_information',
      action: 'signed',
      createdBy: user.id!,
      ipAddress: input.ipAddress || 'unknown',
      userAgent: input.userAgent || 'unknown',
      details: JSON.stringify(values),
    });

    return values;
  });
};

export const getEstablishmentInformation = async ({ year, oshaLocationId }: GetEstablishInformation, user: User) => {
  if (!oshaLocationId) {
    return null;
  }

  const summary = await db
    .select({
      ...getTableColumns(oshaCompanyInformation),
      oshaLocation: {
        id: oshaLocations.id,
        name: oshaLocations.name,
      },
    })
    .from(oshaCompanyInformation)
    .leftJoin(oshaLocations, eq(oshaCompanyInformation.oshaLocationId, oshaLocations.id))
    .where(
      and(
        eq(oshaCompanyInformation.year, year),
        eq(oshaCompanyInformation.upkeepCompanyId, user.upkeepCompanyId),
        eq(oshaCompanyInformation.oshaLocationId, oshaLocationId),
      ),
    );
  return summary.at(0) ?? null;
};

export const toggleArchiveOshaSummary = async (
  { id, ipAddress, userAgent }: { id: string; ipAddress: string; userAgent: string },
  user: User,
) => {
  return db.transaction(async (tx) => {
    const updated = await tx
      .update(oshaCompanyInformation)
      .set({
        archivedAt: sql`CASE WHEN ${oshaCompanyInformation.archivedAt} IS NOT NULL THEN NULL ELSE NOW() END`,
        archivedBy: sql`CASE WHEN ${oshaCompanyInformation.archivedAt} IS NOT NULL THEN NULL ELSE ${user.id} END`,
      })
      .where(eq(oshaCompanyInformation.id, id))
      .returning({
        id: oshaCompanyInformation.id,
        archivedAt: oshaCompanyInformation.archivedAt,
        archivedBy: oshaCompanyInformation.archivedBy,
      });

    const action = updated.at(0)?.archivedAt ? 'archived' : 'restored';

    await tx.insert(oshaAuditTrail).values({
      upkeepCompanyId: user.upkeepCompanyId,
      entityId: id,
      entityType: 'osha_company_information',
      action,
      createdBy: user.id!,
      ipAddress,
      userAgent,
      details: JSON.stringify(updated.at(0)),
    });
  });
};

export const getArchived = async (year: number, user: User) => {
  const oshaLocationsIds = await db
    .select({
      oshaLocationId: oshaCompanyInformation.oshaLocationId,
    })
    .from(oshaCompanyInformation)
    .where(
      and(
        eq(oshaCompanyInformation.year, year),
        eq(oshaCompanyInformation.upkeepCompanyId, user.upkeepCompanyId),
        isNotNull(oshaCompanyInformation.archivedAt),
        isNotNull(oshaCompanyInformation.archivedBy),
      ),
    );

  return new Set(oshaLocationsIds.map(({ oshaLocationId }) => oshaLocationId));
};
