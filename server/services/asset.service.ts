import api, { buildDefaultHeaders, type Headers } from '@server/utils/api';
import { Asset, UpkeepAsset } from '@shared/types/assets.types';
import { PaginatedResponse, PublicSearchInput } from '@shared/types/schema.types';

type BaseAssetSearchParams = {
  search?: string;
  objectLocation?: string[];
};

export type GetAssetsCountParams = BaseAssetSearchParams;

export type GetAssetsParams = BaseAssetSearchParams & {
  cursor?: number;
  limit: number;
  sort?: string;
  objectId?: string[] | string;
  mustIncludeObjectIds?: string[];
};

// Map upstream response to our Asset type
const mapAssetResponse = (upstreamAsset: UpkeepAsset): Asset => ({
  id: upstreamAsset.id,
  name: upstreamAsset.Name,
  description: upstreamAsset.Description,
});

// Map public API response to our Asset type
const mapPublicAssetResponse = (upstreamAsset: UpkeepAsset): Asset => ({
  id: upstreamAsset.id,
  name: upstreamAsset.Name,
  description: upstreamAsset.objectLocation?.stringName || '', // Use location as description if available
});

export const getAssets = async (params: GetAssetsParams, headers: Headers): Promise<PaginatedResponse<Asset>> => {
  const { cursor = 0, limit, search, objectLocation, sort, objectId, mustIncludeObjectIds } = params;
  const offset = cursor;

  // Request one extra item to determine if there are more results
  const requestBody = {
    limit: limit + 1,
    offset,
    sort: sort || 'createdAt DESC',
    ...(search && { search }),
    ...(objectLocation && { objectLocation }),
    ...(objectId && { objectId }),
  };

  const response = await api.post('/api/v1/assets/search', requestBody, {
    headers: buildDefaultHeaders(headers),
  });

  const allAssets = response.data.results.map(mapAssetResponse);

  // Check if we got more results than requested (indicating more pages available)
  const hasMore = allAssets.length > limit;
  const assets = hasMore ? allAssets.slice(0, limit) : allAssets;

  let isNotIncluded = [];

  if (mustIncludeObjectIds && mustIncludeObjectIds.length > 0 && cursor === 0) {
    isNotIncluded = mustIncludeObjectIds.filter((asset) => !assets.map((asset: Asset) => asset.id).includes(asset));

    if (isNotIncluded.length > 0) {
      const mustIncludeAsset = await api.post(
        '/api/v1/assets/search',
        { ...requestBody, objectId: isNotIncluded },
        {
          headers: buildDefaultHeaders(headers),
        },
      );

      if (mustIncludeAsset.data.success && mustIncludeAsset.data.results) {
        const assetsToAdd = mustIncludeAsset.data.results.map(mapAssetResponse);
        assets.unshift(...assetsToAdd);
      }
    }
  }

  return {
    noResults: assets.length === 0,
    result: assets,
    nextCursor: hasMore ? cursor + limit : undefined,
  };
};

export const getAssetById = async (id: string, headers: Headers) => {
  const response = await api.get(`/api/v1/assets/${id}`, {
    headers: buildDefaultHeaders(headers),
  });

  // Map the response to only return needed properties
  return mapAssetResponse(response.data.result);
};

export const getAssetsCount = async (params: GetAssetsCountParams, headers: Headers): Promise<number> => {
  const requestBody = {
    ...(params.search && { search: params.search }),
    ...(params.objectLocation && { objectLocation: params.objectLocation }),
  };

  const response = await api.post('/api/v1/assets/search/count', requestBody, {
    headers: buildDefaultHeaders(headers),
  });
  return response.data.result as number;
};

/**
 * Public asset search - calls the UpKeep API without authentication requirements
 * Uses the search endpoint that requires only roleId with cursor-based pagination
 */
export const searchAssetsPublic = async (params: PublicSearchInput): Promise<PaginatedResponse<Asset>> => {
  const { cursor = 0, limit = 50 } = params;
  const offset = cursor;

  // Request one extra item to determine if there are more results
  const requestBody = {
    roleId: params.upkeepCompanyId,
    limit: limit + 1,
    offset,
    ...(params.search && { search: params.search }),
    ...(params.locationId && { objectLocation: params.locationId }),
    ...(params.objectId && { objectId: params.objectId }),
  };

  const response = await api.post('/api/app/search/assets', requestBody, {
    headers: {},
  });

  // Check if response has expected structure
  if (!response.data.success || !response.data.results) {
    return {
      noResults: true,
      result: [],
      nextCursor: undefined,
    };
  }

  const results = response.data.results.map(mapPublicAssetResponse);

  // Check if there are more results by seeing if we got the extra item
  const hasMore = results.length > limit;
  const finalResults = hasMore ? results.slice(0, limit) : results;

  return {
    noResults: finalResults.length === 0,
    result: finalResults,
    nextCursor: hasMore ? cursor + limit : undefined,
  };
};
