import { createPaginatedResponse } from '@server/utils/pagination';
import { auditTrail, auditTrailActionEnum, capas, capasJhas, events, files, jha, statusEnum } from '@shared/schema';
import { CreateCapasForm, EditCapasForm, ExportCapasSchema, ListCapasSchema } from '@shared/types/capas.types';
import { TransientFileSchema } from '@shared/types/files.types';
import { IdSchema } from '@shared/types/schema.types';
import { User } from '@shared/types/users.types';
import { USER_ACCOUNT_TYPES } from '@shared/user-permissions';
import { TRPCError } from '@trpc/server';
import { endOfDay, startOfDay } from 'date-fns';
import {
  and,
  arrayContains,
  arrayOverlaps,
  asc,
  desc,
  eq,
  getTableColumns,
  gte,
  ilike,
  inArray,
  isNull,
  lt,
  lte,
  or,
  sql,
} from 'drizzle-orm';
import { db } from 'server/db';
import { z } from 'zod';
import { createCapaSummary } from './ai.service';
import { assignCapa, getAllWorkOrdersByCapaId } from './work-order.service';
import { type Headers } from '@server/utils/api';

/**
 * Create a new CAPA (Corrective and Preventive Action)
 *
 * @param capaInput The CAPA data to create
 * @param user The authenticated user
 * @returns The created CAPA
 */
export const createCapa = async (input: CreateCapasForm, user: User, headers: Headers) => {
  return db.transaction(async (tx) => {
    const values = {
      ...input,
      createdBy: user.id,
      upkeepCompanyId: user.upkeepCompanyId,
      status: input.status || statusEnum.enumValues[0], // Default to the first status (open)
    };

    if (!values.summary) {
      const summary = await createCapaSummary(values);
      values.summary = summary;
    }

    const inserted = await tx.insert(capas).values(values).returning({
      id: capas.id,
      createdBy: capas.createdBy,
      upkeepCompanyId: capas.upkeepCompanyId,
      title: capas.title,
      type: capas.type,
      priority: capas.priority,
      status: capas.status,
      dueDate: capas.dueDate,
      ownerId: capas.ownerId,
      eventId: capas.eventId,
      rcaFindings: capas.rcaFindings,
      slug: capas.slug,
      summary: capas.summary,
      locationId: capas.locationId,
      teamMembersToNotify: capas.teamMembersToNotify,
    });

    const capa = inserted.at(0);

    if (!capa) {
      return;
    }

    const { linkedJhaInstanceIds, workOrderIds } = input;

    if (linkedJhaInstanceIds && linkedJhaInstanceIds.length > 0) {
      await tx.insert(capasJhas).values(
        linkedJhaInstanceIds.map((jhaInstanceId) => ({
          capaId: capa.id,
          jhaInstanceId,
          upkeepCompanyId: capa.upkeepCompanyId,
          createdBy: capa.createdBy,
        })),
      );
    }

    if (workOrderIds && workOrderIds.length > 0) {
      await Promise.all(
        workOrderIds.map((workOrderId) => {
          return assignCapa(workOrderId, capa.id, headers);
        }),
      );
    }

    // Create audit trail entry
    await tx.insert(auditTrail).values({
      entityId: capa.id,
      entityType: 'capa',
      details: JSON.stringify(capa),
      userId: capa.createdBy,
      upkeepCompanyId: capa.upkeepCompanyId,
      timestamp: new Date(),
      action: auditTrailActionEnum.enumValues[0], // Default to the first status (created)
    });

    return capa;
  });
};

export const getCapasByIds = (ids: string[], user: User) => {
  return db
    .select()
    .from(capas)
    .where(and(inArray(capas.id, ids), eq(capas.upkeepCompanyId, user.upkeepCompanyId)));
};

export const getCapaById = async (id: string, user: User, needPartialCheck: boolean) => {
  // Fetch the CAPA that matches the ID and company ID, and is not deleted

  const isAdmin = user.role === USER_ACCOUNT_TYPES.ADMIN;

  const result = await db
    .select({
      ...getTableColumns(capas),
      eventSlug: events.slug,
      capaJhas: sql<
        Array<{
          id: string;
          jhaInstanceId: string | null;
          jha: { slug: string | null; title: string | null } | null;
        }>
      >`
      COALESCE(
        json_agg(DISTINCT jsonb_build_object(
          'id', ${capasJhas.id},
          'jhaInstanceId', ${capasJhas.jhaInstanceId},
          'jha', CASE
            WHEN ${jha.instanceId} IS NULL THEN NULL
            ELSE jsonb_build_object(
              'slug', ${jha.slug},
              'title', ${jha.title}
            )
          END
        )) FILTER (WHERE ${capasJhas.id} IS NOT NULL),
        '[]'::json
      )
    `,
      attachments: sql<z.infer<typeof TransientFileSchema>[]>`
      CASE
        WHEN COUNT(${files.id}) = 0 THEN NULL
        ELSE json_agg(DISTINCT jsonb_build_object(
          'id', ${files.id},
          'name', ${files.fileName},
          'url', ${files.presignedUrl},
          'type', ${files.mimeType},
          'size', ${files.fileSize}
        ))::json
      END
    `,
    })
    .from(capas)
    .leftJoin(
      capasJhas,
      and(
        eq(capasJhas.capaId, capas.id),
        eq(capasJhas.upkeepCompanyId, capas.upkeepCompanyId),
        isNull(capasJhas.deletedAt),
      ),
    )
    // 🔗 join to the *active* JHA version by instanceId
    .leftJoin(
      jha,
      and(
        eq(jha.instanceId, capasJhas.jhaInstanceId), // <-- relationship is instanceId
        isNull(jha.archivedAt), // <-- only the active one
        eq(jha.upkeepCompanyId, capas.upkeepCompanyId),
      ),
    )
    .leftJoin(events, eq(capas.eventId, events.id))
    .leftJoin(
      files,
      and(
        eq(files.entityId, capas.id),
        eq(files.upkeepCompanyId, user.upkeepCompanyId),
        eq(files.status, 'completed'),
        eq(files.entityType, 'capa'),
      ),
    )
    .where(
      and(
        eq(capas.id, id),
        eq(capas.upkeepCompanyId, user.upkeepCompanyId),
        isNull(capas.deletedAt),
        !isAdmin ? eq(capas.privateToAdmins, false) : undefined,
        ...(needPartialCheck
          ? [
              or(
                eq(capas.ownerId, user.id),
                eq(capas.implementedBy, user.id),
                eq(capas.voePerformedBy, user.id),
                eq(capas.createdBy, user.id),
                arrayContains(capas.teamMembersToNotify, [user.id]),
              ),
            ]
          : []),
      ),
    )
    .groupBy(capas.id, events.slug);

  const capa = result.at(0);

  if (!capa) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: 'CAPA not found',
    });
  }

  return capa;
};

export const getOverdueCapas = async () => {
  const result = await db
    .select({
      ...getTableColumns(capas),
      eventSlug: events.slug,
      eventTitle: events.title,
    })
    .from(capas)
    .leftJoin(events, eq(capas.eventId, events.id))
    .where(and(eq(capas.status, statusEnum.enumValues[0]), lt(capas.dueDate, new Date()), isNull(capas.archivedAt)));

  return result;
};

export const listCapas = async (
  input: z.infer<typeof ListCapasSchema> & { cursor?: number },
  user: User,
  needPartialCheck: boolean,
) => {
  const {
    cursor = 0,
    limit = 10,
    includeArchived,
    search,
    sortBy = 'createdAt',
    sortOrder = 'desc',
    status = [],
    type = [],
    priority = [],
    owner = [],
    dueDateRange,
    tags = [],
    eventId,
  } = input;

  const isAdmin = user.role === USER_ACCOUNT_TYPES.ADMIN;

  // Apply all conditions at once using and()
  let query = db
    .select({
      id: capas.id,
      title: capas.title,
      type: capas.type,
      status: capas.status,
      priority: capas.priority,
      dueDate: capas.dueDate,
      ownerId: capas.ownerId,
      eventId: capas.eventId,
      eventSlug: events.slug,
      archivedAt: capas.archivedAt,
      slug: capas.slug,
      tags: capas.tags,
      createdAt: capas.createdAt,
      updatedAt: capas.updatedAt,
      deletedAt: capas.deletedAt,
      createdBy: capas.createdBy,
    })
    .from(capas)
    .leftJoin(events, eq(capas.eventId, events.id))
    .where(
      and(
        eq(capas.upkeepCompanyId, user.upkeepCompanyId),
        !isAdmin ? eq(capas.privateToAdmins, false) : undefined,
        needPartialCheck
          ? or(
              eq(capas.ownerId, user.id),
              eq(capas.implementedBy, user.id),
              eq(capas.voePerformedBy, user.id),
              eq(capas.createdBy, user.id),
              arrayContains(capas.teamMembersToNotify, [user.id]),
            )
          : undefined,
        !includeArchived ? isNull(capas.archivedAt) : undefined,
        search
          ? or(
              ilike(capas.title, `%${search}%`),
              ilike(capas.rcaFindings, `%${search}%`),
              ilike(capas.slug, `%${search}%`),
            )
          : undefined,
        status.length > 0 ? inArray(capas.status, status) : undefined,
        type.length > 0 ? inArray(capas.type, type) : undefined,
        priority.length > 0 ? inArray(capas.priority, priority) : undefined,
        owner.length > 0 ? inArray(capas.ownerId, owner) : undefined,
        dueDateRange?.from ? gte(capas.dueDate, startOfDay(dueDateRange.from)) : undefined,
        dueDateRange?.to ? lte(capas.dueDate, endOfDay(dueDateRange.to)) : undefined,
        tags.length > 0 ? arrayOverlaps(capas.tags, tags) : undefined,
        eventId ? eq(capas.eventId, eventId) : undefined,
      ),
    )
    .$dynamic();

  if (sortBy) {
    query = query.orderBy(sortOrder === 'desc' ? desc(capas.createdAt) : asc(capas.createdAt));
  }

  const filtered = await query.limit(limit).offset(cursor);

  return createPaginatedResponse(filtered, { cursor, limit });
};

export const exportCapas = async (input: z.infer<typeof ExportCapasSchema>, user: User) => {
  const {
    search,
    status = [],
    type = [],
    priority = [],
    owner = [],
    dueDateRange,
    tags = [],
    eventId,
    includeArchived,
  } = input;

  return await db
    .select({
      id: capas.id,
      title: capas.title,
      type: capas.type,
      status: capas.status,
      priority: capas.priority,
      dueDate: capas.dueDate,
      ownerId: capas.ownerId,
      eventId: capas.eventId,
      eventSlug: events.slug,
      archivedAt: capas.archivedAt,
      slug: capas.slug,
      tags: capas.tags,
      summary: capas.summary,
      rcaMethod: capas.rcaMethod,
      actionsToAddress: capas.actionsToAddress,
      createdAt: capas.createdAt,
      updatedAt: capas.updatedAt,
      deletedAt: capas.deletedAt,
      createdBy: capas.createdBy,
    })
    .from(capas)
    .leftJoin(events, eq(capas.eventId, events.id))
    .where(
      and(
        eq(capas.upkeepCompanyId, user.upkeepCompanyId),
        !includeArchived ? isNull(capas.archivedAt) : undefined,
        search
          ? or(
              ilike(capas.title, `%${search}%`),
              ilike(capas.rcaFindings, `%${search}%`),
              ilike(capas.slug, `%${search}%`),
            )
          : undefined,
        status.length > 0 ? inArray(capas.status, status) : undefined,
        type.length > 0 ? inArray(capas.type, type) : undefined,
        priority.length > 0 ? inArray(capas.priority, priority) : undefined,
        owner.length > 0 ? inArray(capas.ownerId, owner) : undefined,
        dueDateRange?.from ? gte(capas.dueDate, startOfDay(dueDateRange.from)) : undefined,
        dueDateRange?.to ? lte(capas.dueDate, endOfDay(dueDateRange.to)) : undefined,
        tags.length > 0 ? arrayOverlaps(capas.tags, tags) : undefined,
        eventId ? eq(capas.eventId, eventId) : undefined,
      ),
    )
    .limit(500);
};

/**
 * Update an existing CAPA
 *
 * @param id The ID of the CAPA to update
 * @param capaInput The CAPA data to update
 * @param user The authenticated user
 * @returns The updated CAPA data
 */
export const updateCapa = async (id: string, capaInput: EditCapasForm, user: User, headers: Headers) => {
  return db.transaction(async (tx) => {
    const updated = await tx
      .update(capas)
      .set(capaInput)
      .where(and(eq(capas.id, id), eq(capas.upkeepCompanyId, user.upkeepCompanyId)))
      .returning({
        status: capas.status,
        archivedAt: capas.archivedAt,
        id: capas.id,
        createdBy: capas.createdBy,
        upkeepCompanyId: capas.upkeepCompanyId,
        title: capas.title,
        type: capas.type,
        priority: capas.priority,
        dueDate: capas.dueDate,
        ownerId: capas.ownerId,
        eventId: capas.eventId,
        rcaFindings: capas.rcaFindings,
        slug: capas.slug,
        locationId: capas.locationId,
        teamMembersToNotify: capas.teamMembersToNotify,
      });

    const updatedCapa = updated.at(0);

    if (!updatedCapa) {
      return;
    }

    const { linkedJhaInstanceIds, workOrderIds } = capaInput;

    if (linkedJhaInstanceIds !== undefined) {
      // replace linked JHA instances by soft deleting the old ones
      await tx
        .update(capasJhas)
        .set({
          deletedAt: new Date(),
        })
        .where(and(eq(capasJhas.capaId, updatedCapa.id), eq(capasJhas.upkeepCompanyId, updatedCapa.upkeepCompanyId)));

      if (linkedJhaInstanceIds.length > 0) {
        await tx
          .insert(capasJhas)
          .values(
            linkedJhaInstanceIds.map((jhaInstanceId) => ({
              capaId: updatedCapa.id,
              jhaInstanceId,
              upkeepCompanyId: updatedCapa.upkeepCompanyId,
              createdBy: updatedCapa.createdBy,
            })),
          )
          .onConflictDoUpdate({
            target: [capasJhas.capaId, capasJhas.jhaInstanceId],
            set: {
              deletedAt: null,
            },
          });
      }
    }

    // we use dirty tracking to determine if the work orders have changed
    // if they have, we need to re-assign the CAPA to the work orders
    if (workOrderIds !== undefined) {
      // replace linked work orders by soft deleting the old ones
      // need to load all work orders first
      const allWorkOrders = await getAllWorkOrdersByCapaId(updatedCapa.id, headers);
      if (allWorkOrders.length > 0) {
        await Promise.all(
          allWorkOrders.map((workOrder) => {
            return assignCapa(workOrder.id, '', headers);
          }),
        );
      }

      // then assign the new ones
      if (workOrderIds && workOrderIds.length > 0) {
        await Promise.all(
          workOrderIds.map((workOrderId) => {
            return assignCapa(workOrderId, updatedCapa.id, headers);
          }),
        );
      }
    }

    let action: (typeof auditTrailActionEnum.enumValues)[number] = 'updated';

    if ('status' in capaInput && 'status' in updatedCapa) {
      const statusMap = {
        open: auditTrailActionEnum.enumValues[7],
        in_review: auditTrailActionEnum.enumValues[6],
        closed: auditTrailActionEnum.enumValues[5],
      };

      action = statusMap[
        updatedCapa.status as keyof typeof statusMap
      ] as (typeof auditTrailActionEnum.enumValues)[number];
    }

    if ('archivedAt' in capaInput && 'archivedAt' in updatedCapa) {
      action = updatedCapa.archivedAt ? 'archived' : 'unarchived';
    }

    await tx.insert(auditTrail).values({
      entityId: updatedCapa.id,
      entityType: 'capa',
      details: JSON.stringify(updatedCapa),
      userId: user.id,
      upkeepCompanyId: user.upkeepCompanyId,
      timestamp: new Date(),
      action,
    });

    return updatedCapa;
  });
};

export const toggleArchiveCapa = async (input: z.infer<typeof IdSchema>, user: User) => {
  return await db.transaction(async (tx) => {
    const updated = await tx
      .update(capas)
      .set({
        updatedAt: new Date(),
        archivedAt: sql`CASE WHEN ${capas.archivedAt} IS NOT NULL THEN NULL ELSE NOW() END`,
      })
      .where(eq(capas.id, input.id))
      .returning({
        status: capas.status,
        archivedAt: capas.archivedAt,
        id: capas.id,
        createdBy: capas.createdBy,
        upkeepCompanyId: capas.upkeepCompanyId,
        title: capas.title,
        type: capas.type,
        priority: capas.priority,
        dueDate: capas.dueDate,
        ownerId: capas.ownerId,
        eventId: capas.eventId,
        rcaFindings: capas.rcaFindings,
        slug: capas.slug,
        locationId: capas.locationId,
        teamMembersToNotify: capas.teamMembersToNotify,
      });

    const capa = updated.at(0);

    if (!capa) {
      return;
    }

    const action = capa.archivedAt ? 'unarchived' : 'archived';
    await tx.insert(auditTrail).values({
      entityId: capa.id,
      entityType: 'capa',
      details: JSON.stringify(capa),
      userId: user.id,
      upkeepCompanyId: user.upkeepCompanyId,
      timestamp: new Date(),
      action,
    });

    return capa;
  });
};
