import { redisClient } from '@server/redis/client';
import { EventEmitter } from 'events';
import { logger } from '@server/utils/logger';
import { env } from '../../env';

// Local emitter for this pod
export const notificationEmitter = new EventEmitter();

// Create separate Redis clients for pub/sub
// We need separate clients because Redis clients in subscribe mode can't perform other operations
const publisher = redisClient.duplicate();
const subscriber = redisClient.duplicate();

// Channel name for notifications
const NOTIFICATION_CHANNEL = `ehs:${env.ENVIRONMENT_PREFIX}:notifications:changes`;

// Initialize subscription
export const initializeSubscription = async () => {
  try {
    await subscriber.subscribe(NOTIFICATION_CHANNEL);
    logger.info(`[NotificationPubSub] Subscribed to ${NOTIFICATION_CHANNEL}`);

    subscriber.on('message', (channel: string, message: string) => {
      if (channel === NOTIFICATION_CHANNEL) {
        try {
          const data = JSON.parse(message);
          // Emit to local EventEmitter for any subscriptions on this pod
          notificationEmitter.emit('notificationChange', data);
          logger.debug('[NotificationPubSub] Received notification change:', data);
        } catch (error) {
          logger.error('[NotificationPubSub] Error parsing message:', error);
        }
      }
    });
  } catch (error) {
    logger.error('[NotificationPubSub] Error subscribing to channel:', error);
  }
};

// Helper function to emit notification changes across all pods
export const emitNotificationChange = async (upkeepCompanyId: string, type: 'new' | 'update' = 'new') => {
  const data = { upkeepCompanyId, type, timestamp: new Date().toISOString() };

  try {
    // Publish to Redis for other pods
    await publisher.publish(NOTIFICATION_CHANNEL, JSON.stringify(data));

    // Also emit locally (Redis pub/sub doesn't deliver to the publisher)
    notificationEmitter.emit('notificationChange', data);

    logger.debug('[NotificationPubSub] Published notification change:', data);
  } catch (error) {
    logger.error('[NotificationPubSub] Error publishing notification change:', error);
    // Still emit locally even if Redis publish fails
    notificationEmitter.emit('notificationChange', data);
  }
};

// Cleanup function for graceful shutdown
export const shutdownNotifications = async () => {
  try {
    await subscriber.unsubscribe(NOTIFICATION_CHANNEL);
    await subscriber.quit();
    await publisher.quit();
    logger.info('[NotificationPubSub] Shutdown complete');
  } catch (error) {
    logger.error('[NotificationPubSub] Error during shutdown:', error);
  }
};
