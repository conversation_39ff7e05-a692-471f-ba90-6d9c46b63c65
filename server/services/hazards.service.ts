import { db } from '@server/db';
import { createPaginatedResponse } from '@server/utils/pagination';
import { auditTrail, hazards } from '@shared/schema';
import {
  BulkHazardsCreateSchema,
  ExportHazardsSchema,
  HazardsCreateSchema,
  HazardsUpdateSchema,
  ListHazardsSchema,
} from '@shared/types/settings.types';
import { User } from '@shared/types/users.types';
import { endOfDay, startOfDay } from 'date-fns';
import { and, desc, eq, gte, ilike, inArray, isNull, lte, sql } from 'drizzle-orm';
import { z } from 'zod';

export const createHazard = async (input: z.infer<typeof HazardsCreateSchema>, user: User) => {
  return db.transaction(async (tx) => {
    const inserted = await tx
      .insert(hazards)
      .values({
        ...input,
        upkeepCompanyId: user.upkeepCompanyId,
        createdBy: user.id,
      })
      .returning();

    return inserted.at(0);
  });
};

export const updateHazard = async (input: z.infer<typeof HazardsUpdateSchema>, _: User) => {
  return db.transaction(async (tx) => {
    const updated = await tx
      .update(hazards)
      .set({ ...input, updatedAt: new Date() })
      .where(eq(hazards.id, input.id!))
      .returning();

    return updated.at(0);
  });
};

export const getHazards = async (user: User) => {
  return db
    .select()
    .from(hazards)
    .where(and(eq(hazards.upkeepCompanyId, user.upkeepCompanyId), isNull(hazards.archivedAt)));
};

export const listHazards = async (input: z.infer<typeof ListHazardsSchema>, user: User) => {
  const {
    cursor = 0,
    limit = 10,
    search,
    type = [],
    createdBy = [],
    includeArchived = false,
    createdDateRange,
    mustIncludeObjectIds,
  } = input;

  let query = db
    .select({
      id: hazards.id,
      name: hazards.name,
      type: hazards.type,
      upkeepCompanyId: hazards.upkeepCompanyId,
      createdBy: hazards.createdBy,
      createdAt: hazards.createdAt,
      updatedAt: hazards.updatedAt,
      archivedAt: hazards.archivedAt,
    })
    .from(hazards)
    .where(
      and(
        eq(hazards.upkeepCompanyId, user.upkeepCompanyId),
        includeArchived ? undefined : isNull(hazards.archivedAt),
        search ? ilike(hazards.name, `%${search}%`) : undefined,
        type.length > 0 ? inArray(hazards.type, type) : undefined,
        createdBy.length > 0 ? inArray(hazards.createdBy, createdBy) : undefined,
        createdDateRange?.from ? gte(hazards.createdAt, startOfDay(createdDateRange.from)) : undefined,
        createdDateRange?.to ? lte(hazards.createdAt, endOfDay(createdDateRange.to)) : undefined,
      ),
    )
    .$dynamic();

  if (mustIncludeObjectIds && mustIncludeObjectIds.length > 0) {
    query = query.orderBy(
      sql`CASE WHEN ${hazards.id} IN (${mustIncludeObjectIds}) THEN 0 ELSE 1 END`,
      desc(hazards.createdAt),
    );
  } else {
    query = query.orderBy(desc(hazards.createdAt));
  }

  const filtered = await query.limit(limit).offset(cursor);

  return createPaginatedResponse(filtered, { cursor, limit });
};

export const toggleArchive = async (id: string, user: User) => {
  return db.transaction(async (tx) => {
    const [hazard] = await tx
      .update(hazards)
      .set({ archivedAt: sql`CASE WHEN ${hazards.archivedAt} IS NULL THEN NOW() ELSE NULL END` })
      .where(eq(hazards.id, id))
      .returning();

    if (!hazard) {
      return;
    }

    const archivedAt = hazard.archivedAt;
    const action = archivedAt ? 'archived' : 'unarchived';

    await tx.insert(auditTrail).values({
      entityType: 'hazard',
      entityId: hazard.id,
      action,
      details: JSON.stringify(hazard),
      upkeepCompanyId: user.upkeepCompanyId,
    });
  });
};

export const bulkCreateHazards = async (
  input: z.infer<typeof BulkHazardsCreateSchema>,
  user: User,
  tx?: Parameters<Parameters<typeof db.transaction>[0]>[0],
) => {
  const createHazards = async (transaction: typeof tx) => {
    const hazardToCreate = input.map((hazard) => ({
      ...hazard,
      upkeepCompanyId: user.upkeepCompanyId,
      createdBy: user.id,
    }));

    const results = await transaction!
      .insert(hazards)
      .values(hazardToCreate)
      .onConflictDoUpdate({
        target: [hazards.name, hazards.type, hazards.upkeepCompanyId],
        set: {
          updatedAt: new Date(),
          archivedAt: null, // Unarchive if it was archived
        },
      })
      .returning();

    return results;
  };

  if (tx) {
    return createHazards(tx);
  }

  return db.transaction(createHazards);
};

export const exportHazards = async (input: z.infer<typeof ExportHazardsSchema>, user: User) => {
  const { search, includeArchived, type = [], createdBy = [], createdDateRange } = input;

  return await db
    .select()
    .from(hazards)
    .where(
      and(
        eq(hazards.upkeepCompanyId, user.upkeepCompanyId),
        includeArchived ? undefined : isNull(hazards.archivedAt),
        search ? ilike(hazards.name, `%${search}%`) : undefined,
        type.length > 0 ? inArray(hazards.type, type) : undefined,
        createdBy.length > 0 ? inArray(hazards.createdBy, createdBy) : undefined,
        createdDateRange?.from ? gte(hazards.createdAt, startOfDay(createdDateRange.from)) : undefined,
        createdDateRange?.to ? lte(hazards.createdAt, endOfDay(createdDateRange.to)) : undefined,
      ),
    )
    .limit(500);
};
