import { db } from '@server/db';
import { oshaAuditTrail, oshaEntityTypeEnum } from '@shared/schema';
import { OshaAuditTrail } from '@shared/types/osha.types';
import { User } from '@shared/types/users.types';
import { and, desc, eq } from 'drizzle-orm';

export const getAuditTrail = async (
  input: { id: string; entityType: (typeof oshaEntityTypeEnum.enumValues)[number] },
  user: User,
) => {
  return db
    .select()
    .from(oshaAuditTrail)
    .where(
      and(
        eq(oshaAuditTrail.entityId, input.id),
        eq(oshaAuditTrail.upkeepCompanyId, user.upkeepCompanyId),
        eq(oshaAuditTrail.entityType, input.entityType),
      ),
    )
    .orderBy(desc(oshaAuditTrail.createdAt));
};

export const createOshaAuditTrail = async (input: OshaAuditTrail, user: User) => {
  return db.insert(oshaAuditTrail).values({
    ...input,
    upkeepCompanyId: user.upkeepCompanyId,
    createdBy: user.id,
  });
};
