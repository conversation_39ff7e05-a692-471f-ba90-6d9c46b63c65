import mailchimp from '@mailchimp/mailchimp_transactional';
import { UserPublic } from '@shared/types/users.types';
import { env } from 'env';
import React from 'react';
import { renderToStaticMarkup } from 'react-dom/server';

const mailchimpClient = mailchimp(env.MANDRILL_API_KEY);

export type EmailUser = Partial<UserPublic> &
  Required<Pick<UserPublic, 'email' | 'fullName'>> & { type: 'to' | 'cc' | 'bcc' };

export const sendEmail = async (
  TemplateComponent: React.ReactElement,
  options: {
    to: EmailUser[];
    subject: string;
    attachments?: {
      type: string;
      name: string;
      content: string;
    }[];
    tags?: string[];
  },
) => {
  let html: string;

  if (React.isValidElement(TemplateComponent)) {
    // If it's already a React element, render it directly
    html = renderToStaticMarkup(TemplateComponent);
  } else {
    throw new Error('TemplateComponent must be a React component or element');
  }

  const message = {
    from_email: env.MANDRILL_FROM_EMAIL,
    from_name: env.MANDRILL_FROM_NAME,
    to: options.to.map((recipient) => ({
      email: recipient.email,
      name: recipient.fullName,
      type: recipient.type,
    })),
    subject: options.subject,
    html,
    text: 'UpKeep EHS',
    attachments: options.attachments?.map((attachment) => ({
      type: attachment.type,
      name: attachment.name,
      content: attachment.content,
    })),
    tags: options.tags,
  };

  await mailchimpClient.messages.send({ message });
};
