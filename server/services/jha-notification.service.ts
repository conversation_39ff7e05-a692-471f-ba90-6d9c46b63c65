import { getCompany } from '@server/services/company.service';
import { EmailUser, sendEmail } from '@server/services/email.service';
import { createNotificationForUsers } from '@server/services/notification.service';
import { addJobToQueue } from '@server/queue/queue-utils';
import { QUEUE_JOB_NAMES } from '@server/queue/job-names';
import { searchLocationsPublic } from '@server/services/location.service';
import { getUserPublic, getUsersPublic } from '@server/services/user.service';
import { JhaApprovedTemplateParams } from '@server/templates/jha-approved';
import { JhaRevisedTemplateParams } from '@server/templates/jha-revised';
import JhaOverdueTemplate, { JhaOverdueNotificationParams } from '@server/templates/jha-overdue';
import { JhaRejectedTemplateParams } from '@server/templates/jha-rejected';
import { ROUTES } from '@shared/ROUTE_PATHS';
import {
  AlertNotificationJobPayload,
  JhaApprovedNotificationJobPayload,
  JhaRejectedNotificationJobPayload,
  JhaRevivedNotificationJobPayload,
  EmailJobPayload,
} from '@shared/types/queues.types';
import { USER_ACCOUNT_TYPES } from '@shared/user-permissions';
import { TZDate } from '@date-fns/tz';
import { env } from 'env';
import React from 'react';
import { formatDate } from '@shared/date-utils';

export const sendJhaRevisedNotification = async ({ updatedJha, user, headers }: JhaRevivedNotificationJobPayload) => {
  const [location, approver, owner, { clientTimezone }] = await Promise.all([
    updatedJha?.locationId
      ? searchLocationsPublic({
          upkeepCompanyId: user.upkeepCompanyId,
          search: '',
          objectId: [updatedJha.locationId],
          limit: 1,
        })
      : undefined,
    // Always fetch approver data since they need to be notified
    updatedJha?.approverId
      ? getUsersPublic({
          upkeepCompanyId: user.upkeepCompanyId,
          objectId: [updatedJha.approverId],
        })
      : { noResults: true, result: [], nextCursor: undefined },
    updatedJha?.ownerId
      ? getUsersPublic({
          upkeepCompanyId: user.upkeepCompanyId,
          objectId: [updatedJha.ownerId],
        })
      : { noResults: true, result: [], nextCursor: undefined },
    getCompany(headers),
  ]);

  // Combine all users to notify: owner, approver, and team members
  const usersToNotify = [user, approver?.result?.at(0), owner?.result?.at(0)];

  // Remove duplicates based on user ID
  const uniqueUsersToNotify = [...new Map(usersToNotify.map((user) => [user?.id, user])).values()];

  const timestamp =
    clientTimezone && updatedJha.updatedAt ? new TZDate(updatedJha.updatedAt, clientTimezone) : new Date();

  const jhaData: JhaRevisedTemplateParams = {
    ...updatedJha,
    updatedAt: timestamp,
    location: location?.result?.at(0),
    owner: {
      email: user.email,
      fullName: user.fullName,
    },
    jhaUrl: `${env.EHS_URL}${ROUTES.BUILD_JHA_DETAILS_PATH(updatedJha.instanceId!)}`,
  };

  const toUsers: EmailUser[] = [
    ...uniqueUsersToNotify
      .filter((user) => user?.email && user?.fullName)
      .map((user) => ({
        email: user?.email!,
        fullName: user?.fullName!,
        type: 'to' as const,
      })),
  ];

  // Queue email sending
  const emailJobPayload: EmailJobPayload = {
    templateType: 'jha-revised',
    templateProps: {
      ...jhaData,
      jhaUrl: `${env.EHS_URL}${ROUTES.BUILD_JHA_DETAILS_PATH(updatedJha.instanceId!)}`,
    },
    options: {
      to: toUsers,
      subject: `JHA Submitted for Review: ${updatedJha.title}`,
    },
  };

  const emailJobId = `jha-${updatedJha.instanceId}-revised-email-${Date.now()}`;
  addJobToQueue(QUEUE_JOB_NAMES.SEND_EMAIL, emailJobPayload, { jobId: emailJobId });

  // Create notification record for all users who were notified
  const allNotificationUsers = [user, approver?.result?.at(0), owner?.result?.at(0)].filter(Boolean);
  const uniqueNotificationUsers = [...new Map(allNotificationUsers.map((u) => [u!.id, u])).values()];
  const notificationUserIds = uniqueNotificationUsers.map((u) => u!.id);

  if (notificationUserIds.length > 0) {
    // Create alert notification job payload
    const alertJobPayload: AlertNotificationJobPayload = {
      upkeepCompanyId: user.upkeepCompanyId,
      userIds: notificationUserIds,
      title: 'JHA Submitted for Review',
      description: `JHA "${updatedJha.title}" has been submitted for review${location?.result?.at(0) ? ` at ${location.result[0].name}` : ''}.`,
      url: ROUTES.BUILD_JHA_DETAILS_PATH(updatedJha.instanceId!),
    };

    // Generate unique job ID based on JHA instance ID and updated date
    const jobId = `jha-${updatedJha.instanceId}-revised-notification-${Date.now()}`;

    addJobToQueue(QUEUE_JOB_NAMES.SEND_ALERT_NOTIFICATION, alertJobPayload, { jobId });
  }
};

export const sendJhaApprovedNotification = async ({ updatedJha, user, headers }: JhaApprovedNotificationJobPayload) => {
  // Fetch related data in parallel
  const [locationResponse, ownerResponse, approverResponse, admins, { clientTimezone }] = await Promise.all([
    updatedJha?.locationId
      ? searchLocationsPublic({
          upkeepCompanyId: user.upkeepCompanyId,
          search: '',
          objectId: [updatedJha.locationId],
          limit: 1,
        })
      : undefined,
    updatedJha?.ownerId ? getUserPublic({ upkeepCompanyId: user.upkeepCompanyId, id: updatedJha.ownerId }) : undefined,
    updatedJha?.approverId
      ? getUserPublic({ upkeepCompanyId: user.upkeepCompanyId, id: updatedJha.approverId })
      : undefined,
    getUsersPublic({ upkeepCompanyId: user.upkeepCompanyId, userAccountType: USER_ACCOUNT_TYPES.ADMIN }),
    getCompany(headers),
  ]);

  // Build recipient list
  const toAdmins = (admins?.result || []).map((admin) => ({
    email: admin.email ?? '',
    fullName: admin.fullName ?? '',
    type: 'to' as const,
  }));

  const toOwner = ownerResponse
    ? [
        {
          email: ownerResponse.email ?? '',
          fullName: ownerResponse.fullName ?? '',
          type: 'to' as const,
        },
      ]
    : [];

  const toApprover = approverResponse
    ? [
        {
          email: approverResponse.email ?? '',
          fullName: approverResponse.fullName ?? '',
          type: 'to' as const,
        },
      ]
    : [];

  // Combine and deduplicate recipients
  const allRecipients = [...toAdmins, ...toOwner, ...toApprover];
  const toUsers = Array.from(new Map(allRecipients.map((item) => [item.email, item])).values()).filter(
    (item) => item.email && item.fullName,
  );

  const timestamp =
    clientTimezone && updatedJha.updatedAt ? new TZDate(updatedJha.updatedAt, clientTimezone) : new Date();

  const jhaData: JhaApprovedTemplateParams = {
    ...updatedJha,
    approvedAt: timestamp,
    location: locationResponse?.result?.at(0),
    owner: ownerResponse,
    approver: approverResponse,
    jhaUrl: `${env.EHS_URL}${ROUTES.BUILD_JHA_DETAILS_PATH(updatedJha.instanceId!)}`,
  };

  // Queue email sending
  const emailJobPayload: EmailJobPayload = {
    templateType: 'jha-approved',
    templateProps: jhaData,
    options: {
      to: toUsers,
      subject: `JHA Approved: ${updatedJha.title}`,
    },
  };

  const emailJobId = `jha-${updatedJha.instanceId}-approved-email-${Date.now()}`;
  addJobToQueue(QUEUE_JOB_NAMES.SEND_EMAIL, emailJobPayload, { jobId: emailJobId });

  // Create notification record for all users who were notified
  const allApprovedNotificationUsers = [ownerResponse, approverResponse, ...admins.result].filter(Boolean);
  const uniqueApprovedNotificationUsers = [...new Map(allApprovedNotificationUsers.map((u) => [u!.id, u])).values()];
  const approvedNotificationUserIds = uniqueApprovedNotificationUsers.map((u) => u!.id);

  if (approvedNotificationUserIds.length > 0) {
    // Create alert notification job payload
    const alertJobPayload: AlertNotificationJobPayload = {
      upkeepCompanyId: user.upkeepCompanyId,
      userIds: approvedNotificationUserIds,
      title: 'JHA Approved',
      description: `JHA "${updatedJha.title}" has been approved${locationResponse?.result?.at(0) ? ` at ${locationResponse.result[0].name}` : ''}.`,
      url: ROUTES.BUILD_JHA_DETAILS_PATH(updatedJha.instanceId!),
    };

    // Generate unique job ID based on JHA instance ID and updated date
    const jobId = `jha-${updatedJha.instanceId}-approved-notification-${Date.now()}`;

    addJobToQueue(QUEUE_JOB_NAMES.SEND_ALERT_NOTIFICATION, alertJobPayload, { jobId });
  }
};

export const sendJhaRejectedNotification = async ({
  updatedJha,
  user,
  rejectionReason,
  headers,
}: JhaRejectedNotificationJobPayload) => {
  const [locationResponse, ownerResponse, approverResponse, admins, { clientTimezone }] = await Promise.all([
    updatedJha?.locationId
      ? searchLocationsPublic({
          upkeepCompanyId: user.upkeepCompanyId,
          search: '',
          objectId: [updatedJha.locationId],
          limit: 1,
        })
      : undefined,
    updatedJha?.ownerId ? getUserPublic({ upkeepCompanyId: user.upkeepCompanyId, id: updatedJha.ownerId }) : undefined,
    updatedJha?.approverId
      ? getUserPublic({ upkeepCompanyId: user.upkeepCompanyId, id: updatedJha.approverId })
      : undefined,
    getUsersPublic({ upkeepCompanyId: user.upkeepCompanyId, userAccountType: USER_ACCOUNT_TYPES.ADMIN }),
    getCompany(headers),
  ]);

  // Build recipient list
  const toAdmins = (admins?.result || []).map((admin) => ({
    email: admin.email ?? '',
    fullName: admin.fullName ?? '',
    type: 'to' as const,
  }));

  const toOwner = ownerResponse
    ? [
        {
          email: ownerResponse.email ?? '',
          fullName: ownerResponse.fullName ?? '',
          type: 'to' as const,
        },
      ]
    : [];

  const toApprover = approverResponse
    ? [
        {
          email: approverResponse.email ?? '',
          fullName: approverResponse.fullName ?? '',
          type: 'to' as const,
        },
      ]
    : [];

  // Combine and deduplicate recipients
  const allRecipients = [...toAdmins, ...toOwner, ...toApprover];
  const toUsers = Array.from(new Map(allRecipients.map((item) => [item.email, item])).values()).filter(
    (item) => item.email && item.fullName,
  );

  const timestamp =
    clientTimezone && updatedJha.updatedAt ? new TZDate(updatedJha.updatedAt, clientTimezone) : new Date();

  const jhaData: JhaRejectedTemplateParams = {
    ...updatedJha,
    rejectedAt: timestamp,
    rejectionReason,
    location: locationResponse?.result?.at(0),
    owner: ownerResponse,
    approver: approverResponse,
    jhaUrl: `${env.EHS_URL}${ROUTES.BUILD_JHA_DETAILS_PATH(updatedJha.instanceId!)}`,
  };

  // Queue email sending
  const emailJobPayload: EmailJobPayload = {
    templateType: 'jha-rejected',
    templateProps: jhaData,
    options: {
      to: toUsers,
      subject: `JHA Requires Revision: ${updatedJha.title}`,
    },
  };

  const emailJobId = `jha-${updatedJha.instanceId}-rejected-email-${Date.now()}`;
  addJobToQueue(QUEUE_JOB_NAMES.SEND_EMAIL, emailJobPayload, { jobId: emailJobId });

  // Create notification record for all users who were notified
  const allRejectedNotificationUsers = [ownerResponse, approverResponse, ...admins.result].filter(Boolean);
  const uniqueRejectedNotificationUsers = [...new Map(allRejectedNotificationUsers.map((u) => [u!.id, u])).values()];
  const rejectedNotificationUserIds = uniqueRejectedNotificationUsers.map((u) => u!.id);

  if (rejectedNotificationUserIds.length > 0) {
    // Create alert notification job payload
    const alertJobPayload: AlertNotificationJobPayload = {
      upkeepCompanyId: user.upkeepCompanyId,
      userIds: rejectedNotificationUserIds,
      title: 'JHA Requires Revision',
      description: `JHA "${updatedJha.title}" requires revision${locationResponse?.result?.at(0) ? ` at ${locationResponse.result[0].name}` : ''}. Reason: ${rejectionReason}`,
      url: ROUTES.BUILD_JHA_DETAILS_PATH(updatedJha.instanceId!),
    };

    // Generate unique job ID based on JHA instance ID and updated date
    const jobId = `jha-${updatedJha.instanceId}-rejected-notification-${Date.now()}`;

    addJobToQueue(QUEUE_JOB_NAMES.SEND_ALERT_NOTIFICATION, alertJobPayload, { jobId });
  }
};

export const sendJhaOverdueNotification = async ({ jha, toUsers, toUsersIds }: JhaOverdueNotificationParams) => {
  const template = React.createElement(JhaOverdueTemplate, jha);

  await sendEmail(template, {
    to: toUsers,
    subject: `Upcoming JHA Review Due: ${jha.title}`,
  });

  // Create notification record for all users who were notified
  if (toUsersIds.length > 0) {
    await createNotificationForUsers({
      upkeepCompanyId: jha.upkeepCompanyId,
      userIds: toUsersIds,
      title: 'JHA Overdue',
      description: `${jha.slug} has an upcoming review due on ${formatDate(jha.reviewDate, true)}.`,
      url: ROUTES.BUILD_JHA_DETAILS_PATH(jha.instanceId!),
    });
  }
};
