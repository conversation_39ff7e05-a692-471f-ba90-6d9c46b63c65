import { TZDate } from '@date-fns/tz';
import { getAssets, searchAssetsPublic } from '@server/services/asset.service';
import { getCompany } from '@server/services/company.service';
import { EmailUser } from '@server/services/email.service';
import { addJobToQueue } from '@server/queue/queue-utils';
import { QUEUE_JOB_NAMES } from '@server/queue/job-names';
import { getLocationById, searchLocationsPublic } from '@server/services/location.service';
import { getUsersPublic } from '@server/services/user.service';
import { EventCreateTemplateParams } from '@server/templates/event-create';
import { EventUpdateTemplateParams } from '@server/templates/event-update';
import { type Headers } from '@server/utils/api';
import { ROUTES } from '@shared/ROUTE_PATHS';
import {
  AlertNotificationJobPayload,
  EventCreateNotificationJobPayload,
  EventPublicCreateNotificationJobPayload,
  EmailJobPayload,
} from '@shared/types/queues.types';
import { RouterOutputs } from '@shared/types/router.types';
import { User } from '@shared/types/users.types';

import { env } from 'env';

export const sendPublicEventCreateNotification = async ({
  createdEvent,
  upkeepCompanyId,
  reporterInfo,
  teamMembersToNotify,
}: EventPublicCreateNotificationJobPayload) => {
  const [location, assets] = await Promise.all([
    createdEvent.locationId
      ? searchLocationsPublic({
          upkeepCompanyId,
          search: '',
          objectId: [createdEvent.locationId],
          limit: 1,
        })
      : undefined,
    createdEvent.assetIds && createdEvent.assetIds.length > 0
      ? searchAssetsPublic({
          upkeepCompanyId,
          search: '',
          objectId: createdEvent.assetIds,
          limit: 100,
        })
      : { noResults: true, result: [], nextCursor: undefined },
  ]);

  const eventData: EventCreateTemplateParams = {
    ...createdEvent,
    location: location?.result?.at(0),
    assets: assets?.result || [],
    eventUrl: `${env.EHS_URL}${ROUTES.BUILD_EVENT_DETAILS_PATH(createdEvent.id!)}`,
    reportedBy: {
      email: reporterInfo.email,
      fullName: reporterInfo.fullName,
    },
  };

  const toUsers: EmailUser[] = [
    {
      email: reporterInfo.email,
      fullName: reporterInfo.fullName,
      type: 'to',
    },
    ...teamMembersToNotify.map((user) => ({
      email: user.email,
      fullName: user.fullName,
      type: 'to' as const,
    })),
  ];

  // Queue email sending
  const emailJobPayload: EmailJobPayload = {
    templateType: 'event-create',
    templateProps: {
      ...eventData,
      eventUrl: `${env.EHS_URL}${ROUTES.BUILD_EVENT_DETAILS_PATH(createdEvent.id!)}`,
    },
    options: {
      to: toUsers,
      subject: `New Safety Event Submitted: ${createdEvent.title}`,
    },
  };

  const emailJobId = `event-${createdEvent.id}-public-create-email-${Date.now()}`;
  addJobToQueue(QUEUE_JOB_NAMES.SEND_EMAIL, emailJobPayload, { jobId: emailJobId });

  // Create notification record for all users who were notified
  const userIdsToNotify = [
    reporterInfo.id, // Include the reporter
    ...teamMembersToNotify.map((user) => user.id),
  ].filter((id): id is string => typeof id === 'string');

  if (userIdsToNotify.length > 0) {
    // Create alert notification job payload
    const alertJobPayload: AlertNotificationJobPayload = {
      upkeepCompanyId,
      userIds: userIdsToNotify,
      title: 'New Safety Event Reported',
      description: `Safety event "${createdEvent.title}" has been reported${location?.result?.at(0) ? ` at ${location.result[0].name}` : ''}.`,
      url: ROUTES.BUILD_EVENT_DETAILS_PATH(createdEvent.id!),
    };

    // Generate unique job ID based on event ID and reported date
    const jobId = `event-${createdEvent.id}-notification-${Date.now()}`;

    addJobToQueue(QUEUE_JOB_NAMES.SEND_ALERT_NOTIFICATION, alertJobPayload, { jobId });
  }
};

export const sendEventCreateNotification = async ({
  createdEvent,
  user,
  headers,
  teamMembersToNotify,
}: EventCreateNotificationJobPayload) => {
  const [location, assets, teamMembersData, { clientTimezone }] = await Promise.all([
    createdEvent.locationId ? getLocationById(createdEvent.locationId, headers) : undefined,
    createdEvent.assetIds && createdEvent.assetIds.length > 0
      ? getAssets(
          {
            objectId: createdEvent.assetIds,
            cursor: 0,
            limit: 100,
          },
          headers,
        )
      : { result: [] },
    teamMembersToNotify && teamMembersToNotify.length > 0
      ? getUsersPublic({ upkeepCompanyId: user.upkeepCompanyId, objectId: teamMembersToNotify })
      : { noResults: true, result: [], nextCursor: undefined },
    getCompany(headers),
  ]);

  const usersToNotify = [...teamMembersData.result, user];
  const uniqueUsersToNotify = [...new Map(usersToNotify.map((user) => [user.id, user])).values()];

  const timestamp =
    clientTimezone && createdEvent.reportedAt
      ? new TZDate(createdEvent.reportedAt, clientTimezone)
      : createdEvent.reportedAt;

  const eventData: EventCreateTemplateParams = {
    ...createdEvent,
    reportedAt: timestamp!,
    location,
    assets: assets.result,
    reportedBy: {
      email: user.email,
      fullName: user.fullName,
    },
    eventUrl: `${env.EHS_URL}${ROUTES.BUILD_EVENT_DETAILS_PATH(createdEvent.id!)}`,
  };

  const toUsers: EmailUser[] = [
    ...uniqueUsersToNotify
      .filter((user) => user.email && user.fullName)
      .map((user) => ({
        email: user.email!,
        fullName: user.fullName!,
        type: 'to' as const,
      })),
  ];

  // Queue email sending
  const emailJobPayload: EmailJobPayload = {
    templateType: 'event-create',
    templateProps: {
      ...eventData,
      eventUrl: `${env.EHS_URL}${ROUTES.BUILD_EVENT_DETAILS_PATH(createdEvent.id!)}`,
    },
    options: {
      to: toUsers,
      subject: `New Safety Event Submitted: ${createdEvent.title}`,
    },
  };

  const emailJobId = `event-${createdEvent.id}-create-email-${Date.now()}`;
  addJobToQueue(QUEUE_JOB_NAMES.SEND_EMAIL, emailJobPayload, { jobId: emailJobId });

  // Create notification record for all users who were notified
  const userIdsToNotify = uniqueUsersToNotify.map((user) => user.id);

  // Create alert notification job payload
  const alertJobPayload: AlertNotificationJobPayload = {
    upkeepCompanyId: user.upkeepCompanyId,
    userIds: userIdsToNotify,
    title: 'New Safety Event Reported',
    description: `Safety event "${createdEvent.title}" has been reported${location ? ` at ${location.name}` : ''}.`,
    url: ROUTES.BUILD_EVENT_DETAILS_PATH(createdEvent.id!),
  };

  // Generate unique job ID based on event ID and reported date
  const jobId = `event-${createdEvent.id}-notification-${Date.now()}`;

  addJobToQueue(QUEUE_JOB_NAMES.SEND_ALERT_NOTIFICATION, alertJobPayload, { jobId });
};

export const sendEventUpdateNotification = async ({
  event,
  toUsers,
  actionPrefix = 'Updated',
}: {
  event: EventUpdateTemplateParams;
  toUsers: EmailUser[];
  actionPrefix?: string;
}) => {
  // Queue email sending
  const emailSubject = `Safety Event ${actionPrefix}: ${event.slug} - ${event.title}`;

  const emailJobPayload: EmailJobPayload = {
    templateType: 'event-update',
    templateProps: {
      ...event,
      eventUrl: `${env.EHS_URL}${ROUTES.BUILD_EVENT_DETAILS_PATH(event.id)}`,
    },
    options: {
      to: toUsers,
      subject: emailSubject,
    },
  };

  const emailJobId = `event-${event.id}-update-email-${Date.now()}`;
  addJobToQueue(QUEUE_JOB_NAMES.SEND_EMAIL, emailJobPayload, { jobId: emailJobId });
};

export const sendEventUpdateNotificationWithUser = async ({
  updatedEvent,
  user,
  headers,
  actionPrefix = 'Updated',
}: {
  updatedEvent: RouterOutputs['event']['update'];
  user: User;
  headers: Headers;
  actionPrefix?: string;
}) => {
  const [location, usersToNotify, { clientTimezone }] = await Promise.all([
    updatedEvent.locationId ? getLocationById(updatedEvent.locationId, headers) : undefined,
    updatedEvent?.teamMembersToNotify && updatedEvent?.teamMembersToNotify?.length > 0
      ? getUsersPublic({ upkeepCompanyId: user.upkeepCompanyId, objectId: updatedEvent.teamMembersToNotify })
      : { noResults: true, result: [], nextCursor: undefined },
    getCompany(headers),
  ]);

  const timestamp =
    clientTimezone && updatedEvent.reportedAt
      ? new TZDate(updatedEvent.reportedAt, clientTimezone)
      : updatedEvent.reportedAt;

  const event: EventUpdateTemplateParams = {
    ...updatedEvent,
    action: updatedEvent.archivedAt ? 'archived' : 'unarchived',
    location,
    reportedAt: timestamp!,
    eventUrl: `${env.EHS_URL}${ROUTES.BUILD_EVENT_DETAILS_PATH(updatedEvent.id!)}`,
    reportedBy: {
      email: user.email,
      fullName: user.fullName,
    },
  };

  const toUsers: EmailUser[] = [
    {
      email: user.email,
      fullName: user.fullName,
      type: 'to' as const,
    },
    ...usersToNotify.result
      .filter((user) => user.email && user.fullName)
      .map((user) => ({
        email: user.email!,
        fullName: user.fullName!,
        type: 'to' as const,
      })),
  ];

  await sendEventUpdateNotification({
    event,
    toUsers,
    actionPrefix,
  });

  // Create notification record for all users who were notified
  const allUsersToNotify = [user, ...usersToNotify.result];
  const uniqueUsersToNotify = [...new Map(allUsersToNotify.map((u) => [u.id, u])).values()];
  const userIdsToNotify = uniqueUsersToNotify.map((u) => u.id);

  if (userIdsToNotify.length > 0) {
    // Create alert notification job payload
    const alertJobPayload: AlertNotificationJobPayload = {
      upkeepCompanyId: user.upkeepCompanyId,
      userIds: userIdsToNotify,
      title: `Safety Event ${actionPrefix}`,
      description: `Safety event "${updatedEvent.title}" (${updatedEvent.slug}) has been ${actionPrefix.toLowerCase()}${location ? ` at ${location.name}` : ''}.`,
      url: ROUTES.BUILD_EVENT_DETAILS_PATH(updatedEvent.id!),
    };

    // Generate unique job ID based on event ID and reported date
    const jobId = `event-${updatedEvent.id}-update-notification-${Date.now()}`;

    addJobToQueue(QUEUE_JOB_NAMES.SEND_ALERT_NOTIFICATION, alertJobPayload, { jobId });
  }
};
