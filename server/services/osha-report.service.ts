import { db } from '@server/db';
import { createPaginatedResponse } from '@server/utils/pagination';
import { events, oshaAuditTrail, oshaLocations, oshaReports } from '@shared/schema';
import {
  CreateOshaReportForm,
  EditOshaReportForm,
  ExportOshaReportsSchema,
  GetEstablishInformation,
  ListOshaReportsSchema,
} from '@shared/types/osha.types';
import { IdSchema, PaginatedResponse } from '@shared/types/schema.types';
import { User } from '@shared/types/users.types';
import { TRPCError } from '@trpc/server';
import { and, asc, desc, eq, getTableColumns, ilike, inArray, isNull, or, sql } from 'drizzle-orm';
import { z } from 'zod';

export const createOshaReport = async (input: CreateOshaReportForm, user: User) => {
  return db.transaction(async (tx) => {
    const values = {
      ...input,
      createdBy: user!.id,
      upkeepCompanyId: user!.upkeepCompanyId,
    };

    const inserted = await tx.insert(oshaReports).values(values).returning({
      id: oshaReports.id,
      slug: oshaReports.slug,
    });

    const report = inserted.at(0);
    if (!report) {
      return;
    }

    await tx.insert(oshaAuditTrail).values({
      upkeepCompanyId: user.upkeepCompanyId,
      entityId: report.id,
      entityType: 'osha_report',
      action: 'created',
      createdBy: user.id!,
      userAgent: input.userAgent || 'unknown',
      ipAddress: input.ipAddress || 'unknown',
      details: JSON.stringify(values),
    });

    return report;
  });
};

export const updateOshaReport = async (input: EditOshaReportForm, user: User) => {
  return db.transaction(async (tx) => {
    const values = {
      ...input,
      updatedBy: user!.id,
    };

    const updated = await tx.update(oshaReports).set(values).where(eq(oshaReports.id, input.id!)).returning({
      id: oshaReports.id,
    });

    const report = updated.at(0);
    if (!report) {
      return;
    }

    await tx.insert(oshaAuditTrail).values({
      upkeepCompanyId: user!.upkeepCompanyId,
      entityId: report.id,
      entityType: 'osha_report',
      action: 'updated',
      createdBy: user!.id,
      userAgent: input.userAgent || 'unknown',
      ipAddress: input.ipAddress || 'unknown',
      details: JSON.stringify(values),
    });

    return report;
  });
};
export const getOshaReportById = async (id: string, user: User) => {
  const result = await db
    .select({
      ...getTableColumns(oshaReports),
      event: {
        id: events.id,
        slug: events.slug,
        title: events.title,
        description: events.description,
        reportedAt: events.reportedAt,
        reportedBy: events.reportedBy,
        reportedByName: events.reportedByName,
        reportedByEmail: events.reportedByEmail,
        type: events.type,
        category: events.category,
        severity: events.severity,
        status: events.status,
        locationId: events.locationId,
        assetIds: events.assetIds,
        immediateActions: events.immediateActions,
        oshaReportable: events.oshaReportable,
      },
      oshaLocation: {
        id: oshaLocations.id,
        name: oshaLocations.name,
      },
    })
    .from(oshaReports)
    .leftJoin(events, eq(oshaReports.eventId, events.id))
    .leftJoin(oshaLocations, eq(oshaReports.oshaLocationId, oshaLocations.id))
    .where(and(eq(oshaReports.id, id), eq(oshaReports.upkeepCompanyId, user.upkeepCompanyId)));

  const report = result.at(0);

  if (!report) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: `OSHA report with ID ${id} not found`,
    });
  }

  // Privacy case validation: mask employeeName if privacyCase is true
  if (report.privacyCase) {
    report.employeeName = null;
  }

  return report;
};

export const listOshaReports = async (
  input: z.infer<typeof ListOshaReportsSchema> & { cursor?: number },
  user: User,
): Promise<PaginatedResponse<(typeof filtered)[0]>> => {
  const {
    cursor = 0,
    limit = 10,
    sortBy = 'createdAt',
    sortOrder = 'desc',
    caseType = [],
    includeArchived = false,
    year,
    oshaLocationId,
    search,
  } = input;

  // Build the query
  let query = db
    .select({
      id: oshaReports.id,
      slug: oshaReports.slug,
      employeeName: sql<string | null>`
        CASE 
          WHEN ${oshaReports.privacyCase} = true THEN 'Privacy Case'
          ELSE ${oshaReports.employeeName}
        END
      `,
      employeeJobTitle: oshaReports.employeeJobTitle,
      employeeWorkLocation: oshaReports.employeeWorkLocation,
      type: oshaReports.type,
      wasDeceased: oshaReports.wasDeceased,
      createdAt: oshaReports.createdAt,
      locationId: events.locationId,
      daysAwayFromWork: oshaReports.daysAwayFromWork,
      daysRestrictedFromWork: oshaReports.daysRestrictedFromWork,
      privacyCase: oshaReports.privacyCase,
      archivedBy: oshaReports.archivedBy,
      archivedAt: oshaReports.archivedAt,
      eventId: oshaReports.eventId,
      eventTitle: events.title,
      eventReportedAt: events.reportedAt,
      oshaLocation: {
        id: oshaLocations.id,
        name: oshaLocations.name,
      },
    })
    .from(oshaReports)
    .leftJoin(events, eq(oshaReports.eventId, events.id))
    .leftJoin(oshaLocations, eq(oshaReports.oshaLocationId, oshaLocations.id))
    .where(
      and(
        eq(oshaReports.upkeepCompanyId, user.upkeepCompanyId),
        !includeArchived ? isNull(oshaReports.archivedAt) : undefined,
        caseType?.length ? inArray(oshaReports.type, caseType) : undefined,
        oshaLocationId ? eq(oshaReports.oshaLocationId, oshaLocationId) : undefined,
        year ? eq(sql`EXTRACT(YEAR FROM ${oshaReports.createdAt})`, year) : undefined,
        search
          ? or(
              ilike(oshaReports.employeeName, `%${search}%`),
              ilike(oshaReports.employeeJobTitle, `%${search}%`),
              ilike(oshaReports.employeeWorkLocation, `%${search}%`),
              ilike(oshaReports.slug, `%${search}%`),
            )
          : undefined,
      ),
    )
    .$dynamic();

  // Handle sorting
  if (sortBy) {
    switch (sortBy) {
      case 'createdAt':
        query = query.orderBy(sortOrder === 'desc' ? desc(oshaReports.createdAt) : asc(oshaReports.createdAt));
        break;
      case 'employeeName':
        query = query.orderBy(
          sortOrder === 'desc'
            ? desc(
                sql`CASE WHEN ${oshaReports.privacyCase} = true THEN 'Privacy Case' ELSE ${oshaReports.employeeName} END`,
              )
            : asc(
                sql`CASE WHEN ${oshaReports.privacyCase} = true THEN 'Privacy Case' ELSE ${oshaReports.employeeName} END`,
              ),
        );
        break;
      case 'daysAway':
        query = query.orderBy(
          sortOrder === 'desc' ? desc(oshaReports.daysAwayFromWork) : asc(oshaReports.daysAwayFromWork),
        );
        break;
      default:
        query = query.orderBy(sortOrder === 'desc' ? desc(oshaReports.createdAt) : asc(oshaReports.createdAt));
    }
  }

  const offset = cursor;
  const filtered = await query.limit(limit).offset(offset);

  return createPaginatedResponse(filtered, { cursor, limit });
};

export const exportOshaReports = async (input: z.infer<typeof ExportOshaReportsSchema>, user: User) => {
  const { search, includeArchived, caseType = [], oshaLocationId, year } = input;

  return await db
    .select({
      id: oshaReports.id,
      slug: oshaReports.slug,
      employeeName: sql<string | null>`
        CASE 
          WHEN ${oshaReports.privacyCase} = true THEN 'Privacy Case'
          ELSE ${oshaReports.employeeName}
        END
      `,
      employeeJobTitle: oshaReports.employeeJobTitle,
      employeeWorkLocation: oshaReports.employeeWorkLocation,
      type: oshaReports.type,
      wasDeceased: oshaReports.wasDeceased,
      createdAt: oshaReports.createdAt,
      locationId: events.locationId,
      daysAwayFromWork: oshaReports.daysAwayFromWork,
      daysRestrictedFromWork: oshaReports.daysRestrictedFromWork,
      privacyCase: oshaReports.privacyCase,
      archivedBy: oshaReports.archivedBy,
      archivedAt: oshaReports.archivedAt,
      eventId: oshaReports.eventId,
      eventTitle: events.title,
      eventReportedAt: events.reportedAt,
      oshaLocation: {
        id: oshaLocations.id,
        name: oshaLocations.name,
      },
    })
    .from(oshaReports)
    .leftJoin(events, eq(oshaReports.eventId, events.id))
    .leftJoin(oshaLocations, eq(oshaReports.oshaLocationId, oshaLocations.id))
    .where(
      and(
        eq(oshaReports.upkeepCompanyId, user.upkeepCompanyId),
        !includeArchived ? isNull(oshaReports.archivedAt) : undefined,
        caseType?.length ? inArray(oshaReports.type, caseType) : undefined,
        oshaLocationId ? eq(oshaReports.oshaLocationId, oshaLocationId) : undefined,
        year ? eq(sql`EXTRACT(YEAR FROM ${oshaReports.createdAt})`, year) : undefined,
        search
          ? or(
              ilike(oshaReports.employeeName, `%${search}%`),
              ilike(oshaReports.employeeJobTitle, `%${search}%`),
              ilike(oshaReports.employeeWorkLocation, `%${search}%`),
              ilike(oshaReports.slug, `%${search}%`),
            )
          : undefined,
      ),
    )
    .limit(500);
};

export async function getOshaCasesSummary(input: GetEstablishInformation, user: User) {
  if (!input.oshaLocationId) {
    return null;
  }

  const result = await db
    .select({
      deaths: sql<number>`COUNT(*) FILTER (WHERE ${oshaReports.wasDeceased} = true)`,

      daysAwayCases: sql<number>`COUNT(*) FILTER (
        WHERE ${oshaReports.wasDeceased} = false AND ${oshaReports.daysAwayFromWork} > 0
      )`,

      restrictedWorkCases: sql<number>`COUNT(*) FILTER (
        WHERE ${oshaReports.wasDeceased} = false
          AND ${oshaReports.daysAwayFromWork} = 0
          AND ${oshaReports.daysRestrictedFromWork} > 0
      )`,

      otherCases: sql<number>`COUNT(*) FILTER (
        WHERE ${oshaReports.wasDeceased} = false
          AND ${oshaReports.daysAwayFromWork} = 0
          AND ${oshaReports.daysRestrictedFromWork} = 0
          AND ${oshaReports.type} IS NOT NULL
      )`,

      totalDaysAway: sql<number>`SUM(
        CASE
          WHEN ${oshaReports.wasDeceased} = false AND ${oshaReports.daysAwayFromWork} > 0
          THEN ${oshaReports.daysAwayFromWork}
          ELSE 0
        END
      )`,

      totalDaysRestricted: sql<number>`SUM(
        CASE
          WHEN ${oshaReports.wasDeceased} = false
            AND ${oshaReports.daysAwayFromWork} = 0
            AND ${oshaReports.daysRestrictedFromWork} > 0
          THEN ${oshaReports.daysRestrictedFromWork}
          ELSE 0
        END
      )`,
    })
    .from(oshaReports)
    .where(
      and(
        eq(oshaReports.upkeepCompanyId, user.upkeepCompanyId),
        isNull(oshaReports.archivedAt),
        sql`EXTRACT(YEAR FROM ${oshaReports.createdAt}) = ${input.year}`,
        eq(oshaReports.oshaLocationId, input.oshaLocationId),
      ),
    );

  const summary = result.at(0);

  if (!summary) {
    return {
      deaths: 0,
      daysAwayCases: 0,
      totalDaysAway: 0,
      restrictedWorkCases: 0,
      totalDaysRestricted: 0,
      otherCases: 0,
      totalCases: 0,
    };
  }

  return {
    deaths: Number(summary.deaths),
    daysAwayCases: Number(summary.daysAwayCases),
    totalDaysAway: Number(summary.totalDaysAway),
    restrictedWorkCases: Number(summary.restrictedWorkCases),
    totalDaysRestricted: Number(summary.totalDaysRestricted),
    otherCases: Number(summary.otherCases),
    totalCases:
      Number(summary.deaths) +
      Number(summary.daysAwayCases) +
      Number(summary.restrictedWorkCases) +
      Number(summary.otherCases),
  };
}

export const toggleArchiveOshaReport = async (
  input: z.infer<typeof IdSchema> & { ipAddress: string; userAgent: string },
  user: User,
) => {
  return await db.transaction(async (tx) => {
    const now = new Date();
    const updated = await tx
      .update(oshaReports)
      .set({
        archivedAt: sql`CASE WHEN ${oshaReports.archivedAt} IS NULL THEN CAST(${now} AS TIMESTAMP) ELSE NULL END`,
        archivedBy: sql`CASE WHEN ${oshaReports.archivedAt} IS NULL THEN ${user.id} ELSE NULL END`,
        updatedAt: now,
      })
      .where(eq(oshaReports.id, input.id))
      .returning({
        id: oshaReports.id,
        archivedBy: oshaReports.archivedBy,
        archivedAt: oshaReports.archivedAt,
      });

    const report = updated.at(0);

    if (!report) {
      return;
    }

    await tx.insert(oshaAuditTrail).values({
      upkeepCompanyId: user.upkeepCompanyId,
      entityId: report.id,
      entityType: 'osha_report',
      action: report.archivedAt ? 'archived' : 'restored',
      createdBy: user.id,
      userAgent: input.userAgent,
      ipAddress: input.ipAddress,
      details: JSON.stringify(report),
    });

    return report;
  });
};
