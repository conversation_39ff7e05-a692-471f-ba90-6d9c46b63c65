import { db } from '@server/db';
import { createPaginatedResponse } from '@server/utils/pagination';
import { oshaAgencyReport, oshaAuditTrail, oshaLocations } from '@shared/schema';
import {
  CreateOshaAgencyReportFormSchema,
  EditOshaAgencyReportForm,
  ExportOshaAgencyReportsSchema,
  ListOshaAgencyReportsSchema,
} from '@shared/types/osha.types';
import { IdSchema } from '@shared/types/schema.types';
import { User } from '@shared/types/users.types';

import { and, desc, eq, getTableColumns, inArray, sql, isNull } from 'drizzle-orm';
import { z } from 'zod';

export const createOshaAgencyReport = async (input: z.infer<typeof CreateOshaAgencyReportFormSchema>, user: User) => {
  return db.transaction(async (tx) => {
    const inserted = await tx
      .insert(oshaAgencyReport)
      .values({
        ...input,
        createdBy: user.id,
        upkeepCompanyId: user.upkeepCompanyId,
      })
      .returning({
        id: oshaAgencyReport.id,
        slug: oshaAgencyReport.slug,
        typeOfIncident: oshaAgencyReport.typeOfIncident,
        dateOfIncident: oshaAgencyReport.dateOfIncident,
        description: oshaAgencyReport.description,
        companyContactPerson: oshaAgencyReport.companyContactPerson,
        contactPersonPhone: oshaAgencyReport.contactPersonPhone,
        createdBy: oshaAgencyReport.createdBy,
        upkeepCompanyId: oshaAgencyReport.upkeepCompanyId,
        createdAt: oshaAgencyReport.createdAt,
      });

    const report = inserted.at(0);
    if (!report) {
      return;
    }

    await tx.insert(oshaAuditTrail).values({
      upkeepCompanyId: user.upkeepCompanyId,
      entityId: report.id,
      entityType: 'osha_agency_report',
      action: 'created',
      createdBy: user.id,
      userAgent: input.userAgent || 'unknown',
      ipAddress: input.ipAddress || 'unknown',
    });

    return report;
  });
};

export const updateOshaAgencyReport = async (input: EditOshaAgencyReportForm, user: User) => {
  return db.transaction(async (tx) => {
    const { id, ...updateData } = input;

    if (!id) {
      return;
    }

    // Apply default 0 value for undefined affectedCount field
    const processedUpdateData = {
      ...updateData,
      updatedAt: new Date(),
      // Ensure affectedCount field has default 0 value if undefined
      ...(updateData.affectedCount !== undefined && { affectedCount: updateData.affectedCount ?? 0 }),
    };

    const updated = await tx
      .update(oshaAgencyReport)
      .set(processedUpdateData)
      .where(and(eq(oshaAgencyReport.id, id), eq(oshaAgencyReport.upkeepCompanyId, user.upkeepCompanyId)))
      .returning({
        id: oshaAgencyReport.id,
        slug: oshaAgencyReport.slug,
        typeOfIncident: oshaAgencyReport.typeOfIncident,
        dateOfIncident: oshaAgencyReport.dateOfIncident,
        oshaLocationId: oshaAgencyReport.oshaLocationId,
        description: oshaAgencyReport.description,
        companyContactPerson: oshaAgencyReport.companyContactPerson,
        contactPersonPhone: oshaAgencyReport.contactPersonPhone,
        archivedAt: oshaAgencyReport.archivedAt,
        upkeepCompanyId: oshaAgencyReport.upkeepCompanyId,
        createdBy: oshaAgencyReport.createdBy,
      });

    const report = updated.at(0);
    if (!report) {
      return;
    }

    await tx.insert(oshaAuditTrail).values({
      upkeepCompanyId: user.upkeepCompanyId,
      entityId: report.id,
      entityType: 'osha_agency_report',
      action: 'updated',
      createdBy: user.id,
      userAgent: input.userAgent || 'unknown',
      ipAddress: input.ipAddress || 'unknown',
      details: JSON.stringify(updateData),
    });

    return {
      ...report,
    };
  });
};

export const listOshaAgencyReports = async (
  input: z.infer<typeof ListOshaAgencyReportsSchema> & { cursor?: number },
  user: User,
) => {
  const {
    cursor = 0,
    limit = 10,
    search,
    oshaLocationId,
    typeOfIncident,
    dateRange,
    year,
    includeArchived = false,
  } = input;

  // Build the query using the standard pattern
  let query = db
    .select({
      ...getTableColumns(oshaAgencyReport),
      oshaLocation: {
        id: oshaLocations.id,
        name: oshaLocations.name,
      },
    })
    .from(oshaAgencyReport)
    .leftJoin(oshaLocations, eq(oshaAgencyReport.oshaLocationId, oshaLocations.id))
    .where(
      and(
        eq(oshaAgencyReport.upkeepCompanyId, user.upkeepCompanyId),
        !includeArchived ? isNull(oshaAgencyReport.archivedAt) : undefined,
        search
          ? sql`(
              ${oshaAgencyReport.slug} ILIKE ${`%${search}%`} OR 
              ${oshaAgencyReport.description} ILIKE ${`%${search}%`} OR 
              ${oshaAgencyReport.companyContactPerson} ILIKE ${`%${search}%`} OR 
              ${oshaAgencyReport.contactPersonPhone} ILIKE ${`%${search}%`}
            )`
          : undefined,
        oshaLocationId ? eq(oshaAgencyReport.oshaLocationId, oshaLocationId) : undefined,
        typeOfIncident?.length ? inArray(oshaAgencyReport.typeOfIncident, typeOfIncident) : undefined,
        dateRange?.from ? sql`${oshaAgencyReport.dateOfIncident} >= ${dateRange.from}` : undefined,
        dateRange?.to ? sql`${oshaAgencyReport.dateOfIncident} <= ${dateRange.to}` : undefined,
        year ? sql`EXTRACT(YEAR FROM ${oshaAgencyReport.dateOfIncident}) = ${year}` : undefined,
      ),
    )
    .$dynamic();

  query = query.orderBy(desc(oshaAgencyReport.createdAt));

  const filtered = await query.limit(limit).offset(cursor);

  return createPaginatedResponse(filtered, { cursor, limit });
};

export const exportOshaAgencyReports = async (input: z.infer<typeof ExportOshaAgencyReportsSchema>, user: User) => {
  const { search, oshaLocationId, typeOfIncident, dateRange, year, includeArchived = false } = input;
  return await db
    .select({
      ...getTableColumns(oshaAgencyReport),
      oshaLocation: {
        id: oshaLocations.id,
        name: oshaLocations.name,
      },
    })
    .from(oshaAgencyReport)
    .leftJoin(oshaLocations, eq(oshaAgencyReport.oshaLocationId, oshaLocations.id))
    .where(
      and(
        eq(oshaAgencyReport.upkeepCompanyId, user.upkeepCompanyId),
        !includeArchived ? isNull(oshaAgencyReport.archivedAt) : undefined,
        search
          ? sql`(
            ${oshaAgencyReport.slug} ILIKE ${`%${search}%`} OR 
            ${oshaAgencyReport.description} ILIKE ${`%${search}%`} OR 
            ${oshaAgencyReport.companyContactPerson} ILIKE ${`%${search}%`} OR 
            ${oshaAgencyReport.contactPersonPhone} ILIKE ${`%${search}%`}
          )`
          : undefined,
        oshaLocationId ? eq(oshaAgencyReport.oshaLocationId, oshaLocationId) : undefined,
        typeOfIncident?.length ? inArray(oshaAgencyReport.typeOfIncident, typeOfIncident) : undefined,
        dateRange?.from ? sql`${oshaAgencyReport.dateOfIncident} >= ${dateRange.from}` : undefined,
        dateRange?.to ? sql`${oshaAgencyReport.dateOfIncident} <= ${dateRange.to}` : undefined,
        year ? sql`EXTRACT(YEAR FROM ${oshaAgencyReport.dateOfIncident}) = ${year}` : undefined,
      ),
    )
    .limit(500);
};

export const getOshaAgencyReport = async (id: string, user: User) => {
  const result = await db
    .select({
      ...getTableColumns(oshaAgencyReport),
      oshaLocation: {
        id: oshaLocations.id,
        name: oshaLocations.name,
      },
    })
    .from(oshaAgencyReport)
    .leftJoin(oshaLocations, eq(oshaAgencyReport.oshaLocationId, oshaLocations.id))
    .where(and(eq(oshaAgencyReport.id, id), eq(oshaAgencyReport.upkeepCompanyId, user.upkeepCompanyId)))
    .limit(1);

  return result.at(0);
};

export const toggleArchiveOshaAgencyReport = async (
  input: z.infer<typeof IdSchema> & { ipAddress: string; userAgent: string },
  user: User,
) => {
  return await db.transaction(async (tx) => {
    const now = new Date();
    const updated = await tx
      .update(oshaAgencyReport)
      .set({
        archivedAt: sql`CASE WHEN ${oshaAgencyReport.archivedAt} IS NOT NULL THEN NULL ELSE NOW() END`,
        updatedAt: now,
      })
      .where(eq(oshaAgencyReport.id, input.id))
      .returning({
        id: oshaAgencyReport.id,
        archivedAt: oshaAgencyReport.archivedAt,
      });

    const report = updated.at(0);

    if (!report) {
      return;
    }

    await tx.insert(oshaAuditTrail).values({
      upkeepCompanyId: user.upkeepCompanyId,
      entityId: report.id,
      entityType: 'osha_agency_report',
      action: report.archivedAt ? 'archived' : 'restored',
      createdBy: user.id,
      userAgent: input.userAgent ?? 'unknown',
      ipAddress: input.ipAddress ?? 'unknown',
      details: JSON.stringify(report),
    });

    return report;
  });
};
