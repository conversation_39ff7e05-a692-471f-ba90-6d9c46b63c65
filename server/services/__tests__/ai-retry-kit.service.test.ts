import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { z, ZodError } from 'zod';

// Mock environment variables first
vi.mock('../../env', () => ({
  env: {
    OPENAI_API_KEY: 'test-api-key',
  },
  OpenAIModel: {
    GPT_4_TURBO: 'gpt-4-turbo',
    GPT_5_NANO: 'gpt-5-nano',
    GPT_4_1: 'gpt-4.1',
  },
}));

// Mock the AI SDK
vi.mock('ai', () => ({
  generateObject: vi.fn(),
}));

// Mock logger
vi.mock('@server/utils/logger', () => ({
  logger: {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  },
}));

// Mock prompts service
vi.mock('../prompts.service', () => ({
  getSchemaFixingPrompts: vi.fn(),
}));

// Import the functions under test after mocks are set up
import { generateObjectWithRetry, retryWithSchemaFix } from '../ai-retry-kit.service';
import { generateObject } from 'ai';
import { logger } from '@server/utils/logger';
import { getSchemaFixingPrompts } from '../prompts.service';

// Type the mocked functions
const mockGenerateObject = vi.mocked(generateObject);
const mockLogger = vi.mocked(logger);
const mockGetSchemaFixingPrompts = vi.mocked(getSchemaFixingPrompts);

describe('ai-retry-kit.service', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Default mock for schema fixing prompts
    mockGetSchemaFixingPrompts.mockReturnValue({
      systemPrompt: 'Fix the validation errors',
      generateUserPrompt: vi.fn().mockReturnValue('Fix this object'),
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('generateObjectWithRetry', () => {
    const mockSchema = z.object({
      name: z.string(),
      age: z.number(),
      email: z.string().email(),
    });

    const validParams = {
      model: 'gpt-5-nano',
      schema: mockSchema,
      prompt: 'Generate a user object',
    };

    describe('Strategy 1: Successful original generation', () => {
      it('should return result immediately on successful generation', async () => {
        const expectedResult = { name: 'John', age: 30, email: '<EMAIL>' };
        mockGenerateObject.mockResolvedValueOnce({
          object: expectedResult,
          finishReason: 'stop',
          usage: { inputTokens: 10, outputTokens: 20, totalTokens: 30 },
          warnings: [],
          request: {} as any,
          response: {} as any,
          reasoning: undefined,
          providerMetadata: undefined,
          toJsonResponse: () => ({}) as any,
        });

        const result = await generateObjectWithRetry({
          params: validParams,
          context: 'test-context',
        });

        expect(result).toEqual({
          object: expectedResult,
        });
        expect(mockGenerateObject).toHaveBeenCalledTimes(1);
        expect(mockGenerateObject).toHaveBeenCalledWith(validParams);
      });

      it('should work without context parameter', async () => {
        const expectedResult = { name: 'Jane', age: 25, email: '<EMAIL>' };
        mockGenerateObject.mockResolvedValueOnce({
          object: expectedResult,
          finishReason: 'stop',
          usage: { inputTokens: 10, outputTokens: 20, totalTokens: 30 },
          warnings: [],
          request: {} as any,
          response: {} as any,
          reasoning: undefined,
          providerMetadata: undefined,
          toJsonResponse: () => ({}) as any,
        });

        const result = await generateObjectWithRetry({
          params: validParams,
        });

        expect(result).toEqual({
          object: expectedResult,
        });
        expect(mockGenerateObject).toHaveBeenCalledTimes(1);
      });
    });

    describe('Strategy 2: Schema fixing for Zod errors', () => {
      const createZodError = (issues: any[]) => {
        const error = new ZodError(issues);
        return error;
      };

      const createAINoObjectGeneratedError = (zodError: ZodError, originalValue: any) => {
        const aiError = new Error('AI generation failed');
        aiError.name = 'AI_NoObjectGeneratedError';
        aiError.cause = {
          cause: zodError,
          value: originalValue,
        };
        return aiError;
      };

      it('should attempt schema fixing when Zod validation fails', async () => {
        const originalValue = { name: 'John', age: 'thirty', email: 'invalid-email' };
        const zodError = createZodError([
          { path: ['age'], message: 'Expected number, received string' },
          { path: ['email'], message: 'Invalid email format' },
        ]);
        const aiError = createAINoObjectGeneratedError(zodError, originalValue);

        const fixedResult = { name: 'John', age: 30, email: '<EMAIL>' };

        // First call fails with validation error
        mockGenerateObject.mockRejectedValueOnce(aiError);

        // Schema fixing call succeeds
        mockGenerateObject.mockResolvedValueOnce({
          object: {
            fixedObject: fixedResult,
            explanation: 'Fixed age type and email format',
          },
          finishReason: 'stop',
          usage: { inputTokens: 10, outputTokens: 20, totalTokens: 30 },
          warnings: [],
          request: {} as any,
          response: {} as any,
          reasoning: undefined,
          providerMetadata: undefined,
          toJsonResponse: () => ({}) as any,
        });

        const result = await generateObjectWithRetry({
          params: validParams,
          context: 'test-schema-fix',
          maxRetries: 3,
        });

        expect(result).toEqual({ object: fixedResult });
        expect(mockGenerateObject).toHaveBeenCalledTimes(2);
        expect(mockLogger.warn).toHaveBeenCalledWith(
          'Schema validation failed, attempting schema fixing',
          expect.objectContaining({
            context: 'test-schema-fix',
            errorCount: 2,
            maxRetries: 3,
          }),
        );
        expect(mockLogger.info).toHaveBeenCalledWith('Schema fixing succeeded on attempt 1', {
          context: 'test-schema-fix',
        });
      });

      it('should retry schema fixing up to maxRetries times', async () => {
        const originalValue = { name: 'John', age: 'thirty' };
        const zodError = createZodError([{ path: ['age'], message: 'Expected number, received string' }]);
        const aiError = createAINoObjectGeneratedError(zodError, originalValue);

        // First call fails
        mockGenerateObject.mockRejectedValueOnce(aiError);

        // All schema fixing attempts fail to test exhaustion
        mockGenerateObject.mockRejectedValue(new Error('Schema fixing failed'));

        await expect(
          generateObjectWithRetry({
            params: validParams,
            context: 'test-multiple-retries',
            maxRetries: 3,
            allowOriginalRetryOnUnknownError: false,
          }),
        ).rejects.toThrow();

        // Should have attempted: 1 original + 3 schema fixing attempts = 4 total
        expect(mockGenerateObject).toHaveBeenCalledTimes(4);
        expect(mockLogger.warn).toHaveBeenCalledWith(
          'Schema fix attempt 1/3',
          expect.objectContaining({
            context: 'test-multiple-retries',
          }),
        );
        expect(mockLogger.warn).toHaveBeenCalledWith(
          'Schema fix attempt 2/3',
          expect.objectContaining({
            context: 'test-multiple-retries',
          }),
        );
        expect(mockLogger.warn).toHaveBeenCalledWith(
          'Schema fix attempt 3/3',
          expect.objectContaining({
            context: 'test-multiple-retries',
          }),
        );
      });

      it('should exhaust all schema fix attempts and continue to original retry', async () => {
        const originalValue = { name: 'John', age: 'thirty' };
        const zodError = createZodError([{ path: ['age'], message: 'Expected number, received string' }]);
        const aiError = createAINoObjectGeneratedError(zodError, originalValue);

        // First call fails
        mockGenerateObject.mockRejectedValueOnce(aiError);

        // All schema fixing attempts fail
        mockGenerateObject.mockRejectedValue(zodError);

        await expect(
          generateObjectWithRetry({
            params: validParams,
            context: 'test-exhausted-schema-fix',
            maxRetries: 2,
            allowOriginalRetryOnUnknownError: false,
          }),
        ).rejects.toThrow();

        expect(mockGenerateObject).toHaveBeenCalledTimes(3); // 1 original + 2 fix attempts
        expect(mockLogger.warn).toHaveBeenCalledWith('Schema fix attempt 1/2', expect.any(Object));
        expect(mockLogger.warn).toHaveBeenCalledWith('Schema fix attempt 2/2', expect.any(Object));
      });

      it('should handle non-Zod errors in AI_NoObjectGeneratedError', async () => {
        const nonZodError = new Error('Non-Zod error');
        nonZodError.name = 'AI_NoObjectGeneratedError';
        nonZodError.cause = {
          cause: new Error('Some other error'),
          value: { some: 'value' },
        };

        mockGenerateObject.mockRejectedValueOnce(nonZodError);

        await expect(
          generateObjectWithRetry({
            params: validParams,
            context: 'test-non-zod-error',
            allowOriginalRetryOnUnknownError: false,
          }),
        ).rejects.toThrow();

        expect(mockLogger.info).toHaveBeenCalledWith(
          'Original retry disabled or no attempts configured',
          expect.objectContaining({
            allowOriginalRetryOnUnknownError: false,
            originalRetryAttempts: 1,
          }),
        );
      });
    });

    describe('Strategy 3: Original retry for non-Zod errors', () => {
      it('should attempt original retry when enabled', async () => {
        const genericError = new Error('Generic API error');

        // First call fails
        mockGenerateObject.mockRejectedValueOnce(genericError);

        // Original retry succeeds
        const expectedResult = { name: 'John', age: 30, email: '<EMAIL>' };
        mockGenerateObject.mockResolvedValueOnce({
          object: expectedResult,
          finishReason: 'stop',
          usage: { inputTokens: 10, outputTokens: 20, totalTokens: 30 },
          warnings: [],
          request: {} as any,
          response: {} as any,
          reasoning: undefined,
          providerMetadata: undefined,
          toJsonResponse: () => ({}) as any,
        });

        const result = await generateObjectWithRetry({
          params: validParams,
          context: 'test-original-retry',
          allowOriginalRetryOnUnknownError: true,
          originalRetryAttempts: 1,
        });

        expect(result).toEqual({
          object: expectedResult,
        });
        expect(mockGenerateObject).toHaveBeenCalledTimes(2);
        expect(mockLogger.warn).toHaveBeenCalledWith(
          'Attempting original retry strategy',
          expect.objectContaining({
            context: 'test-original-retry',
            attempts: 1,
            errorName: 'Error',
            errorMessage: 'Generic API error',
          }),
        );
        expect(mockLogger.info).toHaveBeenCalledWith('Original retry succeeded on attempt 1', {
          context: 'test-original-retry',
        });
      });

      it('should retry original generation multiple times', async () => {
        const genericError = new Error('Generic API error');

        // First call fails
        mockGenerateObject.mockRejectedValueOnce(genericError);

        // First retry fails
        mockGenerateObject.mockRejectedValueOnce(genericError);

        // Second retry succeeds
        const expectedResult = { name: 'John', age: 30, email: '<EMAIL>' };
        mockGenerateObject.mockResolvedValueOnce({
          object: expectedResult,
          finishReason: 'stop',
          usage: { inputTokens: 10, outputTokens: 20, totalTokens: 30 },
          warnings: [],
          request: {} as any,
          response: {} as any,
          reasoning: undefined,
          providerMetadata: undefined,
          toJsonResponse: () => ({}) as any,
        });

        const result = await generateObjectWithRetry({
          params: validParams,
          context: 'test-multiple-original-retries',
          allowOriginalRetryOnUnknownError: true,
          originalRetryAttempts: 2,
        });

        expect(result).toEqual({
          object: expectedResult,
        });
        expect(mockGenerateObject).toHaveBeenCalledTimes(3); // 1 original + 2 retries
        expect(mockLogger.warn).toHaveBeenCalledWith('Original retry attempt 1/2', expect.any(Object));
        expect(mockLogger.warn).toHaveBeenCalledWith('Original retry attempt 2/2', expect.any(Object));
        expect(mockLogger.info).toHaveBeenCalledWith('Original retry succeeded on attempt 2', expect.any(Object));
      });

      it('should exhaust all original retry attempts and throw error', async () => {
        const genericError = new Error('Persistent API error');

        // All calls fail
        mockGenerateObject.mockRejectedValue(genericError);

        await expect(
          generateObjectWithRetry({
            params: validParams,
            context: 'test-exhausted-original-retry',
            allowOriginalRetryOnUnknownError: true,
            originalRetryAttempts: 2,
          }),
        ).rejects.toThrow('Persistent API error');

        expect(mockGenerateObject).toHaveBeenCalledTimes(3); // 1 original + 2 retries
      });

      it('should skip original retry when disabled', async () => {
        const genericError = new Error('Generic API error');
        mockGenerateObject.mockRejectedValueOnce(genericError);

        await expect(
          generateObjectWithRetry({
            params: validParams,
            context: 'test-disabled-original-retry',
            allowOriginalRetryOnUnknownError: false,
          }),
        ).rejects.toThrow('Generic API error');

        expect(mockGenerateObject).toHaveBeenCalledTimes(1);
        expect(mockLogger.info).toHaveBeenCalledWith(
          'Original retry disabled or no attempts configured',
          expect.objectContaining({
            allowOriginalRetryOnUnknownError: false,
          }),
        );
      });

      it('should skip original retry when originalRetryAttempts is 0', async () => {
        const genericError = new Error('Generic API error');
        mockGenerateObject.mockRejectedValueOnce(genericError);

        await expect(
          generateObjectWithRetry({
            params: validParams,
            context: 'test-zero-retry-attempts',
            allowOriginalRetryOnUnknownError: true,
            originalRetryAttempts: 0,
          }),
        ).rejects.toThrow('Generic API error');

        expect(mockGenerateObject).toHaveBeenCalledTimes(1);
        expect(mockLogger.info).toHaveBeenCalledWith(
          'Original retry disabled or no attempts configured',
          expect.objectContaining({
            originalRetryAttempts: 0,
          }),
        );
      });

      it('should modify params with error context when attachErrorInfoToOriginalRetry is true', async () => {
        const genericError = new Error('API rate limit');

        // First call fails
        mockGenerateObject.mockRejectedValueOnce(genericError);

        // Retry succeeds
        const expectedResult = { name: 'John', age: 30, email: '<EMAIL>' };
        mockGenerateObject.mockResolvedValueOnce({
          object: expectedResult,
          finishReason: 'stop',
          usage: { inputTokens: 10, outputTokens: 20, totalTokens: 30 },
          warnings: [],
          request: {} as any,
          response: {} as any,
          reasoning: undefined,
          providerMetadata: undefined,
          toJsonResponse: () => ({}) as any,
        });

        await generateObjectWithRetry({
          params: { ...validParams, prompt: 'Generate user' },
          context: 'test-error-context',
          allowOriginalRetryOnUnknownError: true,
          attachErrorInfoToOriginalRetry: true,
        });

        // Check that the second call (retry) has modified prompt with error context
        const secondCall = mockGenerateObject.mock.calls[1][0];
        expect(secondCall.prompt).toContain('Generate user');
        expect(secondCall.prompt).toContain('Previous attempt during test-error-context failed');
        expect(secondCall.prompt).toContain('API rate limit');
      });

      it('should not modify params when attachErrorInfoToOriginalRetry is false', async () => {
        const genericError = new Error('API error');

        // First call fails
        mockGenerateObject.mockRejectedValueOnce(genericError);

        // Retry succeeds
        const expectedResult = { name: 'John', age: 30, email: '<EMAIL>' };
        mockGenerateObject.mockResolvedValueOnce({
          object: expectedResult,
          finishReason: 'stop',
          usage: { inputTokens: 10, outputTokens: 20, totalTokens: 30 },
          warnings: [],
          request: {} as any,
          response: {} as any,
          reasoning: undefined,
          providerMetadata: undefined,
          toJsonResponse: () => ({}) as any,
        });

        await generateObjectWithRetry({
          params: { ...validParams, prompt: 'Generate user' },
          allowOriginalRetryOnUnknownError: true,
          attachErrorInfoToOriginalRetry: false,
        });

        // Check that the second call (retry) has original prompt unchanged
        const secondCall = mockGenerateObject.mock.calls[1][0];
        expect(secondCall.prompt).toBe('Generate user');
      });
    });

    describe('Default parameter behavior', () => {
      it('should use default values for optional parameters', async () => {
        const genericError = new Error('Test error');
        mockGenerateObject.mockRejectedValueOnce(genericError);

        await expect(
          generateObjectWithRetry({
            params: validParams,
          }),
        ).rejects.toThrow('Test error');

        // Should use defaults: maxRetries=3, allowOriginalRetryOnUnknownError=false,
        // originalRetryAttempts=1, attachErrorInfoToOriginalRetry=true
        expect(mockLogger.info).toHaveBeenCalledWith(
          'Original retry disabled or no attempts configured',
          expect.objectContaining({
            allowOriginalRetryOnUnknownError: false,
            originalRetryAttempts: 1,
          }),
        );
      });
    });
  });

  describe('retryWithSchemaFix', () => {
    const mockSchema = z.object({
      name: z.string(),
      age: z.number(),
    });

    const zodError = new ZodError([
      { path: ['age'], message: 'Expected number, received string', code: 'invalid_type', expected: 'number' },
    ]);

    beforeEach(() => {
      mockGetSchemaFixingPrompts.mockReturnValue({
        systemPrompt: 'You are a schema fixer',
        generateUserPrompt: vi.fn().mockReturnValue('Fix this validation error'),
      });
    });

    it('should successfully fix schema validation errors', async () => {
      const originalObject = { name: 'John', age: 'thirty' };
      const fixedObject = { name: 'John', age: 30 };

      mockGenerateObject.mockResolvedValueOnce({
        object: {
          fixedObject,
          explanation: 'Converted age from string to number',
        },
        finishReason: 'stop',
        usage: { inputTokens: 10, outputTokens: 20, totalTokens: 30 },
        warnings: [],
        request: {} as any,
        response: {} as any,
        reasoning: undefined,
        providerMetadata: undefined,
        toJsonResponse: () => ({}) as any,
      });

      const result = await retryWithSchemaFix({
        originalObject,
        zodError,
        expectedSchema: mockSchema,
        context: 'test-schema-fix',
      });

      expect(result).toEqual(fixedObject);
      expect(mockGenerateObject).toHaveBeenCalledWith(
        expect.objectContaining({
          system: 'You are a schema fixer',
          prompt: 'Fix this validation error',
        }),
      );
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Attempting to fix schema validation error with AI',
        expect.objectContaining({
          context: 'test-schema-fix',
          errorCount: 1,
        }),
      );
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Successfully fixed schema validation error',
        expect.objectContaining({
          context: 'test-schema-fix',
          explanation: 'Converted age from string to number',
          originalErrorCount: 1,
        }),
      );
    });

    it('should handle AI fixing failure and re-throw original error', async () => {
      const originalObject = { name: 'John', age: 'thirty' };
      const aiFixingError = new Error('AI fixing failed');

      mockGenerateObject.mockRejectedValueOnce(aiFixingError);

      await expect(
        retryWithSchemaFix({
          originalObject,
          zodError,
          expectedSchema: mockSchema,
          context: 'test-fixing-failure',
        }),
      ).rejects.toThrow();

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to fix schema validation error with AI retry',
        expect.objectContaining({
          context: 'test-fixing-failure',
          retryError: 'AI fixing failed',
        }),
      );
    });

    it('should handle schema validation failure of fixed object', async () => {
      const originalObject = { name: 'John', age: 'thirty' };
      const invalidFixedObject = { name: 'John', age: 'still-invalid' };

      mockGenerateObject.mockResolvedValueOnce({
        object: {
          fixedObject: invalidFixedObject,
          explanation: 'Attempted fix',
        },
        finishReason: 'stop',
        usage: { inputTokens: 10, outputTokens: 20, totalTokens: 30 },
        warnings: [],
        request: {} as any,
        response: {} as any,
        reasoning: undefined,
        providerMetadata: undefined,
        toJsonResponse: () => ({}) as any,
      });

      await expect(
        retryWithSchemaFix({
          originalObject,
          zodError,
          expectedSchema: mockSchema,
          context: 'test-invalid-fix',
        }),
      ).rejects.toThrow();

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to fix schema validation error with AI retry',
        expect.objectContaining({
          context: 'test-invalid-fix',
        }),
      );
    });

    it('should work without context parameter', async () => {
      const originalObject = { name: 'John', age: 'thirty' };
      const fixedObject = { name: 'John', age: 30 };

      mockGenerateObject.mockResolvedValueOnce({
        object: {
          fixedObject,
          explanation: 'Fixed age type',
        },
        finishReason: 'stop',
        usage: { inputTokens: 10, outputTokens: 20, totalTokens: 30 },
        warnings: [],
        request: {} as any,
        response: {} as any,
        reasoning: undefined,
        providerMetadata: undefined,
        toJsonResponse: () => ({}) as any,
      });

      const result = await retryWithSchemaFix({
        originalObject,
        zodError,
        expectedSchema: mockSchema,
      });

      expect(result).toEqual(fixedObject);
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Attempting to fix schema validation error with AI',
        expect.objectContaining({
          context: undefined,
        }),
      );
    });

    it('should use correct AI model and provider options', async () => {
      const originalObject = { name: 'John', age: 'thirty' };
      const fixedObject = { name: 'John', age: 30 };

      mockGenerateObject.mockResolvedValueOnce({
        object: {
          fixedObject,
          explanation: 'Fixed',
        },
        finishReason: 'stop',
        usage: { inputTokens: 10, outputTokens: 20, totalTokens: 30 },
        warnings: [],
        request: {} as any,
        response: {} as any,
        reasoning: undefined,
        providerMetadata: undefined,
        toJsonResponse: () => ({}) as any,
      });

      await retryWithSchemaFix({
        originalObject,
        zodError,
        expectedSchema: mockSchema,
      });

      expect(mockGenerateObject).toHaveBeenCalledWith(
        expect.objectContaining({
          providerOptions: {
            openai: {
              reasoningEffort: 'low',
              textVerbosity: 'low',
            },
          },
        }),
      );
    });
  });

  describe('Error extraction utilities', () => {
    // These are internal functions, but we can test them indirectly through the main functions

    it('should handle deeply nested error structures', async () => {
      const zodError = new ZodError([
        { path: ['name'], message: 'Required field missing', code: 'invalid_type', expected: 'string' },
      ]);

      // Create a deeply nested error structure
      const deepError = new Error('Top level error');
      deepError.name = 'AI_NoObjectGeneratedError';
      deepError.cause = {
        cause: {
          cause: zodError,
        },
        value: { incomplete: 'object' },
      };

      mockGenerateObject.mockRejectedValueOnce(deepError);

      // Schema fixing should be triggered
      mockGenerateObject.mockResolvedValueOnce({
        object: {
          fixedObject: { name: 'Fixed' },
          explanation: 'Added missing name',
        },
        finishReason: 'stop',
        usage: { inputTokens: 10, outputTokens: 20, totalTokens: 30 },
        warnings: [],
        request: {} as any,
        response: {} as any,
        reasoning: undefined,
        providerMetadata: undefined,
        toJsonResponse: () => ({}) as any,
      });

      const result = await generateObjectWithRetry({
        params: {
          model: 'gpt-5-nano',
          schema: z.object({ name: z.string() }),
          prompt: 'test',
        },
        maxRetries: 1,
      });

      expect(result).toEqual({ object: { name: 'Fixed' } });
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Schema validation failed, attempting schema fixing',
        expect.any(Object),
      );
    });

    it('should handle missing value in error structure', async () => {
      const zodError = new ZodError([
        { path: ['name'], message: 'Required', code: 'invalid_type', expected: 'string' },
      ]);

      const errorWithoutValue = new Error('Error without value');
      errorWithoutValue.name = 'AI_NoObjectGeneratedError';
      errorWithoutValue.cause = {
        cause: zodError,
        // no value property
      };

      mockGenerateObject.mockRejectedValueOnce(errorWithoutValue);

      // Schema fixing should still be triggered with undefined original value
      mockGenerateObject.mockResolvedValueOnce({
        object: {
          fixedObject: { name: 'Fixed' },
          explanation: 'Added missing name',
        },
        finishReason: 'stop',
        usage: { inputTokens: 10, outputTokens: 20, totalTokens: 30 },
        warnings: [],
        request: {} as any,
        response: {} as any,
        reasoning: undefined,
        providerMetadata: undefined,
        toJsonResponse: () => ({}) as any,
      });

      const result = await generateObjectWithRetry({
        params: {
          model: 'gpt-5-nano',
          schema: z.object({ name: z.string() }),
          prompt: 'test',
        },
        maxRetries: 1,
      });

      expect(result).toEqual({ object: { name: 'Fixed' } });
    });
  });
});
