import { describe, it, expect, vi, beforeEach } from 'vitest';
import { z } from 'zod';

// Mock environment variables first
vi.mock('../../env', () => ({
  env: {
    OPENAI_API_KEY: 'test-api-key',
  },
  OpenAIModel: {
    GPT_4_TURBO: 'gpt-4-turbo',
    GPT_5_NANO: 'gpt-5-nano',
    GPT_4_1: 'gpt-4.1',
  },
}));

// Mock the AI SDK with hoisted mocks to ensure they're applied before imports
const mockGenerateObject = vi.fn();
const mockExperimentalTranscribe = vi.fn();

vi.mock('ai', () => ({
  generateObject: mockGenerateObject,
  experimental_transcribe: mockExperimentalTranscribe,
}));

// Mock the AI retry kit service
const mockGenerateObjectWithRetry = vi.fn();
vi.mock('../ai-retry-kit.service', () => ({
  generateObjectWithRetry: mockGenerateObjectWithRetry,
}));

// Mock the OpenAI SDK
const mockOpenaiTranscription = vi.fn();
const mockOpenai = vi.fn();

vi.mock('@ai-sdk/openai', () => ({
  openai: Object.assign(mockOpenai, {
    transcription: mockOpenaiTranscription,
  }),
}));

describe('AI Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Setup default mock implementations
    mockGenerateObject.mockResolvedValue({
      object: {
        title: 'Test Event',
        description: 'This is a test event',
        category: 'near_miss',
      },
    });

    mockGenerateObjectWithRetry.mockResolvedValue({
      object: {
        title: 'Test Event',
        description: 'This is a test event',
        category: 'near_miss',
      },
    });

    mockExperimentalTranscribe.mockResolvedValue({
      text: 'This is a transcription of an event report',
    });

    mockOpenai.mockImplementation((modelName) => `mocked-${modelName}`);
    mockOpenaiTranscription.mockImplementation((model) => `mocked-transcription-${model}`);
  });

  describe('transcribeAudio', () => {
    it('should transcribe audio buffer to text', async () => {
      const { transcribeAudio } = await import('../ai.service');

      const audioBuffer = Buffer.from('test audio data');
      const result = await transcribeAudio(audioBuffer);

      expect(mockOpenaiTranscription).toHaveBeenCalledWith('whisper-1');
      expect(mockExperimentalTranscribe).toHaveBeenCalledWith({
        model: 'mocked-transcription-whisper-1',
        audio: audioBuffer,
      });

      expect(result).toEqual({
        text: 'This is a transcription of an event report',
      });
    });
  });

  describe('analyzeEventTranscript', () => {
    it('should analyze transcript and return structured data', async () => {
      const { analyzeEventTranscript } = await import('../ai.service');

      const transcript = 'Someone fell on the factory floor at building 3 yesterday';
      const result = await analyzeEventTranscript(transcript);

      expect(mockOpenai).toHaveBeenCalledWith('gpt-5-nano');
      expect(mockGenerateObjectWithRetry).toHaveBeenCalledWith(
        expect.objectContaining({
          params: expect.objectContaining({
            model: 'mocked-gpt-5-nano',
            schema: expect.anything(),
            prompt: expect.stringContaining(transcript),
          }),
          context: 'event transcript analysis',
        }),
      );

      expect(result).toEqual({
        title: 'Test Event',
        description: 'This is a test event',
        category: 'near_miss',
      });
    });

    it('should use the correct schema for validation', async () => {
      const { AnalyzeEventTranscriptSchema } = await import('../ai.service');

      // Verify schema has the required fields
      expect(AnalyzeEventTranscriptSchema.shape.title).toBeInstanceOf(z.ZodString);
      expect(AnalyzeEventTranscriptSchema.shape.description).toBeInstanceOf(z.ZodString);
      expect(AnalyzeEventTranscriptSchema.shape.category).toBeDefined();
    });
  });

  describe('Retry Mechanism Tests', () => {
    describe('analyzeEventTranscript with retry', () => {
      it('should call generateObjectWithRetry with correct parameters', async () => {
        const { analyzeEventTranscript } = await import('../ai.service');

        const transcript = 'A worker was injured when equipment failed';
        const timezone = 'America/New_York';

        await analyzeEventTranscript(transcript, timezone);

        expect(mockGenerateObjectWithRetry).toHaveBeenCalledWith(
          expect.objectContaining({
            params: expect.objectContaining({
              model: 'mocked-gpt-5-nano',
              schema: expect.anything(),
              system: expect.stringContaining('event'),
              prompt: expect.stringContaining(transcript),
              providerOptions: {
                openai: {
                  reasoningEffort: 'low',
                  textVerbosity: 'high',
                },
              },
            }),
            context: 'event transcript analysis',
          }),
        );
      });

      it('should handle retry mechanism when schema validation fails', async () => {
        const { analyzeEventTranscript } = await import('../ai.service');

        // Mock a successful retry after initial failure
        mockGenerateObjectWithRetry.mockResolvedValueOnce({
          object: {
            title: 'Fixed Event Title',
            description: 'This was fixed by the retry mechanism',
            category: 'incident',
            severity: 'medium',
          },
        });

        const result = await analyzeEventTranscript('Test transcript');

        expect(result).toEqual({
          title: 'Fixed Event Title',
          description: 'This was fixed by the retry mechanism',
          category: 'incident',
          severity: 'medium',
        });
      });
    });

    describe('analyzeCapaTranscript with retry', () => {
      it('should call generateObjectWithRetry with CAPA context', async () => {
        const { analyzeCapaTranscript } = await import('../ai.service');

        mockGenerateObjectWithRetry.mockResolvedValueOnce({
          object: {
            title: 'Root Cause Analysis',
            rcaMethod: 'fishbone',
            priority: 'high',
            dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          },
        });

        const transcript = 'We need to investigate the equipment failure thoroughly';
        await analyzeCapaTranscript(transcript, 'UTC');

        expect(mockGenerateObjectWithRetry).toHaveBeenCalledWith(
          expect.objectContaining({
            params: expect.objectContaining({
              model: 'mocked-gpt-5-nano',
              schema: expect.anything(),
              prompt: expect.stringContaining(transcript),
            }),
            context: 'CAPA transcript analysis',
          }),
        );
      });
    });

    describe('analyzeJhaTranscript with retry', () => {
      it('should call generateObjectWithRetry with JHA context and GPT-4.1', async () => {
        const { analyzeJhaTranscript } = await import('../ai.service');

        const mockUser = {
          id: 'user123',
          username: 'john.doe',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          fullName: 'John Doe',
          upkeepCompanyId: 'company123',
          role: 'admin' as const,
          permissions: {},
          featureFlags: {},
          hasEhsEnabled: true,
        };

        mockGenerateObjectWithRetry.mockResolvedValueOnce({
          object: {
            jha: {
              title: 'Forklift Operation Safety',
              purpose: 'Safe operation of forklifts in warehouse',
              ownerId: 'user123',
            },
            steps: [
              {
                serial: 1,
                description: 'Pre-operation inspection',
                hazards: ['mechanical'],
                controls: ['inspection checklist'],
              },
            ],
          },
        });

        await analyzeJhaTranscript('Analyze forklift safety procedures', mockUser);

        expect(mockGenerateObjectWithRetry).toHaveBeenCalledWith(
          expect.objectContaining({
            params: expect.objectContaining({
              model: 'mocked-gpt-4.1', // Should use GPT-4.1 for JHA
              schema: expect.anything(),
            }),
            context: 'JHA transcript analysis',
          }),
        );
      });
    });

    describe('analyzeSopTranscript with retry', () => {
      it('should call generateObjectWithRetry with SOP context', async () => {
        const { analyzeSopTranscript } = await import('../ai.service');

        const mockUser = {
          id: 'user123',
          username: 'jane.smith',
          email: '<EMAIL>',
          firstName: 'Jane',
          lastName: 'Smith',
          fullName: 'Jane Smith',
          upkeepCompanyId: 'company123',
          role: 'admin' as const,
          permissions: {},
          featureFlags: {},
          hasEhsEnabled: true,
        };

        mockGenerateObjectWithRetry.mockResolvedValueOnce({
          object: {
            sop: {
              title: 'Safety Procedure for Equipment Maintenance',
              ownerId: 'user123',
              approverId: 'user123',
              status: 'draft',
            },
            sections: [
              {
                serial: 1,
                label: 'Purpose',
                value: 'This SOP outlines safety procedures for equipment maintenance',
                sectionType: 'general',
              },
            ],
          },
        });

        await analyzeSopTranscript({
          text: 'Create SOP for equipment maintenance safety',
          user: mockUser,
        });

        expect(mockGenerateObjectWithRetry).toHaveBeenCalledWith(
          expect.objectContaining({
            params: expect.objectContaining({
              model: 'mocked-gpt-4.1',
              schema: expect.anything(),
              prompt: expect.stringContaining('equipment maintenance safety'),
            }),
            context: 'SOP transcript analysis',
          }),
        );
      });

      it('should demonstrate real production error scenario and recovery', async () => {
        const { analyzeSopTranscript } = await import('../ai.service');

        const mockUser = {
          id: 'user123',
          username: 'test.user',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          fullName: 'Test User',
          upkeepCompanyId: 'company123',
          role: 'admin' as const,
          permissions: {},
          featureFlags: {},
          hasEhsEnabled: true,
        };

        // Mock the retry mechanism successfully handling a production error scenario
        // The retry mechanism would have internally handled the error and returned a fixed result
        mockGenerateObjectWithRetry.mockResolvedValueOnce({
          object: {
            sop: {
              title: 'Safe Handling and Response Procedures for Industrial Equipment',
              ownerId: 'user123',
              approverId: 'user123',
              status: 'draft',
              assetIds: [],
              isPublic: false,
            },
            sections: [
              {
                serial: 1,
                label: 'Scope & Equipment',
                // Fixed: combined multiple fields into single value field by retry
                value: 'Scope: This SOP applies to all personnel. Equipment: Industrial safety equipment required.',
                sectionType: 'general',
              },
            ],
          },
        });

        const result = await analyzeSopTranscript({
          text: 'Create comprehensive safety procedures for industrial equipment handling',
          user: mockUser,
        });

        // Verify the result shows successful recovery
        expect(result.sections[0].value).toContain('Scope:'); // Has consolidated value field
        expect(result.sections[0]).not.toHaveProperty('scope_applicability'); // No extra fields

        // Verify retry mechanism was used
        expect(mockGenerateObjectWithRetry).toHaveBeenCalledWith(
          expect.objectContaining({
            params: expect.objectContaining({
              model: 'mocked-gpt-4.1',
              schema: expect.anything(),
            }),
            context: 'SOP transcript analysis',
          }),
        );
      });
    });

    describe('Document analysis with retry', () => {
      it('should handle document analysis with retry for JHA', async () => {
        const { analyzeJhaDocument } = await import('../ai.service');

        const mockUser = {
          id: 'user123',
          username: 'doc.analyzer',
          email: '<EMAIL>',
          firstName: 'Document',
          lastName: 'Analyzer',
          fullName: 'Document Analyzer',
          upkeepCompanyId: 'company123',
          role: 'admin' as const,
          permissions: {},
          featureFlags: {},
          hasEhsEnabled: true,
        };

        const mockDocument = Buffer.from('PDF content');

        mockGenerateObjectWithRetry.mockResolvedValueOnce({
          object: {
            jha: {
              title: 'Document Analysis Result',
              purpose: 'Extracted from PDF document',
              ownerId: 'user123',
            },
            steps: [],
          },
        });

        await analyzeJhaDocument({ document: mockDocument, user: mockUser });

        expect(mockGenerateObjectWithRetry).toHaveBeenCalledWith(
          expect.objectContaining({
            params: expect.objectContaining({
              model: 'mocked-gpt-4.1',
              schema: expect.anything(),
              messages: expect.arrayContaining([
                expect.objectContaining({
                  role: 'user',
                  content: expect.arrayContaining([expect.objectContaining({ type: 'file' })]),
                }),
              ]),
            }),
            context: 'JHA document analysis',
          }),
        );
      });

      it('should handle document analysis with retry for SOP', async () => {
        const { analyzeSopDocument } = await import('../ai.service');

        const mockUser = {
          id: 'user123',
          username: 'sop.analyzer',
          email: '<EMAIL>',
          firstName: 'SOP',
          lastName: 'Analyzer',
          fullName: 'SOP Analyzer',
          upkeepCompanyId: 'company123',
          role: 'admin' as const,
          permissions: {},
          featureFlags: {},
          hasEhsEnabled: true,
        };

        const mockDocument = Buffer.from('SOP PDF content');

        mockGenerateObjectWithRetry.mockResolvedValueOnce({
          object: {
            sop: {
              title: 'Extracted SOP Title',
              ownerId: 'user123',
              approverId: 'user123',
              status: 'draft',
            },
            sections: [
              {
                serial: 1,
                label: 'Document Section',
                value: 'Content extracted from document',
                sectionType: 'general',
              },
            ],
          },
        });

        await analyzeSopDocument({ document: mockDocument, user: mockUser });

        expect(mockGenerateObjectWithRetry).toHaveBeenCalledWith(
          expect.objectContaining({
            params: expect.objectContaining({
              model: 'mocked-gpt-4.1',
              schema: expect.anything(),
              messages: expect.arrayContaining([
                expect.objectContaining({
                  role: 'user',
                  content: expect.arrayContaining([
                    expect.objectContaining({
                      type: 'file',
                      data: mockDocument,
                      mediaType: 'application/pdf',
                    }),
                  ]),
                }),
              ]),
            }),
            context: 'SOP document analysis',
          }),
        );
      });
    });
  });
});
