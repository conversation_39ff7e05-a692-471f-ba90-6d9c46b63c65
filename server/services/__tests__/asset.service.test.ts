import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock the API module before importing anything that uses it
vi.mock('@server/utils/api', () => {
  const buildDefaultHeaders = vi.fn((headers) => ({
    'Content-Type': 'application/json',
    ...headers,
  }));

  return {
    default: {
      post: vi.fn(),
      get: vi.fn(),
    },
    buildDefaultHeaders,
  };
});

// Import after mocking
import { getAssetById, getAssets, getAssetsCount, searchAssetsPublic } from '@server/services/asset.service';
import api from '@server/utils/api';

describe('Asset Service', () => {
  const mockHeaders = { 'x-upkeep-company-id': 'test-company' };
  const expectedHeaders = {
    'Content-Type': 'application/json',
    'x-upkeep-company-id': 'test-company',
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getAssets', () => {
    it('should fetch assets with basic parameters', async () => {
      vi.mocked(api.post).mockResolvedValueOnce({
        data: {
          results: [
            { id: 'asset1', Name: 'Asset 1', Description: 'Desc 1' },
            { id: 'asset2', Name: 'Asset 2', Description: 'Desc 2' },
          ],
        },
      });

      const result = await getAssets({ limit: 10 }, mockHeaders);

      expect(result.result).toHaveLength(2);
      expect(result.result[0].id).toBe('asset1');
      expect(result.result[1].id).toBe('asset2');
    });

    it('should include search parameter when provided', async () => {
      vi.mocked(api.post).mockResolvedValueOnce({
        data: {
          results: [{ id: 'asset1', Name: 'Asset 1', Description: 'Desc 1' }],
        },
      });

      await getAssets({ limit: 10, search: 'test' }, mockHeaders);

      expect(vi.mocked(api.post)).toHaveBeenCalledWith(
        '/api/v1/assets/search',
        expect.objectContaining({ search: 'test' }),
        expect.any(Object),
      );
    });

    it('should include objectLocation parameter when provided', async () => {
      vi.mocked(api.post).mockResolvedValueOnce({
        data: {
          results: [{ id: 'asset1', Name: 'Asset 1', Description: 'Desc 1' }],
        },
      });

      await getAssets({ limit: 10, objectLocation: ['loc1'] }, mockHeaders);

      expect(vi.mocked(api.post)).toHaveBeenCalledWith(
        '/api/v1/assets/search',
        expect.objectContaining({ objectLocation: ['loc1'] }),
        expect.any(Object),
      );
    });

    it('should include both search and objectLocation when provided', async () => {
      vi.mocked(api.post).mockResolvedValueOnce({
        data: {
          results: [{ id: 'asset1', Name: 'Asset 1', Description: 'Desc 1' }],
        },
      });

      await getAssets({ limit: 10, search: 'test', objectLocation: ['loc1'] }, mockHeaders);

      expect(vi.mocked(api.post)).toHaveBeenCalledWith(
        '/api/v1/assets/search',
        expect.objectContaining({
          search: 'test',
          objectLocation: ['loc1'],
        }),
        expect.any(Object),
      );
    });

    it('should include mustIncludeObjectIds in results when not present in initial search', async () => {
      // Mock initial search response
      vi.mocked(api.post).mockResolvedValueOnce({
        data: {
          results: [
            { id: 'asset1', Name: 'Asset 1', Description: 'Desc 1' },
            { id: 'asset2', Name: 'Asset 2', Description: 'Desc 2' },
          ],
        },
      });

      // Mock mustIncludeObjectIds search response
      vi.mocked(api.post).mockResolvedValueOnce({
        data: {
          success: true,
          results: [
            { id: 'asset3', Name: 'Asset 3', Description: 'Desc 3' },
            { id: 'asset4', Name: 'Asset 4', Description: 'Desc 4' },
          ],
        },
      });

      const result = await getAssets(
        {
          limit: 10,
          mustIncludeObjectIds: ['asset3', 'asset4'],
        },
        mockHeaders,
      );

      expect(result.result).toHaveLength(4);
      expect(result.result[0].id).toBe('asset3'); // First mustInclude asset
      expect(result.result[1].id).toBe('asset4'); // Second mustInclude asset
      expect(result.result[2].id).toBe('asset1'); // Original search results
      expect(result.result[3].id).toBe('asset2');
    });

    it('should not make additional request if mustIncludeObjectIds are already in results', async () => {
      // Mock initial search response with mustIncludeObjectIds already present
      vi.mocked(api.post).mockResolvedValueOnce({
        data: {
          results: [
            { id: 'asset1', Name: 'Asset 1', Description: 'Desc 1' },
            { id: 'asset2', Name: 'Asset 2', Description: 'Desc 2' },
            { id: 'asset3', Name: 'Asset 3', Description: 'Desc 3' }, // This is in mustIncludeObjectIds
          ],
        },
      });

      const result = await getAssets(
        {
          limit: 10,
          mustIncludeObjectIds: ['asset3'],
        },
        mockHeaders,
      );

      expect(vi.mocked(api.post)).toHaveBeenCalledTimes(1); // Only one API call
      expect(result.result).toHaveLength(3);
      expect(result.result.some((asset) => asset.id === 'asset3')).toBe(true);
    });

    it('should handle pagination correctly with mustIncludeObjectIds', async () => {
      // Mock initial search response
      vi.mocked(api.post).mockResolvedValueOnce({
        data: {
          results: [
            { id: 'asset1', Name: 'Asset 1', Description: 'Desc 1' },
            { id: 'asset2', Name: 'Asset 2', Description: 'Desc 2' },
          ],
        },
      });

      const result = await getAssets(
        {
          limit: 10,
          cursor: 1, // Not first page
          mustIncludeObjectIds: ['asset3'], // Should be ignored since not first page
        },
        mockHeaders,
      );

      expect(vi.mocked(api.post)).toHaveBeenCalledTimes(1); // Only initial search
      expect(result.result).toHaveLength(2);
      expect(result.result[0].id).toBe('asset1');
      expect(result.result[1].id).toBe('asset2');
    });
  });

  describe('getAssetsCount', () => {
    it('should fetch asset count with basic parameters', async () => {
      vi.mocked(api.post).mockResolvedValueOnce({
        data: {
          result: 42,
        },
      });

      const result = await getAssetsCount({}, mockHeaders);

      expect(vi.mocked(api.post)).toHaveBeenCalledWith(
        '/api/v1/assets/search/count',
        {},
        {
          headers: expectedHeaders,
        },
      );
      expect(result).toBe(42);
    });

    it('should include objectLocation parameter when provided', async () => {
      vi.mocked(api.post).mockResolvedValueOnce({
        data: {
          result: 42,
        },
      });

      const result = await getAssetsCount({ objectLocation: ['loc1'] }, mockHeaders);

      expect(vi.mocked(api.post)).toHaveBeenCalledWith(
        '/api/v1/assets/search/count',
        {
          objectLocation: ['loc1'],
        },
        {
          headers: expectedHeaders,
        },
      );
      expect(result).toBe(42);
    });
  });

  describe('getAssetById', () => {
    it('should fetch single asset by id', async () => {
      vi.mocked(api.get).mockResolvedValueOnce({
        data: {
          result: {
            id: 'asset-1',
            Name: 'Asset 1',
            Description: 'Desc 1',
          },
        },
      });

      const result = await getAssetById('asset-1', mockHeaders);

      expect(vi.mocked(api.get)).toHaveBeenCalledWith('/api/v1/assets/asset-1', {
        headers: expectedHeaders,
      });
      expect(result.id).toBe('asset-1');
      expect(result.name).toBe('Asset 1');
      expect(result.description).toBe('Desc 1');
    });
  });

  describe('searchAssetsPublic', () => {
    it('should fetch assets using public search with basic parameters', async () => {
      vi.mocked(api.post).mockResolvedValueOnce({
        data: {
          success: true,
          results: [{ id: 'asset1', Name: 'Asset 1', objectLocation: { stringName: 'Location 1' } }],
        },
      });

      const result = await searchAssetsPublic({
        upkeepCompanyId: 'test-role',
        search: 'test',
      });

      expect(vi.mocked(api.post)).toHaveBeenCalledWith(
        '/api/app/search/assets',
        expect.objectContaining({
          roleId: 'test-role',
          search: 'test',
        }),
        expect.any(Object),
      );
      expect(result.result[0].id).toBe('asset1');
      expect(result.result[0].name).toBe('Asset 1');
      expect(result.result[0].description).toBe('Location 1');
    });

    it('should include locationId parameter when provided', async () => {
      vi.mocked(api.post).mockResolvedValueOnce({
        data: {
          success: true,
          results: [{ id: 'asset1', Name: 'Asset 1', objectLocation: { stringName: 'Location 1' } }],
        },
      });

      await searchAssetsPublic({
        upkeepCompanyId: 'test-role',
        search: '',
        locationId: 'loc1',
      });

      expect(vi.mocked(api.post)).toHaveBeenCalledWith(
        '/api/app/search/assets',
        expect.objectContaining({
          objectLocation: 'loc1',
        }),
        expect.any(Object),
      );
    });

    it('should work without locationId parameter (backward compatibility)', async () => {
      vi.mocked(api.post).mockResolvedValueOnce({
        data: {
          success: true,
          results: [{ id: 'asset1', Name: 'Asset 1', objectLocation: { stringName: 'Location 1' } }],
        },
      });

      await searchAssetsPublic({
        upkeepCompanyId: 'test-role',
        search: '',
      });

      expect(vi.mocked(api.post)).toHaveBeenCalledWith(
        '/api/app/search/assets',
        expect.not.objectContaining({
          objectLocation: expect.any(Array),
        }),
        expect.any(Object),
      );
    });

    it('should return empty array when response is not successful', async () => {
      vi.mocked(api.post).mockResolvedValueOnce({
        data: {
          success: false,
          results: [],
        },
      });

      const result = await searchAssetsPublic({
        upkeepCompanyId: 'test-role',
        search: '',
      });

      expect(result.result).toHaveLength(0);
      expect(result.noResults).toBe(true);
      expect(result.nextCursor).toBeUndefined();
    });
  });
});
