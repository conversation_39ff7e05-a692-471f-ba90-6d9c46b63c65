import { db } from '@server/db';
import { getOverdueJhas } from '@server/services/jha.service';
import { jha } from '@shared/schema';
import { eq, inArray } from 'drizzle-orm';
import { afterEach, beforeEach, describe, expect, it } from 'vitest';

describe('JHA Service', () => {
  // Use a unique test company ID to avoid conflicts
  const testCompanyId = 'test-jha';

  // Helper function to create test JHA
  const createTestJha = async (overrides: Partial<typeof jha.$inferInsert> = {}) => {
    const defaultJha = {
      upkeepCompanyId: testCompanyId,
      title: 'Test JHA',
      ownerId: 'owner123', // 8 chars, fits varchar(10)
      approverId: 'approve1', // 8 chars, fits varchar(10)
      status: 'approved' as const,
      createdBy: 'creator1', // 8 chars, fits varchar(10)
      ...overrides,
    };

    const [createdJha] = await db.insert(jha).values(defaultJha).returning({ id: jha.id });
    return createdJha;
  };

  // Helper function to get date relative to today
  const getDateRelativeToToday = (daysOffset: number): Date => {
    const date = new Date();
    date.setDate(date.getDate() + daysOffset);
    return date;
  };

  beforeEach(async () => {
    // Clean up any existing JHAs for our test company
    await db.delete(jha).where(eq(jha.upkeepCompanyId, testCompanyId));
  });

  afterEach(async () => {
    // Clean up test data
    await db.delete(jha).where(eq(jha.upkeepCompanyId, testCompanyId));
  });

  describe('getOverdueJhas', () => {
    it('should return JHAs with review dates due today or exactly 7 days from now', async () => {
      // Create JHAs with review dates on specific target days
      await createTestJha({
        title: 'JHA Due Today',
        reviewDate: getDateRelativeToToday(0), // Today
      });

      await createTestJha({
        title: 'JHA Due 3 Days From Now',
        reviewDate: getDateRelativeToToday(3), // 3 days from now - should NOT be returned
      });

      await createTestJha({
        title: 'JHA Due 7 Days From Now',
        reviewDate: getDateRelativeToToday(7), // 7 days from now - should be returned
      });

      const result = await getOverdueJhas();
      const testResults = result.filter((j) => j.upkeepCompanyId === testCompanyId);

      expect(testResults).toHaveLength(2);
      expect(testResults.map((j) => j.title)).toEqual(
        expect.arrayContaining(['JHA Due Today', 'JHA Due 7 Days From Now']),
      );
      expect(testResults.map((j) => j.title)).not.toContain('JHA Due 3 Days From Now');
    });

    it('should exclude JHAs with review dates not due today or exactly 7 days from now', async () => {
      // Create JHAs with dates that should be excluded
      await createTestJha({
        title: 'JHA Due 8 Days Ago',
        reviewDate: getDateRelativeToToday(-8), // Past date
      });

      await createTestJha({
        title: 'JHA Due 3 Days From Now',
        reviewDate: getDateRelativeToToday(3), // Not target day
      });

      await createTestJha({
        title: 'JHA Due Tomorrow',
        reviewDate: getDateRelativeToToday(1), // Not target day
      });

      await createTestJha({
        title: 'JHA Due 8 Days From Now',
        reviewDate: getDateRelativeToToday(8), // Too far in future
      });

      const result = await getOverdueJhas();
      const testResults = result.filter((j) => j.upkeepCompanyId === testCompanyId);

      expect(testResults).toHaveLength(0);
    });

    it('should only include JHAs with status "approved"', async () => {
      // Create JHAs with different statuses - all with valid review dates
      await createTestJha({
        title: 'Draft JHA',
        status: 'draft',
        reviewDate: getDateRelativeToToday(0), // Today - but draft status
      });

      await createTestJha({
        title: 'Review JHA',
        status: 'review',
        reviewDate: getDateRelativeToToday(7), // 7 days from now - but review status
      });

      await createTestJha({
        title: 'Draft JHA',
        status: 'draft',
        reviewDate: getDateRelativeToToday(0), // Today - but revision status
      });

      await createTestJha({
        title: 'Approved JHA',
        status: 'approved',
        reviewDate: getDateRelativeToToday(7), // 7 days from now - approved status
      });

      const result = await getOverdueJhas();
      const testResults = result.filter((j) => j.upkeepCompanyId === testCompanyId);

      expect(testResults).toHaveLength(1);
      expect(testResults[0].title).toBe('Approved JHA');
      expect(testResults[0].status).toBe('approved');
    });

    it('should exclude archived JHAs', async () => {
      const archivedDate = new Date();

      // Create archived JHA that would otherwise match criteria
      await createTestJha({
        title: 'Archived JHA',
        status: 'approved',
        reviewDate: getDateRelativeToToday(0), // Today - but archived
        archivedAt: archivedDate,
      });

      // Create active JHA for comparison
      await createTestJha({
        title: 'Active JHA',
        status: 'approved',
        reviewDate: getDateRelativeToToday(0), // Today - active
        archivedAt: null,
      });

      const result = await getOverdueJhas();
      const testResults = result.filter((j) => j.upkeepCompanyId === testCompanyId);

      expect(testResults).toHaveLength(1);
      expect(testResults[0].title).toBe('Active JHA');
    });

    it('should exclude JHAs with null review dates', async () => {
      // Create JHA without review date
      await createTestJha({
        title: 'JHA Without Review Date',
        status: 'approved',
        reviewDate: null,
      });

      // Create JHA with review date for comparison
      await createTestJha({
        title: 'JHA With Review Date',
        status: 'approved',
        reviewDate: getDateRelativeToToday(7), // 7 days from now - valid target date
      });

      const result = await getOverdueJhas();
      const testResults = result.filter((j) => j.upkeepCompanyId === testCompanyId);

      expect(testResults).toHaveLength(1);
      expect(testResults[0].title).toBe('JHA With Review Date');
      expect(testResults[0].reviewDate).not.toBeNull();
    });

    it('should return JHAs ordered by review date (most recent first)', async () => {
      // Create JHAs only on target dates (today and 7 days from now)
      await createTestJha({
        title: 'JHA Due Today',
        reviewDate: getDateRelativeToToday(0), // Today - should be second
      });

      await createTestJha({
        title: 'JHA Due 7 Days From Now',
        reviewDate: getDateRelativeToToday(7), // 7 days from now - should be first
      });

      // This one should be excluded
      await createTestJha({
        title: 'JHA Due 3 Days From Now',
        reviewDate: getDateRelativeToToday(3), // Should not be returned
      });

      const result = await getOverdueJhas();
      const testResults = result.filter((j) => j.upkeepCompanyId === testCompanyId);

      expect(testResults).toHaveLength(2);
      expect(testResults[0].title).toBe('JHA Due 7 Days From Now'); // Most recent first (future date)
      expect(testResults[1].title).toBe('JHA Due Today'); // Today second
    });
  });
});
