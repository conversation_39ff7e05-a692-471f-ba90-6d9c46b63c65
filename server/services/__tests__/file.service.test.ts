import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { createFile, getFiles, getPresignedUrl } from '@server/services/file.service';
import type { User } from '@shared/types/users.types';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock AWS SDK
vi.mock('@aws-sdk/client-s3', () => ({
  S3Client: vi.fn(),
  PutObjectCommand: vi.fn().mockImplementation((input) => ({ input })),
}));

vi.mock('@aws-sdk/s3-request-presigner', () => ({
  getSignedUrl: vi.fn().mockResolvedValue('https://test-presigned-url.com'),
}));

// Mock CUID2
vi.mock('@paralleldrive/cuid2', () => ({
  createId: vi.fn(() => 'test-unique-id'),
  getConstants: () => ({ defaultLength: 24 }),
}));

// Mock environment
vi.mock('env', () => ({
  env: {
    AWS_REGION: 'us-east-1',
    AWS_ACCESS_KEY_ID: 'test-key',
    AWS_SECRET_ACCESS_KEY: 'test-secret',
    S3_BUCKET_NAME: 'test-bucket',
  },
}));

// Mock database - using the correct import path
vi.mock('@server/db', () => {
  const mockDb = {
    insert: vi.fn(() => ({
      values: vi.fn(() => ({
        returning: vi.fn().mockResolvedValue([
          {
            id: 'test-file-id',
            fileName: 'test.jpg',
            fileSize: 1024,
            mimeType: 'image/jpeg',
            status: 'pending',
          },
        ]),
      })),
    })),
    update: vi.fn(() => ({
      set: vi.fn(() => ({
        where: vi.fn(() => ({
          returning: vi.fn().mockResolvedValue([
            {
              id: 'test-file-id',
              status: 'completed',
              fileName: 'test.jpg',
              fileSize: 1024,
              mimeType: 'image/jpeg',
            },
          ]),
        })),
      })),
    })),
    select: vi.fn(() => ({
      from: vi.fn(() => ({
        where: vi.fn(() => ({
          orderBy: vi.fn().mockResolvedValue([
            {
              id: 'test-file-1',
              fileName: 'test1.jpg',
              entityType: 'event',
              entityId: 'event-123',
            },
            {
              id: 'test-file-2',
              fileName: 'test2.jpg',
              entityType: 'event',
              entityId: 'event-123',
            },
          ]),
        })),
      })),
    })),
    transaction: vi.fn(async (callback) => {
      // Mock transaction: call the callback with the same mock db instance
      return callback(mockDb);
    }),
  };

  return { db: mockDb };
});

// Mock drizzle-orm
vi.mock('drizzle-orm', () => ({
  eq: vi.fn((field, value) => ({ field, value, operator: 'eq' })),
  and: vi.fn((...conditions) => ({ type: 'and', conditions })),
  relations: vi.fn((table, relationsFn) => ({ table, relationsFn })),
}));

describe('file.service.ts', () => {
  const mockUser: User = {
    id: 'user-123',
    username: 'testuser',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    fullName: 'Test User',
    upkeepCompanyId: 'company-123',
    role: 'admin',
    permissions: {},
    featureFlags: {},
    hasEhsEnabled: true,
  };

  const mockFileParams = {
    fileName: 'test.jpg',
    fileSize: 1024,
    mimeType: 'image/jpeg' as const,
    entityType: 'event' as const,
    entityId: 'event-123',
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getPresignedUrl', () => {
    it('should generate presigned URL and create file record', async () => {
      const result = await getPresignedUrl(mockFileParams, mockUser);

      expect(result).toMatchObject({
        presignedUrl: 'https://test-presigned-url.com',
        file: expect.objectContaining({
          id: 'test-file-id',
        }),
      });

      expect(vi.mocked(getSignedUrl)).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          input: expect.objectContaining({
            Bucket: 'test-bucket',
            Key: expect.stringContaining('ehs-files'),
            ContentType: mockFileParams.mimeType,
          }),
        }),
        expect.objectContaining({ expiresIn: 3600 }),
      );
    });
  });

  describe('createFile', () => {
    it('should create file record with pending status', async () => {
      const createFileParams = {
        fileName: 'test.jpg',
        fileSize: 1024,
        mimeType: 'image/jpeg' as const,
        presignedUrl: 'https://test-presigned-url.com',
        s3Key: 'test-key',
        s3Bucket: 'test-bucket',
        upkeepCompanyId: 'company-123',
        uploadedBy: 'user-123',
        expiresAt: new Date(),
        entityType: 'event' as const,
        entityId: 'event-123',
      };

      const result = await createFile(createFileParams);

      expect(result).toMatchObject({
        id: 'test-file-id',
      });
    });
  });

  describe('getFiles', () => {
    it('should apply all filters correctly', async () => {
      const filters = {
        entityType: 'event' as const,
        entityId: 'event-123',
        status: 'completed' as const,
      };

      const result = await getFiles(filters, mockUser);

      expect(result).toEqual([
        {
          id: 'test-file-1',
          fileName: 'test1.jpg',
          entityType: 'event',
          entityId: 'event-123',
        },
        {
          id: 'test-file-2',
          fileName: 'test2.jpg',
          entityType: 'event',
          entityId: 'event-123',
        },
      ]);
    });

    it('should apply entityId filter correctly', async () => {
      const filters = {
        entityId: 'test-entity-123',
        entityType: 'event' as const,
      };

      const result = await getFiles(filters, mockUser);

      expect(result).toEqual([
        {
          id: 'test-file-1',
          fileName: 'test1.jpg',
          entityType: 'event',
          entityId: 'event-123',
        },
        {
          id: 'test-file-2',
          fileName: 'test2.jpg',
          entityType: 'event',
          entityId: 'event-123',
        },
      ]);
    });
  });
});
