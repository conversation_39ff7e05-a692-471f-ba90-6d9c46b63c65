import { Location } from '@shared/types/assets.types';

export function prepareLocationContext(locations: Location[]) {
  const searchableSet = new Set<string>();
  const canonicalMap = new Map<string, Location[]>();
  const fuzzyList: { id: string; name: string; normalizedName: string }[] = [];

  for (const location of locations) {
    const normalized = location.name.trim().toLowerCase();

    searchableSet.add(normalized);
    fuzzyList.push({ ...location, normalizedName: normalized });

    if (!canonicalMap.has(normalized)) {
      canonicalMap.set(normalized, []);
    }
    canonicalMap.get(normalized)!.push(location);
  }

  return {
    searchableSet,
    canonicalMap,
    fuzzyList,
  };
}
