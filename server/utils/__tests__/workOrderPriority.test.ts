import { describe, it, expect } from 'vitest';
import { convertNumberToPriority, convertPriorityToNumber } from '@shared/types/work-orders.types';

describe('workOrderPriority utils', () => {
  describe('convertNumberToPriority', () => {
    it('should convert 1 to low priority', () => {
      const result = convertNumberToPriority(1);
      expect(result).toBe('low');
    });

    it('should convert 2 to medium priority', () => {
      const result = convertNumberToPriority(2);
      expect(result).toBe('medium');
    });

    it('should convert 3 to high priority', () => {
      const result = convertNumberToPriority(3);
      expect(result).toBe('high');
    });

    it('should default to medium for unknown priority number 0', () => {
      const result = convertNumberToPriority(0);
      expect(result).toBe('medium');
    });

    it('should default to medium for unknown priority number 4', () => {
      const result = convertNumberToPriority(4);
      expect(result).toBe('medium');
    });

    it('should default to medium for negative priority numbers', () => {
      const result = convertNumberToPriority(-1);
      expect(result).toBe('medium');
    });

    it('should default to medium for large priority numbers', () => {
      const result = convertNumberToPriority(999);
      expect(result).toBe('medium');
    });

    it('should handle decimal numbers by defaulting to medium', () => {
      const result = convertNumberToPriority(1.5);
      expect(result).toBe('medium');
    });
  });

  describe('convertPriorityToNumber', () => {
    it('should convert low priority to 1', () => {
      const result = convertPriorityToNumber('low');
      expect(result).toBe(1);
    });

    it('should convert medium priority to 2', () => {
      const result = convertPriorityToNumber('medium');
      expect(result).toBe(2);
    });

    it('should convert high priority to 3', () => {
      const result = convertPriorityToNumber('high');
      expect(result).toBe(3);
    });

    it('should default to 2 for invalid priority strings', () => {
      // TypeScript would normally catch this, but testing runtime behavior
      const result = convertPriorityToNumber('invalid' as any);
      expect(result).toBe(2);
    });
  });

  describe('round-trip conversion', () => {
    it('should maintain consistency when converting back and forth', () => {
      const priorities: Array<'low' | 'medium' | 'high'> = ['low', 'medium', 'high'];
      const numbers = [1, 2, 3];

      // Test priority to number to priority
      priorities.forEach((priority) => {
        const number = convertPriorityToNumber(priority);
        const backToPriority = convertNumberToPriority(number);
        expect(backToPriority).toBe(priority);
      });

      // Test number to priority to number
      numbers.forEach((number) => {
        const priority = convertNumberToPriority(number);
        const backToNumber = convertPriorityToNumber(priority);
        expect(backToNumber).toBe(number);
      });
    });
  });
});
