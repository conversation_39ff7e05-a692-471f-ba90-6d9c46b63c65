import { matchSingleLocation, MatchSuccess, MatchFail } from '@server/utils/match-single-location';
import { prepareLocationContext } from '@server/utils/prepare-location-context';
import { BulkCreateAccessPointInput } from '@shared/types/access-points.types';

export type MatchMaps = {
  successMap: Map<string, MatchSuccess>;
  failMap: Map<string, MatchFail>;
  unresolved: BulkCreateAccessPointInput[];
};

export function createInputKey(input: BulkCreateAccessPointInput): string {
  return `${input.name.trim().toLowerCase()}::${input.location.trim().toLowerCase()}`;
}

export function matchManyLocations(
  inputs: BulkCreateAccessPointInput[],
  context: ReturnType<typeof prepareLocationContext>,
): MatchMaps {
  const successMap = new Map<string, MatchSuccess>();
  const failMap = new Map<string, MatchFail>();
  const unresolved: BulkCreateAccessPointInput[] = [];

  for (const input of inputs) {
    const result = matchSingleLocation(input, context);

    const key = createInputKey(input);

    if ('locationId' in result) {
      successMap.set(key, result);
    } else {
      failMap.set(key, result);
      unresolved.push(input);
    }
  }

  return {
    successMap,
    failMap,
    unresolved,
  };
}
