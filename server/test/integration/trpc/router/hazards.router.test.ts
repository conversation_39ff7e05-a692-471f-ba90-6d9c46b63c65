import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { createMockContext } from '@server/trpc/router/__tests__/test-utils';
import { db } from '@server/db';
import { mockUser } from '@server/test/fixtures/user';
import { auditTrail, hazards } from '@shared/schema';
import { hazardsRouter } from '@server/trpc/router/hazards.router';
import { and, eq } from 'drizzle-orm';
import { HAZARDS } from '@shared/seeds/hazards';

describe('hazardsRouter', () => {
  const mockContext = createMockContext(mockUser);
  // Wrap the router procedures in trpc.router for testing
  const caller = hazardsRouter.createCaller(mockContext);

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(async () => {
    await db.delete(hazards);
    await db.delete(auditTrail);
  });

  describe('create', () => {
    it('should create a new JHA hazard', async () => {
      const input = { name: 'Test Hazard', type: 'environmental' as const };
      const result = await caller.create(input);

      expect(result).toBeDefined();
      expect(result.name).toBe(input.name);
      expect(result.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
      expect(result.createdBy).toBe(mockUser.id);
      expect(result.id).toBeDefined();

      // Verify in database
      const dbHazard = await db.query.hazards.findFirst({
        where: eq(hazards.id, result.id),
      });

      expect(dbHazard).toBeDefined();
      expect(dbHazard?.name).toBe(input.name);
      expect(dbHazard?.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
      expect(dbHazard?.createdBy).toBe(mockUser.id);
    });

    it('should handle creation with empty name', async () => {
      await expect(caller.create({ name: '', type: 'environmental' })).rejects.toThrow();
    });

    it('should allow creating multiple hazards with different names', async () => {
      const hazard1 = await caller.create({ name: 'First Hazard', type: 'environmental' });
      const hazard2 = await caller.create({ name: 'Second Hazard', type: 'environmental' });

      expect(hazard1.name).toBe('First Hazard');
      expect(hazard2.name).toBe('Second Hazard');
      expect(hazard1.id).not.toBe(hazard2.id);
    });
  });

  describe('list', () => {
    it('should return company-specific hazards', async () => {
      // Create a company-specific hazard
      const createdHazard = await caller.create({ name: 'Company Test Hazard', type: 'environmental' });

      const result = await caller.list({});

      expect(result).toBeDefined();
      expect(result.result).toBeDefined();
      expect(Array.isArray(result.result)).toBe(true);

      // Find our company-specific hazard
      const companyHazard = result.result.find((h) => h.id === createdHazard.id);
      expect(companyHazard).toBeDefined();
      expect(companyHazard?.name).toBe('Company Test Hazard');
      expect(companyHazard?.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
    });

    it('should not include archived hazards', async () => {
      // Create and archive a hazard
      const created = await caller.create({ name: 'To Be Archived', type: 'environmental' });
      await caller.toggleArchive({ id: created.id });

      const result = await caller.list({});

      // Should not include the archived hazard
      const archivedHazard = result.result.find((h) => h.id === created.id);
      expect(archivedHazard).toBeUndefined();
    });

    it('should only return hazards for the current company', async () => {
      // Create hazards for this company
      await caller.create({ name: 'Company A Hazard', type: 'environmental' });

      // Create a hazard for a different company directly in the database
      await db.insert(hazards).values({
        name: 'Company B Hazard',
        type: 'environmental' as const,
        upkeepCompanyId: '*********', // Use proper 10-char length
        createdBy: '*********',
      });

      const result = await caller.list({});

      // Should not include the other company's hazard
      const otherCompanyHazard = result.result.find((h) => h.name === 'Company B Hazard');
      expect(otherCompanyHazard).toBeUndefined();

      // Should include our company's hazard
      const ourCompanyHazard = result.result.find((h) => h.name === 'Company A Hazard');
      expect(ourCompanyHazard).toBeDefined();
    });
  });

  describe('toggleArchive', () => {
    it('should archive an active hazard', async () => {
      // Create a hazard
      const hazard = await caller.create({ name: 'Archive Test Hazard', type: 'environmental' });
      expect(hazard.archivedAt).toBeNull();

      // Archive the hazard
      await caller.toggleArchive({ id: hazard.id });

      // Verify in database
      const dbHazard = await db.query.hazards.findFirst({
        where: eq(hazards.id, hazard.id),
      });

      expect(dbHazard?.archivedAt).not.toBeNull();

      // Check audit trail
      const auditEntries = await db
        .select()
        .from(auditTrail)
        .where(and(eq(auditTrail.entityId, hazard.id), eq(auditTrail.action, 'archived')));

      expect(auditEntries.length).toBeGreaterThan(0);
      expect(auditEntries[0].entityType).toBe('hazard');
      expect(auditEntries[0].action).toBe('archived');
      expect(auditEntries[0].upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
    });

    it('should unarchive an archived hazard', async () => {
      // Create a hazard
      const hazard = await caller.create({ name: 'Unarchive Test Hazard', type: 'environmental' });

      // Archive it first
      await caller.toggleArchive({ id: hazard.id });

      // Verify it's archived
      let dbHazard = await db.query.hazards.findFirst({
        where: eq(hazards.id, hazard.id),
      });
      expect(dbHazard?.archivedAt).not.toBeNull();

      // Unarchive the hazard
      await caller.toggleArchive({ id: hazard.id });

      // Verify in database it's unarchived
      dbHazard = await db.query.hazards.findFirst({
        where: eq(hazards.id, hazard.id),
      });

      expect(dbHazard?.archivedAt).toBeNull();
    });

    it('should handle non-existent hazard gracefully', async () => {
      // Try to toggle archive on non-existent hazard (using valid CUID2 format)
      const result = await caller.toggleArchive({ id: 'clwqxzj8a0001d6mjzx6k4n2x' });

      // Should return undefined when hazard doesn't exist
      expect(result).toBeUndefined();
    });

    it('should preserve hazard data when toggling archive', async () => {
      // Create a hazard
      const originalHazard = await caller.create({ name: 'Preserve Data Test', type: 'environmental' });

      // Archive the hazard
      await caller.toggleArchive({ id: originalHazard.id });

      // Verify data is preserved
      let dbHazard = await db.query.hazards.findFirst({
        where: eq(hazards.id, originalHazard.id),
      });

      expect(dbHazard?.name).toBe('Preserve Data Test');
      expect(dbHazard?.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
      expect(dbHazard?.createdBy).toBe(mockUser.id);
      expect(dbHazard?.archivedAt).not.toBeNull();

      // Unarchive the hazard
      await caller.toggleArchive({ id: originalHazard.id });

      // Verify data is still preserved
      dbHazard = await db.query.hazards.findFirst({
        where: eq(hazards.id, originalHazard.id),
      });

      expect(dbHazard?.name).toBe('Preserve Data Test');
      expect(dbHazard?.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
      expect(dbHazard?.createdBy).toBe(mockUser.id);
      expect(dbHazard?.archivedAt).toBeNull();
    });

    it('should handle multiple archive/unarchive cycles', async () => {
      // Create a hazard
      const hazard = await caller.create({ name: 'Multiple Cycles Test', type: 'environmental' });

      // Multiple archive/unarchive cycles
      for (let i = 0; i < 3; i++) {
        // Archive
        await caller.toggleArchive({ id: hazard.id });
        let dbHazard = await db.query.hazards.findFirst({
          where: eq(hazards.id, hazard.id),
        });
        expect(dbHazard?.archivedAt).not.toBeNull();

        // Unarchive
        await caller.toggleArchive({ id: hazard.id });
        dbHazard = await db.query.hazards.findFirst({
          where: eq(hazards.id, hazard.id),
        });
        expect(dbHazard?.archivedAt).toBeNull();
      }
    });

    it('should validate required id parameter', async () => {
      // Test missing id field
      await expect(caller.toggleArchive({} as any)).rejects.toThrow();

      // Test invalid id format
      await expect(caller.toggleArchive({ id: 'invalid-id' })).rejects.toThrow();
    });
  });

  describe('integration workflow', () => {
    it('should handle complete workflow: create, list, archive, list again, unarchive', async () => {
      // Step 1: Create a hazard
      const hazard = await caller.create({ name: 'Workflow Test Hazard', type: 'environmental' });
      expect(hazard.name).toBe('Workflow Test Hazard');

      // Step 2: List hazards (should include our hazard)
      let hazards = await caller.list({});
      let ourHazard = hazards.result.find((h) => h.id === hazard.id);
      expect(ourHazard).toBeDefined();

      // Step 3: Archive the hazard
      await caller.toggleArchive({ id: hazard.id });

      // Step 4: List hazards (should not include archived hazard)
      hazards = await caller.list({});
      ourHazard = hazards.result.find((h) => h.id === hazard.id);
      expect(ourHazard).toBeUndefined();

      // Step 5: Unarchive the hazard
      await caller.toggleArchive({ id: hazard.id });

      // Step 6: List hazards (should include our hazard again)
      hazards = await caller.list({});
      ourHazard = hazards.result.find((h) => h.id === hazard.id);
      expect(ourHazard).toBeDefined();
      expect(ourHazard?.name).toBe('Workflow Test Hazard');
    });

    it('should maintain data consistency with multiple hazards and operations', async () => {
      // Create multiple hazards
      const hazard1 = await caller.create({ name: 'Consistency Test 1', type: 'environmental' });
      const hazard2 = await caller.create({ name: 'Consistency Test 2', type: 'environmental' });
      const hazard3 = await caller.create({ name: 'Consistency Test 3', type: 'environmental' });

      // Archive one hazard
      await caller.toggleArchive({ id: hazard2.id });

      // List hazards
      const hazards = await caller.list({});

      // Should include hazard1 and hazard3, but not hazard2
      const hazard1Found = hazards.result.find((h) => h.id === hazard1.id);
      const hazard2Found = hazards.result.find((h) => h.id === hazard2.id);
      const hazard3Found = hazards.result.find((h) => h.id === hazard3.id);

      expect(hazard1Found).toBeDefined();
      expect(hazard2Found).toBeUndefined(); // archived
      expect(hazard3Found).toBeDefined();
    });
  });
  describe('listDefault', () => {
    it('should return the default list of hazards and types', async () => {
      const result = await caller.listDefault();
      expect(result).toEqual(HAZARDS);
    });
  });

  describe('bulkCreate', () => {
    it('should create multiple hazards at once', async () => {
      const input = [
        { name: 'Bulk Hazard 1', type: 'environmental' as const },
        { name: 'Bulk Hazard 2', type: 'chemical' as const },
        { name: 'Bulk Hazard 3', type: 'physical' as const },
      ];

      const result = await caller.bulkCreate(input);

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBe(3);

      // Verify each created hazard
      for (let i = 0; i < input.length; i++) {
        expect(result[i].name).toBe(input[i].name);
        expect(result[i].type).toBe(input[i].type);
        expect(result[i].upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
        expect(result[i].createdBy).toBe(mockUser.id);
        expect(result[i].id).toBeDefined();
      }

      // Query database to validate all hazards were created
      const dbHazards = await db
        .select()
        .from(hazards)
        .where(and(eq(hazards.upkeepCompanyId, mockUser.upkeepCompanyId), eq(hazards.createdBy, mockUser.id)));

      expect(dbHazards.length).toBeGreaterThanOrEqual(3);

      // Verify each hazard exists in database
      for (const createdHazard of result) {
        const dbHazard = dbHazards.find((h) => h.id === createdHazard.id);
        expect(dbHazard).toBeDefined();
        expect(dbHazard?.name).toBe(createdHazard.name);
        expect(dbHazard?.type).toBe(createdHazard.type);
        expect(dbHazard?.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
        expect(dbHazard?.createdBy).toBe(mockUser.id);
      }
    });

    it('should reject empty array input', async () => {
      await expect(caller.bulkCreate([])).rejects.toThrow('At least one hazard is required');
    });

    it('should handle single hazard in bulk create', async () => {
      const input = [{ name: 'Single Bulk Hazard', type: 'electrical' as const }];
      const result = await caller.bulkCreate(input);

      expect(result).toBeDefined();
      expect(result.length).toBe(1);
      expect(result[0].name).toBe('Single Bulk Hazard');
      expect(result[0].type).toBe('electrical');
      expect(result[0].upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
      expect(result[0].createdBy).toBe(mockUser.id);

      // Verify in database
      const dbHazard = await db.query.hazards.findFirst({
        where: eq(hazards.id, result[0].id),
      });

      expect(dbHazard).toBeDefined();
      expect(dbHazard?.name).toBe('Single Bulk Hazard');
      expect(dbHazard?.type).toBe('electrical');
    });

    it('should validate required fields for each hazard', async () => {
      // Test with empty name
      const invalidInput = [{ name: '', type: 'environmental' as const }];
      await expect(caller.bulkCreate(invalidInput)).rejects.toThrow();

      // Test with missing type
      const invalidInput2 = [{ name: 'Valid Name' } as any];
      await expect(caller.bulkCreate(invalidInput2)).rejects.toThrow();
    });

    it('should create hazards with different types', async () => {
      const input = [
        { name: 'Environmental Hazard', type: 'environmental' as const },
        { name: 'Chemical Hazard', type: 'chemical' as const },
        { name: 'Physical Hazard', type: 'physical' as const },
        { name: 'Electrical Hazard', type: 'electrical' as const },
        { name: 'Ergonomic Hazard', type: 'ergonomic' as const },
        { name: 'Fall Hazard', type: 'fall' as const },
        { name: 'Radiation Hazard', type: 'radiation' as const },
        { name: 'Biological Hazard', type: 'biological' as const },
        { name: 'Noise Hazard', type: 'noise' as const },
        { name: 'Thermal Hazard', type: 'thermal' as const },
        { name: 'Mechanical Hazard', type: 'mechanical' as const },
        { name: 'Atmospheric Hazard', type: 'atmospheric' as const },
      ];

      const result = await caller.bulkCreate(input);

      expect(result.length).toBe(12);

      // Verify all different types were created
      const typesCreated = result.map((h) => h.type);
      const uniqueTypes = [...new Set(typesCreated)];
      expect(uniqueTypes.length).toBe(12);

      // Query database to validate all were created with correct types
      const dbHazards = await db
        .select()
        .from(hazards)
        .where(and(eq(hazards.upkeepCompanyId, mockUser.upkeepCompanyId), eq(hazards.createdBy, mockUser.id)));

      for (const expectedHazard of input) {
        const dbHazard = dbHazards.find((h) => h.name === expectedHazard.name);
        expect(dbHazard).toBeDefined();
        expect(dbHazard?.type).toBe(expectedHazard.type);
      }
    });

    it('should maintain company isolation in bulk create', async () => {
      const input = [
        { name: 'Company A Bulk Hazard 1', type: 'environmental' as const },
        { name: 'Company A Bulk Hazard 2', type: 'chemical' as const },
      ];

      const result = await caller.bulkCreate(input);

      // Verify all hazards belong to the correct company
      for (const hazard of result) {
        expect(hazard.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
        expect(hazard.createdBy).toBe(mockUser.id);
      }

      // Query database and verify company isolation
      const dbHazards = await db.select().from(hazards).where(eq(hazards.upkeepCompanyId, mockUser.upkeepCompanyId));

      const bulkCreatedHazards = dbHazards.filter((h) => input.some((inputHazard) => inputHazard.name === h.name));

      expect(bulkCreatedHazards.length).toBe(2);
      for (const dbHazard of bulkCreatedHazards) {
        expect(dbHazard.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
        expect(dbHazard.createdBy).toBe(mockUser.id);
      }
    });

    it('should handle large batch creation', async () => {
      const largeInput = Array.from({ length: 50 }, (_, i) => ({
        name: `Batch Hazard ${i + 1}`,
        type: 'environmental' as const,
      }));

      const result = await caller.bulkCreate(largeInput);

      expect(result.length).toBe(50);

      // Verify all have unique IDs
      const ids = result.map((h) => h.id);
      const uniqueIds = [...new Set(ids)];
      expect(uniqueIds.length).toBe(50);

      // Sample check in database (check first, middle, and last)
      const samplesToCheck = [result[0], result[24], result[49]];
      for (const sample of samplesToCheck) {
        const dbHazard = await db.query.hazards.findFirst({
          where: eq(hazards.id, sample.id),
        });
        expect(dbHazard).toBeDefined();
        expect(dbHazard?.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
        expect(dbHazard?.createdBy).toBe(mockUser.id);
      }
    });

    it('should handle duplicate hazard names with different types as separate hazards', async () => {
      // First, create a hazard
      const originalHazard = await caller.create({ name: 'Duplicate Test Hazard', type: 'environmental' });

      // Try to bulk create with the same name but different type - should create new hazard
      const input = [{ name: 'Duplicate Test Hazard', type: 'chemical' as const }];
      const result = await caller.bulkCreate(input);

      expect(result.length).toBe(1);
      expect(result[0].name).toBe('Duplicate Test Hazard');
      expect(result[0].type).toBe('chemical');
      expect(result[0].id).not.toBe(originalHazard.id); // Should be a different record

      // Verify both hazards exist in database
      const dbHazards = await db
        .select()
        .from(hazards)
        .where(and(eq(hazards.name, 'Duplicate Test Hazard'), eq(hazards.upkeepCompanyId, mockUser.upkeepCompanyId)));

      expect(dbHazards.length).toBe(2);
      const environmentalHazard = dbHazards.find((h) => h.type === 'environmental');
      const chemicalHazard = dbHazards.find((h) => h.type === 'chemical');

      expect(environmentalHazard).toBeDefined();
      expect(chemicalHazard).toBeDefined();
      expect(environmentalHazard?.id).toBe(originalHazard.id);
      expect(chemicalHazard?.id).toBe(result[0].id);
    });

    it('should unarchive previously archived hazard when creating exact duplicate', async () => {
      // Create and archive a hazard
      const originalHazard = await caller.create({ name: 'Archive Test Hazard', type: 'environmental' });
      await caller.toggleArchive({ id: originalHazard.id });

      // Verify it's archived
      let dbHazard = await db.query.hazards.findFirst({
        where: eq(hazards.id, originalHazard.id),
      });
      expect(dbHazard?.archivedAt).not.toBeNull();

      // Try to bulk create with the same name AND type (exact duplicate)
      const input = [{ name: 'Archive Test Hazard', type: 'environmental' as const }];
      const result = await caller.bulkCreate(input);

      expect(result.length).toBe(1);
      expect(result[0].id).toBe(originalHazard.id);
      expect(result[0].type).toBe('environmental');

      // Verify in database that it's unarchived
      dbHazard = await db.query.hazards.findFirst({
        where: eq(hazards.id, originalHazard.id),
      });

      expect(dbHazard?.archivedAt).toBeNull(); // Should be unarchived
      expect(dbHazard?.type).toBe('environmental'); // Should remain same type
      expect(dbHazard?.updatedAt).not.toBeNull();
    });

    it('should handle mix of new and exact duplicate hazards in bulk create', async () => {
      // Create one existing hazard
      const existingHazard = await caller.create({ name: 'Existing Hazard', type: 'environmental' });

      // Bulk create with mix of new and exact duplicate
      const input = [
        { name: 'Existing Hazard', type: 'environmental' as const }, // Exact duplicate - should update
        { name: 'Existing Hazard', type: 'chemical' as const }, // Same name, different type - new hazard
        { name: 'New Hazard 1', type: 'physical' as const }, // New
        { name: 'New Hazard 2', type: 'electrical' as const }, // New
      ];

      const result = await caller.bulkCreate(input);

      expect(result.length).toBe(4);

      // Find the updated existing hazard (same name, same type)
      const updatedExisting = result.find((h) => h.id === existingHazard.id);
      expect(updatedExisting).toBeDefined();
      expect(updatedExisting?.type).toBe('environmental');
      expect(updatedExisting?.name).toBe('Existing Hazard');

      // Find the new hazard with same name but different type
      const sameNameDifferentType = result.find((h) => h.name === 'Existing Hazard' && h.type === 'chemical');
      expect(sameNameDifferentType).toBeDefined();
      expect(sameNameDifferentType?.id).not.toBe(existingHazard.id);

      // Find the completely new hazards
      const completelyNewHazards = result.filter((h) => h.name !== 'Existing Hazard');
      expect(completelyNewHazards.length).toBe(2);

      // Verify all in database
      for (const hazard of result) {
        const dbHazard = await db.query.hazards.findFirst({
          where: eq(hazards.id, hazard.id),
        });
        expect(dbHazard).toBeDefined();
        expect(dbHazard?.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
      }
    });

    it('should handle multiple entries with same name but different types in single bulk create', async () => {
      // Create existing hazard
      const existingHazard = await caller.create({ name: 'Multiple Duplicate Test', type: 'environmental' });

      // Try to bulk create multiple entries with same name but different types
      const input = [
        { name: 'Multiple Duplicate Test', type: 'environmental' as const }, // Exact duplicate
        { name: 'Multiple Duplicate Test', type: 'chemical' as const }, // Same name, different type
        { name: 'Multiple Duplicate Test', type: 'physical' as const }, // Same name, different type
        { name: 'Multiple Duplicate Test', type: 'electrical' as const }, // Same name, different type
      ];

      const result = await caller.bulkCreate(input);

      expect(result.length).toBe(4);

      // Should have 4 different IDs (one exact duplicate, three new)
      const uniqueIds = [...new Set(result.map((h) => h.id))];
      expect(uniqueIds.length).toBe(4);

      // One should be the existing hazard ID (exact duplicate)
      const exactDuplicate = result.find((h) => h.id === existingHazard.id);
      expect(exactDuplicate).toBeDefined();
      expect(exactDuplicate?.type).toBe('environmental');

      // Verify all have the same name but different types
      expect(result.every((h) => h.name === 'Multiple Duplicate Test')).toBe(true);
      const types = result.map((h) => h.type);
      expect(types).toContain('environmental');
      expect(types).toContain('chemical');
      expect(types).toContain('physical');
      expect(types).toContain('electrical');

      // Verify all in database
      const dbHazards = await db
        .select()
        .from(hazards)
        .where(and(eq(hazards.name, 'Multiple Duplicate Test'), eq(hazards.upkeepCompanyId, mockUser.upkeepCompanyId)));

      expect(dbHazards.length).toBe(4);
    });
  });
});
