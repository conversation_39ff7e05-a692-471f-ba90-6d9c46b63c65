import { db } from '@server/db';
import { getUserById, getUsers, hasPermission } from '@server/services/user.service';
import { mockUser } from '@server/test/fixtures/user';
import { createMockContext } from '@server/trpc/router/__tests__/test-utils';
import { oshaAgencyReportRouter } from '@server/trpc/router/osha-agency-report.router';
import { oshaAgencyReport, oshaAuditTrail, oshaLocations } from '@shared/schema';
import { eq } from 'drizzle-orm';
import { afterAll, afterEach, beforeAll, beforeEach, describe, expect, it, vi } from 'vitest';

// Mock the user service
vi.mock('@server/services/user.service', () => ({
  getUserById: vi.fn(),
  getUsers: vi.fn(),
  hasPermission: vi.fn().mockReturnValue(true),
  userHasIncidentPermission: vi.fn().mockReturnValue(true),
  canEditIncident: vi.fn().mockReturnValue(true),
  canExportIncident: vi.fn().mockReturnValue(true),
}));

// Mock the IP address utility
vi.mock('@server/utils/get-ip-address', () => ({
  getIpAddress: vi.fn().mockReturnValue('127.0.0.1'),
}));

let oshaLocationId: string;

describe('oshaAgencyReportRouter', () => {
  const mockContext = createMockContext(mockUser);

  beforeAll(async () => {
    // Create a mock global location
    const oshaLocation = await db
      .insert(oshaLocations)
      .values({
        name: 'Test Global Location',
        upkeepCompanyId: '123',
        createdBy: '123',
      })
      .returning({ id: oshaLocations.id });
    oshaLocationId = oshaLocation.at(0)!.id;
  });

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(getUserById).mockResolvedValue(mockUser);
    vi.mocked(getUsers).mockResolvedValue([mockUser]);
    vi.mocked(hasPermission).mockReturnValue(true);
  });

  afterEach(async () => {
    await db.delete(oshaAuditTrail);
    await db.delete(oshaAgencyReport);
  });

  afterAll(async () => {
    await db.delete(oshaLocations);
  });

  const createValidAgencyReportInput = (overrides = {}) => ({
    dateOfIncident: new Date('2025-01-15'),
    locationId: '123',
    typeOfIncident: 'fatality' as const,
    description: 'Test serious incident description',
    employeesInvolved: 'John Doe, Jane Smith',
    companyContactPerson: 'Safety Manager',
    contactPersonPhone: '************',
    affectedCount: 1,
    datePrepared: new Date('2025-01-15'),
    oshaLocationId,
    ...overrides,
  });

  describe('create', () => {
    it('should require oshaLocationId', async () => {
      const caller = oshaAgencyReportRouter.createCaller(mockContext);
      const input = createValidAgencyReportInput();
      delete (input as any).oshaLocationId;

      await expect(async () => await caller.create(input)).rejects.toThrowError();
    });

    it('should create a new agency report successfully', async () => {
      const caller = oshaAgencyReportRouter.createCaller(mockContext);
      const input = createValidAgencyReportInput();

      const result = await caller.create(input);

      expect(result).toBeDefined();
      if (result) {
        expect(result.id).toBeDefined();
        expect(result.typeOfIncident).toBe('fatality');
        expect(result.description).toBe(input.description);
        expect(result.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
        expect(result.createdBy).toBe(mockUser.id);

        // Verify audit trail was created
        const auditTrail = await db
          .select()
          .from(oshaAuditTrail)
          .where(eq(oshaAuditTrail.entityId, result.id))
          .limit(1);

        expect(auditTrail).toHaveLength(1);
        expect(auditTrail[0].action).toBe('created');
        expect(auditTrail[0].entityType).toBe('osha_agency_report');
      }
    });

    it('should auto-generate slug for the report', async () => {
      const caller = oshaAgencyReportRouter.createCaller(mockContext);
      const input = createValidAgencyReportInput();

      const result = await caller.create(input);

      expect(result).toBeDefined();
      if (result) {
        expect(result.slug).toBeDefined();
        expect(result.slug).toMatch(/^AR-\d{4}-\d{4}$/);
      }
    });

    describe('Validation Tests', () => {
      it('should fail when dateOfIncident is missing', async () => {
        const caller = oshaAgencyReportRouter.createCaller(mockContext);
        const input = createValidAgencyReportInput({ dateOfIncident: undefined });

        await expect(caller.create(input)).rejects.toThrow();
      });

      it('should fail when oshaLocationId is empty', async () => {
        const caller = oshaAgencyReportRouter.createCaller(mockContext);
        const input = createValidAgencyReportInput({ oshaLocationId: '' });

        await expect(caller.create(input)).rejects.toThrow();
      });

      it('should fail when typeOfIncident is invalid', async () => {
        const caller = oshaAgencyReportRouter.createCaller(mockContext);
        const input = createValidAgencyReportInput({ typeOfIncident: 'invalid_type' as any });

        await expect(caller.create(input)).rejects.toThrow();
      });

      it('should fail when required fields are empty', async () => {
        const caller = oshaAgencyReportRouter.createCaller(mockContext);

        const testCases = [{ companyContactPerson: '' }, { contactPersonPhone: '' }];

        for (const testCase of testCases) {
          const input = createValidAgencyReportInput(testCase);
          await expect(caller.create(input)).rejects.toThrow();
        }
      });

      it('should fail when affectedCount is negative', async () => {
        const caller = oshaAgencyReportRouter.createCaller(mockContext);
        const input = createValidAgencyReportInput({ affectedCount: -1 });

        await expect(caller.create(input)).rejects.toThrow();
      });

      it('should accept valid incident types', async () => {
        const caller = oshaAgencyReportRouter.createCaller(mockContext);
        const incidentTypes = ['fatality', 'amputation', 'hospitalization', 'eye_loss'] as const;

        for (const type of incidentTypes) {
          const input = createValidAgencyReportInput({ typeOfIncident: type });
          const result = await caller.create(input);

          expect(result).toBeDefined();
          if (result) {
            expect(result.typeOfIncident).toBe(type);

            // Clean up
            await db.delete(oshaAgencyReport).where(eq(oshaAgencyReport.id, result.id));
            await db.delete(oshaAuditTrail).where(eq(oshaAuditTrail.entityId, result.id));
          }
        }
      });
    });
  });

  describe('update', () => {
    let reportId: string;

    beforeEach(async () => {
      const report = await db
        .insert(oshaAgencyReport)
        .values({
          upkeepCompanyId: mockUser.upkeepCompanyId,
          slug: 'AR-2025-0001',
          dateOfIncident: new Date('2025-01-15'),
          oshaLocationId,
          typeOfIncident: 'fatality',
          description: 'Original description',
          employeesInvolved: 'John Doe',
          companyContactPerson: 'Safety Manager',
          contactPersonPhone: '************',
          affectedCount: 1,
          datePrepared: new Date('2025-01-15'),
          createdBy: mockUser.id!,
        })
        .returning({ id: oshaAgencyReport.id });
      reportId = report[0].id;
    });

    it('should require oshaLocationId when provided', async () => {
      const caller = oshaAgencyReportRouter.createCaller(mockContext);
      const input = {
        id: reportId,
        description: 'Updated description',
        oshaLocationId: '', // Empty oshaLocationId should fail
      };

      await expect(async () => await caller.update(input)).rejects.toThrowError();
    });

    it('should update an existing agency report successfully', async () => {
      const caller = oshaAgencyReportRouter.createCaller(mockContext);

      const updateData = {
        id: reportId,
        description: 'Updated description',
        affectedCount: 3,
        oshaLocationId,
      };

      const result = await caller.update(updateData);

      expect(result).toBeDefined();
      if (result) {
        expect(result.id).toBe(reportId);

        // Verify audit trail
        const auditTrail = await db.select().from(oshaAuditTrail).where(eq(oshaAuditTrail.entityId, reportId)).limit(1);

        expect(auditTrail).toHaveLength(1);
        expect(auditTrail[0].action).toBe('updated');
        expect(auditTrail[0].entityType).toBe('osha_agency_report');
      }
    });

    it('should handle archive/unarchive operations', async () => {
      const caller = oshaAgencyReportRouter.createCaller(mockContext);

      // Archive the report
      const archived = await caller.update({
        id: reportId,
        archivedAt: new Date(),
        oshaLocationId,
      });

      expect(archived).toBeDefined();
      if (archived) {
        expect(archived.id).toBe(reportId);
      }

      // Unarchive the report
      const unarchived = await caller.update({
        id: reportId,
        archivedAt: null,
        oshaLocationId,
      });

      expect(unarchived).toBeDefined();
      if (unarchived) {
        expect(unarchived.id).toBe(reportId);
      }
    });

    it('should return undefined for non-existent report update', async () => {
      const caller = oshaAgencyReportRouter.createCaller(mockContext);
      const nonExistentId = 'non-existent-id';

      const result = await caller.update({
        id: nonExistentId,
        description: 'Updated description',
        oshaLocationId,
      });

      expect(result).toBeUndefined();
    });
  });

  describe('list', () => {
    beforeEach(async () => {
      // Create multiple reports for testing
      await db.insert(oshaAgencyReport).values([
        {
          upkeepCompanyId: mockUser.upkeepCompanyId,
          slug: 'AR-2025-0001',
          dateOfIncident: new Date('2025-01-15'),
          oshaLocationId,
          typeOfIncident: 'fatality',
          description: 'First incident',
          employeesInvolved: 'John Doe',
          companyContactPerson: 'Safety Manager',
          contactPersonPhone: '************',
          affectedCount: 1,
          datePrepared: new Date('2025-01-15'),
          createdBy: mockUser.id!,
        },
        {
          upkeepCompanyId: mockUser.upkeepCompanyId,
          slug: 'AR-2025-0002',
          dateOfIncident: new Date('2025-02-15'),
          oshaLocationId,
          typeOfIncident: 'amputation',
          description: 'Second incident',
          employeesInvolved: 'Jane Smith',
          companyContactPerson: 'Safety Manager',
          contactPersonPhone: '************',
          affectedCount: 1,
          datePrepared: new Date('2025-02-15'),
          archivedAt: new Date(),
          createdBy: mockUser.id!,
        },
      ]);
    });

    it('should list reports for the user company only', async () => {
      const caller = oshaAgencyReportRouter.createCaller(mockContext);

      const result = await caller.list({});

      expect(result.result).toHaveLength(1); // Only non-archived reports
      expect(result.result.every((r) => r.upkeepCompanyId === mockUser.upkeepCompanyId)).toBe(true);
    });

    it('should filter by incident type', async () => {
      const caller = oshaAgencyReportRouter.createCaller(mockContext);

      const result = await caller.list({
        typeOfIncident: ['fatality'],
      });

      expect(result.result).toHaveLength(1);
      expect(result.result[0].typeOfIncident).toBe('fatality');
    });

    it('should include archived reports when requested', async () => {
      const caller = oshaAgencyReportRouter.createCaller(mockContext);

      const result = await caller.list({
        includeArchived: true,
      });

      expect(result.result).toHaveLength(2);
      expect(result.result.some((r) => r.archivedAt !== null)).toBe(true);
    });
  });

  describe('getById', () => {
    let reportId: string;

    beforeEach(async () => {
      const report = await db
        .insert(oshaAgencyReport)
        .values({
          upkeepCompanyId: mockUser.upkeepCompanyId,
          slug: 'AR-2025-0001',
          dateOfIncident: new Date('2025-01-15'),
          oshaLocationId,
          typeOfIncident: 'fatality',
          description: 'Test incident',
          employeesInvolved: 'John Doe',
          companyContactPerson: 'Safety Manager',
          contactPersonPhone: '************',
          affectedCount: 1,
          datePrepared: new Date('2025-01-15'),
          createdBy: mockUser.id!,
        })
        .returning({ id: oshaAgencyReport.id });
      reportId = report[0].id;
    });

    it('should return error for non-existent report', async () => {
      const caller = oshaAgencyReportRouter.createCaller(mockContext);
      const nonExistentId = 'non-existent-id';

      await expect(caller.getById({ id: nonExistentId })).rejects.toThrow();
    });
  });

  describe('getByIdForEdit', () => {
    let reportId: string;

    beforeEach(async () => {
      const report = await db
        .insert(oshaAgencyReport)
        .values({
          upkeepCompanyId: mockUser.upkeepCompanyId,
          slug: 'AR-2025-0001',
          dateOfIncident: new Date('2025-01-15'),
          oshaLocationId,
          typeOfIncident: 'fatality',
          description: 'Test incident',
          employeesInvolved: 'John Doe',
          companyContactPerson: 'Safety Manager',
          contactPersonPhone: '************',
          affectedCount: 1,
          datePrepared: new Date('2025-01-15'),
          createdBy: mockUser.id!,
        })
        .returning({ id: oshaAgencyReport.id });
      reportId = report[0].id;
    });

    it('should retrieve a report for editing', async () => {
      const caller = oshaAgencyReportRouter.createCaller(mockContext);

      const result = await caller.getByIdForEdit({ id: reportId });

      expect(result).toBeDefined();
      if (result) {
        expect(result.id).toBe(reportId);
        expect(result.typeOfIncident).toBe('fatality');
        expect(result.oshaLocationId).toBe(oshaLocationId);
      }
    });

    it('should return undefined for non-existent report', async () => {
      const caller = oshaAgencyReportRouter.createCaller(mockContext);
      const nonExistentId = 'non-existent-id';

      const result = await caller.getByIdForEdit({ id: nonExistentId });

      expect(result).toBeUndefined();
    });
  });

  describe('toggleArchive', () => {
    it('should archive an active agency report', async () => {
      const caller = oshaAgencyReportRouter.createCaller(mockContext);
      const input = createValidAgencyReportInput();

      const createdReport = await caller.create(input);

      // Verify it's initially not archived
      const initialReport = await caller.getById({ id: createdReport!.id });
      expect(initialReport?.archivedAt).toBeNull();

      // Archive the agency report
      const archivedResult = await caller.toggleArchive({ id: createdReport!.id });

      expect(archivedResult).toBeDefined();
      expect(archivedResult?.id).toBe(createdReport!.id);
      expect(archivedResult?.archivedAt).not.toBeNull();
    });

    it('should unarchive an archived agency report', async () => {
      const caller = oshaAgencyReportRouter.createCaller(mockContext);
      const input = createValidAgencyReportInput();

      const createdReport = await caller.create(input);

      // First archive the agency report
      const archivedResult = await caller.toggleArchive({ id: createdReport!.id });
      expect(archivedResult?.archivedAt).not.toBeNull();

      // Then unarchive it
      const unarchivedResult = await caller.toggleArchive({ id: createdReport!.id });

      expect(unarchivedResult).toBeDefined();
      expect(unarchivedResult?.id).toBe(createdReport!.id);
      expect(unarchivedResult?.archivedAt).toBeNull();
    });

    it('should preserve agency report data when toggling archive status', async () => {
      const caller = oshaAgencyReportRouter.createCaller(mockContext);
      const input = createValidAgencyReportInput();

      const createdReport = await caller.create(input);

      // Archive the agency report
      const archivedResult = await caller.toggleArchive({ id: createdReport!.id });
      expect(archivedResult?.archivedAt).not.toBeNull();

      // Verify other fields remain unchanged by getting the full agency report
      const archivedReport = await caller.getById({ id: createdReport!.id });
      expect(archivedReport?.typeOfIncident).toBe(input.typeOfIncident);
      expect(archivedReport?.description).toBe(input.description);
      expect(archivedReport?.employeesInvolved).toBe(input.employeesInvolved);
      expect(archivedReport?.companyContactPerson).toBe(input.companyContactPerson);
      expect(archivedReport?.contactPersonPhone).toBe(input.contactPersonPhone);
      expect(archivedReport?.affectedCount).toBe(input.affectedCount);
      expect(archivedReport?.oshaLocationId).toBe(input.oshaLocationId);

      // Unarchive and verify data is still preserved
      const unarchivedResult = await caller.toggleArchive({ id: createdReport!.id });
      expect(unarchivedResult?.archivedAt).toBeNull();

      const unarchivedReport = await caller.getById({ id: createdReport!.id });
      expect(unarchivedReport?.typeOfIncident).toBe(input.typeOfIncident);
      expect(unarchivedReport?.description).toBe(input.description);
      expect(unarchivedReport?.employeesInvolved).toBe(input.employeesInvolved);
      expect(unarchivedReport?.companyContactPerson).toBe(input.companyContactPerson);
      expect(unarchivedReport?.contactPersonPhone).toBe(input.contactPersonPhone);
      expect(unarchivedReport?.affectedCount).toBe(input.affectedCount);
      expect(unarchivedReport?.oshaLocationId).toBe(input.oshaLocationId);
    });

    it('should handle toggle archive for different incident types', async () => {
      const caller = oshaAgencyReportRouter.createCaller(mockContext);

      // Test with just one incident type to avoid creating too much data
      const input = createValidAgencyReportInput({ typeOfIncident: 'fatality' });
      const createdReport = await caller.create(input);

      // Archive the agency report
      const archivedResult = await caller.toggleArchive({ id: createdReport!.id });
      expect(archivedResult?.archivedAt).not.toBeNull();

      // Verify the agency report can still be retrieved with correct incident type
      const archivedReport = await caller.getById({ id: createdReport!.id });
      expect(archivedReport?.typeOfIncident).toBe('fatality');
      expect(archivedReport?.archivedAt).not.toBeNull();

      // Unarchive and verify incident type is preserved
      const unarchivedResult = await caller.toggleArchive({ id: createdReport!.id });
      expect(unarchivedResult?.archivedAt).toBeNull();

      const finalReport = await caller.getById({ id: createdReport!.id });
      expect(finalReport?.typeOfIncident).toBe('fatality');
      expect(finalReport?.archivedAt).toBeNull();
    });

    it('should handle non-existent agency report', async () => {
      const caller = oshaAgencyReportRouter.createCaller(mockContext);

      // Try to toggle archive on a non-existent agency report
      await expect(caller.toggleArchive({ id: 'non-existent-id' })).rejects.toThrow();
    });

    it('should validate required id parameter', async () => {
      const caller = oshaAgencyReportRouter.createCaller(mockContext);

      // Test missing id field
      await expect(caller.toggleArchive({} as any)).rejects.toThrow();
    });

    it('should affect list results based on includeArchived filter', async () => {
      const caller = oshaAgencyReportRouter.createCaller(mockContext);

      // Create two agency reports
      const input1 = createValidAgencyReportInput({
        typeOfIncident: 'fatality',
        description: 'Active Fatal Incident',
      });
      const input2 = createValidAgencyReportInput({
        typeOfIncident: 'amputation',
        description: 'To Be Archived Amputation',
      });

      const report1 = await caller.create(input1);
      const report2 = await caller.create(input2);

      // Archive one agency report
      await caller.toggleArchive({ id: report2!.id });

      // List without includeArchived should only show active agency report
      const activeList = await caller.list({ includeArchived: false });
      expect(activeList.result.length).toBe(1);
      expect(activeList.result[0].description).toBe('Active Fatal Incident');

      // List with includeArchived should show both agency reports
      const allList = await caller.list({ includeArchived: true });
      expect(allList.result.length).toBe(2);

      const descriptions = allList.result.map((r) => r.description).sort();
      expect(descriptions).toEqual(['Active Fatal Incident', 'To Be Archived Amputation']);
    });

    it('should create audit trail when toggling archive', async () => {
      const caller = oshaAgencyReportRouter.createCaller(mockContext);
      const input = createValidAgencyReportInput();

      const createdReport = await caller.create(input);

      // Clear existing audit trail entries to focus on toggle operations
      await db.delete(oshaAuditTrail).where(eq(oshaAuditTrail.entityId, createdReport!.id));

      // Archive the agency report
      await caller.toggleArchive({ id: createdReport!.id });

      // Check audit trail for archive action
      const archiveAuditTrail = await db
        .select()
        .from(oshaAuditTrail)
        .where(eq(oshaAuditTrail.entityId, createdReport!.id));

      expect(archiveAuditTrail.length).toBe(1);
      expect(archiveAuditTrail[0].action).toBe('archived');
      expect(archiveAuditTrail[0].entityType).toBe('osha_agency_report');

      // Unarchive the agency report
      await caller.toggleArchive({ id: createdReport!.id });

      // Check audit trail for unarchive action
      const unarchiveAuditTrail = await db
        .select()
        .from(oshaAuditTrail)
        .where(eq(oshaAuditTrail.entityId, createdReport!.id))
        .orderBy(oshaAuditTrail.createdAt);

      expect(unarchiveAuditTrail.length).toBe(2);
      expect(unarchiveAuditTrail[1].action).toBe('restored');
      expect(unarchiveAuditTrail[1].entityType).toBe('osha_agency_report');
    });
  });

  describe('Permission Tests', () => {
    it('should deny access when user lacks permissions', async () => {
      vi.mocked(hasPermission).mockReturnValue(false);
      const caller = oshaAgencyReportRouter.createCaller(mockContext);
      const input = createValidAgencyReportInput();

      await expect(caller.list({})).rejects.toThrow();
      await expect(caller.create(input)).rejects.toThrow();
      await expect(caller.update({ id: 'test-id', description: 'Updated', oshaLocationId })).rejects.toThrow();
    });
  });
});
