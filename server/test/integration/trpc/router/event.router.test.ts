import { createId } from '@paralleldrive/cuid2';
import { db } from '@server/db';
import { getAssets } from '@server/services/asset.service';
import { getLocations, searchLocationsPublic } from '@server/services/location.service';
import { getUserById, getUsers, getUsersPublic, hasPermission } from '@server/services/user.service';
import { mockTechnicianUser, mockUser, mockViewOnlyUser } from '@server/test/fixtures/user';
import { createMockContext } from '@server/trpc/router/__tests__/test-utils';
import { eventRouter } from '@server/trpc/router/event.router';
import { comments, entityTypeEnum, events, files } from '@shared/schema';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

const mockEventInput = {
  title: 'Test event',
  description: 'Test description',
  type: 'incident' as const,
  category: 'other' as const,
  severity: 'low' as const,
  reportedAt: new Date(),
  locationId: '123',
  assetIds: ['123'],
};

const mockCustomerEventInput = {
  title: 'Customer event test',
  description: 'Customer event description',
  type: 'customer_incident' as const,
  category: 'other' as const,
  severity: 'medium' as const,
  reportedAt: new Date(),
  locationId: '123',
  assetIds: ['123'],
  customerName: 'João Silva',
  customerPhoneNumber: '(*************',
  customerAddress: 'Rua das Flores, 123 - São Paulo, SP',
};

const mockEventWithWitnessesInput = {
  title: 'Event with witnesses',
  description: 'Test event with multiple witnesses',
  type: 'incident' as const,
  category: 'other' as const,
  severity: 'high' as const,
  reportedAt: new Date(),
  locationId: '123',
  assetIds: ['123'],
  witnesses: [
    {
      fullName: 'John Doe',
      phoneNumber: '(*************',
      email: '<EMAIL>',
      notes: 'Saw the entire incident from start to finish',
    },
    {
      fullName: 'Jane Smith',
      phoneNumber: '(*************',
      email: '<EMAIL>',
      notes: 'Witnessed the aftermath and helped with immediate response',
    },
  ],
};

// Mock the user service
vi.mock('@server/services/user.service', () => ({
  getUserById: vi.fn(),
  getUsers: vi.fn(),
  getUsersPublic: vi.fn(),
  hasPermission: vi.fn().mockReturnValue(true),
  userHasEventPermission: vi.fn().mockReturnValue(true),
  canEditEvent: vi.fn().mockReturnValue(true),
  canExportEvent: vi.fn().mockReturnValue(true),
}));

// Mock the queue system to prevent actual queue operations
vi.mock('@server/queue/queue-utils', () => ({
  addJobToQueue: vi.fn().mockResolvedValue(undefined),
}));

vi.mock('@server/queue/job-names', () => ({
  QUEUE_JOB_NAMES: {
    PUBLIC_EVENT_CREATE_NOTIFICATION: 'public-event-create-notification',
    EVENT_CREATE_NOTIFICATION: 'event-create-notification',
    EVENT_UPDATE_NOTIFICATION: 'event-update-notification',
  },
}));

// Mock the notification services
vi.mock('@server/services/event-notification.service', () => ({
  sendPublicEventCreateNotification: vi.fn().mockResolvedValue(undefined),
  sendEventCreateNotification: vi.fn().mockResolvedValue(undefined),
  sendEventUpdateNotificationWithUser: vi.fn().mockResolvedValue(undefined),
}));

vi.mock('@server/services/email/event-notification.service', () => ({
  handleEventNotifications: vi.fn(),
  handleEventPublicNotifications: vi.fn(),
  sendEventSubmittedNotification: vi.fn(),
}));

vi.mock('@server/services/location.service', () => ({
  getLocationById: vi.fn().mockResolvedValue({ id: '123', name: 'Test Location' }),
  getLocations: vi.fn().mockResolvedValue([{ id: '123', name: 'Test Location' }]),
  searchLocationsPublic: vi.fn(),
}));

vi.mock('@server/services/asset.service', () => ({
  getAssets: vi.fn().mockResolvedValue({
    noResults: false,
    result: [{ id: '123', name: 'Test Asset', description: 'Test Asset Description' }],
    nextCursor: undefined,
  }),
}));

describe('event router', () => {
  const mockContext = createMockContext(mockUser) as any;

  beforeEach(() => {
    vi.clearAllMocks();

    vi.mocked(getUserById).mockResolvedValue(mockUser);
    vi.mocked(getUsers).mockResolvedValue([mockUser]);
    vi.mocked(getUsersPublic).mockResolvedValue({
      noResults: false,
      result: [
        {
          id: mockUser.id,
          firstName: mockUser.firstName,
          lastName: mockUser.lastName,
          fullName: mockUser.fullName,
          username: mockUser.email,
          email: mockUser.email,
        },
      ],
      nextCursor: undefined,
    });
    vi.mocked(hasPermission).mockReturnValue(true);
    vi.mocked(getLocations).mockResolvedValue({
      noResults: false,
      result: [{ id: '123', name: 'Test Location' }],
      nextCursor: undefined,
    });
    vi.mocked(searchLocationsPublic).mockResolvedValue({
      noResults: false,
      nextCursor: undefined,
      result: [{ id: '123', name: 'Test Location' }],
    });
    vi.mocked(getAssets).mockResolvedValue({
      noResults: false,
      result: [{ id: '123', name: 'Test Asset', description: 'Test Asset Description' }],
      nextCursor: undefined,
    });
  });

  afterEach(async () => {
    await db.delete(files);
    await db.delete(comments);
    await db.delete(events);
  });

  describe('create', () => {
    it('should not allow view only users to create events', async () => {
      const mockViewOnlyContext = createMockContext(mockViewOnlyUser);
      const caller = eventRouter.createCaller(mockViewOnlyContext);

      await expect(caller.create({} as any)).rejects.toThrow();
    });

    it('should allow technicians to create events', async () => {
      const mockTechnicianContext = createMockContext(mockTechnicianUser);
      const caller = eventRouter.createCaller(mockTechnicianContext);

      const event = await caller.create(mockEventInput);

      expect(event).toBeDefined();
      expect(event?.title).toBe(mockEventInput.title);
      expect(event?.description).toBe(mockEventInput.description);
    });

    it('should create a new event', async () => {
      const caller = eventRouter.createCaller(mockContext);
      const event = await caller.create(mockEventInput);

      expect(event).toBeDefined();
      expect(event?.title).toBe(mockEventInput.title);
      expect(event?.description).toBe(mockEventInput.description);
    });

    it('should create a new customer incident with all customer fields', async () => {
      const caller = eventRouter.createCaller(mockContext);
      const event = await caller.create(mockCustomerEventInput);

      expect(event).toBeDefined();
      expect(event?.title).toBe(mockCustomerEventInput.title);
      expect(event?.description).toBe(mockCustomerEventInput.description);
      expect(event?.type).toBe('customer_incident');
      expect((event as any)?.customerName).toBe(mockCustomerEventInput.customerName);
      expect((event as any)?.customerPhoneNumber).toBe(mockCustomerEventInput.customerPhoneNumber);
      expect((event as any)?.customerAddress).toBe(mockCustomerEventInput.customerAddress);
    });

    it('should create a customer incident with partial customer information', async () => {
      const caller = eventRouter.createCaller(mockContext);
      const partialCustomerInput = {
        ...mockCustomerEventInput,
        customerPhoneNumber: undefined,
        customerAddress: undefined,
      };

      const event = await caller.create(partialCustomerInput);

      expect(event).toBeDefined();
      expect(event?.type).toBe('customer_incident');
      expect((event as any)?.customerName).toBe(mockCustomerEventInput.customerName);
      expect((event as any)?.customerPhoneNumber).toBeNull();
      expect((event as any)?.customerAddress).toBeNull();
    });

    it('should create a customer incident with only customer name', async () => {
      const caller = eventRouter.createCaller(mockContext);
      const minimalCustomerInput = {
        ...mockCustomerEventInput,
        customerPhoneNumber: undefined,
        customerAddress: undefined,
      };

      const event = await caller.create(minimalCustomerInput);

      expect(event).toBeDefined();
      expect(event?.type).toBe('customer_incident');
      expect((event as any)?.customerName).toBe(mockCustomerEventInput.customerName);
      expect((event as any)?.customerPhoneNumber).toBeNull();
      expect((event as any)?.customerAddress).toBeNull();
    });

    it('should create different types of events correctly', async () => {
      const caller = eventRouter.createCaller(mockContext);

      const eventTypes = ['incident', 'near_miss', 'observation', 'customer_incident'] as const;

      for (const type of eventTypes) {
        const input = {
          ...mockEventInput,
          type,
          title: `Test ${type}`,
        };

        if (type === 'customer_incident') {
          (input as any).customerName = 'Test Customer';
          (input as any).customerPhoneNumber = '(*************';
          (input as any).customerAddress = 'Test Address';
        }

        const event = await caller.create(input);

        expect(event).toBeDefined();
        expect(event?.type).toBe(type);
        expect(event?.title).toBe(`Test ${type}`);

        if (type === 'customer_incident') {
          expect((event as any)?.customerName).toBe('Test Customer');
          expect((event as any)?.customerPhoneNumber).toBe('(*************');
          expect((event as any)?.customerAddress).toBe('Test Address');
        } else {
          expect((event as any)?.customerName).toBeNull();
          expect((event as any)?.customerPhoneNumber).toBeNull();
          expect((event as any)?.customerAddress).toBeNull();
        }
      }
    });

    it('should create an event with witnesses', async () => {
      const caller = eventRouter.createCaller(mockContext);
      const event = await caller.create(mockEventWithWitnessesInput);

      expect(event).toBeDefined();
      expect(event?.title).toBe(mockEventWithWitnessesInput.title);

      // Query the database directly to validate witnesses were stored
      const storedEvent = await caller.getById({ id: event?.id! });
      expect(storedEvent?.witnesses).toBeDefined();
      expect(storedEvent?.witnesses).toHaveLength(2);

      const witnesses = storedEvent?.witnesses as any[];
      expect(witnesses[0].fullName).toBe('John Doe');
      expect(witnesses[0].phoneNumber).toBe('(*************');
      expect(witnesses[0].email).toBe('<EMAIL>');
      expect(witnesses[0].notes).toBe('Saw the entire incident from start to finish');

      expect(witnesses[1].fullName).toBe('Jane Smith');
      expect(witnesses[1].phoneNumber).toBe('(*************');
      expect(witnesses[1].email).toBe('<EMAIL>');
      expect(witnesses[1].notes).toBe('Witnessed the aftermath and helped with immediate response');
    });

    it('should create an event with partial witness information', async () => {
      const caller = eventRouter.createCaller(mockContext);
      const partialWitnessInput = {
        ...mockEventInput,
        witnesses: [
          {
            fullName: 'Bob Wilson',
            // phoneNumber and email omitted
            notes: 'Brief witness observation',
          },
          {
            fullName: 'Alice Johnson',
            email: '<EMAIL>',
            // phoneNumber and notes omitted
          },
        ],
      };

      const event = await caller.create(partialWitnessInput);

      expect(event).toBeDefined();

      // Query the database directly to validate witnesses were stored
      const storedEvent = await caller.getById({ id: event?.id! });
      expect(storedEvent?.witnesses).toBeDefined();
      expect(storedEvent?.witnesses).toHaveLength(2);

      const witnesses = storedEvent?.witnesses as any[];
      expect(witnesses[0].fullName).toBe('Bob Wilson');
      expect(witnesses[0].phoneNumber).toBeUndefined();
      expect(witnesses[0].email).toBeUndefined();
      expect(witnesses[0].notes).toBe('Brief witness observation');

      expect(witnesses[1].fullName).toBe('Alice Johnson');
      expect(witnesses[1].phoneNumber).toBeUndefined();
      expect(witnesses[1].email).toBe('<EMAIL>');
      expect(witnesses[1].notes).toBeUndefined();
    });

    it('should create an event with empty witnesses array', async () => {
      const caller = eventRouter.createCaller(mockContext);
      const noWitnessesInput = {
        ...mockEventInput,
        witnesses: [],
      };

      const event = await caller.create(noWitnessesInput);

      expect(event).toBeDefined();

      // Query the database directly to validate witnesses were stored
      const storedEvent = await caller.getById({ id: event?.id! });
      expect(storedEvent?.witnesses).toBeDefined();
      expect(storedEvent?.witnesses).toHaveLength(0);
    });

    it('should create an event without witnesses field', async () => {
      const caller = eventRouter.createCaller(mockContext);
      const event = await caller.create(mockEventInput);

      expect(event).toBeDefined();

      // Query the database directly to validate witnesses were stored
      const storedEvent = await caller.getById({ id: event?.id! });
      expect(storedEvent?.witnesses).toBeDefined();
      expect(storedEvent?.witnesses).toHaveLength(0);
    });
  });

  describe('getById', () => {
    it('should get an event by id without files', async () => {
      const caller = eventRouter.createCaller(mockContext);
      const event = await caller.create(mockEventInput);
      const eventById = await caller.getById({ id: event?.id! });

      expect(eventById).toBeDefined();
      expect(eventById?.title).toBe(mockEventInput.title);
      expect(eventById?.description).toBe(mockEventInput.description);
      expect(eventById?.media).toBeNull();
    });

    it('should get a customer event by id with customer information', async () => {
      const caller = eventRouter.createCaller(mockContext);
      const event = await caller.create(mockCustomerEventInput);
      const eventById = await caller.getById({ id: event?.id! });

      expect(eventById).toBeDefined();
      expect(eventById?.title).toBe(mockCustomerEventInput.title);
      expect(eventById?.description).toBe(mockCustomerEventInput.description);
      expect(eventById?.type).toBe('customer_incident');
      expect((eventById as any)?.customerName).toBe(mockCustomerEventInput.customerName);
      expect((eventById as any)?.customerPhoneNumber).toBe(mockCustomerEventInput.customerPhoneNumber);
      expect((eventById as any)?.customerAddress).toBe(mockCustomerEventInput.customerAddress);
      expect(eventById?.media).toBeNull();
    });

    it('should get an event by id with files', async () => {
      const caller = eventRouter.createCaller(mockContext);
      const event = await caller.create(mockEventInput);

      await db.insert(files).values({
        id: 'test-file-id',
        upkeepCompanyId: mockUser.upkeepCompanyId,
        fileName: 'test.pdf',
        fileSize: 1024,
        mimeType: 'application/pdf',
        presignedUrl: 'https://test-bucket.s3.amazonaws.com/test.pdf',
        s3Key: 'test/test.pdf',
        s3Bucket: 'test-bucket',
        status: 'completed',
        entityType: 'event',
        entityId: event?.id!,
        uploadedBy: mockUser.id,
        expiresAt: new Date('2024-12-31'),
      });

      const eventById = await caller.getById({ id: event?.id! });

      expect(eventById).toBeDefined();
      expect(eventById?.media).toBeDefined();
      expect(eventById?.media?.length).toBe(1);
      expect(eventById?.media?.[0]?.name).toBe('test.pdf');
      expect(eventById?.media?.[0]?.url).toBe('https://test-bucket.s3.amazonaws.com/test.pdf');
      expect(eventById?.media?.[0]?.type).toBe('application/pdf');
      expect(eventById?.media?.[0]?.size).toBe(1024);
    });

    it('should get an event by id with witnesses', async () => {
      const caller = eventRouter.createCaller(mockContext);
      const event = await caller.create(mockEventWithWitnessesInput);
      const eventById = await caller.getById({ id: event?.id! });

      expect(eventById).toBeDefined();
      expect(eventById?.title).toBe(mockEventWithWitnessesInput.title);
      expect((eventById as any)?.witnesses).toBeDefined();
      expect((eventById as any)?.witnesses).toHaveLength(2);

      const witnesses = (eventById as any)?.witnesses as any[];
      expect(witnesses[0].fullName).toBe('John Doe');
      expect(witnesses[0].phoneNumber).toBe('(*************');
      expect(witnesses[0].email).toBe('<EMAIL>');
      expect(witnesses[0].notes).toBe('Saw the entire incident from start to finish');

      expect(witnesses[1].fullName).toBe('Jane Smith');
      expect(witnesses[1].phoneNumber).toBe('(*************');
      expect(witnesses[1].email).toBe('<EMAIL>');
      expect(witnesses[1].notes).toBe('Witnessed the aftermath and helped with immediate response');
    });
  });

  describe('list', () => {
    it('should list events', async () => {
      const caller = eventRouter.createCaller(mockContext);
      await caller.create(mockEventInput);
      const events = await caller.list({});

      expect(events).toBeDefined();
      expect(events.result.length).toBe(1);
      expect(events.result[0].title).toBe(mockEventInput.title);
      expect(events.result[0].slug).toBeDefined();
    });

    it('should list customer incidents with customer information', async () => {
      const caller = eventRouter.createCaller(mockContext);
      await caller.create(mockCustomerEventInput);
      const events = await caller.list({});

      expect(events).toBeDefined();
      expect(events.result.length).toBe(1);
      expect(events.result[0].title).toBe(mockCustomerEventInput.title);
      expect(events.result[0].type).toBe('customer_incident');
      expect((events.result[0] as any)?.customerName).toBe(mockCustomerEventInput.customerName);
      expect((events.result[0] as any)?.customerPhoneNumber).toBe(mockCustomerEventInput.customerPhoneNumber);
      expect((events.result[0] as any)?.customerAddress).toBe(mockCustomerEventInput.customerAddress);
      expect(events.result[0].slug).toBeDefined();
    });

    it('should filter events by type', async () => {
      const caller = eventRouter.createCaller(mockContext);

      // Create different types of events
      await caller.create(mockEventInput);
      await caller.create(mockCustomerEventInput);

      // Filter by customer event type
      const customerIncidents = await caller.list({ type: ['customer_incident'] });
      expect(customerIncidents.result.length).toBe(1);
      expect(customerIncidents.result[0].type).toBe('customer_incident');

      // Filter by regular event type
      const regularEvents = await caller.list({ type: ['incident'] });
      expect(regularEvents.result.length).toBe(1);
      expect(regularEvents.result[0].type).toBe('incident');
    });

    it('should prioritize mustIncludeObjectIds in results', async () => {
      const caller = eventRouter.createCaller(mockContext);

      // Create 3 events with different dates
      const event1 = await caller.create({
        ...mockEventInput,
        title: 'Recent',
        reportedAt: new Date('2024-03-15'),
      });
      const event2 = await caller.create({
        ...mockEventInput,
        title: 'Must Include',
        reportedAt: new Date('2024-02-15'),
      });
      const event3 = await caller.create({ ...mockEventInput, title: 'Old', reportedAt: new Date('2024-01-15') });

      // List with mustIncludeObjectIds, limit 2, and sorted by reportedAt desc
      const events = await caller.list({
        limit: 2,
        mustIncludeObjectIds: [event2?.id!],
        sortBy: 'reportedAt',
        sortOrder: 'desc',
      });

      expect(events.result.length).toBe(2);
      // Must include event should be first, despite being older
      expect(events.result[0].id).toBe(event2?.id);
      expect(events.result[0].title).toBe('Must Include');
      // Most recent event should be second
      expect(events.result[1].id).toBe(event1?.id);
      expect(events.result[1].title).toBe('Recent');
      // Oldest event should be excluded due to limit
      expect(events.result.find((i: any) => i.id === event3?.id)).toBeUndefined();
    });
  });

  describe('update', () => {
    it('should update an event', async () => {
      const caller = eventRouter.createCaller(mockContext);
      const event = await caller.create(mockEventInput);
      const updatedEvent = await caller.update({ id: event?.id!, title: 'Updated title' });

      expect(updatedEvent).toBeDefined();
      expect(updatedEvent?.title).toBe('Updated title');
    });

    it('should update a customer incident with new customer information', async () => {
      const caller = eventRouter.createCaller(mockContext);
      const event = await caller.create(mockCustomerEventInput);

      const updatedEvent = await caller.update({
        id: event?.id!,
        customerName: 'Maria Santos',
        customerPhoneNumber: '(*************',
        customerAddress: 'Av. Paulista, 1000 - São Paulo, SP',
      });

      expect(updatedEvent).toBeDefined();
      expect((updatedEvent as any)?.customerName).toBe('Maria Santos');
      expect((updatedEvent as any)?.customerPhoneNumber).toBe('(*************');
      expect((updatedEvent as any)?.customerAddress).toBe('Av. Paulista, 1000 - São Paulo, SP');
    });

    it('should update a regular event to customer incident', async () => {
      const caller = eventRouter.createCaller(mockContext);
      const event = await caller.create(mockEventInput);

      const updatedEvent = await caller.update({
        id: event?.id!,
        type: 'customer_incident',
        customerName: 'João Silva',
        customerPhoneNumber: '(*************',
        customerAddress: 'Rua das Flores, 123 - São Paulo, SP',
      });

      expect(updatedEvent).toBeDefined();
      expect(updatedEvent?.type).toBe('customer_incident');
      expect((updatedEvent as any)?.customerName).toBe('João Silva');
      expect((updatedEvent as any)?.customerPhoneNumber).toBe('(*************');
      expect((updatedEvent as any)?.customerAddress).toBe('Rua das Flores, 123 - São Paulo, SP');
    });

    it('should update a customer incident to regular event', async () => {
      const caller = eventRouter.createCaller(mockContext);
      const event = await caller.create(mockCustomerEventInput);

      const updatedEvent = await caller.update({
        id: event?.id!,
        type: 'incident',
        customerName: null,
        customerPhoneNumber: null,
        customerAddress: null,
      });

      expect(updatedEvent).toBeDefined();
      expect(updatedEvent?.type).toBe('incident');
      expect((updatedEvent as any)?.customerName).toBeNull();
      expect((updatedEvent as any)?.customerPhoneNumber).toBeNull();
      expect((updatedEvent as any)?.customerAddress).toBeNull();
    });

    it('should update an event to add witnesses', async () => {
      const caller = eventRouter.createCaller(mockContext);
      const event = await caller.create(mockEventInput);

      const updatedEvent = await caller.update({
        id: event?.id!,
        witnesses: [
          {
            fullName: 'New Witness One',
            phoneNumber: '(*************',
            email: '<EMAIL>',
            notes: 'Added during update',
          },
          {
            fullName: 'New Witness Two',
            email: '<EMAIL>',
            notes: 'Second witness added',
          },
        ],
      });

      expect(updatedEvent).toBeDefined();

      // Query the database directly to validate witnesses were updated
      const storedEvent = await caller.getById({ id: event?.id! });
      expect(storedEvent?.witnesses).toBeDefined();
      expect(storedEvent?.witnesses).toHaveLength(2);

      const witnesses = storedEvent?.witnesses as any[];
      expect(witnesses[0].fullName).toBe('New Witness One');
      expect(witnesses[0].phoneNumber).toBe('(*************');
      expect(witnesses[0].email).toBe('<EMAIL>');
      expect(witnesses[0].notes).toBe('Added during update');

      expect(witnesses[1].fullName).toBe('New Witness Two');
      expect(witnesses[1].phoneNumber).toBeUndefined();
      expect(witnesses[1].email).toBe('<EMAIL>');
      expect(witnesses[1].notes).toBe('Second witness added');
    });

    it('should update an event to modify existing witnesses', async () => {
      const caller = eventRouter.createCaller(mockContext);
      const event = await caller.create(mockEventWithWitnessesInput);

      const updatedEvent = await caller.update({
        id: event?.id!,
        witnesses: [
          {
            fullName: 'Updated John Doe',
            phoneNumber: '(*************',
            email: '<EMAIL>',
            notes: 'Updated witness information',
          },
        ],
      });

      expect(updatedEvent).toBeDefined();

      // Query the database directly to validate witnesses were updated
      const storedEvent = await caller.getById({ id: event?.id! });
      expect(storedEvent?.witnesses).toBeDefined();
      expect(storedEvent?.witnesses).toHaveLength(1);

      const witnesses = storedEvent?.witnesses as any[];
      expect(witnesses[0].fullName).toBe('Updated John Doe');
      expect(witnesses[0].phoneNumber).toBe('(*************');
      expect(witnesses[0].email).toBe('<EMAIL>');
      expect(witnesses[0].notes).toBe('Updated witness information');
    });

    it('should update an event to remove all witnesses', async () => {
      const caller = eventRouter.createCaller(mockContext);
      const event = await caller.create(mockEventWithWitnessesInput);

      // Verify event initially has witnesses by querying the database
      const initialEvent = await caller.getById({ id: event?.id! });
      expect(initialEvent?.witnesses).toHaveLength(2);

      const updatedEvent = await caller.update({
        id: event?.id!,
        witnesses: [],
      });

      expect(updatedEvent).toBeDefined();

      // Query the database directly to validate witnesses were removed
      const storedEvent = await caller.getById({ id: event?.id! });
      expect(storedEvent?.witnesses).toBeDefined();
      expect(storedEvent?.witnesses).toHaveLength(0);
    });

    it('should update an event to add more witnesses to existing ones', async () => {
      const caller = eventRouter.createCaller(mockContext);
      const event = await caller.create({
        ...mockEventInput,
        witnesses: [
          {
            fullName: 'Original Witness',
            phoneNumber: '(*************',
            email: '<EMAIL>',
            notes: 'Original witness',
          },
        ],
      });

      const updatedEvent = await caller.update({
        id: event?.id!,
        witnesses: [
          {
            fullName: 'Original Witness',
            phoneNumber: '(*************',
            email: '<EMAIL>',
            notes: 'Original witness',
          },
          {
            fullName: 'Additional Witness',
            phoneNumber: '(*************',
            email: '<EMAIL>',
            notes: 'Added in update',
          },
          {
            fullName: 'Third Witness',
            email: '<EMAIL>',
            notes: 'Third witness without phone',
          },
        ],
      });

      expect(updatedEvent).toBeDefined();

      // Query the database directly to validate witnesses were updated
      const storedEvent = await caller.getById({ id: event?.id! });
      expect(storedEvent?.witnesses).toBeDefined();
      expect(storedEvent?.witnesses).toHaveLength(3);

      const witnesses = storedEvent?.witnesses as any[];
      expect(witnesses[0].fullName).toBe('Original Witness');
      expect(witnesses[1].fullName).toBe('Additional Witness');
      expect(witnesses[2].fullName).toBe('Third Witness');
      expect(witnesses[2].phoneNumber).toBeUndefined();
    });
  });

  describe('permissions - technician user can only see own events', () => {
    it('should allow technician to get event they created by id', async () => {
      // Create context with needPartialCheck to simulate partial permissions
      const mockTechnicianContext = {
        ...createMockContext(mockTechnicianUser),
        needPartialCheck: true,
      };
      const technicianCaller = eventRouter.createCaller(mockTechnicianContext);

      // Technician creates an event
      const technicianEvent = await technicianCaller.create({
        ...mockEventInput,
        title: 'Technician Event',
      });

      // Technician should be able to get their own event
      const retrievedEvent = await technicianCaller.getById({ id: technicianEvent?.id! });

      expect(retrievedEvent).toBeDefined();
      expect(retrievedEvent?.title).toBe('Technician Event');
      expect(retrievedEvent?.reportedBy).toBe(mockTechnicianUser.id);
    });

    it('should prevent technician from getting event created by admin', async () => {
      const adminCaller = eventRouter.createCaller(mockContext);
      const mockTechnicianContext = {
        ...createMockContext(mockTechnicianUser),
        needPartialCheck: true,
      };
      const technicianCaller = eventRouter.createCaller(mockTechnicianContext);

      // Admin creates an event
      const adminEvent = await adminCaller.create({
        ...mockEventInput,
        title: 'Admin Event',
      });

      // Technician should not be able to get admin's event - should throw NOT_FOUND error
      await expect(technicianCaller.getById({ id: adminEvent?.id! })).rejects.toThrow('Safety event with ID');
    });

    it('should allow technician to list only events they created', async () => {
      const adminCaller = eventRouter.createCaller(mockContext);
      const mockTechnicianContext = {
        ...createMockContext(mockTechnicianUser),
        needPartialCheck: true,
      };
      const technicianCaller = eventRouter.createCaller(mockTechnicianContext);

      // Admin creates an event
      await adminCaller.create({
        ...mockEventInput,
        title: 'Admin Event 1',
      });

      await adminCaller.create({
        ...mockEventInput,
        title: 'Admin Event 2',
      });

      // Technician creates events
      await technicianCaller.create({
        ...mockEventInput,
        title: 'Technician Event 1',
      });

      await technicianCaller.create({
        ...mockEventInput,
        title: 'Technician Event 2',
      });

      // Technician should only see their own events
      const technicianEvents = await technicianCaller.list({});

      expect(technicianEvents.result.length).toBe(2);
      expect(technicianEvents.result.every((event: any) => event.reportedBy === mockTechnicianUser.id)).toBe(true);

      const titles = technicianEvents.result.map((event: any) => event.title).sort();
      expect(titles).toEqual(['Technician Event 1', 'Technician Event 2']);

      // Admin should see all events (without needPartialCheck)
      const adminEvents = await adminCaller.list({});
      expect(adminEvents.result.length).toBe(4);
    });

    it('should allow technician to see events where they are team members', async () => {
      const adminCaller = eventRouter.createCaller(mockContext);
      const mockTechnicianContext = {
        ...createMockContext(mockTechnicianUser),
        needPartialCheck: true,
      };
      const technicianCaller = eventRouter.createCaller(mockTechnicianContext);

      // Admin creates an event and adds technician as team member
      const adminEvent = await adminCaller.create({
        ...mockEventInput,
        title: 'Admin Event with Technician',
        teamMembersToNotify: [mockTechnicianUser.id],
      });

      // Technician should be able to see this event because they're a team member
      const retrievedEvent = await technicianCaller.getById({ id: adminEvent?.id! });
      expect(retrievedEvent).toBeDefined();
      expect(retrievedEvent?.title).toBe('Admin Event with Technician');

      // Technician should see this event in list too
      const technicianEvents = await technicianCaller.list({});
      expect(technicianEvents.result.length).toBe(1);
      expect(technicianEvents.result[0].title).toBe('Admin Event with Technician');
    });

    it('should prevent technician from updating events they did not create', async () => {
      const adminCaller = eventRouter.createCaller(mockContext);
      const mockTechnicianContext = {
        ...createMockContext(mockTechnicianUser),
        needPartialCheck: true,
      };
      const technicianCaller = eventRouter.createCaller(mockTechnicianContext);

      // Admin creates an event
      const adminEvent = await adminCaller.create({
        ...mockEventInput,
        title: 'Admin Event',
      });

      // Technician should not be able to update admin's event
      await expect(
        technicianCaller.update({
          id: adminEvent?.id!,
          title: 'Modified by Technician',
        }),
      ).rejects.toThrow();
    });

    it('should allow technician to update their own event without providing status', async () => {
      const mockTechnicianContext = {
        ...createMockContext(mockTechnicianUser),
        needPartialCheck: true,
      };
      const technicianCaller = eventRouter.createCaller(mockTechnicianContext);

      // Technician creates an event
      const technicianEvent = await technicianCaller.create({
        ...mockEventInput,
        title: 'Technician Event',
        description: 'Original Description',
      });

      // Technician should be able to update their own event without providing status
      const updatedEvent = await technicianCaller.update({
        id: technicianEvent?.id!,
        title: 'Updated Title',
        description: 'Updated Description',
        // Not providing status field - should be allowed
      });

      expect(updatedEvent).toBeDefined();
      expect(updatedEvent?.title).toBe('Updated Title');
      expect(updatedEvent?.description).toBe('Updated Description');
      expect(updatedEvent?.status).toBe('open'); // Should remain unchanged
    });

    it('should prevent technician from updating event when status is provided in request', async () => {
      const mockTechnicianContext = {
        ...createMockContext(mockTechnicianUser),
        needPartialCheck: true,
      };
      const technicianCaller = eventRouter.createCaller(mockTechnicianContext);

      // Technician creates an event
      const technicianEvent = await technicianCaller.create({
        ...mockEventInput,
        title: 'Technician Event for Status Test',
      });

      // Technician should not be able to update when providing any status (even same status)
      await expect(
        technicianCaller.update({
          id: technicianEvent?.id!,
          title: 'Updated Title',
          status: 'open', // Even providing the same status should be blocked
        }),
      ).rejects.toThrow('You are not allowed to change event status');

      // Also test with different status
      await expect(
        technicianCaller.update({
          id: technicianEvent?.id!,
          title: 'Updated Title',
          status: 'closed',
        }),
      ).rejects.toThrow('You are not allowed to change event status');
    });

    it('should allow admin to update event with status', async () => {
      const adminCaller = eventRouter.createCaller(mockContext);

      // Admin creates an event
      const adminEvent = await adminCaller.create({
        ...mockEventInput,
        title: 'Admin Event',
      });

      // Admin should be able to update with status
      const updatedEvent = await adminCaller.update({
        id: adminEvent?.id!,
        title: 'Updated by Admin',
        status: 'closed',
      });

      expect(updatedEvent).toBeDefined();
      expect(updatedEvent?.title).toBe('Updated by Admin');
      expect(updatedEvent?.status).toBe('closed');
    });
  });

  describe('toggleArchive', () => {
    it('should archive an active event', async () => {
      const caller = eventRouter.createCaller(mockContext);
      const event = await caller.create(mockEventInput);

      // Verify it's initially not archived
      const initialEvent = await caller.getById({ id: event?.id! });
      expect(initialEvent?.archivedAt).toBeNull();

      // Archive the event
      const archivedResult = await caller.toggleArchive({ id: event?.id! });

      expect(archivedResult).toBeDefined();
      expect(archivedResult?.id).toBe(event?.id);
      expect(archivedResult?.archivedAt).toBeTruthy();
    });

    it('should unarchive an archived event', async () => {
      const caller = eventRouter.createCaller(mockContext);
      const event = await caller.create(mockEventInput);

      // First archive the event
      const archivedResult = await caller.toggleArchive({ id: event?.id! });
      expect(archivedResult?.archivedAt).toBeTruthy();

      // Then unarchive it
      const unarchivedResult = await caller.toggleArchive({ id: event?.id! });

      expect(unarchivedResult).toBeDefined();
      expect(unarchivedResult?.id).toBe(event?.id);
      expect(unarchivedResult?.archivedAt).toBeNull();
    });

    it('should preserve event data when toggling archive status', async () => {
      const caller = eventRouter.createCaller(mockContext);
      const event = await caller.create(mockEventInput);

      // Archive the event
      const archivedResult = await caller.toggleArchive({ id: event?.id! });
      expect(archivedResult?.archivedAt).toBeTruthy();

      // Verify other fields remain unchanged by getting the full event
      const archivedEvent = await caller.getById({ id: event?.id! });
      expect(archivedEvent?.title).toBe(mockEventInput.title);
      expect(archivedEvent?.description).toBe(mockEventInput.description);
      expect(archivedEvent?.type).toBe(mockEventInput.type);
      expect(archivedEvent?.category).toBe(mockEventInput.category);
      expect(archivedEvent?.severity).toBe(mockEventInput.severity);

      // Unarchive and verify data is still preserved
      const unarchivedResult = await caller.toggleArchive({ id: event?.id! });
      expect(unarchivedResult?.archivedAt).toBeNull();

      const unarchivedEvent = await caller.getById({ id: event?.id! });
      expect(unarchivedEvent?.title).toBe(mockEventInput.title);
      expect(unarchivedEvent?.description).toBe(mockEventInput.description);
      expect(unarchivedEvent?.type).toBe(mockEventInput.type);
      expect(unarchivedEvent?.category).toBe(mockEventInput.category);
      expect(unarchivedEvent?.severity).toBe(mockEventInput.severity);
    });

    it('should handle toggle archive for event with files', async () => {
      const caller = eventRouter.createCaller(mockContext);
      const event = await caller.create(mockEventInput);

      // Add a file to the event
      await db.insert(files).values({
        id: 'test-file-archive',
        upkeepCompanyId: mockUser.upkeepCompanyId,
        fileName: 'archive-test.pdf',
        fileSize: 2048,
        mimeType: 'application/pdf',
        presignedUrl: 'https://test-bucket.s3.amazonaws.com/archive-test.pdf',
        s3Key: 'test/archive-test.pdf',
        s3Bucket: 'test-bucket',
        status: 'completed',
        entityType: 'event',
        entityId: event?.id!,
        uploadedBy: mockUser.id,
        expiresAt: new Date('2024-12-31'),
      });

      // Archive the event
      const archivedResult = await caller.toggleArchive({ id: event?.id! });
      expect(archivedResult?.archivedAt).toBeTruthy();

      // Verify the event can still be retrieved with its files
      const eventWithFiles = await caller.getById({ id: event?.id! });
      expect(eventWithFiles?.media).toBeDefined();
      expect(eventWithFiles?.media?.length).toBe(1);
      expect(eventWithFiles?.archivedAt).toBeTruthy();

      // Unarchive and verify files are still accessible
      const unarchivedResult = await caller.toggleArchive({ id: event?.id! });
      expect(unarchivedResult?.archivedAt).toBeNull();

      const finalEvent = await caller.getById({ id: event?.id! });
      expect(finalEvent?.media?.length).toBe(1);
      expect(finalEvent?.archivedAt).toBeNull();
    });

    it('should handle non-existent event', async () => {
      const caller = eventRouter.createCaller(mockContext);

      // Try to toggle archive on a non-existent event
      await expect(caller.toggleArchive({ id: 'non-existent-id' })).rejects.toThrow();
    });

    it('should validate required id parameter', async () => {
      const caller = eventRouter.createCaller(mockContext);

      // Test missing id field
      await expect(caller.toggleArchive({} as any)).rejects.toThrow();
    });

    it('should affect list results based on includeArchived filter', async () => {
      const caller = eventRouter.createCaller(mockContext);

      // Create two events
      const event1 = await caller.create({ ...mockEventInput, title: 'Active Event' });
      const event2 = await caller.create({ ...mockEventInput, title: 'To Be Archived' });

      // Archive one event
      await caller.toggleArchive({ id: event2?.id! });

      // List without includeArchived should only show active event
      const activeList = await caller.list({ includeArchived: false });
      expect(activeList.result.length).toBe(1);
      expect(activeList.result[0].title).toBe('Active Event');

      // List with includeArchived should show both events
      const allList = await caller.list({ includeArchived: true });
      expect(allList.result.length).toBe(2);

      const titles = allList.result.map((i: any) => i.title).sort();
      expect(titles).toEqual(['Active Event', 'To Be Archived']);
    });
  });

  describe('listComments', () => {
    it('should list comments for an event', async () => {
      const caller = eventRouter.createCaller(mockContext);
      const event = await caller.create(mockEventInput);

      // Create a comment directly in the database for testing
      const commentId = createId();
      await db
        .insert(comments)
        .values({
          id: commentId,
          entityType: 'event',
          entityId: event!.id,
          content: 'Test comment for list',
          userId: mockUser.id,
          upkeepCompanyId: mockUser.upkeepCompanyId,
        })
        .returning();

      const commentsList = await caller.listComments({
        entityId: event!.id,
        options: { limit: 50, offset: 0 },
      });

      expect(commentsList).toBeDefined();
      expect(commentsList.length).toBe(1);
      expect(commentsList[0].content).toBe('Test comment for list');
    });
  });

  describe('getCommentById', () => {
    it('should get a comment by id', async () => {
      const caller = eventRouter.createCaller(mockContext);
      const event = await caller.create(mockEventInput);

      // Create a comment directly in the database for testing
      const commentId = createId();
      await db
        .insert(comments)
        .values({
          id: commentId,
          entityType: 'event',
          entityId: event!.id,
          content: 'Test comment for get',
          userId: mockUser.id,
          upkeepCompanyId: mockUser.upkeepCompanyId,
        })
        .returning();

      const commentById = await caller.getCommentById({ id: commentId });

      expect(commentById).toBeDefined();
      expect(commentById?.id).toBe(commentId);
      expect(commentById?.content).toBe('Test comment for get');
    });
  });

  describe('deleteComment', () => {
    it('should delete a comment', async () => {
      const caller = eventRouter.createCaller(mockContext);
      const event = await caller.create(mockEventInput);

      // Create a comment directly in the database for testing
      const commentId = createId();
      await db
        .insert(comments)
        .values({
          id: commentId,
          entityType: 'event',
          entityId: event!.id,
          content: 'Test comment for delete',
          userId: mockUser.id,
          upkeepCompanyId: mockUser.upkeepCompanyId,
        })
        .returning();

      await caller.deleteComment({ id: commentId });
      const commentById = await caller.getCommentById({ id: commentId });

      expect(commentById).toBeNull();
    });
  });

  describe('createComment', () => {
    it('should create a new comment on an event', async () => {
      const caller = eventRouter.createCaller(mockContext);
      const event = await caller.create(mockEventInput);

      const mockCommentInput = {
        content: 'Test comment for event',
        entityId: event!.id,
        entitySlug: event!.slug,
        entityTitle: event!.title,
        status: 'open' as const,
        entityType: entityTypeEnum.enumValues[0],
      };

      const comment = await caller.createComment(mockCommentInput);

      expect(comment).toBeDefined();
      expect(comment.content).toBe(mockCommentInput.content);
      expect(comment.id).toMatch(/^[a-z0-9]+$/); // CUID2 format
    });
  });
});
