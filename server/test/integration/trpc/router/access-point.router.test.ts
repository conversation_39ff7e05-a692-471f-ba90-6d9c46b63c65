import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { accessPointRouter } from '@server/trpc/router/access-point.router';
import { createMockContext } from '@server/trpc/router/__tests__/test-utils';
import { db } from '@server/db';
import { accessPoints } from '@shared/schema';
import { mockTechnicianUser, mockUser, mockViewOnlyUser } from '@server/test/fixtures/user';
import { getUserById, getUserPublic, getUsers, getUsersPublic, hasPermission } from '@server/services/user.service';
import { getLocations, getAllLocations, searchLocationsPublic } from '@server/services/location.service';
import { mockLocations } from '@server/test/fixtures/location';
import { getAssets } from '@server/services/asset.service';
import { matchLocationsWithAI } from '@server/services/ai.service';

// Mock assets data
const mockAssets = [
  { id: 'asset-1', name: 'Air Handler Unit 1', description: 'HVAC system for building A' },
  { id: 'asset-2', name: 'Pump Station Alpha', description: 'Water pump system' },
  { id: 'asset-3', name: 'Electrical Panel B', description: 'Main electrical distribution' },
];

// Mock the user service
vi.mock('@server/services/user.service', () => ({
  getUserById: vi.fn(),
  getUsers: vi.fn(),
  getUsersPublic: vi.fn(),
  getUserPublic: vi.fn(),
  hasPermission: vi.fn().mockReturnValue(true),
  userHasEventPermission: vi.fn().mockReturnValue(true),
  canEditEvent: vi.fn().mockReturnValue(true),
  canExportEvent: vi.fn().mockReturnValue(true),
}));

vi.mock('@server/services/location.service', () => ({
  getLocations: vi.fn(),
  getAllLocations: vi.fn(),
  searchLocationsPublic: vi.fn(),
}));

vi.mock('@server/services/asset.service', () => ({
  getAssets: vi.fn(),
}));

vi.mock('@server/services/ai.service', () => ({
  matchLocationsWithAI: vi.fn(),
}));

describe('accessPointRouter', () => {
  const mockContext = createMockContext(mockUser);

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(getUserById).mockResolvedValue(mockUser);
    vi.mocked(getUsers).mockResolvedValue([mockUser]);
    vi.mocked(getUsersPublic).mockResolvedValue({
      noResults: false,
      result: [mockUser],
      nextCursor: undefined,
    });
    vi.mocked(getUserPublic).mockResolvedValue(mockUser);
    vi.mocked(getLocations).mockResolvedValue({
      noResults: false,
      result: mockLocations,
      nextCursor: undefined,
    });
    vi.mocked(getAllLocations).mockResolvedValue(mockLocations);
    vi.mocked(searchLocationsPublic).mockResolvedValue({
      noResults: false,
      nextCursor: undefined,
      result: mockLocations,
    });
    vi.mocked(getAssets).mockResolvedValue({
      noResults: false,
      result: mockAssets,
      nextCursor: undefined,
    });
    vi.mocked(hasPermission).mockReturnValue(true);
  });

  afterEach(async () => {
    await db.delete(accessPoints);
  });

  describe('create', () => {
    it('should not allow view only users to create access points', async () => {
      const mockViewOnlyContext = createMockContext(mockViewOnlyUser);
      const caller = accessPointRouter.createCaller(mockViewOnlyContext);

      await expect(caller.create({} as any)).rejects.toThrow();
    });

    it('should not allow technicians to create access points', async () => {
      const mockTechnicianContext = createMockContext(mockTechnicianUser);
      const caller = accessPointRouter.createCaller(mockTechnicianContext);

      await expect(caller.create({} as any)).rejects.toThrow();
    });

    it('should create a new access point', async () => {
      const mockAccessPointInput = {
        name: `Test Access Point ${Math.random().toString(36).substring(2, 5)}`,
        locationId: '123',
        type: '123',
        status: '123',
      };

      const caller = accessPointRouter.createCaller(mockContext);
      const accessPoint = await caller.create(mockAccessPointInput);

      // Verify the access point was created
      expect(accessPoint).toBeDefined();
      expect(accessPoint.name).toBe(mockAccessPointInput.name);
    });

    it('should validate required fields', async () => {
      const caller = accessPointRouter.createCaller(mockContext);

      // Test missing required fields
      await expect(caller.create({} as any)).rejects.toThrow();
    });

    it('should create an access point with asset for equipment-specific incidents', async () => {
      const mockAccessPointInput = {
        name: `Equipment Access Point ${Math.random().toString(36).substring(2, 5)}`,
        locationId: '123',
        assetId: 'asset-1',
      };

      const caller = accessPointRouter.createCaller(mockContext);
      const accessPoint = await caller.create(mockAccessPointInput);

      // Verify the access point was created with asset
      expect(accessPoint).toBeDefined();
      expect(accessPoint.name).toBe(mockAccessPointInput.name);
      expect(accessPoint.locationId).toBe(mockAccessPointInput.locationId);
      expect(accessPoint.assetId).toBe(mockAccessPointInput.assetId);
    });

    it('should create an access point without asset for location-only access', async () => {
      const mockAccessPointInput = {
        name: `Location Access Point ${Math.random().toString(36).substring(2, 5)}`,
        locationId: '123',
        // assetId is optional and not provided
      };

      const caller = accessPointRouter.createCaller(mockContext);
      const accessPoint = await caller.create(mockAccessPointInput);

      // Verify the access point was created without asset
      expect(accessPoint).toBeDefined();
      expect(accessPoint.name).toBe(mockAccessPointInput.name);
      expect(accessPoint.locationId).toBe(mockAccessPointInput.locationId);
      expect(accessPoint.assetId).toBeNull();
    });
  });

  describe('update', () => {
    it('should update an access point', async () => {
      const mockAccessPointInput = {
        name: `Test Access Point ${Math.random().toString(36).substring(2, 5)}`,
        locationId: '123',
        type: '123',
      };

      const caller = accessPointRouter.createCaller(mockContext);
      const accessPoint = await caller.create(mockAccessPointInput);

      const updatedAccessPointInput = {
        id: accessPoint.id,
        name: 'Updated Access Point',
        description: 'Updated description',
        locationId: '456',
        type: '456',
      };

      const updatedAccessPoint = await caller.update(updatedAccessPointInput);

      expect(updatedAccessPoint).toBeDefined();
      expect(updatedAccessPoint.id).toBe(accessPoint.id);
      expect(updatedAccessPoint.name).toBe(updatedAccessPointInput.name);
      expect(updatedAccessPoint.description).toBe(updatedAccessPointInput.description);
      expect(updatedAccessPoint.locationId).toBe(updatedAccessPointInput.locationId);
    });

    it('should update only the name without affecting other fields', async () => {
      const mockAccessPointInput = {
        name: `Original Name ${Math.random().toString(36).substring(2, 5)}`,
        locationId: '123',
        assetId: 'asset-1',
      };

      const caller = accessPointRouter.createCaller(mockContext);
      const accessPoint = await caller.create(mockAccessPointInput);

      // Update only the name
      const updatedAccessPointInput = {
        id: accessPoint.id,
        name: 'Updated Name Only',
      };

      const updatedAccessPoint = await caller.update(updatedAccessPointInput);

      // Verify only name was updated
      expect(updatedAccessPoint).toBeDefined();
      expect(updatedAccessPoint.id).toBe(accessPoint.id);
      expect(updatedAccessPoint.name).toBe(updatedAccessPointInput.name);
      expect(updatedAccessPoint.locationId).toBe(accessPoint.locationId);
      expect(updatedAccessPoint.assetId).toBe(accessPoint.assetId);
      expect(updatedAccessPoint.description).toBe(accessPoint.description);
    });

    it('should update asset without affecting other fields', async () => {
      const mockAccessPointInput = {
        name: `Asset Test ${Math.random().toString(36).substring(2, 5)}`,
        locationId: '123',
        assetId: 'asset-1',
      };

      const caller = accessPointRouter.createCaller(mockContext);
      const accessPoint = await caller.create(mockAccessPointInput);

      // Update only the asset
      const updatedAccessPointInput = {
        id: accessPoint.id,
        assetId: 'asset-2',
      };

      const updatedAccessPoint = await caller.update(updatedAccessPointInput);

      // Verify only asset was updated
      expect(updatedAccessPoint).toBeDefined();
      expect(updatedAccessPoint.id).toBe(accessPoint.id);
      expect(updatedAccessPoint.name).toBe(accessPoint.name);
      expect(updatedAccessPoint.locationId).toBe(accessPoint.locationId);
      expect(updatedAccessPoint.assetId).toBe(updatedAccessPointInput.assetId);
    });
  });

  describe('list', () => {
    it('should list access points', async () => {
      const mockAccessPointInput = {
        name: `Test Access Point ${Math.random().toString(36).substring(2, 5)}`,
        locationId: 'abc123',
        type: '123',
      };

      const caller = accessPointRouter.createCaller(mockContext);
      await caller.create(mockAccessPointInput);
      const accessPoints = await caller.list({});

      expect(accessPoints).toBeDefined();

      expect(accessPoints.result.length).toBe(1);
    });

    it('should populate asset information when listing access points with assets', async () => {
      const mockAccessPointInput = {
        name: `Asset Access Point ${Math.random().toString(36).substring(2, 5)}`,
        locationId: 'abc123',
        assetId: 'asset-1',
      };

      const caller = accessPointRouter.createCaller(mockContext);
      await caller.create(mockAccessPointInput);
      const accessPoints = await caller.list({});

      expect(accessPoints).toBeDefined();
      expect(accessPoints.result.length).toBe(1);

      const accessPoint = accessPoints.result[0];
      expect(accessPoint.asset).toBeDefined();
      expect(accessPoint.asset?.id).toBe('asset-1');
      expect(accessPoint.asset?.name).toBe('Air Handler Unit 1');
    });

    it('should handle access points without assets correctly', async () => {
      const mockAccessPointInput = {
        name: `No Asset Access Point ${Math.random().toString(36).substring(2, 5)}`,
        locationId: 'abc123',
        // No assetId
      };

      const caller = accessPointRouter.createCaller(mockContext);
      await caller.create(mockAccessPointInput);
      const accessPoints = await caller.list({});

      expect(accessPoints).toBeDefined();
      expect(accessPoints.result.length).toBe(1);

      const accessPoint = accessPoints.result[0];
      expect(accessPoint.asset).toBeUndefined();
      expect(accessPoint.assetId).toBeNull();
    });
  });

  describe('getById', () => {
    it('should get an access point by id', async () => {
      const mockAccessPointInput = {
        name: `Test Access Point ${Math.random().toString(36).substring(2, 5)}`,
        locationId: '123',
        type: '123',
      };

      const caller = accessPointRouter.createCaller(mockContext);
      const accessPoint = await caller.create(mockAccessPointInput);

      const accessPointById = await caller.getById({ id: accessPoint.id });

      expect(accessPointById).toBeDefined();

      expect(accessPointById.id).toBe(accessPoint.id);
      expect(accessPointById.name).toBe(mockAccessPointInput.name);
    });
  });

  describe('getByIdPublic', () => {
    it('should get an access point by id and upkeepCompanyId for public access', async () => {
      const mockAccessPointInput = {
        name: `Public Access Point ${Math.random().toString(36).substring(2, 5)}`,
        locationId: '123',
        assetId: 'asset-1',
      };

      const caller = accessPointRouter.createCaller(mockContext);
      const accessPoint = await caller.create(mockAccessPointInput);

      // Create a public context without user authentication
      const publicContext = {
        ...mockContext,
        user: null,
      };

      const publicCaller = accessPointRouter.createCaller(publicContext);
      const publicAccessPoint = await publicCaller.getByIdPublic({
        id: accessPoint.id,
        upkeepCompanyId: accessPoint.upkeepCompanyId,
      });

      expect(publicAccessPoint).toBeDefined();
      expect(publicAccessPoint.id).toBe(accessPoint.id);
      expect(publicAccessPoint.name).toBe(mockAccessPointInput.name);
      expect(publicAccessPoint.locationId).toBe(mockAccessPointInput.locationId);
      expect(publicAccessPoint.assetId).toBe(mockAccessPointInput.assetId);
    });

    it('should return null for non-existent access point in public access', async () => {
      const publicContext = {
        ...mockContext,
        user: null,
      };

      const publicCaller = accessPointRouter.createCaller(publicContext);
      const publicAccessPoint = await publicCaller.getByIdPublic({
        id: 'clh1234567890123456789012', // Valid CUID2 format
        upkeepCompanyId: mockUser.upkeepCompanyId,
      });

      expect(publicAccessPoint).toBeNull();
    });

    it('should return null for access point from different company', async () => {
      const mockAccessPointInput = {
        name: `Company Access Point ${Math.random().toString(36).substring(2, 5)}`,
        locationId: '123',
      };

      const caller = accessPointRouter.createCaller(mockContext);
      const accessPoint = await caller.create(mockAccessPointInput);

      // Try to access with different company ID
      const publicContext = {
        ...mockContext,
        user: null,
      };

      const publicCaller = accessPointRouter.createCaller(publicContext);
      const publicAccessPoint = await publicCaller.getByIdPublic({
        id: accessPoint.id,
        upkeepCompanyId: 'diff123456', // Valid format with max 10 chars
      });

      expect(publicAccessPoint).toBeNull();
    });
  });

  describe('export', () => {
    it('should export access points with asset information', async () => {
      const mockAccessPointInput = {
        name: `Export Asset Access Point ${Math.random().toString(36).substring(2, 5)}`,
        locationId: 'abc123',
        assetId: 'asset-1',
      };

      const caller = accessPointRouter.createCaller(mockContext);
      await caller.create(mockAccessPointInput);
      const exportedAccessPoints = await caller.export({});

      expect(exportedAccessPoints).toBeDefined();
      expect(exportedAccessPoints.length).toBe(1);

      const accessPoint = exportedAccessPoints[0];
      expect(accessPoint.asset).toBeDefined();
      expect(accessPoint.asset?.id).toBe('asset-1');
      expect(accessPoint.asset?.name).toBe('Air Handler Unit 1');
    });

    it('should export access points without assets correctly', async () => {
      const mockAccessPointInput = {
        name: `Export No Asset Access Point ${Math.random().toString(36).substring(2, 5)}`,
        locationId: 'abc123',
        // No assetId
      };

      const caller = accessPointRouter.createCaller(mockContext);
      await caller.create(mockAccessPointInput);
      const exportedAccessPoints = await caller.export({});

      expect(exportedAccessPoints).toBeDefined();
      expect(exportedAccessPoints.length).toBe(1);

      const accessPoint = exportedAccessPoints[0];
      expect(accessPoint.asset).toBeUndefined();
      expect(accessPoint.assetId).toBeNull();
    });
  });

  describe('bulkCreate', () => {
    it('should create access points in bulk with AI fallback', async () => {
      const caller = accessPointRouter.createCaller(mockContext);

      const mockInput = [
        {
          name: 'Test AP 1',
          location: 'Kamino Sector', // Should match directly
          type: 'type-1',
          status: 'active',
        },
        {
          name: 'Test AP 2',
          location: 'Unknown Planet', // Should fallback to AI
          type: 'type-2',
          status: 'inactive',
        },
      ];

      // Mock AI response for fallback case
      vi.mocked(matchLocationsWithAI).mockImplementation(async (locationName) => {
        if (locationName === 'Unknown Planet') {
          return {
            locationId: 'ai-matched',
            locationName: 'AI Location',
          };
        }
        return { locationId: '', locationName: '' };
      });

      const result = await caller.bulkCreate(mockInput);

      expect(result.success).toBe(true);
      expect(result.created).toBe(2);
      expect(result.failed).toBe(0);
      expect(result.failedItems).toEqual([]);
    });

    it('should return failed items if AI also fails', async () => {
      const caller = accessPointRouter.createCaller(mockContext);

      const mockInput = [
        {
          name: 'Test AP Failed',
          location: 'Totally Unknown Planet',
          type: 'type-x',
          status: 'archived',
        },
      ];

      vi.mocked(matchLocationsWithAI).mockResolvedValue({
        locationId: '',
        locationName: '',
      });

      const result = await caller.bulkCreate(mockInput);

      expect(result.success).toBe(true);
      expect(result.created).toBe(0);
      expect(result.failed).toBe(1);
      expect(result.failedItems[0].location).toBe('Totally Unknown Planet');
    });
  });

  describe('toggleArchive', () => {
    it('should archive an active access point', async () => {
      const mockAccessPointInput = {
        name: `Test Access Point ${Math.random().toString(36).substring(2, 5)}`,
        locationId: '123',
        type: '123',
      };

      const caller = accessPointRouter.createCaller(mockContext);
      const accessPoint = await caller.create(mockAccessPointInput);

      // Verify it's initially not archived
      expect(accessPoint.archivedAt).toBeNull();

      // Archive the access point
      const archivedAccessPoint = await caller.toggleArchive({ id: accessPoint.id });

      expect(archivedAccessPoint).toBeDefined();
      expect(archivedAccessPoint.id).toBe(accessPoint.id);
      expect(archivedAccessPoint.archivedAt).toBeDefined();
      expect(archivedAccessPoint.archivedAt).not.toBeNull();
    });

    it('should unarchive an archived access point', async () => {
      const mockAccessPointInput = {
        name: `Test Access Point ${Math.random().toString(36).substring(2, 5)}`,
        locationId: '123',
        type: '123',
      };

      const caller = accessPointRouter.createCaller(mockContext);
      const accessPoint = await caller.create(mockAccessPointInput);

      // First archive the access point
      const archivedAccessPoint = await caller.toggleArchive({ id: accessPoint.id });
      expect(archivedAccessPoint.archivedAt).toBeDefined();

      // Then unarchive it
      const unarchivedAccessPoint = await caller.toggleArchive({ id: accessPoint.id });

      expect(unarchivedAccessPoint).toBeDefined();
      expect(unarchivedAccessPoint.id).toBe(accessPoint.id);
      expect(unarchivedAccessPoint.archivedAt).toBeNull();
    });

    it('should toggle status correctly when archiving and unarchiving', async () => {
      const mockAccessPointInput = {
        name: `Test Access Point ${Math.random().toString(36).substring(2, 5)}`,
        locationId: '123',
        type: '123',
      };

      const caller = accessPointRouter.createCaller(mockContext);
      const accessPoint = await caller.create(mockAccessPointInput);

      // Get the initial access point to check its status
      const initialAccessPoint = await caller.getById({ id: accessPoint.id });
      const initialStatus = initialAccessPoint.status;

      // Archive the access point - status should change
      const archivedAccessPoint = await caller.toggleArchive({ id: accessPoint.id });
      expect(archivedAccessPoint.archivedAt).toBeDefined();

      // Get the archived access point to verify status change
      const archivedDetails = await caller.getById({ id: accessPoint.id });
      expect(archivedDetails.status).not.toBe(initialStatus);

      // Unarchive the access point - status should revert
      const unarchivedAccessPoint = await caller.toggleArchive({ id: accessPoint.id });
      expect(unarchivedAccessPoint.archivedAt).toBeNull();

      // Get the unarchived access point to verify status reverted
      const unarchivedDetails = await caller.getById({ id: accessPoint.id });
      expect(unarchivedDetails.status).toBe(initialStatus);
    });

    it('should handle non-existent access point', async () => {
      const caller = accessPointRouter.createCaller(mockContext);

      // Try to toggle archive on a non-existent access point
      await expect(caller.toggleArchive({ id: 'non-existent-id' })).rejects.toThrow();
    });

    it('should validate required id parameter', async () => {
      const caller = accessPointRouter.createCaller(mockContext);

      // Test missing id field
      await expect(caller.toggleArchive({} as any)).rejects.toThrow();
    });
  });
});
