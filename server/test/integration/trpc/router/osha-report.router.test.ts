import { db } from '@server/db';
import { getAssets } from '@server/services/asset.service';
import { getLocationById, getLocations } from '@server/services/location.service';
import { getUserById, getUserPublic, getUsers, getUsersPublic, hasPermission } from '@server/services/user.service';
import { mockPublicUser, mockUser } from '@server/test/fixtures/user';
import { createMockContext } from '@server/trpc/router/__tests__/test-utils';
import { oshaReportRouter } from '@server/trpc/router/osha-report.router';
import { events, oshaAuditTrail, oshaLocations, oshaReports, shiftsEnum, typeOfMedicalCareEnum } from '@shared/schema';
import { eq } from 'drizzle-orm';
import { afterAll, afterEach, beforeAll, beforeEach, describe, expect, it, vi } from 'vitest';

// Mock the user service
vi.mock('@server/services/user.service', () => ({
  getUserById: vi.fn(),
  getUsers: vi.fn(),
  getUsersPublic: vi.fn(),
  getUserPublic: vi.fn(),
  hasPermission: vi.fn().mockReturnValue(true),
  userHasIncidentPermission: vi.fn().mockReturnValue(true),
  canEditIncident: vi.fn().mockReturnValue(true),
  canExportIncident: vi.fn().mockReturnValue(true),
}));

vi.mock('@server/services/location.service', () => ({
  getLocationById: vi.fn().mockResolvedValue({ id: '123', name: 'Test Location' }),
  getLocations: vi.fn().mockResolvedValue([{ id: '123', name: 'Test Location' }]),
}));

vi.mock('@server/services/asset.service', () => ({
  getAssets: vi.fn().mockResolvedValue({
    noResults: false,
    result: [{ id: '123', name: 'Test Asset', description: 'Test Asset Description' }],
    nextCursor: undefined,
  }),
}));

let eventId: string;
let oshaLocationId: string;

describe('oshaReportRouter', () => {
  beforeAll(async () => {
    // Create a mock global location
    const oshaLocation = await db
      .insert(oshaLocations)
      .values({
        name: 'Test Global Location',
        upkeepCompanyId: '123',
        createdBy: '123',
      })
      .returning({ id: oshaLocations.id });
    oshaLocationId = oshaLocation.at(0)!.id;

    // Create a test event
    const event = await db
      .insert(events)
      .values({
        upkeepCompanyId: '123',
        title: 'Test incident',
        description: 'Test description',
        type: 'incident',
        category: 'other',
        severity: 'low',
        reportedAt: new Date(),
        locationId: '123',
        assetIds: ['123'],
        status: 'open',
      })
      .returning({ id: events.id });
    eventId = event.at(0)!.id;
  });

  const mockContext = createMockContext(mockUser);
  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(getUserById).mockResolvedValue(mockUser);
    vi.mocked(getUsers).mockResolvedValue([mockUser]);
    vi.mocked(hasPermission).mockReturnValue(true);
    vi.mocked(getUsersPublic).mockResolvedValue({
      noResults: false,
      result: [mockUser],
      nextCursor: undefined,
    });
    vi.mocked(getUserPublic).mockResolvedValue(mockPublicUser);
    vi.mocked(getAssets).mockResolvedValue({
      noResults: false,
      result: [{ id: '123', name: 'Test Asset', description: 'Test Asset Description' }],
      nextCursor: undefined,
    });
    vi.mocked(getLocationById).mockResolvedValue({ id: '123', name: 'Test Location' });
    vi.mocked(getLocations).mockResolvedValue({
      noResults: false,
      result: [{ id: '123', name: 'Test Location' }],
      nextCursor: undefined,
    });
  });

  afterEach(async () => {
    await db.delete(oshaAuditTrail);
    await db.delete(oshaReports);
  });

  afterAll(async () => {
    await db.delete(events);
    await db.delete(oshaLocations);
  });

  const createValidOshaReportInput = (overrides = {}) => ({
    eventId,
    privacyCase: false,
    employeeName: 'John Doe',
    employeeWorkLocation: 'Main Office',
    employeeDepartment: 'IT',
    employeeJobTitle: 'Software Engineer',
    employeeDateOfHire: new Date(),
    employeeShift: shiftsEnum.enumValues[0],
    bodyPartInjured: 'head',
    typeOfInjury: 'cut',
    treatmentLocation: 'hospital',
    typeOfMedicalCare: typeOfMedicalCareEnum.enumValues[0],
    wasHospitalized: false,
    daysAwayFromWork: 0,
    daysRestrictedFromWork: 0,
    reportedBy: 'John Doe',
    preparedByUserId: '123',
    reasonForReport: 'some reason',
    type: 'medical_treatment_beyond_first_aid' as const,
    ipAddress: '127.0.0.1',
    userAgent: 'Test Browser',
    oshaLocationId,
    ...overrides,
  });

  describe('create', () => {
    it('should require oshaLocationId', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);
      const input = createValidOshaReportInput();
      delete (input as any).oshaLocationId;

      await expect(async () => await caller.create(input)).rejects.toThrowError();
    });

    it('should not allow employeeName to be empty when privacyCase is false', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);
      const input = createValidOshaReportInput({ employeeName: '' });

      await expect(async () => await caller.create(input)).rejects.toThrowError();
    });

    it('should not allow reasonForPrivacyCase to be empty when privacyCase is true', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);
      const input = createValidOshaReportInput({
        privacyCase: true,
        reasonForPrivacyCase: '',
        employeeName: '',
      });

      await expect(async () => await caller.create(input)).rejects.toThrowError();
    });

    it('should require daysAwayFromWork when wasHospitalized is true', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);
      const input = createValidOshaReportInput({
        wasHospitalized: true,
        daysAwayFromWork: undefined,
        daysRestrictedFromWork: 0, // Keep this valid so we only test daysAwayFromWork
      });

      await expect(async () => await caller.create(input)).rejects.toThrowError();
    });

    it('should require daysRestrictedFromWork when wasHospitalized is true', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);
      const input = createValidOshaReportInput({
        wasHospitalized: true,
        wasDeceased: false,
        daysAwayFromWork: 0, // Keep this valid so we only test daysRestrictedFromWork
        daysRestrictedFromWork: undefined,
      });

      await expect(async () => await caller.create(input)).rejects.toThrowError();
    });

    it('should allow undefined daysRestrictedFromWork when wasDeceased is true', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);
      const input = createValidOshaReportInput({
        wasDeceased: true,
        daysAwayFromWork: undefined, // Both should be allowed when deceased
        daysRestrictedFromWork: undefined,
      });

      const result = await caller.create(input);
      expect(result).toBeDefined();
      expect(result.id).toBeDefined();
    });

    it('should create a new OSHA report with slug generated and an audit trail', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);
      const input = createValidOshaReportInput();

      const report = await caller.create(input);
      expect(report).toBeDefined();

      const auditTrail = await db.select().from(oshaAuditTrail).where(eq(oshaAuditTrail.entityId, report.id)).limit(1);
      expect(auditTrail).toBeDefined();
      expect(auditTrail.length).toBe(1);
      expect(auditTrail[0].action).toBe('created');

      const createdReport = await db.select().from(oshaReports).where(eq(oshaReports.id, report.id)).limit(1);
      expect(createdReport).toBeDefined();
      expect(createdReport.length).toBe(1);
      expect(createdReport[0].id).toBe(report.id);
      expect(createdReport[0].eventId).toBe(eventId);
      expect(createdReport[0].employeeName).toBe(input.employeeName);
      expect(createdReport[0].employeeDepartment).toBe(input.employeeDepartment);
      expect(createdReport[0].employeeJobTitle).toBe(input.employeeJobTitle);
      expect(createdReport[0].oshaLocationId).toBe(oshaLocationId);
      expect(createdReport[0].slug).toBeDefined();
    });

    it('should create a new OSHA report with privacy case true and employeeName null', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);
      const input = createValidOshaReportInput({
        privacyCase: true,
        reasonForPrivacyCase: 'Test reason',
        employeeName: 'John Doe',
      });

      const report = await caller.create(input);
      expect(report).toBeDefined();

      const auditTrail = await db.select().from(oshaAuditTrail).where(eq(oshaAuditTrail.entityId, report.id)).limit(1);
      expect(auditTrail).toBeDefined();
      expect(auditTrail.length).toBe(1);
      expect(auditTrail[0].action).toBe('created');

      const createdReport = await db.select().from(oshaReports).where(eq(oshaReports.id, report.id)).limit(1);
      expect(createdReport).toBeDefined();
      expect(createdReport.length).toBe(1);
      expect(createdReport[0].id).toBe(report.id);
      expect(createdReport[0].eventId).toBe(eventId);
      expect(createdReport[0].employeeName).toBeNull();
      expect(createdReport[0].employeeDepartment).toBe(input.employeeDepartment);
      expect(createdReport[0].employeeJobTitle).toBe(input.employeeJobTitle);
      expect(createdReport[0].oshaLocationId).toBe(oshaLocationId);
      expect(createdReport[0].slug).toBeDefined();
    });

    it('should allow 0 values for daysAwayFromWork and daysRestrictedFromWork when not hospitalized or deceased', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);
      const input = {
        bodyPartInjured: 'test',
        correctiveActions: '',
        daysAwayFromWork: 0,
        daysRestrictedFromWork: 0,
        employeeDateOfHire: null,
        employeeDepartment: '',
        employeeJobTitle: 'test',
        employeeName: 'test',
        employeeShift: 'day' as const,
        employeeSupervisor: '',
        employeeWorkLocation: 'test',
        eventId,
        oshaLocationId,
        prescribedMedication: false,
        privacyCase: false,
        reasonForPrivacyCase: '',
        reasonForReport: '',
        reportedByName: '',
        rootCauseAnalysis: '',
        treatmentLocation: '',
        type: 'days_away_from_work' as const,
        typeOfInjury: 'test',
        typeOfMedicalCare: 'first_aid' as const,
        wasDeceased: false,
        wasHospitalized: false,
        witnesses: '',
        ipAddress: '127.0.0.1',
        userAgent: 'Test Browser',
      };

      const report = await caller.create(input);
      expect(report).toBeDefined();

      // Fetch the created report from database to verify the stored values
      const createdReport = await db.select().from(oshaReports).where(eq(oshaReports.id, report.id)).limit(1);
      expect(createdReport).toBeDefined();
      expect(createdReport.length).toBe(1);
      expect(createdReport[0].daysAwayFromWork).toBe(0);
      expect(createdReport[0].daysRestrictedFromWork).toBe(0);
    });

    it('should allow 0 values for daysAwayFromWork and daysRestrictedFromWork when hospitalized', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);
      const input = createValidOshaReportInput({
        wasHospitalized: true,
        daysAwayFromWork: 0,
        daysRestrictedFromWork: 0,
      });

      const report = await caller.create(input);
      expect(report).toBeDefined();

      // Fetch the created report from database to verify the stored values
      const createdReport = await db.select().from(oshaReports).where(eq(oshaReports.id, report.id)).limit(1);
      expect(createdReport).toBeDefined();
      expect(createdReport.length).toBe(1);
      expect(createdReport[0].daysAwayFromWork).toBe(0);
      expect(createdReport[0].daysRestrictedFromWork).toBe(0);
      expect(createdReport[0].wasHospitalized).toBe(true);
    });

    it('should allow 0 values for daysAwayFromWork and daysRestrictedFromWork when deceased', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);
      const input = createValidOshaReportInput({
        wasDeceased: true,
        daysAwayFromWork: 0,
        daysRestrictedFromWork: 0,
      });

      const report = await caller.create(input);
      expect(report).toBeDefined();

      // Fetch the created report from database to verify the stored values
      const createdReport = await db.select().from(oshaReports).where(eq(oshaReports.id, report.id)).limit(1);
      expect(createdReport).toBeDefined();
      expect(createdReport.length).toBe(1);
      expect(createdReport[0].daysAwayFromWork).toBe(0);
      expect(createdReport[0].daysRestrictedFromWork).toBe(0);
      expect(createdReport[0].wasDeceased).toBe(true);
    });
  });

  describe('update', () => {
    it('should require oshaLocationId when provided', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);
      const input = {
        id: '123',
        privacyCase: false,
        ipAddress: '127.0.0.1',
        userAgent: 'Test Browser',
        oshaLocationId: '', // Empty oshaLocationId should fail
      };

      await expect(async () => await caller.update(input)).rejects.toThrowError();
    });

    it('should not allow employeeName to be empty when privacyCase is false', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);
      const input = {
        id: '123',
        privacyCase: false,
        ipAddress: '127.0.0.1',
        userAgent: 'Test Browser',
        oshaLocationId,
      };

      await expect(async () => await caller.update(input)).rejects.toThrowError();
    });

    it('should not allow reasonForPrivacyCase to be empty when privacyCase is true', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);
      const input = {
        id: '123',
        privacyCase: true,
        ipAddress: '127.0.0.1',
        userAgent: 'Test Browser',
        oshaLocationId,
      };

      await expect(async () => await caller.update(input)).rejects.toThrowError();
    });

    it('should update an OSHA report and create an audit trail', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);

      const report = await db
        .insert(oshaReports)
        .values({
          eventId,
          privacyCase: false,
          employeeName: 'John Doe',
          employeeWorkLocation: 'Main Office',
          employeeDepartment: 'IT',
          employeeJobTitle: 'Software Engineer',
          employeeDateOfHire: new Date(),
          employeeShift: shiftsEnum.enumValues[0],
          bodyPartInjured: 'head',
          typeOfInjury: 'cut',
          treatmentLocation: 'hospital',
          typeOfMedicalCare: typeOfMedicalCareEnum.enumValues[0],
          wasHospitalized: false,
          reasonForReport: 'some reason here',
          type: 'medical_treatment_beyond_first_aid' as const,
          upkeepCompanyId: '123',
          createdBy: '123',
          oshaLocationId,
        })
        .returning({ id: oshaReports.id });

      const updateInput = {
        id: report[0].id,
        employeeJobTitle: 'HR Manager',
        ipAddress: '127.0.0.1',
        userAgent: 'Test Browser',
        oshaLocationId,
      };

      const updatedReport = await caller.update(updateInput);
      expect(updatedReport).toBeDefined();

      const auditTrail = await db
        .select()
        .from(oshaAuditTrail)
        .where(eq(oshaAuditTrail.entityId, updatedReport.id))
        .limit(1);
      expect(auditTrail).toBeDefined();
      expect(auditTrail.length).toBe(1);
      expect(auditTrail[0].action).toBe('updated');

      const loadedReport = await db.select().from(oshaReports).where(eq(oshaReports.id, updatedReport.id)).limit(1);
      expect(loadedReport).toBeDefined();
      expect(loadedReport.length).toBe(1);
      expect(loadedReport[0].id).toBe(updatedReport.id);
      expect(loadedReport[0].employeeJobTitle).toBe(updateInput.employeeJobTitle);
    });

    it('should set employeeName to null when privacyCase is true', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);

      const report = await db
        .insert(oshaReports)
        .values({
          eventId,
          privacyCase: false,
          employeeName: 'John Doe',
          employeeWorkLocation: 'Main Office',
          employeeDepartment: 'IT',
          employeeJobTitle: 'Software Engineer',
          employeeDateOfHire: new Date(),
          employeeShift: shiftsEnum.enumValues[0],
          bodyPartInjured: 'head',
          typeOfInjury: 'cut',
          treatmentLocation: 'hospital',
          typeOfMedicalCare: typeOfMedicalCareEnum.enumValues[0],
          wasHospitalized: false,
          reasonForReport: 'some reason here',
          type: 'medical_treatment_beyond_first_aid' as const,
          upkeepCompanyId: '123',
          createdBy: '123',
          oshaLocationId,
        })
        .returning({ id: oshaReports.id });

      const updateInput = {
        id: report[0].id,
        privacyCase: true,
        reasonForPrivacyCase: 'Test reason',
        ipAddress: '127.0.0.1',
        userAgent: 'Test Browser',
        oshaLocationId,
      };

      const updatedReport = await caller.update(updateInput);

      const loadedReport = await db.select().from(oshaReports).where(eq(oshaReports.id, updatedReport.id)).limit(1);
      expect(loadedReport).toBeDefined();
      expect(loadedReport.length).toBe(1);
      expect(loadedReport[0].id).toBe(updatedReport.id);
      expect(loadedReport[0].employeeName).toBeNull();
    });
  });

  describe('getById', () => {
    it('should fetch an OSHA report by ID with related event data', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);

      const input = createValidOshaReportInput({ upkeepCompanyId: '123' });
      const createdReport = await caller.create(input);

      const fetchedReport = await caller.getById({ id: createdReport.id });

      expect(fetchedReport).toBeDefined();
      expect(fetchedReport.id).toBe(createdReport.id);
      expect(fetchedReport.employeeName).toBe(input.employeeName);
      expect(fetchedReport.employeeDepartment).toBe(input.employeeDepartment);
      expect(fetchedReport.employeeJobTitle).toBe(input.employeeJobTitle);
      expect(fetchedReport.oshaLocationId).toBe(oshaLocationId);

      // Check that event data is included
      expect(fetchedReport.event).toBeDefined();
      expect(fetchedReport.event?.id).toBe(eventId);
      expect(fetchedReport.event?.title).toBe('Test incident');
      expect(fetchedReport.event?.assets).to.deep.equal([
        { id: '123', name: 'Test Asset', description: 'Test Asset Description' },
      ]);
      expect(fetchedReport.event?.location).to.deep.equal({ id: '123', name: 'Test Location' });
    });

    it('should return 404 for non-existent OSHA report', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);
      const nonExistentId = 'non-existent-id';

      await expect(async () => await caller.getById({ id: nonExistentId })).rejects.toThrowError();
    });

    it('should handle privacy case data correctly when fetching by ID', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);

      const input = createValidOshaReportInput({
        privacyCase: true,
        reasonForPrivacyCase: 'Employee requested privacy',
        employeeName: 'John Doe',
        upkeepCompanyId: '123',
      });

      const createdReport = await caller.create(input);
      const fetchedReport = await caller.getById({ id: createdReport.id });

      expect(fetchedReport).toBeDefined();
      expect(fetchedReport.privacyCase).toBe(true);
      expect(fetchedReport.reasonForPrivacyCase).toBe('Employee requested privacy');
      expect(fetchedReport.employeeName).toBeNull(); // Should be null for privacy cases
    });
  });

  describe('list', () => {
    it('should list OSHA reports with pagination', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);

      const input1 = createValidOshaReportInput({ upkeepCompanyId: '123' });
      const input2 = createValidOshaReportInput({
        employeeName: 'Jane Smith',
        employeeJobTitle: 'HR Manager',
        privacyCase: true,
        reasonForPrivacyCase: 'Employee requested privacy',
        upkeepCompanyId: '123',
      });

      await caller.create(input1);
      await caller.create(input2);

      const result = await caller.list({
        cursor: 0,
        limit: 10,
        year: 2025,
      });

      expect(result).toBeDefined();
      expect(result.result).toHaveLength(2);

      // Find the privacy case and regular case
      const privacyCase = result.result.find((r) => r.privacyCase === true);
      const regularCase = result.result.find((r) => r.privacyCase === false);

      expect(privacyCase).toBeDefined();
      expect(regularCase).toBeDefined();
      expect(privacyCase!.employeeName).toBe('Privacy Case'); // Privacy case should be masked
      expect(regularCase!.employeeName).toBeDefined(); // Regular case should have name
    });
  });

  describe('toggleArchive', () => {
    it('should archive an active OSHA report', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);
      const input = createValidOshaReportInput();

      const createdReport = await caller.create(input);

      // Verify it's initially not archived
      const initialReport = await caller.getById({ id: createdReport.id });
      expect(initialReport?.archivedAt).toBeNull();

      // Archive the OSHA report
      const archivedResult = await caller.toggleArchive({ id: createdReport.id });

      expect(archivedResult).toBeDefined();
      expect(archivedResult?.id).toBe(createdReport.id);
      expect(archivedResult?.archivedAt).toBeTruthy();
    });

    it('should unarchive an archived OSHA report', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);
      const input = createValidOshaReportInput();

      const createdReport = await caller.create(input);

      // First archive the OSHA report
      const archivedResult = await caller.toggleArchive({ id: createdReport.id });
      expect(archivedResult?.archivedAt).toBeTruthy();

      // Then unarchive it
      const unarchivedResult = await caller.toggleArchive({ id: createdReport.id });

      expect(unarchivedResult).toBeDefined();
      expect(unarchivedResult?.id).toBe(createdReport.id);
      expect(unarchivedResult?.archivedAt).toBeNull();
    });

    it('should preserve OSHA report data when toggling archive status', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);
      const input = createValidOshaReportInput();

      const createdReport = await caller.create(input);

      // Archive the OSHA report
      const archivedResult = await caller.toggleArchive({ id: createdReport.id });
      expect(archivedResult?.archivedAt).toBeTruthy();

      // Verify other fields remain unchanged by getting the full OSHA report
      const archivedReport = await caller.getById({ id: createdReport.id });
      expect(archivedReport?.employeeName).toBe(input.employeeName);
      expect(archivedReport?.employeeDepartment).toBe(input.employeeDepartment);
      expect(archivedReport?.employeeJobTitle).toBe(input.employeeJobTitle);
      expect(archivedReport?.eventId).toBe(input.eventId);
      expect(archivedReport?.oshaLocationId).toBe(input.oshaLocationId);

      // Unarchive and verify data is still preserved
      const unarchivedResult = await caller.toggleArchive({ id: createdReport.id });
      expect(unarchivedResult?.archivedAt).toBeNull();

      const unarchivedReport = await caller.getById({ id: createdReport.id });
      expect(unarchivedReport?.employeeName).toBe(input.employeeName);
      expect(unarchivedReport?.employeeDepartment).toBe(input.employeeDepartment);
      expect(unarchivedReport?.employeeJobTitle).toBe(input.employeeJobTitle);
      expect(unarchivedReport?.eventId).toBe(input.eventId);
      expect(unarchivedReport?.oshaLocationId).toBe(input.oshaLocationId);
    });

    it('should handle toggle archive for OSHA report with privacy case', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);
      const input = createValidOshaReportInput({
        privacyCase: true,
        reasonForPrivacyCase: 'Employee requested privacy',
        employeeName: 'John Doe',
      });

      const createdReport = await caller.create(input);

      // Archive the OSHA report
      const archivedResult = await caller.toggleArchive({ id: createdReport.id });
      expect(archivedResult?.archivedAt).toBeTruthy();

      // Verify the OSHA report can still be retrieved with privacy data
      const reportWithPrivacy = await caller.getById({ id: createdReport.id });
      expect(reportWithPrivacy?.privacyCase).toBe(true);
      expect(reportWithPrivacy?.reasonForPrivacyCase).toBe('Employee requested privacy');
      expect(reportWithPrivacy?.employeeName).toBeNull(); // Should be null for privacy cases
      expect(reportWithPrivacy?.archivedAt).toBeTruthy();

      // Unarchive and verify privacy data is still intact
      const unarchivedResult = await caller.toggleArchive({ id: createdReport.id });
      expect(unarchivedResult?.archivedAt).toBeNull();

      const finalReport = await caller.getById({ id: createdReport.id });
      expect(finalReport?.privacyCase).toBe(true);
      expect(finalReport?.reasonForPrivacyCase).toBe('Employee requested privacy');
      expect(finalReport?.employeeName).toBeNull();
      expect(finalReport?.archivedAt).toBeNull();
    });

    it('should handle non-existent OSHA report', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);

      // Try to toggle archive on a non-existent OSHA report
      await expect(caller.toggleArchive({ id: 'non-existent-id' })).rejects.toThrow();
    });

    it('should validate required id parameter', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);

      // Test missing id field
      await expect(caller.toggleArchive({} as any)).rejects.toThrow();
    });

    it('should affect list results based on includeArchived filter', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);

      // Create two OSHA reports
      const input1 = createValidOshaReportInput({ employeeName: 'Active Employee' });
      const input2 = createValidOshaReportInput({ employeeName: 'To Be Archived Employee' });

      const report1 = await caller.create(input1);
      const report2 = await caller.create(input2);

      // Archive one OSHA report
      await caller.toggleArchive({ id: report2.id });

      // List without includeArchived should only show active OSHA report
      const activeList = await caller.list({
        cursor: 0,
        limit: 10,
        year: 2025,
        includeArchived: false,
      });
      expect(activeList.result.length).toBe(1);
      expect(activeList.result[0].employeeName).toBe('Active Employee');

      // List with includeArchived should show both OSHA reports
      const allList = await caller.list({
        cursor: 0,
        limit: 10,
        year: 2025,
        includeArchived: true,
      });
      expect(allList.result.length).toBe(2);

      const employeeNames = allList.result.map((r) => r.employeeName).sort();
      expect(employeeNames).toEqual(['Active Employee', 'To Be Archived Employee']);
    });
  });
});
