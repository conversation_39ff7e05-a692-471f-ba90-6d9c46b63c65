import { db } from '@server/db';
import { hasPermission, getUsersPublic } from '@server/services/user.service';
import { mockUser } from '@server/test/fixtures/user';
import { createMockContext, createMockUser } from '@server/trpc/router/__tests__/test-utils';
import { controlMeasuresRouter } from '@server/trpc/router/control-measures.router';
import { hazardsRouter } from '@server/trpc/router/hazards.router';
import { sopRouter } from '@server/trpc/router/sop.router';
import { auditTrail, controlMeasures, hazards, sops, sopSection } from '@shared/schema';
import { CreateSopFormSchema, UpdateSopFormSchema } from '@shared/types/sop.types';
import { afterAll, afterEach, beforeAll, beforeEach, describe, expect, it, vi } from 'vitest';
import { z } from 'zod';
import { and, desc, eq } from 'drizzle-orm';
import { randomUUID } from 'crypto';

vi.mock('@server/services/user.service', () => ({
  hasPermission: vi.fn().mockReturnValue(true),
  getUserById: vi.fn(),
  getUsers: vi.fn(),
  getUsersPublic: vi.fn(),
  getUserPublic: vi.fn(),
}));

let createdHazardId: string;
let createdControlMeasureId: string;

describe('sop router', () => {
  const mockContext = createMockContext(mockUser) as any;

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(hasPermission).mockReturnValue(true);

    // Mock getUsersPublic to return the mock user data
    vi.mocked(getUsersPublic).mockResolvedValue({
      result: [
        {
          id: mockUser.id,
          username: mockUser.username,
          firstName: mockUser.firstName,
          lastName: mockUser.lastName,
          fullName: mockUser.fullName,
          email: mockUser.email,
        },
      ],
      nextCursor: undefined,
      noResults: false,
    });
  });

  afterEach(async () => {
    await db.delete(auditTrail);
    await db.delete(sopSection);
    await db.delete(sops);
  });

  beforeAll(async () => {
    const hazardCaller = hazardsRouter.createCaller(mockContext);
    const controlMeasureCaller = controlMeasuresRouter.createCaller(mockContext);

    const createdHazard = await hazardCaller.create({
      name: 'Custom Test Hazard',
      type: 'physical' as const,
    });
    createdHazardId = createdHazard.id;

    const createdControlMeasure = await controlMeasureCaller.create({
      name: 'Custom Test Control Measure',
      type: 'personal_protective_equipment' as const,
    });
    createdControlMeasureId = createdControlMeasure.id;
  });

  afterAll(async () => {
    await db.delete(hazards);
    await db.delete(controlMeasures);
  });

  describe('create', () => {
    it('should create a new SOP with existing hazards and control measures', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      const mockInput: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'Test Safety Operating Procedure',
          purpose: 'To ensure safe operation of equipment',
          responsibilities: 'All operators must follow this procedure',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            sectionType: 'general',
            serial: 1,
            label: 'Scope & Applicability',
            value: 'This SOP applies to all facility operations during normal business hours.',
          },
          {
            sectionType: 'step',
            serial: 1,
            label: 'Hazard 1',
            value: 'Identify potential chemical exposure risks in the work area.',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 3,
            likelihood: 4,
          },
          {
            sectionType: 'emergency',
            serial: 1,
            label: 'Emergency Contacts',
            value: 'In case of emergency, contact facility manager at ext. 911.',
          },
        ],
      };

      const result = await sopCallerRouter.create(mockInput);

      expect(result).toBeDefined();
      expect(result?.id).toEqual(expect.any(String));
      expect(result?.slug).toMatch(/^SOP-\d{4}-1\.0$/);
      expect(result?.instanceId).toEqual(expect.any(String));

      // Verify SOP was created
      const createdSop = await db.query.sops.findFirst({
        where: (s, { eq }) => eq(s.id, result!.id),
      });
      expect(createdSop).toBeDefined();
      expect(createdSop?.title).toBe(mockInput.sop.title);
      expect(createdSop?.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
      expect(createdSop?.createdBy).toBe(mockUser.id);
      // Verify highest severity calculation (max severity × likelihood = 3 × 4 = 12)
      expect(createdSop?.highestSeverity).toBe(12);

      // Verify sections were created
      const createdSections = await db.query.sopSection.findMany({
        where: (s, { eq }) => eq(s.sopId, result!.id),
        orderBy: (s, { asc }) => [asc(s.sectionType), asc(s.serial)],
      });
      expect(createdSections).toHaveLength(3);

      // Verify general section
      const generalSection = createdSections.find((s) => s.sectionType === 'general');
      expect(generalSection).toBeDefined();
      expect(generalSection?.label).toBe('Scope & Applicability');
      expect(generalSection?.serial).toBe(1);
      expect(generalSection?.severity).toBeNull();

      // Verify step section with hazards and control measures
      const stepSection = createdSections.find((s) => s.sectionType === 'step');
      expect(stepSection).toBeDefined();
      expect(stepSection?.hazardIds).toEqual(expect.arrayContaining([createdHazardId]));
      expect(stepSection?.controlMeasureIds).toEqual(expect.arrayContaining([createdControlMeasureId]));
      expect(stepSection?.severity).toBe(3);

      // Verify emergency section
      const emergencySection = createdSections.find((s) => s.sectionType === 'emergency');
      expect(emergencySection).toBeDefined();
      expect(emergencySection?.label).toBe('Emergency Contacts');
      expect(emergencySection?.severity).toBeNull();

      // Verify audit trail was created
      const auditLog = await db.query.auditTrail.findFirst({
        where: (a, { eq, and }) => and(eq(a.entityType, 'sop'), eq(a.entityId, result!.id), eq(a.action, 'drafted')),
      });
      expect(auditLog).toBeDefined();
      expect(auditLog?.details).toBeDefined();
    });

    it('should create a new SOP and then create a revision with higher severity', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      const mockInput: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'Original Equipment SOP',
          purpose: 'Safe equipment operation',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            sectionType: 'general',
            serial: 1,
            label: 'Equipment Overview',
            value: 'This procedure covers basic equipment operation.',
          },
          {
            sectionType: 'step',
            serial: 1,
            label: 'Hazard 1',
            value: 'Check equipment for basic safety.',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 2,
            likelihood: 3,
          },
        ],
      };

      const result = await sopCallerRouter.create(mockInput);

      expect(result).toBeDefined();
      expect(result?.version).toBe('1.0');
      expect(result?.instanceId).toEqual(expect.any(String));

      // Create revision with higher severity and additional sections
      const revision = await sopCallerRouter.create({
        sop: {
          title: 'Updated Equipment SOP - Revised',
          purpose: 'Enhanced safety equipment operation with critical controls',
          responsibilities: 'All operators and supervisors must follow this updated procedure',
          ownerId: mockUser.id,
          approverId: mockUser.id,
          instanceId: result?.instanceId,
        },
        sections: [
          {
            sectionType: 'general',
            serial: 1,
            label: 'Equipment Overview',
            value: 'This updated procedure covers advanced equipment operation with enhanced safety measures.',
          },
          {
            sectionType: 'general',
            serial: 2,
            label: 'Training Requirements',
            value: 'All operators must complete certified training before using this equipment.',
          },
          {
            sectionType: 'step',
            serial: 1,
            label: 'Hazard 1',
            value: 'Perform comprehensive safety inspection with detailed checklist.',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 4, // Higher severity than original
            likelihood: 3,
          },
          {
            sectionType: 'step',
            serial: 2,
            label: 'Hazard 2',
            value: 'Monitor critical safety parameters during operation.',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 3,
            likelihood: 4,
          },
          {
            sectionType: 'emergency',
            serial: 1,
            label: 'Emergency Response',
            value: 'Immediate shutdown procedures for emergency situations.',
          },
        ],
      });

      expect(revision).toBeDefined();
      expect(revision?.id).not.toEqual(result?.id);
      expect(revision?.version).toBe('2.0');
      expect(revision?.instanceId).toBe(result?.instanceId);
      expect(revision?.slug).toMatch(/^SOP-\d{4}-2\.0$/);

      // Verify highest severity calculation in revision (max is step 1 with severity 4 × likelihood 3 = 12)
      const revisionSop = await db.query.sops.findFirst({
        where: (s, { eq }) => eq(s.id, revision!.id),
      });
      expect(revisionSop).toBeDefined();
      expect(revisionSop?.highestSeverity).toBe(12);

      // Verify sections were created correctly
      const revisionSections = await db.query.sopSection.findMany({
        where: (s, { eq }) => eq(s.sopId, revision!.id),
        orderBy: (s, { asc }) => [asc(s.sectionType), asc(s.serial)],
      });
      expect(revisionSections).toHaveLength(5);

      // Verify general sections (should have 2)
      const generalSections = revisionSections.filter((s) => s.sectionType === 'general');
      expect(generalSections).toHaveLength(2);
      expect(generalSections[0]?.serial).toBe(1);
      expect(generalSections[1]?.serial).toBe(2);

      // Verify step sections (should have 2 with different severities)
      const stepSections = revisionSections.filter((s) => s.sectionType === 'step');
      expect(stepSections).toHaveLength(2);
      expect(stepSections[0]?.severity).toBe(4);
      expect(stepSections[1]?.severity).toBe(3);
    });

    it('should create a new SOP with bulk created hazards and control measures within the same transaction', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      const mockInput: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'Chemical Handling SOP with Bulk Creation',
          purpose: 'Safe handling of hazardous chemicals with comprehensive controls',
          responsibilities: 'Chemical handlers, supervisors, and safety officers',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            sectionType: 'general',
            serial: 1,
            label: 'Scope & Applicability',
            value: 'This SOP applies to all chemical handling operations in designated areas.',
          },
          {
            sectionType: 'general',
            serial: 2,
            label: 'Regulatory Basis & References',
            value: 'Based on OSHA 29 CFR 1910.1200 and EPA regulations for chemical safety.',
          },
          {
            sectionType: 'step',
            serial: 1,
            label: 'Hazard 1',
            value: 'Identify and assess chemical exposure risks before handling.',
            hazardIds: [createdHazardId], // Use existing hazard
            controlMeasureIds: [createdControlMeasureId], // Use existing control measure
            hazardsToCreate: [
              {
                name: 'Chemical Vapor Inhalation Risk',
                type: 'chemical' as const,
              },
              {
                name: 'Corrosive Material Contact',
                type: 'chemical' as const,
              },
            ],
            controlMeasuresToCreate: [
              {
                name: 'Chemical-Resistant Apron',
                type: 'personal_protective_equipment' as const,
              },
              {
                name: 'Ventilation System Protocol',
                type: 'engineering_controls' as const,
              },
            ],
            severity: 4,
            likelihood: 3,
          },
          {
            sectionType: 'step',
            serial: 2,
            label: 'Hazard 2',
            value: 'Monitor environmental conditions during chemical storage.',
            hazardsToCreate: [
              {
                name: 'Temperature-Sensitive Storage Risk',
                type: 'environmental' as const,
              },
            ],
            controlMeasuresToCreate: [
              {
                name: 'Environmental Monitoring System',
                type: 'administrative_controls' as const,
              },
            ],
            severity: 2,
            likelihood: 3,
          },
          {
            sectionType: 'emergency',
            serial: 1,
            label: 'Chemical Spill Response',
            value: 'Immediate containment and evacuation procedures for chemical spills.',
          },
          {
            sectionType: 'emergency',
            serial: 2,
            label: 'First Aid Response',
            value: 'Emergency medical procedures for chemical exposure incidents.',
          },
        ],
      };

      const result = await sopCallerRouter.create(mockInput);

      expect(result).toBeDefined();
      expect(result?.id).toEqual(expect.any(String));
      expect(result?.slug).toMatch(/^SOP-\d{4}-1\.0$/);

      // Verify SOP was created
      const createdSop = await db.query.sops.findFirst({
        where: (s, { eq }) => eq(s.id, result!.id),
      });
      expect(createdSop).toBeDefined();
      expect(createdSop?.title).toBe(mockInput.sop.title);
      // Verify highest severity calculation (max is step 1 with severity 4 × likelihood 3 = 12)
      expect(createdSop?.highestSeverity).toBe(12);

      // Verify sections were created
      const createdSections = await db.query.sopSection.findMany({
        where: (s, { eq }) => eq(s.sopId, result!.id),
        orderBy: (s, { asc }) => [asc(s.sectionType), asc(s.serial)],
      });
      expect(createdSections).toHaveLength(6);

      // Verify general sections
      const generalSections = createdSections.filter((s) => s.sectionType === 'general');
      expect(generalSections).toHaveLength(2);

      // Verify step sections with both existing and newly created hazards/control measures
      const stepSections = createdSections.filter((s) => s.sectionType === 'step');
      expect(stepSections).toHaveLength(2);

      const firstStepSection = stepSections.find((s) => s.serial === 1);
      expect(firstStepSection?.hazardIds).toEqual(expect.arrayContaining([createdHazardId]));
      expect(firstStepSection?.controlMeasureIds).toEqual(expect.arrayContaining([createdControlMeasureId]));

      // Should have 3 hazards total: 1 existing + 2 created
      expect(firstStepSection?.hazardIds).toHaveLength(3);
      // Should have 3 control measures total: 1 existing + 2 created
      expect(firstStepSection?.controlMeasureIds).toHaveLength(3);

      // Verify second step section has only newly created hazards/control measures
      const secondStepSection = stepSections.find((s) => s.serial === 2);
      expect(secondStepSection?.hazardIds).toHaveLength(1);
      expect(secondStepSection?.controlMeasureIds).toHaveLength(1);
      expect(secondStepSection?.severity).toBe(2);

      // Verify emergency sections
      const emergencySections = createdSections.filter((s) => s.sectionType === 'emergency');
      expect(emergencySections).toHaveLength(2);
      expect(emergencySections[0]?.serial).toBe(1);
      expect(emergencySections[1]?.serial).toBe(2);

      // Verify that the new hazards were actually created in the database
      const allHazards = await db.query.hazards.findMany({
        where: (h, { eq }) => eq(h.upkeepCompanyId, mockUser.upkeepCompanyId),
      });

      const newHazardNames = [
        'Chemical Vapor Inhalation Risk',
        'Corrosive Material Contact',
        'Temperature-Sensitive Storage Risk',
      ];
      const createdHazardNames = allHazards.filter((h) => newHazardNames.includes(h.name)).map((h) => h.name);

      expect(createdHazardNames).toEqual(expect.arrayContaining(newHazardNames));

      // Verify that the new control measures were actually created in the database
      const allControlMeasures = await db.query.controlMeasures.findMany({
        where: (cm, { eq }) => eq(cm.upkeepCompanyId, mockUser.upkeepCompanyId),
      });

      const newControlMeasureNames = [
        'Chemical-Resistant Apron',
        'Ventilation System Protocol',
        'Environmental Monitoring System',
      ];
      const createdControlMeasureNames = allControlMeasures
        .filter((cm) => newControlMeasureNames.includes(cm.name))
        .map((cm) => cm.name);

      expect(createdControlMeasureNames).toEqual(expect.arrayContaining(newControlMeasureNames));

      // Verify audit trail was created
      const auditLog = await db.query.auditTrail.findFirst({
        where: (a, { eq, and }) => and(eq(a.entityType, 'sop'), eq(a.entityId, result!.id), eq(a.action, 'drafted')),
      });
      expect(auditLog).toBeDefined();
      expect(auditLog?.details).toBeDefined();

      // Verify the audit trail contains the processed SOP result (with mapped IDs)
      const auditDetails = JSON.parse(auditLog!.details!);
      expect(auditDetails.sections).toHaveLength(6);

      // Verify step sections have the correct hazard and control measure counts
      const stepSectionsInAudit = auditDetails.sections.filter((s: any) => s.sectionType === 'step');
      expect(stepSectionsInAudit[0].hazardIds).toHaveLength(3);
      expect(stepSectionsInAudit[0].controlMeasureIds).toHaveLength(3);
      expect(stepSectionsInAudit[1].hazardIds).toHaveLength(1);
      expect(stepSectionsInAudit[1].controlMeasureIds).toHaveLength(1);
    });

    it('should validate serial uniqueness per section type', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      const mockInput: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'Serial Validation Test SOP',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            sectionType: 'general',
            serial: 1,
            label: 'First General Section',
            value: 'Content for first general section.',
          },
          {
            sectionType: 'general',
            serial: 2,
            label: 'Second General Section',
            value: 'Content for second general section.',
          },
          {
            sectionType: 'step',
            serial: 1,
            label: 'Hazard 1',
            value: 'First step hazard identification.',
            severity: 2,
            likelihood: 3,
          },
          {
            sectionType: 'step',
            serial: 2,
            label: 'Hazard 2',
            value: 'Second step hazard identification.',
            severity: 3,
            likelihood: 4,
          },
          {
            sectionType: 'emergency',
            serial: 1,
            label: 'Emergency Protocol 1',
            value: 'First emergency response protocol.',
          },
        ],
      };

      const result = await sopCallerRouter.create(mockInput);

      expect(result).toBeDefined();

      // Verify sections were created with correct serials per type
      const createdSections = await db.query.sopSection.findMany({
        where: (s, { eq }) => eq(s.sopId, result!.id),
        orderBy: (s, { asc }) => [asc(s.sectionType), asc(s.serial)],
      });

      // Check that each section type has its own serial numbering
      const generalSections = createdSections.filter((s) => s.sectionType === 'general');
      expect(generalSections).toHaveLength(2);
      expect(generalSections.map((s) => s.serial)).toEqual([1, 2]);

      const stepSections = createdSections.filter((s) => s.sectionType === 'step');
      expect(stepSections).toHaveLength(2);
      expect(stepSections.map((s) => s.serial)).toEqual([1, 2]);

      const emergencySections = createdSections.filter((s) => s.sectionType === 'emergency');
      expect(emergencySections).toHaveLength(1);
      expect(emergencySections.map((s) => s.serial)).toEqual([1]);
    });

    it('should validate that severity is required for step sections', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      const mockInputWithoutSeverity = {
        sop: {
          title: 'Severity Validation Test SOP',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            sectionType: 'step' as const,
            serial: 1,
            label: 'Hazard 1',
            value: 'Step without severity should fail.',
            // Missing severity field
          },
        ],
      };

      // This should fail validation because step sections require severity
      await expect(sopCallerRouter.create(mockInputWithoutSeverity as any)).rejects.toThrow();
    });

    it('should handle different section types with appropriate data', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      const mockInput: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'Comprehensive Section Types SOP',
          purpose: 'Test all section types and their specific requirements',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            sectionType: 'pre_procedure',
            serial: 1,
            label: 'Pre-Operation Checklist',
            value: 'Complete equipment inspection before starting operations.',
          },
          {
            sectionType: 'procedure',
            serial: 1,
            label: 'Main Operating Procedure',
            value: 'Step-by-step instructions for normal operations.',
          },
          {
            sectionType: 'post_procedure',
            serial: 1,
            label: 'Post-Operation Activities',
            value: 'Shutdown and cleanup procedures after operations.',
          },
        ],
      };

      const result = await sopCallerRouter.create(mockInput);

      expect(result).toBeDefined();

      // Verify all section types were created correctly
      const createdSections = await db.query.sopSection.findMany({
        where: (s, { eq }) => eq(s.sopId, result!.id),
        orderBy: (s, { asc }) => [asc(s.sectionType)],
      });

      expect(createdSections).toHaveLength(3);

      const sectionTypes = createdSections.map((s) => s.sectionType);
      expect(sectionTypes).toContain('pre_procedure');
      expect(sectionTypes).toContain('procedure');
      expect(sectionTypes).toContain('post_procedure');

      // Verify these sections don't have severity (since they're not step types)
      createdSections.forEach((section) => {
        if (section.sectionType !== 'step') {
          expect(section.severity).toBeNull();
        }
      });
    });
  });

  describe('update', () => {
    it('should update an existing SOP with modified sections', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // First create a SOP
      const createInput: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'Original Safety Procedure',
          purpose: 'Basic safety guidelines',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            sectionType: 'general',
            serial: 1,
            label: 'Original Scope',
            value: 'This covers basic operations.',
          },
          {
            sectionType: 'step',
            serial: 1,
            label: 'Original Hazard',
            value: 'Basic safety check.',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 2,
            likelihood: 3,
          },
        ],
      };

      const createdSop = await sopCallerRouter.create(createInput);
      expect(createdSop).toBeDefined();

      // Get the created sections to get their IDs
      const originalSections = await db.query.sopSection.findMany({
        where: (s, { eq }) => eq(s.sopId, createdSop!.id),
        orderBy: (s, { asc }) => [asc(s.sectionType), asc(s.serial)],
      });

      expect(originalSections).toHaveLength(2);
      const generalSectionId = originalSections.find((s) => s.sectionType === 'general')?.id;
      const stepSectionId = originalSections.find((s) => s.sectionType === 'step')?.id;

      // Now update the SOP
      const updateInput: z.infer<typeof UpdateSopFormSchema> = {
        sop: {
          id: createdSop!.id,
          title: 'Updated Safety Procedure',
          purpose: 'Enhanced safety guidelines with additional controls',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            id: generalSectionId, // Update existing section
            sectionType: 'general',
            serial: 1,
            label: 'Updated Scope',
            value: 'This covers enhanced operations with additional safety measures.',
          },
          {
            id: undefined, // New section
            sectionType: 'general',
            serial: 2,
            label: 'Training Requirements',
            value: 'All operators must complete certified training.',
          },
          {
            id: stepSectionId, // Update existing section
            sectionType: 'step',
            serial: 1,
            label: 'Enhanced Hazard Control',
            value: 'Comprehensive safety assessment with detailed checklist.',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 3, // Increased severity
            likelihood: 4,
          },
          {
            id: undefined, // New section
            sectionType: 'step',
            serial: 2,
            label: 'Additional Safety Step',
            value: 'Monitor critical parameters during operation.',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 2,
            likelihood: 3,
          },
        ],
      };

      const result = await sopCallerRouter.update(updateInput);

      expect(result).toBeDefined();
      expect(result?.id).toBe(createdSop!.id);

      // Verify SOP was updated
      const updatedSop = await db.query.sops.findFirst({
        where: (s, { eq }) => eq(s.id, result!.id),
      });
      expect(updatedSop).toBeDefined();
      expect(updatedSop?.title).toBe('Updated Safety Procedure');
      expect(updatedSop?.purpose).toBe('Enhanced safety guidelines with additional controls');
      // Verify slug and version remain the same after update
      expect(updatedSop?.slug).toBe(createdSop?.slug);
      expect(updatedSop?.version).toBe(createdSop?.version);
      // Verify highest severity calculation (max severity × likelihood = 3 × 4 = 12)
      expect(updatedSop?.highestSeverity).toBe(12);

      // Verify sections were updated/created
      const updatedSections = await db.query.sopSection.findMany({
        where: (s, { eq }) => eq(s.sopId, result!.id),
        orderBy: (s, { asc }) => [asc(s.sectionType), asc(s.serial)],
      });
      expect(updatedSections).toHaveLength(4);

      // Verify general sections
      const generalSections = updatedSections.filter((s) => s.sectionType === 'general');
      expect(generalSections).toHaveLength(2);
      expect(generalSections[0]?.label).toBe('Updated Scope');
      expect(generalSections[1]?.label).toBe('Training Requirements');

      // Verify step sections
      const stepSections = updatedSections.filter((s) => s.sectionType === 'step');
      expect(stepSections).toHaveLength(2);
      expect(stepSections[0]?.severity).toBe(3);
      expect(stepSections[1]?.severity).toBe(2);

      // Verify audit trail was created
      const auditLog = await db.query.auditTrail.findFirst({
        where: (a, { eq, and }) => and(eq(a.entityType, 'sop'), eq(a.entityId, result!.id), eq(a.action, 'updated')),
      });
      expect(auditLog).toBeDefined();
      expect(auditLog?.details).toBeDefined();
    });

    it('should update an existing SOP with bulk created hazards and control measures', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // First create a SOP
      const createInput: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'Basic Safety Procedure',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            sectionType: 'step',
            serial: 1,
            label: 'Basic Step',
            value: 'Basic safety check.',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 1,
            likelihood: 2,
          },
        ],
      };

      const createdSop = await sopCallerRouter.create(createInput);
      expect(createdSop).toBeDefined();

      // Now update with additional hazards and control measures
      const updateInput: z.infer<typeof UpdateSopFormSchema> = {
        sop: {
          id: createdSop!.id,
          title: 'Enhanced Safety Procedure',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            id: undefined, // New section
            sectionType: 'step',
            serial: 1,
            label: 'Enhanced Step',
            value: 'Enhanced safety procedure with additional controls.',
            hazardIds: [createdHazardId], // Use existing hazard
            controlMeasureIds: [createdControlMeasureId], // Use existing control measure
            hazardsToCreate: [
              {
                name: 'New Chemical Risk',
                type: 'chemical' as const,
              },
              {
                name: 'Equipment Failure Risk',
                type: 'physical' as const,
              },
            ],
            controlMeasuresToCreate: [
              {
                name: 'Enhanced PPE Protocol',
                type: 'personal_protective_equipment' as const,
              },
              {
                name: 'Emergency Shutdown Procedure',
                type: 'administrative_controls' as const,
              },
            ],
            severity: 4, // Increased severity
            likelihood: 3,
          },
        ],
      };

      const result = await sopCallerRouter.update(updateInput);

      expect(result).toBeDefined();
      expect(result?.id).toBe(createdSop!.id);

      // Verify SOP was updated
      const updatedSop = await db.query.sops.findFirst({
        where: (s, { eq }) => eq(s.id, result!.id),
      });
      expect(updatedSop).toBeDefined();
      expect(updatedSop?.title).toBe('Enhanced Safety Procedure');
      // Verify slug and version remain the same after update
      expect(updatedSop?.slug).toBe(createdSop?.slug);
      expect(updatedSop?.version).toBe(createdSop?.version);
      // Verify highest severity calculation (max severity × likelihood = 4 × 3 = 12)
      expect(updatedSop?.highestSeverity).toBe(12);

      // Verify sections were updated
      const updatedSections = await db.query.sopSection.findMany({
        where: (s, { eq }) => eq(s.sopId, result!.id),
        orderBy: (s, { asc }) => [asc(s.serial)],
      });
      expect(updatedSections).toHaveLength(2); // Original section + new section

      // The new section with bulk creation should be the one with higher severity (4)
      const newSection = updatedSections.find((s) => s.severity === 4);
      expect(newSection).toBeDefined();
      expect(newSection?.hazardIds).toHaveLength(3); // 1 existing + 2 created
      expect(newSection?.controlMeasureIds).toHaveLength(3); // 1 existing + 2 created

      // Verify that the new hazards were actually created in the database
      const allHazards = await db.query.hazards.findMany({
        where: (h, { eq }) => eq(h.upkeepCompanyId, mockUser.upkeepCompanyId),
      });

      const newHazardNames = ['New Chemical Risk', 'Equipment Failure Risk'];
      const createdHazardNames = allHazards.filter((h) => newHazardNames.includes(h.name)).map((h) => h.name);

      expect(createdHazardNames).toEqual(expect.arrayContaining(newHazardNames));

      // Verify that the new control measures were actually created in the database
      const allControlMeasures = await db.query.controlMeasures.findMany({
        where: (cm, { eq }) => eq(cm.upkeepCompanyId, mockUser.upkeepCompanyId),
      });

      const newControlMeasureNames = ['Enhanced PPE Protocol', 'Emergency Shutdown Procedure'];
      const createdControlMeasureNames = allControlMeasures
        .filter((cm) => newControlMeasureNames.includes(cm.name))
        .map((cm) => cm.name);

      expect(createdControlMeasureNames).toEqual(expect.arrayContaining(newControlMeasureNames));

      // Verify audit trail was created
      const auditLog = await db.query.auditTrail.findFirst({
        where: (a, { eq, and }) => and(eq(a.entityType, 'sop'), eq(a.entityId, result!.id), eq(a.action, 'updated')),
      });
      expect(auditLog).toBeDefined();
      expect(auditLog?.details).toBeDefined();
    });

    it('should throw error when SOP does not exist', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      const nonExistentId = 'clyabc123def456ghi789jkl';
      const updateInput: z.infer<typeof UpdateSopFormSchema> = {
        sop: {
          id: nonExistentId,
          title: 'Non-existent SOP',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            sectionType: 'general',
            serial: 1,
            label: 'Test Section',
            value: 'Test content.',
          },
        ],
      };

      await expect(sopCallerRouter.update(updateInput)).rejects.toThrow('Failed to update SOP');
    });

    it('should not affect SOPs from different companies', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // First create a SOP
      const createInput: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'Company-specific SOP',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            sectionType: 'general',
            serial: 1,
            label: 'Test Section',
            value: 'Test content.',
          },
        ],
      };

      const createdSop = await sopCallerRouter.create(createInput);

      // Create a context with a different company ID
      const differentCompanyContext = createMockContext({
        ...mockUser,
        upkeepCompanyId: 'different-company-id',
      }) as any;

      const differentCompanyCallerRouter = sopRouter.createCaller(differentCompanyContext);

      const updateInput: z.infer<typeof UpdateSopFormSchema> = {
        sop: {
          id: createdSop!.id,
          title: 'Unauthorized Update',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            sectionType: 'general',
            serial: 1,
            label: 'Unauthorized Section',
            value: 'Unauthorized content.',
          },
        ],
      };

      // Should fail because the SOP belongs to a different company
      await expect(differentCompanyCallerRouter.update(updateInput)).rejects.toThrow('Failed to update SOP');
    });
  });

  describe('list', () => {
    it('should list SOPs with default pagination', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // Create a SOP for testing
      const mockInput: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'Test SOP for List',
          purpose: 'Test purpose',
          responsibilities: 'Test responsibilities',
          ownerId: mockUser.id,
          approverId: mockUser.id,
          notes: 'Test notes',
          isPublic: true,
        },
        sections: [
          {
            sectionType: 'step',
            serial: 1,
            label: 'Step 1: Safety Check',
            value: 'Perform safety check',
            severity: 3,
            likelihood: 3,
          },
          {
            sectionType: 'step',
            serial: 2,
            label: 'Step 2: Equipment Setup',
            value: 'Setup equipment',
            severity: 4,
            likelihood: 2,
          },
        ],
      };

      await sopCallerRouter.create(mockInput);

      // Test the list endpoint
      const result = await sopCallerRouter.list({});

      expect(result).toBeDefined();
      expect(result.result).toHaveLength(1);
      expect(result.nextCursor).toBeUndefined();
      expect(result.noResults).toBe(false);

      const sopItem = result.result[0];
      expect(sopItem.instanceId).toEqual(expect.any(String));
      expect(sopItem.slug).toEqual(expect.any(String));
      expect(sopItem.status).toBe('draft');
      expect(sopItem.highestSeverity).toBe(9); // Max severity × likelihood = max(3×3, 4×2) = 9
      expect(sopItem.sectionCount).toBe(2);
    });

    it('should filter SOPs by status', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // Create a SOP
      const mockInput: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'Draft SOP',
          purpose: 'Test purpose',
          responsibilities: 'Test responsibilities',
          ownerId: mockUser.id,
          approverId: mockUser.id,
          notes: 'Test notes',
          isPublic: true,
        },
        sections: [
          {
            sectionType: 'step',
            serial: 1,
            label: 'Step 1',
            value: 'Test step',
            severity: 1,
            likelihood: 2,
          },
        ],
      };

      await sopCallerRouter.create(mockInput);

      // Test filtering by status
      const result = await sopCallerRouter.list({
        status: ['draft'],
      });

      expect(result.result).toHaveLength(1);
      expect(result.result[0]?.status).toBe('draft');
    });

    it('should filter SOPs by risk level', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // Create SOPs with different risk levels
      const lowRiskInput: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'Low Risk SOP',
          purpose: 'Test purpose',
          responsibilities: 'Test responsibilities',
          ownerId: mockUser.id,
          approverId: mockUser.id,
          notes: 'Test notes',
          isPublic: true,
        },
        sections: [
          {
            sectionType: 'step',
            serial: 1,
            label: 'Low Risk Step',
            value: 'Low risk step',
            severity: 1, // Low severity
            likelihood: 3,
          },
        ],
      };

      const highRiskInput: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'High Risk SOP',
          purpose: 'Test purpose',
          responsibilities: 'Test responsibilities',
          ownerId: mockUser.id,
          approverId: mockUser.id,
          notes: 'Test notes',
          isPublic: true,
        },
        sections: [
          {
            sectionType: 'step',
            serial: 1,
            label: 'High Risk Step',
            value: 'High risk step',
            severity: 4, // High severity
            likelihood: 4,
          },
        ],
      };

      await sopCallerRouter.create(lowRiskInput);
      await sopCallerRouter.create(highRiskInput);

      // Test filtering by high risk
      const highRiskResult = await sopCallerRouter.list({
        riskLevel: 'high',
      });

      expect(highRiskResult.result).toHaveLength(1);
      expect(highRiskResult.result[0]?.highestSeverity).toBe(16);

      // Test filtering by low risk
      const lowRiskResult = await sopCallerRouter.list({
        riskLevel: 'low',
      });

      expect(lowRiskResult.result).toHaveLength(1);
      expect(lowRiskResult.result[0]?.highestSeverity).toBe(3);
    });

    it('should sort SOPs by title A-Z', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // Create multiple SOPs with different titles
      const sopInputA: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'Alpha SOP',
          purpose: 'Test purpose',
          responsibilities: 'Test responsibilities',
          ownerId: mockUser.id,
          approverId: mockUser.id,
          notes: 'Test notes',
          isPublic: true,
        },
        sections: [
          {
            sectionType: 'step',
            serial: 1,
            label: 'Step 1',
            value: 'Test step',
            severity: 1,
            likelihood: 2,
          },
        ],
      };

      const sopInputZ: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'Zulu SOP',
          purpose: 'Test purpose',
          responsibilities: 'Test responsibilities',
          ownerId: mockUser.id,
          approverId: mockUser.id,
          notes: 'Test notes',
          isPublic: true,
        },
        sections: [
          {
            sectionType: 'step',
            serial: 1,
            label: 'Step 1',
            value: 'Test step',
            severity: 1,
            likelihood: 2,
          },
        ],
      };

      await sopCallerRouter.create(sopInputZ); // Create Z first
      await sopCallerRouter.create(sopInputA); // Create A second

      // Test sorting by title ascending
      const result = await sopCallerRouter.list({
        sortBy: 'title',
        sortOrder: 'asc',
      });

      expect(result.result).toHaveLength(2);
      expect(result.result[0]?.title).toContain('Alpha');
      expect(result.result[1]?.title).toContain('Zulu');
    });

    it('should sort SOPs by highest risk', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // Create SOPs with different risk levels
      const lowRiskInput: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'Low Risk SOP',
          purpose: 'Test purpose',
          responsibilities: 'Test responsibilities',
          ownerId: mockUser.id,
          approverId: mockUser.id,
          notes: 'Test notes',
          isPublic: true,
        },
        sections: [
          {
            sectionType: 'step',
            serial: 1,
            label: 'Low Risk Step',
            value: 'Low risk step',
            severity: 2,
            likelihood: 2,
          },
        ],
      };

      const highRiskInput: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'High Risk SOP',
          purpose: 'Test purpose',
          responsibilities: 'Test responsibilities',
          ownerId: mockUser.id,
          approverId: mockUser.id,
          notes: 'Test notes',
          isPublic: true,
        },
        sections: [
          {
            sectionType: 'step',
            serial: 1,
            label: 'High Risk Step',
            value: 'High risk step',
            severity: 4,
            likelihood: 4,
          },
        ],
      };

      await sopCallerRouter.create(lowRiskInput); // Create low risk first
      await sopCallerRouter.create(highRiskInput); // Create high risk second

      // Test sorting by highest severity descending
      const result = await sopCallerRouter.list({
        sortBy: 'highestSeverity',
        sortOrder: 'desc',
      });

      expect(result.result).toHaveLength(2);
      expect(result.result[0]?.highestSeverity).toBeGreaterThan(result.result[1]?.highestSeverity);
    });

    it('should handle pagination correctly', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // Create multiple SOPs
      for (let i = 1; i <= 5; i++) {
        const mockInput: z.infer<typeof CreateSopFormSchema> = {
          sop: {
            title: `SOP ${i}`,
            purpose: 'Test purpose',
            responsibilities: 'Test responsibilities',
            ownerId: mockUser.id,
            approverId: mockUser.id,
            notes: 'Test notes',
            isPublic: true,
          },
          sections: [
            {
              sectionType: 'step',
              serial: 1,
              label: 'Step 1',
              value: 'Test step',
              severity: 1,
              likelihood: 2,
            },
          ],
        };
        await sopCallerRouter.create(mockInput);
      }

      // Test first page with limit 3
      const firstPage = await sopCallerRouter.list({
        limit: 3,
        cursor: 0,
      });

      expect(firstPage.result).toHaveLength(3);
      expect(firstPage.nextCursor).toBe(3);

      // Test second page
      const secondPage = await sopCallerRouter.list({
        limit: 3,
        cursor: 3,
      });

      expect(secondPage.result).toHaveLength(2); // Only 2 remaining
      expect(secondPage.nextCursor).toBeUndefined();
    });

    it('should throw error when user lacks VIEW permission', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // Mock permission check to return false
      vi.mocked(hasPermission).mockReturnValue(false);

      await expect(sopCallerRouter.list({})).rejects.toThrow();
    });
  });

  describe('toggleArchive', () => {
    it('should archive a SOP when it is not archived', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // Create a SOP
      const mockInput: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'SOP to Archive',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            sectionType: 'step',
            label: 'Step 1',
            value: 'First step content',
            serial: 1,
            severity: 1,
            likelihood: 2,
          },
        ],
      };

      const createdSop = await sopCallerRouter.create(mockInput);
      expect(createdSop).toBeDefined();
      expect(createdSop?.id).toEqual(expect.any(String));
      expect(createdSop?.instanceId).toEqual(expect.any(String));

      // Archive the SOP
      const archivedSop = await sopCallerRouter.toggleArchive({ id: createdSop.instanceId! });

      expect(archivedSop).toBeDefined();
      expect(archivedSop.id).toBe(createdSop!.id);
      expect(archivedSop.archivedAt).toBeDefined();
      expect(archivedSop.archivedAt).toBeInstanceOf(Date);
      expect(archivedSop.updatedAt).toBeDefined();
      expect(archivedSop.updatedAt).toBeInstanceOf(Date);

      // Verify audit trail was created
      const auditLog = await db.query.auditTrail.findFirst({
        where: and(
          eq(auditTrail.entityId, createdSop.id),
          eq(auditTrail.entityType, 'sop'),
          eq(auditTrail.action, 'archived'),
        ),
        orderBy: desc(auditTrail.timestamp),
      });

      expect(auditLog).toBeDefined();
      expect(auditLog?.userId).toBe(mockUser.id);
      expect(auditLog?.details).toContain('archivedAt');
    });

    it('should unarchive a SOP when it is archived', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // Create a SOP
      const mockInput: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'SOP to Unarchive',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            sectionType: 'step',
            label: 'Step 1',
            value: 'First step content',
            serial: 1,
            severity: 1,
            likelihood: 2,
          },
        ],
      };

      const createdSop = await sopCallerRouter.create(mockInput);
      expect(createdSop).toBeDefined();

      // First archive the SOP
      const archivedSop = await sopCallerRouter.toggleArchive({ id: createdSop.instanceId! });
      expect(archivedSop.archivedAt).toBeDefined();

      // Now unarchive the SOP
      const unarchivedSop = await sopCallerRouter.toggleArchive({ id: createdSop.instanceId! });

      expect(unarchivedSop).toBeDefined();
      expect(unarchivedSop.id).toBe(createdSop!.id);
      expect(unarchivedSop.archivedAt).toBeNull();
      expect(unarchivedSop.updatedAt).toBeDefined();
      expect(unarchivedSop.updatedAt).toBeInstanceOf(Date);

      // Verify that updatedAt was changed from the archive operation
      expect(unarchivedSop.updatedAt!.getTime()).toBeGreaterThan(archivedSop.updatedAt!.getTime());
    });

    it('should throw error when SOP does not exist', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // Generate a random CUID2 that doesn't exist
      const nonExistentId = randomUUID();

      await expect(sopCallerRouter.toggleArchive({ id: nonExistentId })).rejects.toThrow(
        'Failed to archive/unarchive SOP',
      );
    });

    it('should throw error when user lacks EDIT permission', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // Create a SOP first
      const mockInput: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'SOP for Permission Test',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            sectionType: 'step',
            label: 'Step 1',
            value: 'First step content',
            serial: 1,
            severity: 1,
            likelihood: 2,
          },
        ],
      };

      const createdSop = await sopCallerRouter.create(mockInput);

      // Mock permission check to return false
      vi.mocked(hasPermission).mockReturnValue(false);

      await expect(sopCallerRouter.toggleArchive({ id: createdSop!.instanceId! })).rejects.toThrow();
    });

    it('should not affect SOPs from different companies', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // Create a SOP
      const mockInput: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'Company-specific SOP',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            sectionType: 'step',
            label: 'Step 1',
            value: 'First step content',
            serial: 1,
            severity: 1,
            likelihood: 2,
          },
        ],
      };

      const createdSop = await sopCallerRouter.create(mockInput);

      // Create a context for a different company
      const differentCompanyContext = createMockContext({
        ...mockUser,
        upkeepCompanyId: 'different-company-id',
      }) as any;

      const differentCompanyCallerRouter = sopRouter.createCaller(differentCompanyContext);

      // Try to archive the SOP from a different company
      await expect(differentCompanyCallerRouter.toggleArchive({ id: createdSop.instanceId! })).rejects.toThrow(
        'Failed to archive/unarchive SOP',
      );
    });
  });

  describe('getByInstanceId', () => {
    it('should get SOP by instance ID with related data', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // Create a SOP first
      const mockInput: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'Test SOP for getByInstanceId',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            sectionType: 'step',
            label: 'Step 1',
            value: 'First step content',
            serial: 1,
            severity: 1,
            likelihood: 2,
          },
          {
            sectionType: 'step',
            label: 'Step 2',
            value: 'Second step content',
            serial: 2,
            severity: 2,
            likelihood: 1,
          },
        ],
      };

      const createdSop = await sopCallerRouter.create(mockInput);
      expect(createdSop).toBeDefined();
      expect(createdSop?.instanceId).toBeDefined();

      // Get SOP by instance ID
      const retrievedSop = await sopCallerRouter.getByInstanceId({ id: createdSop.instanceId! });

      expect(retrievedSop).toBeDefined();
      expect(retrievedSop.id).toBe(createdSop.id);
      expect(retrievedSop.instanceId).toBe(createdSop.instanceId);
      expect(retrievedSop.title).toBe(mockInput.sop.title);
      expect(retrievedSop.sections).toBeDefined();
      expect(Array.isArray(retrievedSop.sections)).toBe(true);
      expect(retrievedSop.sections.length).toBe(2);
      expect(retrievedSop.owner).toBeDefined();
      expect(retrievedSop.approver).toBeDefined();

      // Verify section data
      expect(retrievedSop.sections[0].label).toBe('Step 1');
      expect(retrievedSop.sections[0].value).toBe('First step content');
      expect(retrievedSop.sections[0].severity).toBe(1);
      expect(retrievedSop.sections[1].label).toBe('Step 2');
      expect(retrievedSop.sections[1].value).toBe('Second step content');
      expect(retrievedSop.sections[1].severity).toBe(2);
    });

    it('should get SOP by instance ID with specific version', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // Create a SOP first
      const mockInput: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'Test SOP for version retrieval',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            sectionType: 'step',
            label: 'Step 1',
            value: 'First step content',
            serial: 1,
            severity: 1,
            likelihood: 2,
          },
        ],
      };

      const createdSop = await sopCallerRouter.create(mockInput);
      expect(createdSop).toBeDefined();

      // Get SOP by instance ID with specific version ID
      const retrievedSop = await sopCallerRouter.getByInstanceId({
        id: createdSop.instanceId!,
        versionId: createdSop.id,
      });

      expect(retrievedSop).toBeDefined();
      expect(retrievedSop.id).toBe(createdSop.id);
      expect(retrievedSop.instanceId).toBe(createdSop.instanceId);
      expect(retrievedSop.title).toBe(mockInput.sop.title);
    });

    it('should throw error when SOP not found', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // Generate a random CUID2 that doesn't exist
      const nonExistentId = randomUUID();

      await expect(sopCallerRouter.getByInstanceId({ id: nonExistentId })).rejects.toThrow('SOP not found');
    });

    it('should throw error when user lacks VIEW permission', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // Create a SOP first
      const mockInput: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'SOP for Permission Test',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            sectionType: 'step',
            label: 'Step 1',
            value: 'First step content',
            serial: 1,
            severity: 1,
            likelihood: 2,
          },
        ],
      };

      const createdSop = await sopCallerRouter.create(mockInput);

      // Mock permission check to return false
      vi.mocked(hasPermission).mockReturnValue(false);

      await expect(sopCallerRouter.getByInstanceId({ id: createdSop.instanceId! })).rejects.toThrow();
    });

    it('should not return SOPs from different companies', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // Create a SOP
      const mockInput: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'Company-specific SOP',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            sectionType: 'step',
            label: 'Step 1',
            value: 'First step content',
            serial: 1,
            severity: 1,
            likelihood: 2,
          },
        ],
      };

      const createdSop = await sopCallerRouter.create(mockInput);

      // Create a context for a different company
      const differentCompanyContext = createMockContext({
        ...mockUser,
        upkeepCompanyId: 'different-company-id',
      }) as any;

      const differentCompanyCallerRouter = sopRouter.createCaller(differentCompanyContext);

      // Try to get the SOP from a different company
      await expect(differentCompanyCallerRouter.getByInstanceId({ id: createdSop.instanceId! })).rejects.toThrow(
        'SOP not found',
      );
    });

    it('should return SOP with sections array properly structured', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // Create a SOP with one section
      const mockInput: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'SOP with one section',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            sectionType: 'step',
            label: 'Step 1',
            value: 'First step content',
            serial: 1,
            severity: 1,
            likelihood: 2,
          },
        ],
      };

      const createdSop = await sopCallerRouter.create(mockInput);
      expect(createdSop).toBeDefined();

      // Get SOP by instance ID
      const retrievedSop = await sopCallerRouter.getByInstanceId({ id: createdSop.instanceId! });

      expect(retrievedSop).toBeDefined();
      expect(retrievedSop.id).toBe(createdSop.id);
      expect(retrievedSop.sections).toBeDefined();
      expect(Array.isArray(retrievedSop.sections)).toBe(true);
      expect(retrievedSop.sections.length).toBe(1);
      expect(retrievedSop.sections[0].label).toBe('Step 1');
      expect(retrievedSop.sections[0].value).toBe('First step content');
    });

    it('should return SOP with sections that have hazards and control measures', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // Create a SOP with sections that reference hazards and control measures
      const mockInput: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'SOP with hazards and control measures',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            sectionType: 'step',
            label: 'Step with hazard',
            value: 'Step content with hazard reference',
            serial: 1,
            severity: 3,
            likelihood: 2,
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
          },
        ],
      };

      const createdSop = await sopCallerRouter.create(mockInput);
      expect(createdSop).toBeDefined();

      // Get SOP by instance ID
      const retrievedSop = await sopCallerRouter.getByInstanceId({ id: createdSop.instanceId! });

      expect(retrievedSop).toBeDefined();
      expect(retrievedSop.sections).toBeDefined();
      expect(retrievedSop.sections.length).toBe(1);
      expect(retrievedSop.sections[0].hazardIds).toContain(createdHazardId);
      expect(retrievedSop.sections[0].controlMeasureIds).toContain(createdControlMeasureId);
      expect(retrievedSop.sections[0].severity).toBe(3);
    });
  });

  describe('getByInstanceIdForEdit', () => {
    it('should get SOP by instance ID for editing without related data', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // Create a SOP first
      const mockInput: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'Test SOP for getByInstanceIdForEdit',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            sectionType: 'step',
            label: 'Step 1',
            value: 'First step content',
            serial: 1,
            severity: 1,
            likelihood: 2,
          },
        ],
      };

      const createdSop = await sopCallerRouter.create(mockInput);
      expect(createdSop).toBeDefined();

      // Get SOP for editing
      const sopForEdit = await sopCallerRouter.getByInstanceIdForEdit({
        id: createdSop.instanceId!,
      });

      expect(sopForEdit).toBeDefined();
      expect(sopForEdit.instanceId).toBe(createdSop.instanceId);
      expect(sopForEdit.title).toBe('Test SOP for getByInstanceIdForEdit');
      expect(sopForEdit.sections).toBeDefined();
      expect(sopForEdit.sections.length).toBe(1);
      expect(sopForEdit.sections[0].label).toBe('Step 1');
      expect(sopForEdit.sections[0].value).toBe('First step content');

      // Should not include related data like owner, approver, location, assets
      // These properties don't exist on the raw SOP object from getByInstanceIdForEdit
      expect((sopForEdit as any).owner).toBeUndefined();
      expect((sopForEdit as any).approver).toBeUndefined();
      expect((sopForEdit as any).location).toBeUndefined();
      expect((sopForEdit as any).assets).toBeUndefined();
    });

    it('should return 404 for non-existent SOP instance ID', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      await expect(
        sopCallerRouter.getByInstanceIdForEdit({
          id: '00000000-0000-0000-0000-000000000000',
        }),
      ).rejects.toThrow('SOP not found');
    });

    it('should get SOP by instance ID with specific version ID', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // Create a SOP first
      const mockInput: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'Test SOP for version test',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            sectionType: 'step',
            label: 'Step 1',
            value: 'First step content',
            serial: 1,
            severity: 1,
            likelihood: 2,
          },
        ],
      };

      const createdSop = await sopCallerRouter.create(mockInput);
      expect(createdSop).toBeDefined();

      // Get SOP for editing with specific version ID
      const sopForEdit = await sopCallerRouter.getByInstanceIdForEdit({
        id: createdSop.instanceId!,
        versionId: createdSop.id,
      });

      expect(sopForEdit).toBeDefined();
      expect(sopForEdit.instanceId).toBe(createdSop.instanceId);
      expect(sopForEdit.id).toBe(createdSop.id);
      expect(sopForEdit.title).toBe('Test SOP for version test');
    });

    it('should return 404 for non-existent version ID', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // Create a SOP first
      const mockInput: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'Test SOP for version test',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            sectionType: 'step',
            label: 'Step 1',
            value: 'First step content',
            serial: 1,
            severity: 1,
            likelihood: 2,
          },
        ],
      };

      const createdSop = await sopCallerRouter.create(mockInput);
      expect(createdSop).toBeDefined();

      // Try to get SOP with non-existent version ID
      await expect(
        sopCallerRouter.getByInstanceIdForEdit({
          id: createdSop.instanceId!,
          versionId: '00000000000000000000000000',
        }),
      ).rejects.toThrow('SOP not found');
    });

    it('should return 404 for version ID that does not belong to the instance', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // Create two SOPs
      const mockInput1: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'Test SOP 1',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            sectionType: 'step',
            label: 'Step 1',
            value: 'First step content',
            serial: 1,
            severity: 1,
            likelihood: 2,
          },
        ],
      };

      const mockInput2: z.infer<typeof CreateSopFormSchema> = {
        sop: {
          title: 'Test SOP 2',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            sectionType: 'step',
            label: 'Step 1',
            value: 'First step content',
            serial: 1,
            severity: 1,
            likelihood: 2,
          },
        ],
      };

      const createdSop1 = await sopCallerRouter.create(mockInput1);
      const createdSop2 = await sopCallerRouter.create(mockInput2);
      expect(createdSop1).toBeDefined();
      expect(createdSop2).toBeDefined();

      // Try to get SOP1 with SOP2's version ID
      await expect(
        sopCallerRouter.getByInstanceIdForEdit({
          id: createdSop1.instanceId!,
          versionId: createdSop2.id,
        }),
      ).rejects.toThrow('SOP not found');
    });
  });

  describe('getVersions', () => {
    it('should get all versions of a SOP instance', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // Create a SOP instance
      const sopData = {
        sop: {
          title: 'Test SOP for Versions',
          purpose: 'Testing version retrieval',
          responsibilities: 'Test responsibilities',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            sectionType: 'general' as const,
            serial: 1,
            label: 'Purpose',
            value: 'Test purpose',
            hazardIds: [],
            controlMeasureIds: [],
            severity: null,
          },
        ],
      };

      const createdSop = await sopCallerRouter.create(sopData);
      expect(createdSop).toBeDefined();
      expect(createdSop.instanceId).toBeDefined();

      // Get versions (should return at least the one we just created)
      const versions = await sopCallerRouter.getVersions({ id: createdSop.instanceId! });

      expect(versions).toHaveLength(1);
      expect(versions[0].title).toBe('Test SOP for Versions');
      expect(versions[0].createdBy).toBeDefined();
      expect(versions[0].createdBy.id).toBe(mockUser.id);
      expect(versions[0].createdBy.fullName).toBe(mockUser.fullName);
    });

    it('should return empty array for non-existent SOP instance', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);
      const versions = await sopCallerRouter.getVersions({ id: '00000000-0000-0000-0000-000000000000' });
      expect(versions).toHaveLength(0);
    });

    it('should throw error when user lacks VIEW permission', async () => {
      vi.mocked(hasPermission).mockReturnValue(false);
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      await expect(sopCallerRouter.getVersions({ id: '00000000-0000-0000-0000-000000000000' })).rejects.toThrow(
        'Insufficient permissions',
      );
    });

    it('should not return versions from different companies', async () => {
      // Create a SOP with a different company ID
      const differentCompanyUser = {
        ...mockUser,
        upkeepCompanyId: 'diff-comp',
      };

      const differentCompanyContext = createMockContext(differentCompanyUser) as any;
      const differentCompanyCaller = sopRouter.createCaller(differentCompanyContext);

      const sopData = {
        sop: {
          title: 'Test SOP Different Company',
          purpose: 'Testing company isolation',
          responsibilities: 'Test responsibilities',
          ownerId: differentCompanyUser.id,
          approverId: differentCompanyUser.id,
        },
        sections: [
          {
            sectionType: 'general' as const,
            serial: 1,
            label: 'Purpose',
            value: 'Test purpose',
            hazardIds: [],
            controlMeasureIds: [],
            severity: null,
          },
        ],
      };

      const createdSop = await differentCompanyCaller.create(sopData);
      expect(createdSop).toBeDefined();

      // Try to get versions with original user (different company)
      const sopCallerRouter = sopRouter.createCaller(mockContext);
      const versions = await sopCallerRouter.getVersions({ id: createdSop.instanceId! });
      expect(versions).toHaveLength(0);
    });

    it('should return versions ordered by creation date (newest first)', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // Create a SOP instance
      const sopData = {
        sop: {
          title: 'Test SOP for Ordering',
          purpose: 'Testing version ordering',
          responsibilities: 'Test responsibilities',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        sections: [
          {
            sectionType: 'general' as const,
            serial: 1,
            label: 'Purpose',
            value: 'Test purpose',
            hazardIds: [],
            controlMeasureIds: [],
            severity: null,
          },
        ],
      };

      const createdSop = await sopCallerRouter.create(sopData);
      expect(createdSop).toBeDefined();

      // Get versions
      const versions = await sopCallerRouter.getVersions({ id: createdSop.instanceId! });

      expect(versions).toHaveLength(1);
      expect(versions[0].title).toBe('Test SOP for Ordering');
      expect(versions[0].createdBy).toBeDefined();
      expect(versions[0].createdBy.id).toBe(mockUser.id);
    });
  });

  describe('updateStatus', () => {
    it('should update SOP status to approved', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      const sopData = {
        sop: {
          title: 'Test SOP for Status Update',
          purpose: 'Test purpose',
          responsibilities: 'Test responsibilities',
          ownerId: mockUser.id,
          approverId: mockUser.id,
          reviewDate: new Date('2024-12-31'),
        },
        sections: [
          {
            sectionType: 'general' as const,
            serial: 1,
            label: 'General Section',
            value: 'General content',
          },
        ],
      };

      const createdSop = await sopCallerRouter.create(sopData);
      expect(createdSop).toBeDefined();

      const updateResult = await sopCallerRouter.updateStatus({
        id: createdSop.id,
        status: 'approved',
      });

      expect(updateResult).toBeDefined();
      expect(updateResult.status).toBe('approved');
      expect(updateResult.id).toBe(createdSop.id);
    });

    it('should update SOP status to rejected with rejection reason', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      const sopData = {
        sop: {
          title: 'Test SOP for Rejection',
          purpose: 'Test purpose',
          responsibilities: 'Test responsibilities',
          ownerId: mockUser.id,
          approverId: mockUser.id,
          reviewDate: new Date('2024-12-31'),
        },
        sections: [
          {
            sectionType: 'general' as const,
            serial: 1,
            label: 'General Section',
            value: 'General content',
          },
        ],
      };

      const createdSop = await sopCallerRouter.create(sopData);
      expect(createdSop).toBeDefined();

      const updateResult = await sopCallerRouter.updateStatus({
        id: createdSop.id,
        status: 'draft',
        rejectionReason: 'Needs more detail in the procedures section',
      });

      expect(updateResult).toBeDefined();
      expect(updateResult.status).toBe('draft');
      expect(updateResult.id).toBe(createdSop.id);
      expect(updateResult.notes).toContain('Needs more detail in the procedures section');
    });

    it('should throw error when SOP does not exist', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      await expect(
        sopCallerRouter.updateStatus({
          id: 'clyabc123def456ghi789jkl',
          status: 'approved',
        }),
      ).rejects.toThrow('Failed to update SOP status');
    });

    it('should throw error when user lacks EDIT permission', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      const sopData = {
        sop: {
          title: 'Test SOP for Permission Check',
          purpose: 'Test purpose',
          responsibilities: 'Test responsibilities',
          ownerId: mockUser.id,
          approverId: mockUser.id,
          reviewDate: new Date('2024-12-31'),
        },
        sections: [
          {
            sectionType: 'general' as const,
            serial: 1,
            label: 'General Section',
            value: 'General content',
          },
        ],
      };

      const createdSop = await sopCallerRouter.create(sopData);
      expect(createdSop).toBeDefined();

      // Mock permission check to return false
      vi.mocked(hasPermission).mockReturnValue(false);

      await expect(
        sopCallerRouter.updateStatus({
          id: createdSop.id,
          status: 'approved',
        }),
      ).rejects.toThrow();
    });

    it('should not affect SOPs from different companies', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      const sopData = {
        sop: {
          title: 'Test SOP for Company Isolation',
          purpose: 'Test purpose',
          responsibilities: 'Test responsibilities',
          ownerId: mockUser.id,
          approverId: mockUser.id,
          reviewDate: new Date('2024-12-31'),
        },
        sections: [
          {
            sectionType: 'general' as const,
            serial: 1,
            label: 'General Section',
            value: 'General content',
          },
        ],
      };

      const createdSop = await sopCallerRouter.create(sopData);
      expect(createdSop).toBeDefined();

      const differentCompanyUser = createMockUser('ADMIN', {
        id: 'different-company-user',
        upkeepCompanyId: 'diff-comp',
      });

      const differentCompanyContext = createMockContext(differentCompanyUser);
      const differentCompanyCaller = sopRouter.createCaller(differentCompanyContext);

      await expect(
        differentCompanyCaller.updateStatus({
          id: createdSop.id,
          status: 'approved',
        }),
      ).rejects.toThrow('Failed to update SOP status');
    });
  });

  describe('minimalList', () => {
    it('should return minimal list of SOPs', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      const sopData = {
        sop: {
          title: 'Test SOP for Minimal List',
          purpose: 'Test purpose',
          responsibilities: 'Test responsibilities',
          ownerId: mockUser.id,
          approverId: mockUser.id,
          reviewDate: new Date('2024-12-31'),
        },
        sections: [
          {
            sectionType: 'general' as const,
            serial: 1,
            label: 'General Section',
            value: 'General content',
          },
        ],
      };

      const createdSop = await sopCallerRouter.create(sopData);
      expect(createdSop).toBeDefined();

      const minimalList = await sopCallerRouter.minimalList({
        cursor: 0,
        limit: 10,
      });

      expect(minimalList).toBeDefined();
      expect(minimalList.result).toBeDefined();
      expect(minimalList.result.length).toBeGreaterThan(0);
      expect(minimalList.result[0]).toHaveProperty('id');
      expect(minimalList.result[0]).toHaveProperty('title');
      expect(minimalList.result[0]).toHaveProperty('status');
    });

    it('should handle includeLocation parameter correctly', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      const sopData = {
        sop: {
          title: 'Test SOP with Location',
          purpose: 'Test purpose',
          responsibilities: 'Test responsibilities',
          ownerId: mockUser.id,
          approverId: mockUser.id,
          reviewDate: new Date('2024-12-31'),
        },
        sections: [
          {
            sectionType: 'general' as const,
            serial: 1,
            label: 'General Section',
            value: 'General content',
          },
        ],
      };

      const createdSop = await sopCallerRouter.create(sopData);
      expect(createdSop).toBeDefined();

      const minimalList = await sopCallerRouter.minimalList({
        cursor: 0,
        limit: 10,
        includeLocation: true,
      });

      expect(minimalList).toBeDefined();
      expect(minimalList.result).toBeDefined();
      expect(minimalList.result.length).toBeGreaterThan(0);

      const sopWithLocation = minimalList.result.find((sop) => sop.id === createdSop.id);
      expect(sopWithLocation).toBeDefined();
      // Location will be undefined since we don't have a real locationId and the API call fails in test
      expect(sopWithLocation?.location).toBeUndefined();
    });

    it('should not include location data when includeLocation is false', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      const sopData = {
        sop: {
          title: 'Test SOP without Location',
          purpose: 'Test purpose',
          responsibilities: 'Test responsibilities',
          ownerId: mockUser.id,
          approverId: mockUser.id,
          reviewDate: new Date('2024-12-31'),
        },
        sections: [
          {
            sectionType: 'general' as const,
            serial: 1,
            label: 'General Section',
            value: 'General content',
          },
        ],
      };

      const createdSop = await sopCallerRouter.create(sopData);
      expect(createdSop).toBeDefined();

      const minimalList = await sopCallerRouter.minimalList({
        cursor: 0,
        limit: 10,
        includeLocation: false,
      });

      expect(minimalList).toBeDefined();
      expect(minimalList.result).toBeDefined();
      expect(minimalList.result.length).toBeGreaterThan(0);

      const sopWithoutLocation = minimalList.result.find((sop) => sop.id === createdSop.id);
      expect(sopWithoutLocation).toBeDefined();
      expect(sopWithoutLocation?.location).toBeUndefined();
    });

    it('should throw error when user lacks VIEW permission', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // Mock permission check to return false
      vi.mocked(hasPermission).mockReturnValue(false);

      await expect(
        sopCallerRouter.minimalList({
          cursor: 0,
          limit: 10,
        }),
      ).rejects.toThrow();
    });

    it('should handle pagination correctly', async () => {
      const sopCallerRouter = sopRouter.createCaller(mockContext);

      // Create multiple SOPs
      for (let i = 0; i < 3; i++) {
        const sopData = {
          sop: {
            title: `Test SOP ${i + 1}`,
            purpose: 'Test purpose',
            responsibilities: 'Test responsibilities',
            ownerId: mockUser.id,
            approverId: mockUser.id,
            reviewDate: new Date('2024-12-31'),
          },
          sections: [
            {
              sectionType: 'general' as const,
              serial: 1,
              label: 'General Section',
              value: 'General content',
            },
          ],
        };

        await sopCallerRouter.create(sopData);
      }

      const firstPage = await sopCallerRouter.minimalList({
        cursor: 0,
        limit: 2,
      });

      expect(firstPage.result).toHaveLength(2);
      expect(firstPage.nextCursor).toBeDefined();

      const secondPage = await sopCallerRouter.minimalList({
        cursor: firstPage.nextCursor!,
        limit: 2,
      });

      expect(secondPage.result).toHaveLength(1);
    });
  });
});
