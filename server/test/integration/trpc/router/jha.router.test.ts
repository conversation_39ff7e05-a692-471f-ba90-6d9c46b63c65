import { db } from '@server/db';
import { QUEUE_JOB_NAMES } from '@server/queue/job-names';
import { addJobToQueue } from '@server/queue/queue-utils';
import { searchAssetsPublic } from '@server/services/asset.service';
import { searchLocationsPublic } from '@server/services/location.service';
import { getUserPublic, getUsersPublic, hasPermission } from '@server/services/user.service';
import { mockUser, mockTechnicianUser, mockViewOnlyUser } from '@server/test/fixtures/user';
import { createMockContext } from '@server/trpc/router/__tests__/test-utils';
import { controlMeasuresRouter } from '@server/trpc/router/control-measures.router';
import { hazardsRouter } from '@server/trpc/router/hazards.router';
import { jhaRouter } from '@server/trpc/router/jha.router';
import { auditTrail, controlMeasures, hazards, jha, approvalStatusEnum, jhaSteps } from '@shared/schema';
import { CreateJhaFormSchema, UpdateJhaStatusSchema } from '@shared/types/jha.types';
import { randomUUID } from 'crypto';
import { afterAll, afterEach, beforeAll, beforeEach, describe, expect, it, vi } from 'vitest';
import { z } from 'zod';

vi.mock('@server/services/user.service', () => ({
  hasPermission: vi.fn().mockReturnValue(true),
  getUserById: vi.fn(),
  getUsers: vi.fn(),
  getUsersPublic: vi.fn(),
  getUserPublic: vi.fn(),
}));

vi.mock('@server/services/asset.service', () => ({
  searchAssetsPublic: vi.fn(),
}));

vi.mock('@server/services/location.service', () => ({
  searchLocationsPublic: vi.fn(),
}));

vi.mock('@server/queue/queue-utils', () => ({
  addJobToQueue: vi.fn(),
}));

let createdHazardId: string;
let createdControlMeasureId: string;

describe('jha router', () => {
  const mockContext = createMockContext(mockUser) as any;

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(hasPermission).mockReturnValue(true);
    vi.mocked(addJobToQueue).mockClear();
    vi.mocked(getUserPublic).mockResolvedValue({
      id: mockUser.id,
      username: mockUser.username,
      firstName: 'Test',
      lastName: 'User',
      fullName: 'Test User',
      email: '<EMAIL>',
    });
    vi.mocked(getUsersPublic).mockResolvedValue({
      result: [
        {
          id: mockUser.id,
          username: mockUser.username,
          firstName: 'Test',
          lastName: 'User',
          fullName: 'Test User',
          email: '<EMAIL>',
        },
      ],
      nextCursor: undefined,
      noResults: false,
    });
    vi.mocked(searchLocationsPublic).mockResolvedValue({
      result: [
        {
          id: 'location-1',
          name: 'Test Location',
        },
      ],
      nextCursor: undefined,
      noResults: false,
    });
    vi.mocked(searchAssetsPublic).mockResolvedValue({
      result: [
        {
          id: 'asset-1',
          name: 'Test Asset',
          description: 'Test Asset Description',
        },
      ],
      nextCursor: undefined,
      noResults: false,
    });
  });

  afterEach(async () => {
    await db.delete(auditTrail);
    await db.delete(jhaSteps);
    await db.delete(jha);
  });

  beforeAll(async () => {
    const hazardCaller = hazardsRouter.createCaller(mockContext);
    const controlMeasureCaller = controlMeasuresRouter.createCaller(mockContext);
    const createdHazard = await hazardCaller.create({
      name: 'Custom Test Hazard',
      type: 'physical' as const,
    });
    createdHazardId = createdHazard.id;
    const createdControlMeasure = await controlMeasureCaller.create({
      name: 'Custom Test Control Measure',
      type: 'personal_protective_equipment' as const,
    });
    createdControlMeasureId = createdControlMeasure.id;
  });

  afterAll(async () => {
    await db.delete(hazards);
    await db.delete(controlMeasures);
  });

  describe('create', () => {
    it('should create a new JHA with created hazards and control measures', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      const mockInput: z.infer<typeof CreateJhaFormSchema> = {
        jha: {
          title: 'Test JHA',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1: Inspect Area',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 2,
            likelihood: 3,
          },
        ],
      };

      const result = await jhaCallerRouter.create(mockInput);

      expect(result).toBeDefined();
      expect(result?.id).toEqual(expect.any(String));
      expect(result?.slug).toMatch(/^JHA-\d{4}-1\.0$/);
      expect(result?.instanceId).toEqual(expect.any(String));

      // Verify JHA was created
      const createdJha = await db.query.jha.findFirst({
        where: (j, { eq }) => eq(j.id, result!.id),
      });
      expect(createdJha).toBeDefined();
      expect(createdJha?.title).toBe(mockInput.jha.title);
      expect(createdJha?.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
      expect(createdJha?.createdBy).toBe(mockUser.id);
      // Verify highest severity calculation (single step with severity 2 * likelihood 3 = 6)
      expect(createdJha?.highestSeverity).toBe(6);

      // Verify step was created with correct IDs
      const createdStep = await db.query.jhaSteps.findFirst({
        where: (s, { eq }) => eq(s.jhaId, result!.id),
      });
      expect(createdStep).toBeDefined();
      expect(createdStep?.hazardIds).toEqual(expect.arrayContaining([createdHazardId]));
      expect(createdStep?.controlMeasureIds).toEqual(expect.arrayContaining([createdControlMeasureId]));

      // Verify the hazards and control measures used in the step exist
      expect(createdStep?.hazardIds).toHaveLength(1);
      expect(createdStep?.controlMeasureIds).toHaveLength(1);

      // Verify audit trail was created
      const auditLog = await db.query.auditTrail.findFirst({
        where: (a, { eq, and }) => and(eq(a.entityType, 'jha'), eq(a.entityId, result!.id), eq(a.action, 'drafted')),
      });
      expect(auditLog).toBeDefined();
      expect(auditLog?.details).toBeDefined();

      // Verify that our created entities are properly referenced
      expect(createdStep?.hazardIds).toContain(createdHazardId);
      expect(createdStep?.controlMeasureIds).toContain(createdControlMeasureId);
    });

    it('should create a new JHA and a create a revision after it is created', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      const mockInput: z.infer<typeof CreateJhaFormSchema> = {
        jha: {
          title: 'Test JHA',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1: Inspect Area',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 2,
            likelihood: 3,
          },
        ],
      };

      const result = await jhaCallerRouter.create(mockInput);

      expect(result).toBeDefined();
      expect(result?.id).toEqual(expect.any(String));
      expect(result?.slug).toMatch(/^JHA-\d{4}-1\.0$/);
      expect(result?.instanceId).toEqual(expect.any(String));
      expect(result?.version).toEqual('1.0');

      // Create revision with multiple steps and different severities
      const revision = await jhaCallerRouter.create({
        jha: {
          title: 'Test JHA Revision',
          ownerId: mockUser.id,
          approverId: mockUser.id,
          instanceId: result?.instanceId,
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1: Low Risk Task',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 1,
            likelihood: 2,
          },
          {
            serial: 2,
            title: 'Step 2: High Risk Task',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 5,
            likelihood: 4,
          },
          {
            serial: 3,
            title: 'Step 3: Medium Risk Task',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 3,
            likelihood: 3,
          },
        ],
      });

      expect(revision).toBeDefined();
      expect(revision?.id).not.toEqual(result?.id);
      expect(revision?.version).toEqual('2.0');
      expect(revision?.instanceId).toEqual(result?.instanceId);
      expect(revision?.slug).toMatch(/^JHA-\d{4}-2\.0$/);

      // Verify highest severity calculation in revision (max is step 2: severity 5 * likelihood 4 = 20)
      const revisionJha = await db.query.jha.findFirst({
        where: (j, { eq }) => eq(j.id, revision!.id),
      });
      expect(revisionJha).toBeDefined();
      expect(revisionJha?.highestSeverity).toBe(20);
    });

    it('should create a new JHA with bulk created hazards and control measures within the same transaction', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      const mockInput: z.infer<typeof CreateJhaFormSchema> = {
        jha: {
          title: 'Test JHA with Bulk Creation',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1: Handle Chemical Spill',
            hazardIds: [createdHazardId], // Use existing hazard
            controlMeasureIds: [createdControlMeasureId], // Use existing control measure
            hazardsToCreate: [
              {
                name: 'Chemical Exposure Risk',
                type: 'chemical' as const,
              },
              {
                name: 'Slip and Fall Risk',
                type: 'physical' as const,
              },
            ],
            controlMeasuresToCreate: [
              {
                name: 'Chemical-resistant Gloves',
                type: 'personal_protective_equipment' as const,
              },
              {
                name: 'Emergency Evacuation Protocol',
                type: 'administrative_controls' as const,
              },
            ],
            severity: 4,
            likelihood: 3,
          },
          {
            serial: 2,
            title: 'Step 2: Clean Up Area',
            hazardsToCreate: [
              {
                name: 'Contaminated Surface Risk',
                type: 'environmental' as const,
              },
            ],
            controlMeasuresToCreate: [
              {
                name: 'Proper Disposal Procedures',
                type: 'administrative_controls' as const,
              },
            ],
            severity: 2,
            likelihood: 2,
          },
        ],
      };

      const result = await jhaCallerRouter.create(mockInput);

      expect(result).toBeDefined();
      expect(result?.id).toEqual(expect.any(String));
      expect(result?.slug).toMatch(/^JHA-\d{4}-1\.0$/);
      expect(result?.instanceId).toEqual(expect.any(String));

      // Verify JHA was created
      const createdJha = await db.query.jha.findFirst({
        where: (j, { eq }) => eq(j.id, result!.id),
      });
      expect(createdJha).toBeDefined();
      expect(createdJha?.title).toBe(mockInput.jha.title);
      expect(createdJha?.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
      expect(createdJha?.createdBy).toBe(mockUser.id);
      // Verify highest severity calculation (max is step 1: severity 4 * likelihood 3 = 12)
      expect(createdJha?.highestSeverity).toBe(12);

      // Verify steps were created
      const createdSteps = await db.query.jhaSteps.findMany({
        where: (s, { eq }) => eq(s.jhaId, result!.id),
        orderBy: (s, { asc }) => [asc(s.serial)],
      });
      expect(createdSteps).toHaveLength(2);

      // Verify first step has both existing and newly created hazards/control measures
      const firstStep = createdSteps[0];
      expect(firstStep?.hazardIds).toEqual(expect.arrayContaining([createdHazardId]));
      expect(firstStep?.controlMeasureIds).toEqual(expect.arrayContaining([createdControlMeasureId]));

      // Should have 3 hazards total: 1 existing + 2 created
      expect(firstStep?.hazardIds).toHaveLength(3);
      // Should have 3 control measures total: 1 existing + 2 created
      expect(firstStep?.controlMeasureIds).toHaveLength(3);

      // Verify second step has only newly created hazards/control measures
      const secondStep = createdSteps[1];
      expect(secondStep?.hazardIds).toHaveLength(1);
      expect(secondStep?.controlMeasureIds).toHaveLength(1);

      // Verify that the new hazards were actually created in the database
      const allHazards = await db.query.hazards.findMany({
        where: (h, { eq }) => eq(h.upkeepCompanyId, mockUser.upkeepCompanyId),
      });

      const newHazardNames = ['Chemical Exposure Risk', 'Slip and Fall Risk', 'Contaminated Surface Risk'];
      const createdHazardNames = allHazards.filter((h) => newHazardNames.includes(h.name)).map((h) => h.name);

      expect(createdHazardNames).toEqual(expect.arrayContaining(newHazardNames));

      // Verify that the new control measures were actually created in the database
      const allControlMeasures = await db.query.controlMeasures.findMany({
        where: (cm, { eq }) => eq(cm.upkeepCompanyId, mockUser.upkeepCompanyId),
      });

      const newControlMeasureNames = [
        'Chemical-resistant Gloves',
        'Emergency Evacuation Protocol',
        'Proper Disposal Procedures',
      ];
      const createdControlMeasureNames = allControlMeasures
        .filter((cm) => newControlMeasureNames.includes(cm.name))
        .map((cm) => cm.name);

      expect(createdControlMeasureNames).toEqual(expect.arrayContaining(newControlMeasureNames));

      // Verify audit trail was created
      const auditLog = await db.query.auditTrail.findFirst({
        where: (a, { eq, and }) => and(eq(a.entityType, 'jha'), eq(a.entityId, result!.id), eq(a.action, 'drafted')),
      });
      expect(auditLog).toBeDefined();
      expect(auditLog?.details).toBeDefined();

      // Verify the audit trail contains the processed JHA result (with mapped IDs)
      const auditDetails = JSON.parse(auditLog!.details!);
      expect(auditDetails.steps).toHaveLength(2);
      expect(auditDetails.steps[0].hazardIds).toHaveLength(3);
      expect(auditDetails.steps[0].controlMeasureIds).toHaveLength(3);
      expect(auditDetails.steps[1].hazardIds).toHaveLength(1);
      expect(auditDetails.steps[1].controlMeasureIds).toHaveLength(1);
    });
  });

  describe('getByInstanceId', () => {
    it('should retrieve a JHA by instance ID with related user information and steps', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // First create a JHA with steps
      const mockInput: z.infer<typeof CreateJhaFormSchema> = {
        jha: {
          title: 'Test JHA for Retrieval',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1: Safety Check',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 3,
            likelihood: 2,
          },
          {
            serial: 2,
            title: 'Step 2: Equipment Setup',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 2,
            likelihood: 1,
          },
        ],
      };

      const createdJha = await jhaCallerRouter.create(mockInput);
      expect(createdJha).toBeDefined();
      expect(createdJha?.instanceId).toEqual(expect.any(String));

      // Now test retrieving the JHA by instance ID
      const retrievedJha = await jhaCallerRouter.getByInstanceId({ id: createdJha!.instanceId! });

      // Verify the JHA data
      expect(retrievedJha).toBeDefined();
      expect(retrievedJha.id).toBe(createdJha!.id);
      expect(retrievedJha.instanceId).toBe(createdJha!.instanceId);
      expect(retrievedJha.title).toBe(mockInput.jha.title);
      expect(retrievedJha.ownerId).toBe(mockUser.id);
      expect(retrievedJha.approverId).toBe(mockUser.id);
      expect(retrievedJha.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
      expect(retrievedJha.highestSeverity).toBe(6); // Max is step 1: severity 3 * likelihood 2 = 6

      // Verify the steps are included
      expect(retrievedJha.steps).toBeDefined();
      expect(retrievedJha.steps).toHaveLength(2);

      const firstStep = retrievedJha.steps.find((step) => step.serial === 1);
      const secondStep = retrievedJha.steps.find((step) => step.serial === 2);

      expect(firstStep).toBeDefined();
      expect(firstStep?.title).toBe('Step 1: Safety Check');
      expect(firstStep?.severity).toBe(3);
      expect(firstStep?.likelihood).toBe(2);
      expect(firstStep?.hazardIds).toEqual(expect.arrayContaining([createdHazardId]));
      expect(firstStep?.controlMeasureIds).toEqual(expect.arrayContaining([createdControlMeasureId]));

      expect(secondStep).toBeDefined();
      expect(secondStep?.title).toBe('Step 2: Equipment Setup');
      expect(secondStep?.severity).toBe(2);
      expect(secondStep?.likelihood).toBe(1);

      // Verify the related user information is included
      expect(retrievedJha.owner).toBeDefined();
      expect(retrievedJha.owner?.id).toBe(mockUser.id);
      expect(retrievedJha.owner?.firstName).toBe('Test');
      expect(retrievedJha.owner?.lastName).toBe('User');
      expect(retrievedJha.owner?.email).toBe('<EMAIL>');

      expect(retrievedJha.approver).toBeDefined();
      expect(retrievedJha.approver?.id).toBe(mockUser.id);

      expect(retrievedJha.reviewer).toBeDefined();
      expect(retrievedJha.reviewer?.id).toBe(mockUser.id);

      // Verify getUserPublic was called for each user role
      expect(getUserPublic).toHaveBeenCalledTimes(3);
      expect(getUserPublic).toHaveBeenCalledWith({
        id: mockUser.id,
        upkeepCompanyId: mockUser.upkeepCompanyId,
      });
    });

    it('should throw NOT_FOUND error when JHA does not exist', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      await expect(jhaCallerRouter.getByInstanceId({ id: randomUUID() })).rejects.toThrow('JHA not found');
    });

    it('should throw error when user lacks VIEW permission', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Mock permission check to return false
      vi.mocked(hasPermission).mockReturnValue(false);

      await expect(jhaCallerRouter.getByInstanceId({ id: 'some-id' })).rejects.toThrow();
    });

    it('should only return steps for the specific JHA when multiple JHAs exist', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Mock permission check to return true for this test
      vi.mocked(hasPermission).mockReturnValue(true);

      // Create first JHA with its steps
      const jhaAInstanceId = randomUUID();
      const [createdJhaA] = await db
        .insert(jha)
        .values({
          instanceId: jhaAInstanceId,
          title: 'JHA A',
          version: '1.0',
          status: 'draft',
          upkeepCompanyId: mockUser.upkeepCompanyId,
          ownerId: mockUser.id,
          approverId: mockUser.id,
          createdBy: mockUser.id,
        })
        .returning();

      // Create steps for JHA A
      await db.insert(jhaSteps).values([
        {
          jhaId: createdJhaA.id,
          title: 'JHA A - Step 1',
          serial: 1,
          severity: 3,
          likelihood: 2,
          upkeepCompanyId: mockUser.upkeepCompanyId,
          hazardIds: [],
          controlMeasureIds: [],
          createdBy: mockUser.id,
        },
        {
          jhaId: createdJhaA.id,
          title: 'JHA A - Step 2',
          serial: 2,
          severity: 4,
          likelihood: 3,
          upkeepCompanyId: mockUser.upkeepCompanyId,
          hazardIds: [],
          controlMeasureIds: [],
          createdBy: mockUser.id,
        },
      ]);

      // Create second JHA with its steps
      const jhaBInstanceId = randomUUID();
      const [createdJhaB] = await db
        .insert(jha)
        .values({
          instanceId: jhaBInstanceId,
          title: 'JHA B',
          version: '1.0',
          status: 'draft',
          upkeepCompanyId: mockUser.upkeepCompanyId,
          ownerId: mockUser.id,
          approverId: mockUser.id,
          createdBy: mockUser.id,
        })
        .returning();

      // Create steps for JHA B
      await db.insert(jhaSteps).values([
        {
          jhaId: createdJhaB.id,
          title: 'JHA B - Step 1',
          serial: 1,
          severity: 2,
          likelihood: 1,
          upkeepCompanyId: mockUser.upkeepCompanyId,
          hazardIds: [],
          controlMeasureIds: [],
          createdBy: mockUser.id,
        },
        {
          jhaId: createdJhaB.id,
          title: 'JHA B - Step 2',
          serial: 2,
          severity: 5,
          likelihood: 4,
          upkeepCompanyId: mockUser.upkeepCompanyId,
          hazardIds: [],
          controlMeasureIds: [],
          createdBy: mockUser.id,
        },
      ]);

      // Retrieve JHA A by instanceId
      const retrievedJhaA = await jhaCallerRouter.getByInstanceId({ id: jhaAInstanceId });

      // Verify only JHA A data is returned
      expect(retrievedJhaA.instanceId).toBe(jhaAInstanceId);
      expect(retrievedJhaA.title).toBe('JHA A');

      // Verify only steps from JHA A are returned (not from JHA B)
      expect(retrievedJhaA.steps).toHaveLength(2);

      const stepTitles = retrievedJhaA.steps.map((step) => step.title);
      expect(stepTitles).toEqual(['JHA A - Step 1', 'JHA A - Step 2']);

      // Ensure no steps from JHA B are included
      expect(stepTitles).not.toContain('JHA B - Step 1');
      expect(stepTitles).not.toContain('JHA B - Step 2');

      // Verify step details belong to JHA A
      expect(retrievedJhaA.steps[0].jhaId).toBe(createdJhaA.id);
      expect(retrievedJhaA.steps[1].jhaId).toBe(createdJhaA.id);
    });

    it('should return the latest version when multiple instances exist with the same instanceId', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create original JHA (version 1.0)
      const originalInput: z.infer<typeof CreateJhaFormSchema> = {
        jha: {
          title: 'Original Safety Procedure',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1: Basic Check',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 2,
            likelihood: 2,
          },
        ],
      };

      const originalJha = await jhaCallerRouter.create(originalInput);
      expect(originalJha?.version).toBe('1.0');

      // Wait a small amount to ensure different timestamps
      await new Promise((resolve) => setTimeout(resolve, 10));

      // Create first revision (version 2.0)
      const firstRevisionInput: z.infer<typeof CreateJhaFormSchema> = {
        jha: {
          title: 'Updated Safety Procedure',
          ownerId: mockUser.id,
          approverId: mockUser.id,
          instanceId: originalJha?.instanceId, // Same instanceId
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1: Enhanced Check',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 3,
            likelihood: 2,
          },
          {
            serial: 2,
            title: 'Step 2: Additional Safety Measure',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 2,
            likelihood: 1,
          },
        ],
      };

      const firstRevision = await jhaCallerRouter.create(firstRevisionInput);
      expect(firstRevision?.version).toBe('2.0');
      expect(firstRevision?.instanceId).toBe(originalJha?.instanceId);

      // Create second revision (version 3.0)
      const secondRevisionInput: z.infer<typeof CreateJhaFormSchema> = {
        jha: {
          title: 'Final Safety Procedure',
          ownerId: mockUser.id,
          approverId: mockUser.id,
          instanceId: originalJha?.instanceId, // Same instanceId
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1: Comprehensive Check',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 4,
            likelihood: 2,
          },
          {
            serial: 2,
            title: 'Step 2: Advanced Safety Measure',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 3,
            likelihood: 1,
          },
          {
            serial: 3,
            title: 'Step 3: Final Verification',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 2,
            likelihood: 1,
          },
        ],
      };

      const secondRevision = await jhaCallerRouter.create(secondRevisionInput);
      expect(secondRevision?.version).toBe('3.0');
      expect(secondRevision?.instanceId).toBe(originalJha?.instanceId);

      // Now test getByInstanceId - it should return the latest version (3.0)
      const retrievedJha = await jhaCallerRouter.getByInstanceId({ id: originalJha!.instanceId! });

      // Verify we got the latest version (3.0)
      expect(retrievedJha).toBeDefined();
      expect(retrievedJha.id).toBe(secondRevision?.id); // Should be the latest revision's ID
      expect(retrievedJha.instanceId).toBe(originalJha?.instanceId);
      expect(retrievedJha.title).toBe('Final Safety Procedure'); // Title from latest revision
      expect(retrievedJha.highestSeverity).toBe(8); // Max is step 1: severity 4 * likelihood 2 = 8
      expect(retrievedJha.steps).toHaveLength(3); // Latest revision has 3 steps
      expect(retrievedJha.version).toBe('3.0');

      // Verify the steps are from the latest revision
      const step1 = retrievedJha.steps.find((step) => step.serial === 1);
      const step3 = retrievedJha.steps.find((step) => step.serial === 3);

      expect(step1?.title).toBe('Step 1: Comprehensive Check');
      expect(step3?.title).toBe('Step 3: Final Verification');
      expect(step3).toBeDefined(); // Only the latest revision has step 3

      // Verify that we're getting user information for the latest revision
      expect(retrievedJha.owner).toBeDefined();
      expect(retrievedJha.owner?.id).toBe(mockUser.id);
      expect(retrievedJha.approver).toBeDefined();
      expect(retrievedJha.reviewer).toBeDefined();

      // Verify getUserPublic was called for the latest revision's users
      expect(getUserPublic).toHaveBeenCalledWith({
        id: mockUser.id,
        upkeepCompanyId: mockUser.upkeepCompanyId,
      });
    });
  });

  describe('list', () => {
    it('should list JHAs with default pagination and related data', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create a JHA for testing
      const mockInput: z.infer<typeof CreateJhaFormSchema> = {
        jha: {
          title: 'Test JHA for List',
          ownerId: mockUser.id,
          approverId: mockUser.id,
          locationId: 'location-1',
          assetIds: ['asset-1'],
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1: Safety Check',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 3,
            likelihood: 2,
          },
          {
            serial: 2,
            title: 'Step 2: Equipment Setup',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 4,
            likelihood: 3,
          },
        ],
      };

      await jhaCallerRouter.create(mockInput);

      // Test the list endpoint
      const result = await jhaCallerRouter.list({});

      expect(result).toBeDefined();
      expect(result.result).toHaveLength(1);
      expect(result.nextCursor).toBeUndefined();
      expect(result.noResults).toBe(false);

      const jhaItem = result.result[0];
      expect(jhaItem.instanceId).toEqual(expect.any(String));
      expect(jhaItem.slug).toEqual(expect.any(String));
      expect(jhaItem.status).toBe('draft');
      expect(jhaItem.highestSeverity).toBe(12); // Max is step 2: severity 4 * likelihood 3 = 12
      expect(jhaItem.stepCount).toBe(2);

      // Verify owner data is attached
      expect(jhaItem.owner).toBeDefined();
      expect(jhaItem.owner?.id).toBe(mockUser.id);
      expect(jhaItem.owner?.fullName).toBe('Test User');

      // Verify location data is attached
      expect(jhaItem.location).toBeDefined();
      expect(jhaItem.location?.id).toBe('location-1');
      expect(jhaItem.location?.name).toBe('Test Location');

      // Verify assets data is attached
      expect(jhaItem.assets).toBeDefined();
      expect(jhaItem.assets).toHaveLength(1);
      expect(jhaItem.assets[0]?.id).toBe('asset-1');
      expect(jhaItem.assets[0]?.name).toBe('Test Asset');

      // Verify service calls were made
      expect(getUsersPublic).toHaveBeenCalledWith({
        upkeepCompanyId: mockUser.upkeepCompanyId,
        objectId: [mockUser.id],
        limit: 100,
      });
      expect(searchLocationsPublic).toHaveBeenCalledWith({
        upkeepCompanyId: mockUser.upkeepCompanyId,
        objectId: ['location-1'],
        limit: 100,
      });
      expect(searchAssetsPublic).toHaveBeenCalledWith({
        upkeepCompanyId: mockUser.upkeepCompanyId,
        objectId: ['asset-1'],
        limit: 100,
      });
    });

    it('should filter JHAs by status', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create a JHA
      const mockInput: z.infer<typeof CreateJhaFormSchema> = {
        jha: {
          title: 'Draft JHA',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 1,
            likelihood: 1,
          },
        ],
      };

      await jhaCallerRouter.create(mockInput);

      // Test filtering by status
      const result = await jhaCallerRouter.list({
        status: ['draft'],
      });

      expect(result.result).toHaveLength(1);
      expect(result.result[0]?.status).toBe('draft');
    });

    it('should filter JHAs by risk level', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create JHAs with different risk levels
      const lowRiskInput: z.infer<typeof CreateJhaFormSchema> = {
        jha: {
          title: 'Low Risk JHA',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: 'Low Risk Step',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 1,
            likelihood: 2, // severity * likelihood = 2 (low risk)
          },
        ],
      };

      const highRiskInput: z.infer<typeof CreateJhaFormSchema> = {
        jha: {
          title: 'High Risk JHA',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: 'High Risk Step',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 20, // severity = 20, which should put this in high range (>=15)
            likelihood: 1,
          },
        ],
      };

      await jhaCallerRouter.create(lowRiskInput);
      await jhaCallerRouter.create(highRiskInput);

      // Test filtering by high risk
      const highRiskResult = await jhaCallerRouter.list({
        riskLevel: 'high',
      });

      expect(highRiskResult.result).toHaveLength(1);
      expect(highRiskResult.result[0]?.highestSeverity).toBeGreaterThanOrEqual(15);

      // Test filtering by low risk
      const lowRiskResult = await jhaCallerRouter.list({
        riskLevel: 'low',
      });

      expect(lowRiskResult.result).toHaveLength(1);
      expect(lowRiskResult.result[0]?.highestSeverity).toBeLessThan(5);
    });

    it('should sort JHAs by title A-Z', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create multiple JHAs with different titles
      const jhaInputA: z.infer<typeof CreateJhaFormSchema> = {
        jha: {
          title: 'Alpha JHA',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 1,
            likelihood: 1,
          },
        ],
      };

      const jhaInputZ: z.infer<typeof CreateJhaFormSchema> = {
        jha: {
          title: 'Zulu JHA',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 1,
            likelihood: 1,
          },
        ],
      };

      await jhaCallerRouter.create(jhaInputZ); // Create Z first
      await jhaCallerRouter.create(jhaInputA); // Create A second

      // Test sorting by title ascending
      const result = await jhaCallerRouter.list({
        sortBy: 'title',
        sortOrder: 'asc',
      });

      expect(result.result).toHaveLength(2);
      expect(result.result[0]?.title).toContain('Alpha');
      expect(result.result[1]?.title).toContain('Zulu');
    });

    it('should sort JHAs by highest risk', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create JHAs with different risk levels
      const lowRiskInput: z.infer<typeof CreateJhaFormSchema> = {
        jha: {
          title: 'Low Risk JHA',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: 'Low Risk Step',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 2,
            likelihood: 1,
          },
        ],
      };

      const highRiskInput: z.infer<typeof CreateJhaFormSchema> = {
        jha: {
          title: 'High Risk JHA',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: 'High Risk Step',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 5,
            likelihood: 4,
          },
        ],
      };

      await jhaCallerRouter.create(lowRiskInput); // Create low risk first
      await jhaCallerRouter.create(highRiskInput); // Create high risk second

      // Test sorting by highest severity descending
      const result = await jhaCallerRouter.list({
        sortBy: 'highestSeverity',
        sortOrder: 'desc',
      });

      expect(result.result).toHaveLength(2);
      expect(result.result[0]?.highestSeverity).toBeGreaterThan(result.result[1]?.highestSeverity);
    });

    it('should handle pagination correctly', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create multiple JHAs
      for (let i = 1; i <= 5; i++) {
        const mockInput: z.infer<typeof CreateJhaFormSchema> = {
          jha: {
            title: `JHA ${i}`,
            ownerId: mockUser.id,
            approverId: mockUser.id,
          },
          steps: [
            {
              serial: 1,
              title: 'Step 1',
              hazardIds: [createdHazardId],
              controlMeasureIds: [createdControlMeasureId],
              severity: 1,
              likelihood: 1,
            },
          ],
        };
        await jhaCallerRouter.create(mockInput);
      }

      // Test first page with limit 3
      const firstPage = await jhaCallerRouter.list({
        limit: 3,
        cursor: 0,
      });

      expect(firstPage.result).toHaveLength(3);
      expect(firstPage.nextCursor).toBe(3);

      // Test second page
      const secondPage = await jhaCallerRouter.list({
        limit: 3,
        cursor: 3,
      });

      expect(secondPage.result).toHaveLength(2); // Only 2 remaining
      expect(secondPage.nextCursor).toBeUndefined();
    });

    it('should throw error when user lacks VIEW permission', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Mock permission check to return false
      vi.mocked(hasPermission).mockReturnValue(false);

      await expect(jhaCallerRouter.list({})).rejects.toThrow();
    });
  });

  describe('updateStatus', () => {
    it('should update JHA status to approved and trigger JHA_APPROVED_NOTIFICATION', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create a JHA first
      const mockInput: z.infer<typeof CreateJhaFormSchema> = {
        jha: {
          title: 'JHA for Approval',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1: Safety Check',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 2,
            likelihood: 2,
          },
        ],
      };

      const createdJha = await jhaCallerRouter.create(mockInput);
      expect(createdJha).toBeDefined();

      // Clear any previous mock calls from creation
      vi.mocked(addJobToQueue).mockClear();

      // Update status to approved
      const updateStatusInput: z.infer<typeof UpdateJhaStatusSchema> = {
        id: createdJha!.id,
        status: approvalStatusEnum.enumValues[2], // 'approved'
      };

      const result = await jhaCallerRouter.updateStatus(updateStatusInput);

      expect(result).toBeDefined();
      expect(result.status).toBe('approved');

      // Verify that addJobToQueue was called with correct job name and payload
      expect(addJobToQueue).toHaveBeenCalledTimes(1);
      expect(addJobToQueue).toHaveBeenCalledWith(
        QUEUE_JOB_NAMES.JHA_APPROVED_NOTIFICATION,
        expect.objectContaining({
          updatedJha: expect.objectContaining({
            id: createdJha!.id,
            status: 'approved',
          }),
          user: mockUser,
          headers: expect.any(Object),
        }),
      );
    });

    it('should update JHA status to rejected (draft) and trigger JHA_REJECTED_NOTIFICATION with rejection reason', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create a JHA first and set it to review status
      const mockInput: z.infer<typeof CreateJhaFormSchema> = {
        jha: {
          title: 'JHA for Rejection',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1: Safety Check',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 3,
            likelihood: 2,
          },
        ],
      };

      const createdJha = await jhaCallerRouter.create(mockInput);
      expect(createdJha).toBeDefined();

      // Clear any previous mock calls from creation
      vi.mocked(addJobToQueue).mockClear();

      // Update status to rejected (draft) with rejection reason
      const rejectionReason = 'Insufficient safety measures identified for high-risk activities';
      const updateStatusInput: z.infer<typeof UpdateJhaStatusSchema> = {
        id: createdJha!.id,
        status: approvalStatusEnum.enumValues[0], // 'draft' (rejection)
        rejectionReason,
      };

      const result = await jhaCallerRouter.updateStatus(updateStatusInput);

      expect(result).toBeDefined();
      expect(result.status).toBe('draft');

      // Verify that addJobToQueue was called with correct job name and payload including rejection reason
      expect(addJobToQueue).toHaveBeenCalledTimes(1);
      expect(addJobToQueue).toHaveBeenCalledWith(
        QUEUE_JOB_NAMES.JHA_REJECTED_NOTIFICATION,
        expect.objectContaining({
          updatedJha: expect.objectContaining({
            id: createdJha!.id,
            status: 'draft',
          }),
          user: mockUser,
          headers: expect.any(Object),
          rejectionReason,
        }),
      );
    });

    it('should update JHA status to review and trigger JHA_REVISED_NOTIFICATION', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create a JHA first
      const mockInput: z.infer<typeof CreateJhaFormSchema> = {
        jha: {
          title: 'JHA for Review',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1: Equipment Inspection',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 2,
            likelihood: 3,
          },
        ],
      };

      const createdJha = await jhaCallerRouter.create(mockInput);
      expect(createdJha).toBeDefined();

      // Clear any previous mock calls from creation
      vi.mocked(addJobToQueue).mockClear();

      // Update status to review
      const updateStatusInput: z.infer<typeof UpdateJhaStatusSchema> = {
        id: createdJha!.id,
        status: approvalStatusEnum.enumValues[1], // 'review'
      };

      const result = await jhaCallerRouter.updateStatus(updateStatusInput);

      expect(result).toBeDefined();
      expect(result.status).toBe('review');

      // Verify that addJobToQueue was called with correct job name and payload
      expect(addJobToQueue).toHaveBeenCalledTimes(1);
      expect(addJobToQueue).toHaveBeenCalledWith(
        QUEUE_JOB_NAMES.JHA_REVISED_NOTIFICATION,
        expect.objectContaining({
          updatedJha: expect.objectContaining({
            id: createdJha!.id,
            status: 'review',
          }),
          user: mockUser,
          headers: expect.any(Object),
        }),
      );
    });

    it('should validate that rejection reason is required when rejecting (status = draft)', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create a JHA first
      const mockInput: z.infer<typeof CreateJhaFormSchema> = {
        jha: {
          title: 'JHA for Validation Test',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1: Safety Check',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 1,
            likelihood: 1,
          },
        ],
      };

      const createdJha = await jhaCallerRouter.create(mockInput);
      expect(createdJha).toBeDefined();

      // Try to reject without providing rejection reason
      const updateStatusInput = {
        id: createdJha!.id,
        status: approvalStatusEnum.enumValues[0], // 'draft' (rejection)
        rejectionReason: '', // Empty string should fail validation
      };

      // This should fail validation
      await expect(jhaCallerRouter.updateStatus(updateStatusInput as any)).rejects.toThrow();

      // Verify no queue job was added
      expect(addJobToQueue).not.toHaveBeenCalled();
    });

    it('should throw error when JHA does not exist', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Use a valid CUID2 format that doesn't exist in the database
      const nonExistentId = 'clyabc123def456ghi789jkl';
      const updateStatusInput: z.infer<typeof UpdateJhaStatusSchema> = {
        id: nonExistentId,
        status: approvalStatusEnum.enumValues[2], // 'approved'
      };

      await expect(jhaCallerRouter.updateStatus(updateStatusInput)).rejects.toThrow('Failed to update JHA status');

      // Verify no queue job was added
      expect(addJobToQueue).not.toHaveBeenCalled();
    });

    it('should throw error when user lacks EDIT permission', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create a JHA first
      const mockInput: z.infer<typeof CreateJhaFormSchema> = {
        jha: {
          title: 'JHA for Permission Test',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1: Safety Check',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 1,
            likelihood: 1,
          },
        ],
      };

      const createdJha = await jhaCallerRouter.create(mockInput);

      // Mock permission check to return false
      vi.mocked(hasPermission).mockReturnValue(false);

      const updateStatusInput: z.infer<typeof UpdateJhaStatusSchema> = {
        id: createdJha!.id,
        status: approvalStatusEnum.enumValues[2], // 'approved'
      };

      await expect(jhaCallerRouter.updateStatus(updateStatusInput)).rejects.toThrow();

      // Verify no queue job was added
      expect(addJobToQueue).not.toHaveBeenCalled();
    });

    it('should handle status updates that do not trigger notifications', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create a JHA first
      const mockInput: z.infer<typeof CreateJhaFormSchema> = {
        jha: {
          title: 'JHA for No Notification Test',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1: Safety Check',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 1,
            likelihood: 1,
          },
        ],
      };

      const createdJha = await jhaCallerRouter.create(mockInput);
      expect(createdJha).toBeDefined();

      // Clear any previous mock calls from creation
      vi.mocked(addJobToQueue).mockClear();

      // Update status to the same status (should still work but might not trigger notification)
      const updateStatusInput: z.infer<typeof UpdateJhaStatusSchema> = {
        id: createdJha!.id,
        status: approvalStatusEnum.enumValues[0], // 'draft' - same as creation status
        rejectionReason: 'Test rejection reason',
      };

      const result = await jhaCallerRouter.updateStatus(updateStatusInput);

      expect(result).toBeDefined();
      expect(result.status).toBe('draft');

      // This should still trigger a notification for draft status (rejection)
      expect(addJobToQueue).toHaveBeenCalledTimes(1);
      expect(addJobToQueue).toHaveBeenCalledWith(
        QUEUE_JOB_NAMES.JHA_REJECTED_NOTIFICATION,
        expect.objectContaining({
          rejectionReason: 'Test rejection reason',
        }),
      );
    });

    it('should verify notification payload contains all required fields', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create a JHA first
      const mockInput: z.infer<typeof CreateJhaFormSchema> = {
        jha: {
          title: 'JHA for Payload Verification',
          ownerId: mockUser.id,
          approverId: mockUser.id,
          locationId: 'location-1',
          assetIds: ['asset-1'],
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1: Comprehensive Check',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 4,
            likelihood: 3,
          },
        ],
      };

      const createdJha = await jhaCallerRouter.create(mockInput);
      expect(createdJha).toBeDefined();

      // Clear any previous mock calls from creation
      vi.mocked(addJobToQueue).mockClear();

      // Update status to approved
      const updateStatusInput: z.infer<typeof UpdateJhaStatusSchema> = {
        id: createdJha!.id,
        status: approvalStatusEnum.enumValues[2], // 'approved'
      };

      const result = await jhaCallerRouter.updateStatus(updateStatusInput);

      expect(result).toBeDefined();

      // Verify that addJobToQueue was called with comprehensive payload
      expect(addJobToQueue).toHaveBeenCalledTimes(1);

      const [jobName, payload] = vi.mocked(addJobToQueue).mock.calls[0];

      expect(jobName).toBe(QUEUE_JOB_NAMES.JHA_APPROVED_NOTIFICATION);
      expect(payload).toEqual(
        expect.objectContaining({
          updatedJha: expect.objectContaining({
            id: createdJha!.id,
            title: 'JHA for Payload Verification',
            status: 'approved',
            ownerId: mockUser.id,
            approverId: mockUser.id,
            locationId: 'location-1',
            assetIds: ['asset-1'],
            upkeepCompanyId: mockUser.upkeepCompanyId,
          }),
          user: expect.objectContaining({
            id: mockUser.id,
            upkeepCompanyId: mockUser.upkeepCompanyId,
          }),
          headers: expect.any(Object),
        }),
      );
    });
  });

  describe('toggleArchive', () => {
    it('should archive a JHA when it is not archived', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create a JHA
      const mockInput: z.infer<typeof CreateJhaFormSchema> = {
        jha: {
          title: 'JHA to Archive',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1: Safety Check',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 2,
            likelihood: 2,
          },
        ],
      };

      const createdJha = await jhaCallerRouter.create(mockInput);
      expect(createdJha).toBeDefined();
      expect(createdJha?.id).toEqual(expect.any(String));

      // Archive the JHA
      const archivedJha = await jhaCallerRouter.toggleArchive({ id: createdJha.instanceId! });

      expect(archivedJha).toBeDefined();
      expect(archivedJha.id).toBe(createdJha!.id);
      expect(archivedJha.archivedAt).toBeDefined();
      expect(archivedJha.archivedAt).toBeInstanceOf(Date);
      expect(archivedJha.updatedAt).toBeDefined();
      expect(archivedJha.updatedAt).toBeInstanceOf(Date);

      // Verify audit trail was created
      const auditLog = await db.query.auditTrail.findFirst({
        where: (a, { eq, and }) =>
          and(eq(a.entityType, 'jha'), eq(a.entityId, createdJha!.id), eq(a.action, 'archived')),
      });
      expect(auditLog).toBeDefined();
      expect(auditLog?.userId).toBe(mockUser.id);
      expect(auditLog?.details).toBeDefined();

      const auditDetails = JSON.parse(auditLog!.details!);
      expect(auditDetails.archivedAt).toBeDefined();
      expect(auditDetails.updatedAt).toBeDefined();
    });

    it('should unarchive a JHA when it is archived', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create a JHA
      const mockInput: z.infer<typeof CreateJhaFormSchema> = {
        jha: {
          title: 'JHA to Unarchive',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1: Safety Check',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 2,
            likelihood: 2,
          },
        ],
      };

      const createdJha = await jhaCallerRouter.create(mockInput);
      expect(createdJha).toBeDefined();

      // First archive the JHA
      const archivedJha = await jhaCallerRouter.toggleArchive({ id: createdJha.instanceId! });
      expect(archivedJha.archivedAt).toBeDefined();

      // Now unarchive the JHA
      const unarchivedJha = await jhaCallerRouter.toggleArchive({ id: createdJha.instanceId! });

      expect(unarchivedJha).toBeDefined();
      expect(unarchivedJha.id).toBe(createdJha!.id);
      expect(unarchivedJha.archivedAt).toBeNull();
      expect(unarchivedJha.updatedAt).toBeDefined();
      expect(unarchivedJha.updatedAt).toBeInstanceOf(Date);

      // Verify that updatedAt was changed from the archive operation
      expect(unarchivedJha.updatedAt!.getTime()).toBeGreaterThan(archivedJha.updatedAt!.getTime());

      // Verify audit trail was created for unarchive
      const auditLog = await db.query.auditTrail.findFirst({
        where: (a, { eq, and }) =>
          and(eq(a.entityType, 'jha'), eq(a.entityId, createdJha!.id), eq(a.action, 'unarchived')),
      });
      expect(auditLog).toBeDefined();
      expect(auditLog?.userId).toBe(mockUser.id);
      expect(auditLog?.details).toBeDefined();

      const auditDetails = JSON.parse(auditLog!.details!);
      expect(auditDetails.archivedAt).toBeNull();
      expect(auditDetails.updatedAt).toBeDefined();
    });

    it('should throw NOT_FOUND error when JHA does not exist', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Generate a random CUID2 that doesn't exist
      const nonExistentId = randomUUID();

      await expect(jhaCallerRouter.toggleArchive({ id: nonExistentId })).rejects.toThrow(
        'Failed to archive/unarchive JHA',
      );
    });

    it('should throw error when user lacks EDIT permission', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create a JHA first
      const mockInput: z.infer<typeof CreateJhaFormSchema> = {
        jha: {
          title: 'JHA for Permission Test',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1: Safety Check',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 1,
            likelihood: 1,
          },
        ],
      };

      const createdJha = await jhaCallerRouter.create(mockInput);

      // Mock permission check to return false
      vi.mocked(hasPermission).mockReturnValue(false);

      await expect(jhaCallerRouter.toggleArchive({ id: createdJha!.id })).rejects.toThrow();
    });

    it('should not affect JHAs from different companies', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create a JHA
      const mockInput: z.infer<typeof CreateJhaFormSchema> = {
        jha: {
          title: 'Company-specific JHA',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1: Safety Check',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 1,
            likelihood: 1,
          },
        ],
      };

      const createdJha = await jhaCallerRouter.create(mockInput);

      // Create a context with a different company ID
      const differentCompanyContext = createMockContext({
        ...mockUser,
        upkeepCompanyId: 'different-company-id',
      }) as any;

      const differentCompanyCallerRouter = jhaRouter.createCaller(differentCompanyContext);

      // Try to archive the JHA from a different company
      await expect(differentCompanyCallerRouter.toggleArchive({ id: createdJha.instanceId! })).rejects.toThrow(
        'Failed to archive/unarchive JHA',
      );
    });
  });

  describe('minimalList', () => {
    it('should return minimal list of JHAs with basic pagination', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create multiple JHAs for testing
      const jhaInputs = Array.from({ length: 15 }, (_, i) => ({
        jha: {
          title: `Minimal Test JHA ${i + 1}`,
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: `Step 1 for JHA ${i + 1}`,
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: (i % 5) + 1, // Vary severity 1-5
            likelihood: ((i + 1) % 3) + 1, // Vary likelihood 1-3
          },
        ],
      }));

      // Create all JHAs
      for (const input of jhaInputs) {
        await jhaCallerRouter.create(input as any);
      }

      // Test minimal list with default parameters
      const result = await jhaCallerRouter.minimalList({});

      expect(result).toBeDefined();
      expect(result.result).toBeDefined();
      expect(Array.isArray(result.result)).toBe(true);
      expect(result.result.length).toBeGreaterThan(0);
      expect(result.result.length).toBeLessThanOrEqual(10); // Default limit

      // Verify pagination properties
      expect(typeof result.nextCursor).toBeDefined();
      expect(typeof result.noResults).toBe('boolean');

      // Verify basic JHA properties are present
      const firstJha = result.result[0];
      expect(firstJha).toHaveProperty('id');
      expect(firstJha).toHaveProperty('instanceId');
      expect(firstJha).toHaveProperty('title');
      expect(firstJha).toHaveProperty('status');
      expect(firstJha).toHaveProperty('highestSeverity');
      expect(firstJha).toHaveProperty('stepCount');
      expect(firstJha).toHaveProperty('ownerId');
      expect(firstJha.ownerId).toBe(mockUser.id);

      // Since these JHAs don't have locations, searchLocationsPublic should NOT be called
      expect(searchLocationsPublic).not.toHaveBeenCalled();
    });

    it('should return JHAs with location information when locationId is provided', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create JHA with location
      const jhaWithLocationInput = {
        jha: {
          title: 'JHA with Location',
          ownerId: mockUser.id,
          approverId: mockUser.id,
          locationId: 'location-1', // This matches our mock location
        },
        steps: [
          {
            serial: 1,
            title: 'Step with location',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 2,
            likelihood: 2,
          },
        ],
      };

      // Create JHA without location
      const jhaWithoutLocationInput = {
        jha: {
          title: 'JHA without Location',
          ownerId: mockUser.id,
          approverId: mockUser.id,
          // No locationId provided
        },
        steps: [
          {
            serial: 1,
            title: 'Step without location',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 1,
            likelihood: 1,
          },
        ],
      };

      await jhaCallerRouter.create(jhaWithLocationInput as any);
      await jhaCallerRouter.create(jhaWithoutLocationInput as any);

      // Clear previous mock calls
      vi.mocked(searchLocationsPublic).mockClear();

      // Test minimal list with includeLocation: true
      const result = await jhaCallerRouter.minimalList({
        includeLocation: true,
      });

      expect(result.result).toHaveLength(2);

      // Find JHAs by title
      const jhaWithLocation = result.result.find((jha) => jha.title === 'JHA with Location');
      const jhaWithoutLocation = result.result.find((jha) => jha.title === 'JHA without Location');

      // Verify JHA with location has location data
      expect(jhaWithLocation).toBeDefined();
      expect(jhaWithLocation?.locationId).toBe('location-1');
      expect(jhaWithLocation?.location).toBeDefined();
      expect(jhaWithLocation?.location?.id).toBe('location-1');
      expect(jhaWithLocation?.location?.name).toBe('Test Location');

      // Verify JHA without location doesn't have location data
      expect(jhaWithoutLocation).toBeDefined();
      expect(jhaWithoutLocation?.locationId).toBeNull();
      expect(jhaWithoutLocation?.location).toBeUndefined();

      // Verify searchLocationsPublic was called with correct parameters
      expect(searchLocationsPublic).toHaveBeenCalledWith({
        upkeepCompanyId: mockUser.upkeepCompanyId,
        objectId: ['location-1'], // Only the location ID from the JHA with location
        limit: 100,
      });
    });

    it('should handle multiple different locations efficiently', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Mock multiple locations
      vi.mocked(searchLocationsPublic).mockResolvedValue({
        result: [
          {
            id: 'location-1',
            name: 'Test Location',
          },
          {
            id: 'location-2',
            name: 'Secondary Location',
          },
          {
            id: 'location-3',
            name: 'Third Location',
          },
        ],
        nextCursor: undefined,
        noResults: false,
      });

      // Create JHAs with different locations
      const jhaInputs = [
        {
          title: 'JHA at Location 1',
          locationId: 'location-1',
        },
        {
          title: 'JHA at Location 2',
          locationId: 'location-2',
        },
        {
          title: 'JHA at Location 3',
          locationId: 'location-3',
        },
        {
          title: 'Another JHA at Location 1',
          locationId: 'location-1', // Duplicate location
        },
        {
          title: 'JHA without Location',
          locationId: null, // No location
        },
      ];

      for (const { title, locationId } of jhaInputs) {
        const input = {
          jha: {
            title,
            ownerId: mockUser.id,
            approverId: mockUser.id,
            ...(locationId && { locationId }),
          },
          steps: [
            {
              serial: 1,
              title: 'Test Step',
              hazardIds: [createdHazardId],
              controlMeasureIds: [createdControlMeasureId],
              severity: 2,
              likelihood: 2,
            },
          ],
        };
        await jhaCallerRouter.create(input as any);
      }

      // Clear mock calls from creation
      vi.mocked(searchLocationsPublic).mockClear();

      // Test minimal list with includeLocation: true
      const result = await jhaCallerRouter.minimalList({
        includeLocation: true,
      });

      expect(result.result).toHaveLength(5);

      // Verify searchLocationsPublic was called with unique location IDs
      expect(searchLocationsPublic).toHaveBeenCalledWith({
        upkeepCompanyId: mockUser.upkeepCompanyId,
        objectId: expect.arrayContaining(['location-1', 'location-2', 'location-3']),
        limit: 100,
      });

      // Verify that location IDs are unique (no duplicates)
      const calledArgs = vi.mocked(searchLocationsPublic).mock.calls[0][0];
      expect(calledArgs.objectId).toHaveLength(3); // Should be 3 unique locations

      // Verify location data is properly mapped
      const jhaAtLocation1 = result.result.find((jha) => jha.title === 'JHA at Location 1');
      const jhaAtLocation2 = result.result.find((jha) => jha.title === 'JHA at Location 2');
      const jhaAtLocation3 = result.result.find((jha) => jha.title === 'JHA at Location 3');
      const anotherJhaAtLocation1 = result.result.find((jha) => jha.title === 'Another JHA at Location 1');
      const jhaWithoutLocation = result.result.find((jha) => jha.title === 'JHA without Location');

      // Verify location mappings
      expect(jhaAtLocation1?.location?.name).toBe('Test Location');
      expect(jhaAtLocation2?.location?.name).toBe('Secondary Location');
      expect(jhaAtLocation3?.location?.name).toBe('Third Location');
      expect(anotherJhaAtLocation1?.location?.name).toBe('Test Location'); // Same location as first
      expect(jhaWithoutLocation?.location).toBeUndefined();

      // Verify location IDs are correct
      expect(jhaAtLocation1?.locationId).toBe('location-1');
      expect(jhaAtLocation2?.locationId).toBe('location-2');
      expect(jhaAtLocation3?.locationId).toBe('location-3');
      expect(anotherJhaAtLocation1?.locationId).toBe('location-1');
      expect(jhaWithoutLocation?.locationId).toBeNull();
    });

    it('should not call searchLocationsPublic when no JHAs have locations', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create JHA without location
      const jhaInput = {
        jha: {
          title: 'JHA without Location',
          ownerId: mockUser.id,
          approverId: mockUser.id,
          // No locationId
        },
        steps: [
          {
            serial: 1,
            title: 'Step without location',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 1,
            likelihood: 1,
          },
        ],
      };

      await jhaCallerRouter.create(jhaInput as any);

      // Clear mock calls from creation
      vi.mocked(searchLocationsPublic).mockClear();

      // Test minimal list
      const result = await jhaCallerRouter.minimalList({});

      expect(result.result).toHaveLength(1);
      expect(result.result[0]?.title).toBe('JHA without Location');
      expect(result.result[0]?.locationId).toBeNull();
      expect(result.result[0]?.location).toBeUndefined();

      // Verify searchLocationsPublic was NOT called since no locations exist
      expect(searchLocationsPublic).not.toHaveBeenCalled();
    });

    it('should not fetch locations when includeLocation is false', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create JHA with location
      const jhaInput = {
        jha: {
          title: 'JHA with Location',
          ownerId: mockUser.id,
          approverId: mockUser.id,
          locationId: 'location-1',
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 3,
            likelihood: 2,
          },
        ],
      };

      await jhaCallerRouter.create(jhaInput as any);

      // Clear mock calls from creation
      vi.mocked(searchLocationsPublic).mockClear();

      // Test minimal list with includeLocation: false
      const result = await jhaCallerRouter.minimalList({
        includeLocation: false,
      });

      expect(result).toBeDefined();
      expect(result.result).toHaveLength(1);
      expect(result.result[0]?.location).toBeUndefined();

      // Verify searchLocationsPublic was NOT called when includeLocation is false
      expect(searchLocationsPublic).not.toHaveBeenCalled();
    });

    it('should fetch locations when includeLocation is true', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create JHA with location
      const jhaInput = {
        jha: {
          title: 'JHA with Location',
          ownerId: mockUser.id,
          approverId: mockUser.id,
          locationId: 'location-1',
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 3,
            likelihood: 2,
          },
        ],
      };

      await jhaCallerRouter.create(jhaInput as any);

      // Clear mock calls from creation
      vi.mocked(searchLocationsPublic).mockClear();

      // Test minimal list with includeLocation: true
      const result = await jhaCallerRouter.minimalList({
        includeLocation: true,
      });

      expect(result).toBeDefined();
      expect(result.result).toHaveLength(1);
      expect(result.result[0]?.location).toBeDefined();

      // Verify searchLocationsPublic was called when includeLocation is true
      expect(searchLocationsPublic).toHaveBeenCalledWith({
        upkeepCompanyId: mockUser.upkeepCompanyId,
        objectId: ['location-1'],
        limit: 100,
      });
    });

    it('should not fetch locations when includeLocation is undefined (default behavior)', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create JHA with location
      const jhaInput = {
        jha: {
          title: 'JHA with Location',
          ownerId: mockUser.id,
          approverId: mockUser.id,
          locationId: 'location-1',
        },
        steps: [
          {
            serial: 1,
            title: 'Step 1',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 3,
            likelihood: 2,
          },
        ],
      };

      await jhaCallerRouter.create(jhaInput as any);

      // Clear mock calls from creation
      vi.mocked(searchLocationsPublic).mockClear();

      // Test minimal list without includeLocation parameter (should default to not fetching)
      const result = await jhaCallerRouter.minimalList({});

      expect(result).toBeDefined();
      expect(result.result).toHaveLength(1);
      expect(result.result[0]?.location).toBeUndefined();

      // Verify searchLocationsPublic was NOT called when includeLocation is undefined
      expect(searchLocationsPublic).not.toHaveBeenCalled();
    });

    it('should respect custom limit parameter', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create 8 JHAs for testing
      for (let i = 1; i <= 8; i++) {
        const mockInput = {
          jha: {
            title: `Custom Limit JHA ${i}`,
            ownerId: mockUser.id,
            approverId: mockUser.id,
          },
          steps: [
            {
              serial: 1,
              title: `Step 1`,
              hazardIds: [createdHazardId],
              controlMeasureIds: [createdControlMeasureId],
              severity: 2,
              likelihood: 2,
            },
          ],
        };
        await jhaCallerRouter.create(mockInput as any);
      }

      // Test with custom limit of 5
      const result = await jhaCallerRouter.minimalList({
        limit: 5,
      });

      expect(result.result).toHaveLength(5);
      expect(result.nextCursor).toBe(5); // Should have more items
    });

    it('should respect cursor for pagination', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create 12 JHAs for testing
      for (let i = 1; i <= 12; i++) {
        const mockInput = {
          jha: {
            title: `Cursor Test JHA ${i.toString().padStart(2, '0')}`, // Pad for consistent sorting
            ownerId: mockUser.id,
            approverId: mockUser.id,
          },
          steps: [
            {
              serial: 1,
              title: `Step 1`,
              hazardIds: [createdHazardId],
              controlMeasureIds: [createdControlMeasureId],
              severity: 1,
              likelihood: 1,
            },
          ],
        };
        await jhaCallerRouter.create(mockInput as any);
      }

      // Get first page
      const firstPage = await jhaCallerRouter.minimalList({
        limit: 5,
        cursor: 0,
      });

      expect(firstPage.result).toHaveLength(5);
      expect(firstPage.nextCursor).toBe(5);

      // Get second page using cursor
      const secondPage = await jhaCallerRouter.minimalList({
        limit: 5,
        cursor: firstPage.nextCursor,
      });

      expect(secondPage.result).toHaveLength(5);
      expect(secondPage.nextCursor).toBe(10);

      // Verify different JHAs are returned
      const firstPageIds = firstPage.result.map((jha) => jha.id);
      const secondPageIds = secondPage.result.map((jha) => jha.id);
      expect(firstPageIds).not.toEqual(secondPageIds);

      // Get third page
      const thirdPage = await jhaCallerRouter.minimalList({
        limit: 5,
        cursor: secondPage.nextCursor,
      });

      expect(thirdPage.result).toHaveLength(2); // Only 2 remaining
      expect(thirdPage.nextCursor).toBeUndefined(); // No more items
    });

    it('should support status filtering', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create JHA in draft status
      const draftInput = {
        jha: {
          title: 'Draft Status JHA',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: 'Draft Step',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 2,
            likelihood: 2,
          },
        ],
      };

      const createdDraftJha = await jhaCallerRouter.create(draftInput as any);

      // Update to approved status
      await jhaCallerRouter.updateStatus({
        id: createdDraftJha!.id,
        status: approvalStatusEnum.enumValues[2], // 'approved'
      });

      // Create another draft JHA
      const anotherDraftInput = {
        jha: {
          title: 'Another Draft JHA',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: 'Another Draft Step',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 1,
            likelihood: 1,
          },
        ],
      };

      await jhaCallerRouter.create(anotherDraftInput as any);

      // Filter by approved status
      const approvedResult = await jhaCallerRouter.minimalList({
        status: ['approved'],
      });

      expect(approvedResult.result).toHaveLength(1);
      expect(approvedResult.result[0]?.status).toBe('approved');
      expect(approvedResult.result[0]?.title).toBe('Draft Status JHA');

      // Filter by draft status
      const draftResult = await jhaCallerRouter.minimalList({
        status: ['draft'],
      });

      expect(draftResult.result).toHaveLength(1);
      expect(draftResult.result[0]?.status).toBe('draft');
      expect(draftResult.result[0]?.title).toBe('Another Draft JHA');
    });

    it('should support risk level filtering', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create low risk JHA
      const lowRiskInput = {
        jha: {
          title: 'Low Risk Minimal JHA',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: 'Low Risk Step',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 2, // Low severity
            likelihood: 1,
          },
        ],
      };

      // Create high risk JHA
      const highRiskInput = {
        jha: {
          title: 'High Risk Minimal JHA',
          ownerId: mockUser.id,
          approverId: mockUser.id,
        },
        steps: [
          {
            serial: 1,
            title: 'High Risk Step',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 20, // High severity (>=15)
            likelihood: 1,
          },
        ],
      };

      await jhaCallerRouter.create(lowRiskInput as any);
      await jhaCallerRouter.create(highRiskInput as any);

      // Filter by high risk
      const highRiskResult = await jhaCallerRouter.minimalList({
        riskLevel: 'high',
      });

      expect(highRiskResult.result).toHaveLength(1);
      expect(highRiskResult.result[0]?.highestSeverity).toBeGreaterThanOrEqual(15);
      expect(highRiskResult.result[0]?.title).toBe('High Risk Minimal JHA');

      // Filter by low risk
      const lowRiskResult = await jhaCallerRouter.minimalList({
        riskLevel: 'low',
      });

      expect(lowRiskResult.result).toHaveLength(1);
      expect(lowRiskResult.result[0]?.highestSeverity).toBeLessThan(5);
      expect(lowRiskResult.result[0]?.title).toBe('Low Risk Minimal JHA');
    });

    it('should support sorting by title', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create JHAs with specific titles for sorting test
      const jhaInputs = [
        { title: 'Zebra JHA', severity: 1 },
        { title: 'Alpha JHA', severity: 2 },
        { title: 'Beta JHA', severity: 3 },
      ];

      for (const { title, severity } of jhaInputs) {
        const mockInput = {
          jha: {
            title,
            ownerId: mockUser.id,
            approverId: mockUser.id,
          },
          steps: [
            {
              serial: 1,
              title: 'Step 1',
              hazardIds: [createdHazardId],
              controlMeasureIds: [createdControlMeasureId],
              severity,
              likelihood: 1,
            },
          ],
        };
        await jhaCallerRouter.create(mockInput as any);
      }

      // Test ascending sort by title
      const ascResult = await jhaCallerRouter.minimalList({
        sortBy: 'title',
        sortOrder: 'asc',
      });

      expect(ascResult.result).toHaveLength(3);
      expect(ascResult.result[0]?.title).toBe('Alpha JHA');
      expect(ascResult.result[1]?.title).toBe('Beta JHA');
      expect(ascResult.result[2]?.title).toBe('Zebra JHA');

      // Test descending sort by title
      const descResult = await jhaCallerRouter.minimalList({
        sortBy: 'title',
        sortOrder: 'desc',
      });

      expect(descResult.result).toHaveLength(3);
      expect(descResult.result[0]?.title).toBe('Zebra JHA');
      expect(descResult.result[1]?.title).toBe('Beta JHA');
      expect(descResult.result[2]?.title).toBe('Alpha JHA');
    });

    it('should support sorting by highest severity', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create JHAs with different severities
      const severityInputs = [
        { title: 'Low Severity JHA', severity: 1 },
        { title: 'High Severity JHA', severity: 5 },
        { title: 'Medium Severity JHA', severity: 3 },
      ];

      for (const { title, severity } of severityInputs) {
        const mockInput = {
          jha: {
            title,
            ownerId: mockUser.id,
            approverId: mockUser.id,
          },
          steps: [
            {
              serial: 1,
              title: 'Step 1',
              hazardIds: [createdHazardId],
              controlMeasureIds: [createdControlMeasureId],
              severity,
              likelihood: 1,
            },
          ],
        };
        await jhaCallerRouter.create(mockInput as any);
      }

      // Test descending sort by severity (highest first)
      const descResult = await jhaCallerRouter.minimalList({
        sortBy: 'highestSeverity',
        sortOrder: 'desc',
      });

      expect(descResult.result).toHaveLength(3);
      expect(descResult.result[0]?.highestSeverity).toBe(5);
      expect(descResult.result[1]?.highestSeverity).toBe(3);
      expect(descResult.result[2]?.highestSeverity).toBe(1);

      // Test ascending sort by severity (lowest first)
      const ascResult = await jhaCallerRouter.minimalList({
        sortBy: 'highestSeverity',
        sortOrder: 'asc',
      });

      expect(ascResult.result).toHaveLength(3);
      expect(ascResult.result[0]?.highestSeverity).toBe(1);
      expect(ascResult.result[1]?.highestSeverity).toBe(3);
      expect(ascResult.result[2]?.highestSeverity).toBe(5);
    });

    it('should throw error when user lacks VIEW permission', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Mock permission check to return false
      vi.mocked(hasPermission).mockReturnValue(false);

      await expect(jhaCallerRouter.minimalList({})).rejects.toThrow();
    });

    it('should return empty result when no JHAs exist', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Clear any previous mock calls
      vi.mocked(searchLocationsPublic).mockClear();

      // Test with fresh context (no JHAs created in this test)
      const result = await jhaCallerRouter.minimalList({});

      expect(result).toBeDefined();
      expect(result.result).toEqual([]);
      expect(result.nextCursor).toBeUndefined();
      expect(result.noResults).toBe(true);

      // Verify searchLocationsPublic was NOT called when no JHAs exist
      expect(searchLocationsPublic).not.toHaveBeenCalled();
    });

    it('should handle search query filtering', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create JHAs with searchable titles
      const searchableInputs = [
        { title: 'Electrical Safety Procedure', keyword: 'electrical' },
        { title: 'Chemical Handling Guidelines', keyword: 'chemical' },
        { title: 'Fire Safety Protocol', keyword: 'fire' },
      ];

      for (const { title } of searchableInputs) {
        const mockInput = {
          jha: {
            title,
            ownerId: mockUser.id,
            approverId: mockUser.id,
          },
          steps: [
            {
              serial: 1,
              title: 'Safety Step',
              hazardIds: [createdHazardId],
              controlMeasureIds: [createdControlMeasureId],
              severity: 2,
              likelihood: 2,
            },
          ],
        };
        await jhaCallerRouter.create(mockInput as any);
      }

      // Search for "electrical"
      const electricalResult = await jhaCallerRouter.minimalList({
        search: 'electrical',
      });

      expect(electricalResult.result).toHaveLength(1);
      expect(electricalResult.result[0]?.title).toContain('Electrical');

      // Search for "safety" (should match multiple)
      const safetyResult = await jhaCallerRouter.minimalList({
        search: 'safety',
      });

      expect(safetyResult.result.length).toBeGreaterThanOrEqual(2);
      safetyResult.result.forEach((jha) => {
        expect(jha.title.toLowerCase()).toContain('safety');
      });
    });

    it('should support review status filtering', async () => {
      const jhaCallerRouter = jhaRouter.createCaller(mockContext);

      // Create JHA and update to review status
      const reviewInput = {
        jha: {
          title: 'Review Status JHA',
          ownerId: mockUser.id,
          approverId: mockUser.id,
          reviewDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Set review date 7 days from now
        },
        steps: [
          {
            serial: 1,
            title: 'Review Step',
            hazardIds: [createdHazardId],
            controlMeasureIds: [createdControlMeasureId],
            severity: 2,
            likelihood: 2,
          },
        ],
      };

      const createdJha = await jhaCallerRouter.create(reviewInput as any);

      // Update to review status
      await jhaCallerRouter.updateStatus({
        id: createdJha!.id,
        status: approvalStatusEnum.enumValues[1], // 'review'
      });

      // Test filtering without review status (should return all JHAs including the one we created)
      const allResult = await jhaCallerRouter.minimalList({});

      expect(allResult.result.length).toBeGreaterThanOrEqual(1);
      const reviewJha = allResult.result.find((jha) => jha.title === 'Review Status JHA');
      expect(reviewJha).toBeDefined();
      expect(reviewJha?.status).toBe('review');

      // Test with a different review status filter
      const overdueResult = await jhaCallerRouter.minimalList({
        reviewStatus: 'overdue',
      });

      // This may return 0 results since our test JHA has a future review date
      expect(Array.isArray(overdueResult.result)).toBe(true);
    });
  });

  describe('permissions - user access restrictions', () => {
    describe('getByInstanceId permissions', () => {
      it('should allow technician to view JHA where they are the owner', async () => {
        const mockTechnicianContext = {
          ...createMockContext(mockTechnicianUser),
          needPartialCheck: true,
        };
        const technicianCaller = jhaRouter.createCaller(mockTechnicianContext);

        // Create JHA where technician is the owner
        const mockInput: z.infer<typeof CreateJhaFormSchema> = {
          jha: {
            title: 'Technician Owned JHA',
            ownerId: mockTechnicianUser.id,
            approverId: mockUser.id,
          },
          steps: [
            {
              serial: 1,
              title: 'Step 1: Safety Check',
              hazardIds: [createdHazardId],
              controlMeasureIds: [createdControlMeasureId],
              severity: 2,
              likelihood: 2,
            },
          ],
        };

        const adminCaller = jhaRouter.createCaller(mockContext);
        const createdJha = await adminCaller.create(mockInput);

        // Technician should be able to view their own JHA
        const retrievedJha = await technicianCaller.getByInstanceId({ id: createdJha!.instanceId! });

        expect(retrievedJha).toBeDefined();
        expect(retrievedJha.ownerId).toBe(mockTechnicianUser.id);
        expect(retrievedJha.title).toBe('Technician Owned JHA');
      });

      it('should allow technician to view JHA where they are the approver', async () => {
        const mockTechnicianContext = {
          ...createMockContext(mockTechnicianUser),
          needPartialCheck: true,
        };
        const technicianCaller = jhaRouter.createCaller(mockTechnicianContext);

        // Create JHA where technician is the approver
        const mockInput: z.infer<typeof CreateJhaFormSchema> = {
          jha: {
            title: 'Technician Approved JHA',
            ownerId: mockUser.id,
            approverId: mockTechnicianUser.id,
          },
          steps: [
            {
              serial: 1,
              title: 'Step 1: Equipment Check',
              hazardIds: [createdHazardId],
              controlMeasureIds: [createdControlMeasureId],
              severity: 3,
              likelihood: 2,
            },
          ],
        };

        const adminCaller = jhaRouter.createCaller(mockContext);
        const createdJha = await adminCaller.create(mockInput);

        // Technician should be able to view JHA they approve
        const retrievedJha = await technicianCaller.getByInstanceId({ id: createdJha!.instanceId! });

        expect(retrievedJha).toBeDefined();
        expect(retrievedJha.approverId).toBe(mockTechnicianUser.id);
        expect(retrievedJha.title).toBe('Technician Approved JHA');
      });

      it('should allow users to view public JHAs', async () => {
        const mockTechnicianContext = {
          ...createMockContext(mockTechnicianUser),
          needPartialCheck: true,
        };
        const technicianCaller = jhaRouter.createCaller(mockTechnicianContext);

        // Create a public JHA where technician has no direct association
        const [publicJha] = await db
          .insert(jha)
          .values({
            title: 'Public Safety Procedure',
            ownerId: mockUser.id,
            approverId: mockUser.id,
            createdBy: mockUser.id,
            upkeepCompanyId: mockTechnicianUser.upkeepCompanyId,
            isPublic: true, // This is the key - public JHA
            status: 'approved',
          })
          .returning();

        await db.insert(jhaSteps).values({
          jhaId: publicJha.id,
          title: 'Public Step 1',
          serial: 1,
          severity: 2,
          likelihood: 2,
          upkeepCompanyId: mockTechnicianUser.upkeepCompanyId,
          hazardIds: [],
          controlMeasureIds: [],
          createdBy: mockUser.id,
        });

        // Technician should be able to view public JHA
        const retrievedJha = await technicianCaller.getByInstanceId({ id: publicJha.instanceId! });

        expect(retrievedJha).toBeDefined();
        expect(retrievedJha.title).toBe('Public Safety Procedure');
        expect(retrievedJha.isPublic).toBe(true);
      });

      it('should prevent technician from viewing private JHA where they have no association', async () => {
        const mockTechnicianContext = {
          ...createMockContext(mockTechnicianUser),
          needPartialCheck: true,
        };
        const technicianCaller = jhaRouter.createCaller(mockTechnicianContext);

        // Create a private JHA where technician has no association
        const [privateJha] = await db
          .insert(jha)
          .values({
            title: 'Private Admin JHA',
            ownerId: mockUser.id,
            approverId: mockUser.id,
            createdBy: mockUser.id,
            upkeepCompanyId: mockTechnicianUser.upkeepCompanyId,
            isPublic: false, // Private JHA
            status: 'draft',
          })
          .returning();

        await db.insert(jhaSteps).values({
          jhaId: privateJha.id,
          title: 'Private Step 1',
          serial: 1,
          severity: 3,
          likelihood: 3,
          upkeepCompanyId: mockTechnicianUser.upkeepCompanyId,
          hazardIds: [],
          controlMeasureIds: [],
          createdBy: mockUser.id,
        });

        // Technician should NOT be able to view private JHA they're not associated with
        await expect(technicianCaller.getByInstanceId({ id: privateJha.instanceId! })).rejects.toThrow('JHA not found');
      });

      it('should allow view-only user to view JHA where they are in a role', async () => {
        const mockViewOnlyContext = {
          ...createMockContext(mockViewOnlyUser),
          needPartialCheck: true,
        };
        const viewOnlyCaller = jhaRouter.createCaller(mockViewOnlyContext);

        // Create JHA where view-only user is the owner
        const mockInput: z.infer<typeof CreateJhaFormSchema> = {
          jha: {
            title: 'View-Only User JHA',
            ownerId: mockViewOnlyUser.id,
            approverId: mockUser.id,
          },
          steps: [
            {
              serial: 1,
              title: 'Step 1: Observation',
              hazardIds: [createdHazardId],
              controlMeasureIds: [createdControlMeasureId],
              severity: 1,
              likelihood: 1,
            },
          ],
        };

        const adminCaller = jhaRouter.createCaller(mockContext);
        const createdJha = await adminCaller.create(mockInput);

        // View-only user should be able to view their JHA
        const retrievedJha = await viewOnlyCaller.getByInstanceId({ id: createdJha!.instanceId! });

        expect(retrievedJha).toBeDefined();
        expect(retrievedJha.ownerId).toBe(mockViewOnlyUser.id);
        expect(retrievedJha.title).toBe('View-Only User JHA');
      });
    });

    describe('list permissions', () => {
      it('should allow technician to list only JHAs they are associated with or public ones', async () => {
        const mockTechnicianContext = {
          ...createMockContext(mockTechnicianUser),
          needPartialCheck: true,
        };
        const technicianCaller = jhaRouter.createCaller(mockTechnicianContext);

        // Create JHA where technician is owner
        const technicianOwnedInput: z.infer<typeof CreateJhaFormSchema> = {
          jha: {
            title: 'Technician Owned JHA',
            ownerId: mockTechnicianUser.id,
            approverId: mockUser.id,
          },
          steps: [
            {
              serial: 1,
              title: 'Owned Step',
              hazardIds: [createdHazardId],
              controlMeasureIds: [createdControlMeasureId],
              severity: 2,
              likelihood: 2,
            },
          ],
        };

        // Create public JHA (not associated with technician)
        const [publicJha] = await db
          .insert(jha)
          .values({
            title: 'Public Company JHA',
            ownerId: mockUser.id,
            approverId: mockUser.id,
            createdBy: mockUser.id,
            upkeepCompanyId: mockTechnicianUser.upkeepCompanyId,
            isPublic: true,
            status: 'approved',
          })
          .returning();

        await db.insert(jhaSteps).values({
          jhaId: publicJha.id,
          title: 'Public Step',
          serial: 1,
          severity: 1,
          likelihood: 1,
          upkeepCompanyId: mockTechnicianUser.upkeepCompanyId,
          hazardIds: [],
          controlMeasureIds: [],
          createdBy: mockUser.id,
        });

        // Create private JHA (not associated with technician)
        const [privateJha] = await db
          .insert(jha)
          .values({
            title: 'Private Admin JHA',
            ownerId: mockUser.id,
            approverId: mockUser.id,
            createdBy: mockUser.id,
            upkeepCompanyId: mockTechnicianUser.upkeepCompanyId,
            isPublic: false,
            status: 'draft',
          })
          .returning();

        await db.insert(jhaSteps).values({
          jhaId: privateJha.id,
          title: 'Private Step',
          serial: 1,
          severity: 2,
          likelihood: 2,
          upkeepCompanyId: mockTechnicianUser.upkeepCompanyId,
          hazardIds: [],
          controlMeasureIds: [],
          createdBy: mockUser.id,
        });

        const adminCaller = jhaRouter.createCaller(mockContext);
        await adminCaller.create(technicianOwnedInput);

        // Technician should see: their owned JHA + public JHA (but NOT private JHA)
        const result = await technicianCaller.list({});

        expect(result.result.length).toBeGreaterThanOrEqual(2);

        const jhaIds = result.result.map((jha) => jha.instanceId);
        expect(jhaIds).toContain(publicJha.instanceId);

        // Ensure all returned JHAs are either owned by technician or public (service layer handles this filtering)
        expect(
          result.result.every(
            (jha) =>
              jha.owner?.id === mockTechnicianUser.id ||
              // Note: We don't have access to isPublic in list view by default, but the query should filter correctly
              true, // The service layer handles this filtering
          ),
        ).toBe(true);

        // Should NOT contain the private JHA
        expect(jhaIds).not.toContain(privateJha.instanceId);
      });

      it('should allow view-only user to list only associated or public JHAs', async () => {
        const mockViewOnlyContext = {
          ...createMockContext(mockViewOnlyUser),
          needPartialCheck: true,
        };
        const viewOnlyCaller = jhaRouter.createCaller(mockViewOnlyContext);

        // Create JHA where view-only user is approver
        const viewOnlyApprovedInput: z.infer<typeof CreateJhaFormSchema> = {
          jha: {
            title: 'View-Only Approved JHA',
            ownerId: mockUser.id,
            approverId: mockViewOnlyUser.id,
          },
          steps: [
            {
              serial: 1,
              title: 'Approved Step',
              hazardIds: [createdHazardId],
              controlMeasureIds: [createdControlMeasureId],
              severity: 1,
              likelihood: 2,
            },
          ],
        };

        const adminCaller = jhaRouter.createCaller(mockContext);
        const createdJha = await adminCaller.create(viewOnlyApprovedInput);

        // View-only user should see JHAs they're associated with
        const result = await viewOnlyCaller.list({});

        expect(result.result.length).toBeGreaterThanOrEqual(1);
        expect(result.result.some((jha) => jha.instanceId === createdJha?.instanceId)).toBe(true);

        // Verify the user can see their approved JHA
        const approvedJha = result.result.find((jha) => jha.title === 'View-Only Approved JHA');
        expect(approvedJha).toBeDefined();
      });

      it('should allow admin users to see all JHAs (no partial check)', async () => {
        const adminCaller = jhaRouter.createCaller(mockContext); // Admin context without needPartialCheck

        // Create various JHAs
        const adminJhaInput: z.infer<typeof CreateJhaFormSchema> = {
          jha: {
            title: 'Admin JHA',
            ownerId: mockUser.id,
            approverId: mockUser.id,
          },
          steps: [
            {
              serial: 1,
              title: 'Admin Step',
              hazardIds: [createdHazardId],
              controlMeasureIds: [createdControlMeasureId],
              severity: 3,
              likelihood: 3,
            },
          ],
        };

        // Create public JHA
        const [publicJha] = await db
          .insert(jha)
          .values({
            title: 'Public JHA',
            ownerId: mockUser.id,
            approverId: mockUser.id,
            createdBy: mockUser.id,
            upkeepCompanyId: mockUser.upkeepCompanyId,
            isPublic: true,
            status: 'approved',
          })
          .returning();

        await db.insert(jhaSteps).values({
          jhaId: publicJha.id,
          title: 'Public Step',
          serial: 1,
          severity: 1,
          likelihood: 1,
          upkeepCompanyId: mockUser.upkeepCompanyId,
          hazardIds: [],
          controlMeasureIds: [],
          createdBy: mockUser.id,
        });

        // Create private JHA for comparison
        const [privateJha] = await db
          .insert(jha)
          .values({
            title: 'Private JHA',
            ownerId: mockUser.id,
            approverId: mockUser.id,
            createdBy: mockUser.id,
            upkeepCompanyId: mockUser.upkeepCompanyId,
            isPublic: false,
            status: 'draft',
          })
          .returning();

        await db.insert(jhaSteps).values({
          jhaId: privateJha.id,
          title: 'Private Step',
          serial: 1,
          severity: 2,
          likelihood: 2,
          upkeepCompanyId: mockUser.upkeepCompanyId,
          hazardIds: [],
          controlMeasureIds: [],
          createdBy: mockUser.id,
        });

        // Admin should see ALL JHAs (no filtering by association)
        const result = await adminCaller.list({});

        expect(result.result.length).toBeGreaterThanOrEqual(2);

        // Should include both public and private JHAs
        const jhaIds = result.result.map((jha) => jha.instanceId);
        expect(jhaIds).toContain(publicJha.instanceId);
        expect(jhaIds).toContain(privateJha.instanceId);

        // Verify admin sees both public and private JHAs (demonstrates no filtering)
        expect(result.result.length).toBe(2);
        expect(jhaIds).toEqual(expect.arrayContaining([publicJha.instanceId, privateJha.instanceId]));
      });
    });
  });
});
