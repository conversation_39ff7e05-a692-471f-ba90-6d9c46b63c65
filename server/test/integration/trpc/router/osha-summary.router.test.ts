import { describe, it, expect, beforeEach, afterEach, vi, afterAll, beforeAll } from 'vitest';
import { createMockContext } from '@server/trpc/router/__tests__/test-utils';
import { db } from '@server/db';
import { mockUser } from '@server/test/fixtures/user';
import { getUserById, getUsers, hasPermission } from '@server/services/user.service';
import { events, oshaAuditTrail, oshaCompanyInformation, oshaReports, oshaLocations } from '@shared/schema';
import { oshaSummaryRouter } from '@server/trpc/router/osha-summary.router';
import { eq, desc } from 'drizzle-orm';

// Mock the user service
vi.mock('@server/services/user.service', () => ({
  getUserById: vi.fn(),
  getUsers: vi.fn(),
  hasPermission: vi.fn().mockReturnValue(true),
  userHasIncidentPermission: vi.fn().mockReturnValue(true),
  canEditIncident: vi.fn().mockReturnValue(true),
  canExportIncident: vi.fn().mockReturnValue(true),
}));

let eventId: string;
let oshaLocationId: string;

describe('oshaSummaryRouter', () => {
  beforeAll(async () => {
    // Create a mock global location
    const oshaLocation = await db
      .insert(oshaLocations)
      .values({
        name: 'Test Global Location',
        upkeepCompanyId: '123',
        createdBy: '123',
      })
      .returning({ id: oshaLocations.id });
    oshaLocationId = oshaLocation.at(0)!.id;

    // Create a test event
    const event = await db
      .insert(events)
      .values({
        upkeepCompanyId: '123',
        title: 'Test incident',
        description: 'Test description',
        type: 'incident',
        category: 'other',
        severity: 'low',
        reportedAt: new Date(),
        locationId: '123',
        assetIds: ['123'],
        status: 'open',
      })
      .returning({ id: events.id });
    eventId = event.at(0)!.id;
  });

  const mockContext = createMockContext(mockUser);
  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(getUserById).mockResolvedValue(mockUser);
    vi.mocked(getUsers).mockResolvedValue([mockUser]);
    vi.mocked(hasPermission).mockReturnValue(true);
  });

  afterEach(async () => {
    await db.delete(oshaAuditTrail);
    await db.delete(oshaCompanyInformation);
    await db.delete(oshaReports);
  });

  afterAll(async () => {
    await db.delete(events);
    await db.delete(oshaLocations);
  });

  const createValidEstablishmentInput = (overrides = {}) => ({
    companyName: 'Test Company',
    companyFacilityId: 'FAC-123',
    companyNAICSCode: 123456,
    companyEIN: '*********',
    year: 2025,
    companyAnnualAverageNumberOfEmployees: 100,
    companyTotalHoursWorked: 10000,
    oshaLocationId,
    ...overrides,
  });

  describe('upsertEstablishInformation', () => {
    it('should require oshaLocationId', async () => {
      const caller = oshaSummaryRouter.createCaller(mockContext);
      const input = createValidEstablishmentInput();
      delete (input as any).oshaLocationId;

      await expect(async () => await caller.upsertEstablishmentInformation(input)).rejects.toThrowError();
    });

    it('should update an existing establishment information', async () => {
      const caller = oshaSummaryRouter.createCaller(mockContext);
      const input = createValidEstablishmentInput();

      const inserted = await db
        .insert(oshaCompanyInformation)
        .values({
          ...input,
          upkeepCompanyId: mockUser.upkeepCompanyId,
          createdBy: mockUser.id!,
        })
        .returning({ id: oshaCompanyInformation.id });

      const summary = await caller.upsertEstablishmentInformation({
        ...input,
        companyName: 'Test Company Updated',
      });
      expect(summary?.id).toBe(inserted.at(0)?.id);

      const auditTrail = await db
        .select()
        .from(oshaAuditTrail)
        .where(eq(oshaAuditTrail?.entityId, summary?.id ?? ''))
        .limit(1);
      expect(auditTrail).toBeDefined();
      expect(auditTrail.length).toBe(1);
      expect(auditTrail[0].action).toBe('updated');

      const summaryData = await db
        .select()
        .from(oshaCompanyInformation)
        .where(eq(oshaCompanyInformation?.id, summary?.id ?? ''))
        .limit(1);
      expect(summaryData).toBeDefined();
      expect(summaryData.length).toBe(1);
      expect(summaryData[0].companyName).toBe('Test Company Updated');
      expect(summaryData[0].oshaLocationId).toBe(oshaLocationId);
    });

    it('should create a new establishment information', async () => {
      const caller = oshaSummaryRouter.createCaller(mockContext);
      const input = createValidEstablishmentInput();

      const summary = await caller.upsertEstablishmentInformation(input);
      expect(summary?.id).toBeDefined();

      const auditTrail = await db
        .select()
        .from(oshaAuditTrail)
        .where(eq(oshaAuditTrail?.entityId, summary?.id ?? ''))
        .limit(1);
      expect(auditTrail).toBeDefined();
      expect(auditTrail.length).toBe(1);
      expect(auditTrail[0].action).toBe('created');

      const summaryData = await db
        .select()
        .from(oshaCompanyInformation)
        .where(eq(oshaCompanyInformation?.id, summary?.id ?? ''))
        .limit(1);
      expect(summaryData).toBeDefined();
      expect(summaryData.length).toBe(1);
      expect(summaryData[0].companyName).toBe(input.companyName);
      expect(summaryData[0].oshaLocationId).toBe(oshaLocationId);
    });

    describe('Validation Tests', () => {
      it('should fail when companyName is empty', async () => {
        const caller = oshaSummaryRouter.createCaller(mockContext);
        const input = createValidEstablishmentInput({ companyName: '' });

        await expect(caller.upsertEstablishmentInformation(input)).rejects.toThrow();
      });

      it('should fail when companyFacilityId is empty', async () => {
        const caller = oshaSummaryRouter.createCaller(mockContext);
        const input = createValidEstablishmentInput({ companyFacilityId: '' });

        await expect(caller.upsertEstablishmentInformation(input)).rejects.toThrow();
      });

      it('should fail when EIN is not exactly 9 digits', async () => {
        const caller = oshaSummaryRouter.createCaller(mockContext);
        const input = createValidEstablishmentInput({ companyEIN: '12345678' }); // 8 digits

        await expect(caller.upsertEstablishmentInformation(input)).rejects.toThrow();
      });

      it('should accept valid EIN with leading zeros', async () => {
        const caller = oshaSummaryRouter.createCaller(mockContext);
        const input = createValidEstablishmentInput({ companyEIN: '*********' });

        const result = await caller.upsertEstablishmentInformation(input);
        expect(result).toBeDefined();

        const summary = await db
          .select()
          .from(oshaCompanyInformation)
          .where(eq(oshaCompanyInformation?.id, result?.id ?? ''))
          .limit(1);

        expect(summary[0].companyEIN).toBe('*********');
      });

      it('should fail when employee count is zero or negative', async () => {
        const caller = oshaSummaryRouter.createCaller(mockContext);
        const input1 = createValidEstablishmentInput({ companyAnnualAverageNumberOfEmployees: 0 });
        const input2 = createValidEstablishmentInput({ companyAnnualAverageNumberOfEmployees: -1 });

        await expect(caller.upsertEstablishmentInformation(input1)).rejects.toThrow();
        await expect(caller.upsertEstablishmentInformation(input2)).rejects.toThrow();
      });

      it('should fail when total hours is zero or negative', async () => {
        const caller = oshaSummaryRouter.createCaller(mockContext);
        const input1 = createValidEstablishmentInput({ companyTotalHoursWorked: 0 });
        const input2 = createValidEstablishmentInput({ companyTotalHoursWorked: -1 });

        await expect(caller.upsertEstablishmentInformation(input1)).rejects.toThrow();
        await expect(caller.upsertEstablishmentInformation(input2)).rejects.toThrow();
      });

      it('should fail when NAICS code is invalid', async () => {
        const caller = oshaSummaryRouter.createCaller(mockContext);
        const input1 = createValidEstablishmentInput({ companyNAICSCode: 9 }); // too small
        const input2 = createValidEstablishmentInput({ companyNAICSCode: 1000000 }); // too large

        await expect(caller.upsertEstablishmentInformation(input1)).rejects.toThrow();
        await expect(caller.upsertEstablishmentInformation(input2)).rejects.toThrow();
      });

      it('should fail when year is invalid', async () => {
        const caller = oshaSummaryRouter.createCaller(mockContext);
        const currentYear = new Date().getFullYear();
        const input1 = createValidEstablishmentInput({ year: 1899 }); // too old
        const input2 = createValidEstablishmentInput({ year: currentYear + 2 }); // too far in future

        await expect(caller.upsertEstablishmentInformation(input1)).rejects.toThrow();
        await expect(caller.upsertEstablishmentInformation(input2)).rejects.toThrow();
      });
    });
  });

  describe('upsertExecutiveCertification', () => {
    let summaryId: string;

    beforeEach(async () => {
      // Create a summary record to be certified
      const inserted = await db
        .insert(oshaCompanyInformation)
        .values({
          companyName: 'Test Company for Certification',
          companyFacilityId: 'FAC-CERT',
          companyNAICSCode: 123456,
          companyEIN: '*********',
          year: 2026,
          companyAnnualAverageNumberOfEmployees: 150,
          companyTotalHoursWorked: 15000,
          upkeepCompanyId: mockUser.upkeepCompanyId,
          createdBy: mockUser.id!,
          oshaLocationId,
        })
        .returning({ id: oshaCompanyInformation.id });
      summaryId = inserted[0].id;
    });

    it('should successfully update executive certification information', async () => {
      const caller = oshaSummaryRouter.createCaller(mockContext);
      const certificationInput = {
        id: summaryId,
        executiveName: 'John Doe',
        executiveTitle: 'CEO',
        dateCertified: new Date(),
        digitalSignature: 'John Doe',
        year: 2026,
        oshaLocationId,
      };

      const result = await caller.upsertExecutiveCertification(certificationInput);
      expect(result).toBeDefined();

      // Verify the database was updated
      const updatedSummary = await db
        .select()
        .from(oshaCompanyInformation)
        .where(eq(oshaCompanyInformation.id, summaryId))
        .limit(1);

      expect(updatedSummary).toHaveLength(1);
      expect(updatedSummary[0].executiveName).toBe(certificationInput.executiveName);
      expect(updatedSummary[0].executiveTitle).toBe(certificationInput.executiveTitle);
      expect(updatedSummary[0].digitalSignature).toBe(certificationInput.digitalSignature);
      expect(updatedSummary[0].dateCertified).toEqual(expect.any(Date));

      // Verify audit trail
      const auditTrail = await db
        .select()
        .from(oshaAuditTrail)
        .where(eq(oshaAuditTrail.entityId, summaryId))
        .orderBy(desc(oshaAuditTrail.createdAt))
        .limit(1);

      expect(auditTrail).toHaveLength(1);
      expect(auditTrail[0].action).toBe('signed');
      expect(auditTrail[0].entityType).toBe('osha_company_information');
    });

    it('should fail validation if required fields are missing', async () => {
      const caller = oshaSummaryRouter.createCaller(mockContext);

      const inputs = [
        { executiveName: '', executiveTitle: 'CEO', digitalSignature: 'John Doe' },
        { executiveName: 'John Doe', executiveTitle: '', digitalSignature: 'John Doe' },
        { executiveName: 'John Doe', executiveTitle: 'CEO', digitalSignature: '' },
      ];

      for (const inputFields of inputs) {
        const certificationInput = {
          id: summaryId,
          dateCertified: new Date(),
          year: 2026,
          oshaLocationId,
          ...inputFields,
        };
        await expect(caller.upsertExecutiveCertification(certificationInput)).rejects.toThrow();
      }
    });
  });

  describe('getEstablishInformation', () => {
    beforeEach(async () => {
      // Create records for multiple years for the primary mock user's company
      await db.insert(oshaCompanyInformation).values([
        {
          companyName: 'Test Company 2025',
          companyFacilityId: 'FAC-2025',
          companyNAICSCode: 123456,
          companyEIN: '*********',
          year: 2025,
          companyAnnualAverageNumberOfEmployees: 100,
          companyTotalHoursWorked: 200000,
          upkeepCompanyId: mockUser.upkeepCompanyId,
          createdBy: mockUser.id!,
          oshaLocationId,
        },
        {
          companyName: 'Test Company 2026',
          companyFacilityId: 'FAC-2026',
          companyNAICSCode: 123456,
          companyEIN: '*********',
          year: 2026,
          companyAnnualAverageNumberOfEmployees: 110,
          companyTotalHoursWorked: 220000,
          upkeepCompanyId: mockUser.upkeepCompanyId,
          createdBy: mockUser.id!,
          oshaLocationId,
        },
        // Create a record for a different company in the same year to test isolation
        {
          companyName: 'Other Company 2026',
          companyFacilityId: 'FAC-OTHER-2026',
          companyNAICSCode: 654321,
          companyEIN: '*********',
          year: 2026,
          companyAnnualAverageNumberOfEmployees: 50,
          companyTotalHoursWorked: 100000,
          upkeepCompanyId: 'other-comp',
          createdBy: 'other-user',
          oshaLocationId,
        },
      ]);
    });

    it('should retrieve the correct establishment information for a given year', async () => {
      const caller = oshaSummaryRouter.createCaller(mockContext);
      const result = await caller.getEstablishmentInformation({ year: 2026, oshaLocationId });

      expect(result).toBeDefined();
      expect(result?.year).toBe(2026);
      expect(result?.companyName).toBe('Test Company 2026');
      expect(result?.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
      expect(result?.oshaLocationId).toBe(oshaLocationId);
    });

    it('should return null if no information exists for the given year', async () => {
      const caller = oshaSummaryRouter.createCaller(mockContext);
      const result = await caller.getEstablishmentInformation({ year: 2028, oshaLocationId });

      expect(result).toBeNull();
    });

    it('should not retrieve information for a different company', async () => {
      const caller = oshaSummaryRouter.createCaller(mockContext);
      const resultFor2026 = await caller.getEstablishmentInformation({ year: 2026, oshaLocationId });

      // Ensure the retrieved record belongs to the user's company, not the "other" company
      expect(resultFor2026).toBeDefined();
      expect(resultFor2026?.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
      expect(resultFor2026?.companyName).not.toBe('Other Company 2026');
    });
  });

  describe('getOshaCasesSummary', () => {
    const testYear = 2025;

    const createValidOshaReportInput = (overrides = {}) => ({
      eventId,
      upkeepCompanyId: mockUser.upkeepCompanyId,
      employeeWorkLocation: 'location',
      employeeDepartment: 'department',
      employeeJobTitle: 'title',
      employeeDateOfHire: new Date(),
      employeeShift: 'day' as const,
      bodyPartInjured: 'head',
      typeOfInjury: 'trauma',
      treatmentLocation: 'hospital',
      typeOfMedicalCare: 'emergency_room' as const,
      type: 'medical_treatment_beyond_first_aid' as const,
      reasonForReport: 'reason',
      createdBy: mockUser.id!,
      oshaLocationId,
      ...overrides,
    });

    beforeEach(async () => {
      await db.insert(oshaCompanyInformation).values({
        companyName: 'Test Company',
        companyFacilityId: 'FAC-123',
        companyNAICSCode: 123456,
        companyEIN: '*********',
        year: testYear,
        companyAnnualAverageNumberOfEmployees: 100,
        companyTotalHoursWorked: 200000,
        upkeepCompanyId: mockUser.upkeepCompanyId,
        createdBy: mockUser.id!,
        oshaLocationId,
      });

      await db.insert(oshaReports).values([
        // Case 1: Death
        createValidOshaReportInput({
          wasDeceased: true,
          createdAt: new Date(`${testYear}-01-15`),
          type: 'fatality',
        }),
        // Case 2: Days Away
        createValidOshaReportInput({
          wasDeceased: false,
          daysAwayFromWork: 10,
          createdAt: new Date(`${testYear}-02-15`),
          type: 'days_away_from_work',
        }),
        // Case 3: Days Away
        createValidOshaReportInput({
          wasDeceased: false,
          daysAwayFromWork: 5,
          createdAt: new Date(`${testYear}-03-15`),
          type: 'days_away_from_work',
        }),
        // Case 4: Restricted Work
        createValidOshaReportInput({
          wasDeceased: false,
          daysAwayFromWork: 0,
          daysRestrictedFromWork: 8,
          createdAt: new Date(`${testYear}-04-15`),
          type: 'job_restriction',
        }),
        // Case 5: Restricted Work
        createValidOshaReportInput({
          wasDeceased: false,
          daysAwayFromWork: 0,
          daysRestrictedFromWork: 2,
          createdAt: new Date(`${testYear}-05-15`),
          type: 'job_restriction',
        }),
        // Case 6: Other Case
        createValidOshaReportInput({
          wasDeceased: false,
          daysAwayFromWork: 0,
          daysRestrictedFromWork: 0,
          type: 'medical_treatment_beyond_first_aid',
          createdAt: new Date(`${testYear}-06-15`),
        }),
        // Case 7: Death (should be counted)
        createValidOshaReportInput({
          wasDeceased: true,
          createdAt: new Date(`${testYear}-07-15`),
          type: 'fatality',
        }),
        // Case 8: Archived (should be ignored)
        createValidOshaReportInput({
          wasDeceased: true,
          archivedAt: new Date(),
          createdAt: new Date(`${testYear}-08-15`),
          type: 'fatality',
        }),
        // Case 9: Different Year (should be ignored)
        createValidOshaReportInput({
          wasDeceased: true,
          createdAt: new Date(`${testYear + 1}-01-15`),
          type: 'fatality',
        }),
        // Case 10: Different Company (should be ignored)
        createValidOshaReportInput({
          upkeepCompanyId: 'other-comp',
          wasDeceased: true,
          createdAt: new Date(`${testYear}-09-15`),
          type: 'fatality',
        }),
      ]);
    });

    it('should correctly calculate all case summaries and rates', async () => {
      const caller = oshaSummaryRouter.createCaller(mockContext);
      const summary = await caller.getOshaCasesSummary({ year: testYear, oshaLocationId });

      expect(summary.deaths).toBe(2);
      expect(summary.totalDaysAway).toBe(15);
      expect(summary.restrictedWorkCases).toBe(2);
      expect(summary.otherCases).toBe(1);
      expect(summary.totalCases).toBe(7);
      expect(summary.trcRate).toBe(7);
      expect(summary.dartRate).toBe(4);
    });

    it('should return zero for rates if total hours worked is not available', async () => {
      await db.delete(oshaCompanyInformation);
      const caller = oshaSummaryRouter.createCaller(mockContext);
      const summary = await caller.getOshaCasesSummary({ year: testYear, oshaLocationId });

      expect(summary.totalCases).toBe(7);
      expect(summary.trcRate).toBe(0);
      expect(summary.dartRate).toBe(0);
    });
  });
});
