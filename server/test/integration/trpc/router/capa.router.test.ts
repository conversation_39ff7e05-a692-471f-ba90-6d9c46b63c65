import { createId } from '@paralleldrive/cuid2';
import { db } from '@server/db';
import { getUserById, getUsers, getUsersPublic, hasPermission } from '@server/services/user.service';
import { mockTechnicianUser, mockUser, mockViewOnlyUser } from '@server/test/fixtures/user';
import { createMockContext } from '@server/trpc/router/__tests__/test-utils';
import { capaRouter } from '@server/trpc/router/capa.router';
import {
  auditTrail,
  capaPriorityEnum,
  capas,
  capasJhas,
  capaTypeEnum,
  comments,
  entityTypeEnum,
  files,
  jha,
  jhaSteps,
  statusEnum,
} from '@shared/schema';
import { randomUUID } from 'crypto';
import { eq } from 'drizzle-orm';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

// Mock the user service
vi.mock('@server/services/user.service', () => ({
  getUserById: vi.fn(),
  getUsers: vi.fn(),
  getUsersPublic: vi.fn(),
  getUserPublic: vi.fn(),
  hasPermission: vi.fn().mockReturnValue(true),
  userHasEventPermission: vi.fn().mockReturnValue(true),
  canEditEvent: vi.fn().mockReturnValue(true),
  canExportEvent: vi.fn().mockReturnValue(true),
}));

// Mock the queue system
vi.mock('@server/queue/queue-utils', () => ({
  addJobToQueue: vi.fn().mockResolvedValue(undefined),
}));

vi.mock('@server/queue/job-names', () => ({
  QUEUE_JOB_NAMES: {
    UPDATE_CAPA_NOTIFICATION: 'update-capa-notification',
    ASSIGNED_CAPA_NOTIFICATION: 'assigned-capa-notification',
  },
}));

// Mock the notification service (this is still used for create operations)
vi.mock('@server/services/capa-notification.service', () => ({
  sendCapaAssignedNotification: vi.fn().mockResolvedValue(undefined),
  sendCapaUpdateNotification: vi.fn().mockResolvedValue(undefined),
}));

vi.mock('@server/services/location.service', () => ({
  getLocationById: vi.fn().mockResolvedValue({ id: '123', name: 'Test Location' }),
  getLocations: vi.fn().mockResolvedValue([{ id: '123', name: 'Test Location' }]),
}));

vi.mock('@server/services/asset.service', () => ({
  getAssets: vi.fn().mockResolvedValue([{ id: '123', name: 'Test Asset' }]),
}));

vi.mock('@server/services/ai.service', () => ({
  createCapaSummary: vi.fn().mockResolvedValue('Test summary'),
}));

describe('capa router', () => {
  const mockContext = createMockContext(mockUser) as any;
  let createdCapaId: string;

  // Helper function to create a test JHA
  const createTestJha = async (overrides = {}) => {
    const instanceId = randomUUID();
    const jhaData = {
      instanceId,
      title: `Test JHA ${Math.random().toString(36).substring(2, 5)}`,
      slug: `JHA-${Date.now()}-1.0`,
      version: '1.0',
      ownerId: mockUser.id,
      approverId: mockUser.id,
      status: 'approved' as const,
      upkeepCompanyId: mockUser.upkeepCompanyId,
      createdBy: mockUser.id,
      highestSeverity: 3,
      isPublic: true,
      ...overrides,
    };

    const [inserted] = await db.insert(jha).values(jhaData).returning({
      id: jha.id,
      instanceId: jha.instanceId,
      slug: jha.slug,
      title: jha.title,
      version: jha.version,
      status: jha.status,
    });

    return inserted;
  };

  // Helper function to create multiple test JHAs
  const createMultipleTestJhas = async (count: number) => {
    const jhas = [];
    for (let i = 0; i < count; i++) {
      const jhaData = await createTestJha({
        title: `Test JHA ${i + 1}`,
        slug: `JHA-${Date.now()}-${i + 1}-1.0`,
      });
      jhas.push(jhaData);
    }
    return jhas;
  };

  beforeEach(async () => {
    // Reset all mocks before each test
    vi.clearAllMocks();

    // Setup default mock implementations
    vi.mocked(getUserById).mockResolvedValue(mockUser);
    vi.mocked(getUsers).mockResolvedValue([mockUser]);
    // Set up getUsersPublic mock to return users based on objectId array
    vi.mocked(getUsersPublic).mockImplementation(async ({ objectId, upkeepCompanyId }) => {
      const results = [];
      if (objectId?.includes(mockUser.id)) {
        results.push({
          id: mockUser.id,
          firstName: mockUser.firstName,
          lastName: mockUser.lastName,
          fullName: mockUser.fullName,
          username: mockUser.email,
          email: mockUser.email,
        });
      }
      if (objectId?.includes(mockTechnicianUser.id)) {
        results.push({
          id: mockTechnicianUser.id,
          firstName: mockTechnicianUser.firstName,
          lastName: mockTechnicianUser.lastName,
          fullName: mockTechnicianUser.fullName,
          username: mockTechnicianUser.email,
          email: mockTechnicianUser.email,
        });
      }
      if (objectId?.includes(mockViewOnlyUser.id)) {
        results.push({
          id: mockViewOnlyUser.id,
          firstName: mockViewOnlyUser.firstName,
          lastName: mockViewOnlyUser.lastName,
          fullName: mockViewOnlyUser.fullName,
          username: mockViewOnlyUser.email,
          email: mockViewOnlyUser.email,
        });
      }
      return {
        noResults: results.length === 0,
        result: results,
        nextCursor: undefined,
      };
    });
    vi.mocked(hasPermission).mockReturnValue(true);

    // Re-establish AI service mock after clearAllMocks
    const { createCapaSummary } = await import('@server/services/ai.service');
    vi.mocked(createCapaSummary).mockResolvedValue('Test summary');

    // Set up getUserPublic mock to return appropriate users based on ID
    const { getUserPublic } = await import('@server/services/user.service');
    vi.mocked(getUserPublic).mockImplementation(async ({ id, upkeepCompanyId }) => {
      if (id === mockUser.id) {
        return {
          id: mockUser.id,
          firstName: mockUser.firstName,
          lastName: mockUser.lastName,
          fullName: mockUser.fullName,
          username: mockUser.email,
          email: mockUser.email,
        };
      }
      if (id === mockTechnicianUser.id) {
        return {
          id: mockTechnicianUser.id,
          firstName: mockTechnicianUser.firstName,
          lastName: mockTechnicianUser.lastName,
          fullName: mockTechnicianUser.fullName,
          username: mockTechnicianUser.email,
          email: mockTechnicianUser.email,
        };
      }
      if (id === mockViewOnlyUser.id) {
        return {
          id: mockViewOnlyUser.id,
          firstName: mockViewOnlyUser.firstName,
          lastName: mockViewOnlyUser.lastName,
          fullName: mockViewOnlyUser.fullName,
          username: mockViewOnlyUser.email,
          email: mockViewOnlyUser.email,
        };
      }
      return undefined;
    });
  });

  afterEach(async () => {
    // Clean up any created CAPAs, JHAs, and files
    await db.delete(files);
    await db.delete(comments);
    await db.delete(capasJhas);
    await db.delete(jhaSteps);
    await db.delete(jha);
    await db.delete(capas);
    await db.delete(auditTrail);
  });

  describe('create', () => {
    it('should not allow technicians to create capas', async () => {
      const mockTechnicianContext = createMockContext(mockTechnicianUser);
      const caller = capaRouter.createCaller(mockTechnicianContext);

      await expect(caller.create({} as any)).rejects.toThrow();
    });

    it('should not allow view only users to create capas', async () => {
      const mockViewOnlyContext = createMockContext(mockViewOnlyUser);
      const caller = capaRouter.createCaller(mockViewOnlyContext);

      await expect(caller.create({} as any)).rejects.toThrow();
    });

    it('should create a new capa and fetch a summary', async () => {
      const { addJobToQueue } = await import('@server/queue/queue-utils');
      const { QUEUE_JOB_NAMES } = await import('@server/queue/job-names');

      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const caller = capaRouter.createCaller(mockContext);
      const capa = await caller.create(mockCapaInput);

      // Store the ID for cleanup
      createdCapaId = capa!.id;

      // Verify the CAPA was created
      expect(capa).toBeDefined();
      expect(capa!.title).toBe(mockCapaInput.title);
      expect(capa!.slug).toBeDefined(); // this is generated by the database sequence
      expect(capa!.type).toBe(mockCapaInput.type);
      expect(capa!.rcaFindings).toBe(mockCapaInput.rcaFindings);
      expect(capa!.ownerId).toBe(mockCapaInput.ownerId);
      expect(capa!.status).toBe(mockCapaInput.status);
      expect(capa!.priority).toBe(mockCapaInput.priority);
      expect(capa!.summary).toBe('Test summary');

      // Verify we can find it in the database
      const dbCapa = await db.query.capas.findFirst({
        where: eq(capas.id, capa!.id),
      });

      expect(dbCapa).toBeDefined();
      expect(dbCapa?.title).toBe(mockCapaInput.title);

      // Verify permission check was called
      expect(hasPermission).toHaveBeenCalled();

      // Verify queue job was added for assigned notification
      expect(addJobToQueue).toHaveBeenCalledWith(
        QUEUE_JOB_NAMES.ASSIGNED_CAPA_NOTIFICATION,
        expect.objectContaining({
          capa: expect.objectContaining({
            id: createdCapaId,
            title: mockCapaInput.title,
          }),
          user: expect.objectContaining({
            id: mockUser.id,
          }),
          headers: expect.any(Object),
          needPartialCheck: false,
        }),
        expect.objectContaining({
          jobId: expect.stringMatching(new RegExp(`capa-${createdCapaId}-assign-\\d+`)),
        }),
      );
    });

    it('should create a new capa and not fetch a summary', async () => {
      const { addJobToQueue } = await import('@server/queue/queue-utils');
      const { QUEUE_JOB_NAMES } = await import('@server/queue/job-names');

      const mockCapaInput = {
        title: `Test CAPA with summary ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
        summary: 'Summary provided in the request',
      };

      const caller = capaRouter.createCaller(mockContext);
      const capa = await caller.create(mockCapaInput);

      expect(capa).toBeDefined();
      expect(capa!.title).toBe(mockCapaInput.title);
      expect(capa!.slug).toBeDefined(); // this is generated by the database sequence
      expect(capa!.type).toBe(mockCapaInput.type);
      expect(capa!.rcaFindings).toBe(mockCapaInput.rcaFindings);
      expect(capa!.ownerId).toBe(mockCapaInput.ownerId);
      expect(capa!.status).toBe(mockCapaInput.status);
      expect(capa!.priority).toBe(mockCapaInput.priority);
      expect(capa!.summary).toBe(mockCapaInput.summary);

      // Verify queue job was added for assigned notification
      expect(addJobToQueue).toHaveBeenCalledWith(
        QUEUE_JOB_NAMES.ASSIGNED_CAPA_NOTIFICATION,
        expect.objectContaining({
          capa: expect.objectContaining({
            title: mockCapaInput.title,
          }),
          user: expect.objectContaining({
            id: mockUser.id,
          }),
          headers: expect.any(Object),
          needPartialCheck: false,
        }),
        expect.objectContaining({
          jobId: expect.stringMatching(/capa-.*-assign-\d+/),
        }),
      );
    });

    it('should validate required fields', async () => {
      const caller = capaRouter.createCaller(mockContext);

      // Test missing required fields
      await expect(caller.create({} as any)).rejects.toThrow();

      // Test missing title
      await expect(
        caller.create({
          type: capaTypeEnum.enumValues[0],
          rcaFindings: 'Test findings',
          ownerId: mockUser.id,
          actionsToAddress: 'Test actions',
          status: statusEnum.enumValues[0],
          priority: capaPriorityEnum.enumValues[0],
        } as any),
      ).rejects.toThrow();
    });

    it('should create a capa with linked JHA instance IDs', async () => {
      // First create test JHAs
      const testJhas = await createMultipleTestJhas(2);

      const mockCapaInput = {
        title: `Test CAPA with JHAs ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
        linkedJhaInstanceIds: testJhas.map((jha) => jha.instanceId!),
      };

      const caller = capaRouter.createCaller(mockContext);
      const capa = await caller.create(mockCapaInput);

      // Store the ID for cleanup
      createdCapaId = capa!.id;

      // Verify the CAPA was created
      expect(capa).toBeDefined();
      expect(capa!.title).toBe(mockCapaInput.title);

      // Verify JHA links were created in the database
      const capaJhaLinks = await db.select().from(capasJhas).where(eq(capasJhas.capaId, capa!.id));

      expect(capaJhaLinks).toHaveLength(2);
      expect(capaJhaLinks.map((link) => link.jhaInstanceId)).toEqual(
        expect.arrayContaining(testJhas.map((jha) => jha.instanceId)),
      );
    });

    it('should create a capa with any provided JHA instance IDs (validation handled by client)', async () => {
      // Create one valid JHA and one invalid instance ID
      const validJha = await createTestJha();
      const invalidInstanceId = randomUUID();

      const mockCapaInput = {
        title: `Test CAPA with mixed JHAs ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
        linkedJhaInstanceIds: [validJha.instanceId!, invalidInstanceId],
      };

      const caller = capaRouter.createCaller(mockContext);
      const capa = await caller.create(mockCapaInput);

      // Store the ID for cleanup
      createdCapaId = capa!.id;

      // Verify both JHA links were created (service doesn't validate)
      const capaJhaLinks = await db.select().from(capasJhas).where(eq(capasJhas.capaId, capa!.id));

      expect(capaJhaLinks).toHaveLength(2);
      expect(capaJhaLinks.map((link) => link.jhaInstanceId)).toEqual(
        expect.arrayContaining([validJha.instanceId, invalidInstanceId]),
      );
    });
  });

  describe('getById', () => {
    it('should get a capa by id without files', async () => {
      // First create a CAPA
      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      // Now get it by ID
      const capa = await caller.getById({ id: createdCapaId });

      expect(capa).toBeDefined();
      expect(capa.id).toBe(createdCapaId);
      expect(capa.title).toBe(mockCapaInput.title);
      expect(capa.slug).toBeDefined();
      expect(capa.type).toBe(mockCapaInput.type);
      expect(capa.rcaFindings).toBe(mockCapaInput.rcaFindings);
      expect(capa.ownerId).toBe(mockCapaInput.ownerId);
      expect(capa.status).toBe(mockCapaInput.status);
      expect(capa.priority).toBe(mockCapaInput.priority);
      expect(capa.attachments).toBeNull();

      // Verify user service was called
      expect(hasPermission).toHaveBeenCalled();
    });

    it('should get a capa with its associated files', async () => {
      // First create a CAPA
      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      // Create an associated file
      await db.insert(files).values({
        upkeepCompanyId: mockUser.upkeepCompanyId,
        fileName: 'test.pdf',
        fileSize: 1024,
        mimeType: 'application/pdf',
        presignedUrl: 'https://test-bucket.s3.amazonaws.com/test.pdf',
        s3Key: 'test/test.pdf',
        s3Bucket: 'test-bucket',
        status: 'completed',
        entityType: 'capa',
        entityId: createdCapaId,
        uploadedBy: mockUser.id,
        expiresAt: new Date('2024-12-31'),
      });

      // Now get the CAPA by ID
      const capa = await caller.getById({ id: createdCapaId });

      // Verify CAPA data
      expect(capa).toBeDefined();
      expect(capa.id).toBe(createdCapaId);
      expect(capa.slug).toBeDefined();

      // Verify file data
      expect(capa.attachments).toBeDefined();
      expect(capa.attachments).toHaveLength(1);
      expect(capa.attachments[0]).toMatchObject({
        name: 'test.pdf',
        url: 'https://test-bucket.s3.amazonaws.com/test.pdf',
        type: 'application/pdf',
        size: 1024,
      });
    });

    it('should allow admin to fetch private capa', async () => {
      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
        privateToAdmins: true,
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      const capa = await caller.getById({ id: createdCapaId });

      expect(capa).toBeDefined();
      expect(capa.id).toBe(createdCapaId);
      expect(capa.title).toBe(mockCapaInput.title);
      expect(capa.slug).toBeDefined();
    });

    it('should throw error for non-admin fetching private capa', async () => {
      const mockContext = createMockContext(mockTechnicianUser);

      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
        privateToAdmins: true,
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      await expect(caller.getById({ id: 'non-existent-id' })).rejects.toThrow();
    });

    it('should allow technician to view CAPA where they are implementedBy', async () => {
      // Admin creates a CAPA with technician as implementer
      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id, // Admin owns it
        implementedBy: mockTechnicianUser.id, // Technician implements it
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const adminCaller = capaRouter.createCaller(mockContext);
      const createdCapa = await adminCaller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      // Technician should be able to view this CAPA
      const mockTechnicianContext = {
        ...createMockContext(mockTechnicianUser),
        needPartialCheck: true,
      };
      const technicianCaller = capaRouter.createCaller(mockTechnicianContext);
      const capa = await technicianCaller.getById({ id: createdCapaId });

      expect(capa).toBeDefined();
      expect(capa.id).toBe(createdCapaId);
      expect(capa.implementedBy?.id).toBe(mockTechnicianUser.id);
    });

    it('should allow technician to view CAPA where they are voePerformedBy', async () => {
      // Admin creates a CAPA with technician as VOE performer
      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id, // Admin owns it
        voePerformedBy: mockTechnicianUser.id, // Technician performs VOE
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const adminCaller = capaRouter.createCaller(mockContext);
      const createdCapa = await adminCaller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      // Technician should be able to view this CAPA
      const mockTechnicianContext = {
        ...createMockContext(mockTechnicianUser),
        needPartialCheck: true,
      };
      const technicianCaller = capaRouter.createCaller(mockTechnicianContext);
      const capa = await technicianCaller.getById({ id: createdCapaId });

      expect(capa).toBeDefined();
      expect(capa.id).toBe(createdCapaId);
      expect(capa.voePerformedBy?.id).toBe(mockTechnicianUser.id);
    });

    it('should allow technician to view CAPA where they are in teamMembersToNotify', async () => {
      // Admin creates a CAPA with technician in team members to notify
      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id, // Admin owns it
        teamMembersToNotify: [mockTechnicianUser.id], // Technician is notified
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const adminCaller = capaRouter.createCaller(mockContext);
      const createdCapa = await adminCaller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      // Technician should be able to view this CAPA
      const mockTechnicianContext = {
        ...createMockContext(mockTechnicianUser),
        needPartialCheck: true,
      };
      const technicianCaller = capaRouter.createCaller(mockTechnicianContext);
      const capa = await technicianCaller.getById({ id: createdCapaId });

      expect(capa).toBeDefined();
      expect(capa.id).toBe(createdCapaId);
      expect(capa.teamMembersToNotify).toContainEqual(expect.objectContaining({ id: mockTechnicianUser.id }));
    });

    it('should prevent technician from viewing CAPA where they have no association', async () => {
      // Admin creates a CAPA with no association to technician
      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id, // Admin owns it
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
        // No technician association
      };

      const adminCaller = capaRouter.createCaller(mockContext);
      const createdCapa = await adminCaller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      // Technician should NOT be able to view this CAPA
      const mockTechnicianContext = {
        ...createMockContext(mockTechnicianUser),
        needPartialCheck: true,
      };
      const technicianCaller = capaRouter.createCaller(mockTechnicianContext);

      await expect(technicianCaller.getById({ id: createdCapaId })).rejects.toThrow('not found');
    });

    it('should allow view-only user to view CAPA where they are in teamMembersToNotify', async () => {
      // Admin creates a CAPA with view-only user in team members to notify
      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id, // Admin owns it
        teamMembersToNotify: [mockViewOnlyUser.id], // View-only user is notified
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const adminCaller = capaRouter.createCaller(mockContext);
      const createdCapa = await adminCaller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      // View-only user should be able to view this CAPA
      const mockViewOnlyContext = {
        ...createMockContext(mockViewOnlyUser),
        needPartialCheck: true,
      };
      const viewOnlyCaller = capaRouter.createCaller(mockViewOnlyContext);
      const capa = await viewOnlyCaller.getById({ id: createdCapaId });

      expect(capa).toBeDefined();
      expect(capa.id).toBe(createdCapaId);
      expect(capa.teamMembersToNotify).toContainEqual(expect.objectContaining({ id: mockViewOnlyUser.id }));
    });

    it('should prevent view-only user from viewing CAPA where they are not notified', async () => {
      // Create a view-only user with different company ID to ensure proper isolation
      const isolatedViewOnlyUser = {
        ...mockViewOnlyUser,
        id: 'isolated-view-only',
        upkeepCompanyId: 'different-company',
      };

      // Admin creates a CAPA with no association to view-only user
      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id, // Admin owns it
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
        // No view-only user association
      };

      const adminCaller = capaRouter.createCaller(mockContext);
      const createdCapa = await adminCaller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      // View-only user from different company should NOT be able to view this CAPA
      const mockViewOnlyContext = {
        ...createMockContext(isolatedViewOnlyUser),
        needPartialCheck: true,
      };
      const viewOnlyCaller = capaRouter.createCaller(mockViewOnlyContext);

      await expect(viewOnlyCaller.getById({ id: createdCapaId })).rejects.toThrow();
    });

    it('should get a capa with linked JHA details', async () => {
      // First create test JHAs
      const testJhas = await createMultipleTestJhas(2);

      // Create a CAPA with linked JHAs
      const mockCapaInput = {
        title: `Test CAPA with JHA details ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
        linkedJhaInstanceIds: testJhas.map((jha) => jha.instanceId!),
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      // Now get it by ID and verify JHA details are included
      const capa = await caller.getById({ id: createdCapaId });

      expect(capa).toBeDefined();
      expect(capa.id).toBe(createdCapaId);
      expect(capa.title).toBe(mockCapaInput.title);

      // Verify linked JHA data is returned
      expect(capa.capaJhas).toBeDefined();
      expect(capa.capaJhas).toHaveLength(2);

      // Check that the JHA instance IDs are correct
      const linkedInstanceIds = capa.capaJhas.map((link: any) => link.jhaInstanceId);
      expect(linkedInstanceIds).toEqual(expect.arrayContaining(testJhas.map((jha) => jha.instanceId)));

      // Verify each JHA link contains title and slug
      capa.capaJhas.forEach((link: any) => {
        expect(link).toHaveProperty('id');
        expect(link).toHaveProperty('jhaInstanceId');
        expect(link).toHaveProperty('jha');

        if (link.jha) {
          expect(link.jha).toHaveProperty('title');
          expect(link.jha).toHaveProperty('slug');
          expect(typeof link.jha.title).toBe('string');
          expect(typeof link.jha.slug).toBe('string');

          // Verify the JHA data matches one of our test JHAs
          const matchingJha = testJhas.find((jha) => jha.instanceId === link.jhaInstanceId);
          expect(matchingJha).toBeDefined();
          expect(link.jha.title).toBe(matchingJha?.title);
          expect(link.jha.slug).toBe(matchingJha?.slug);
        }
      });
    });

    it('should get a capa with no linked JHAs', async () => {
      // Create a CAPA without linked JHAs
      const mockCapaInput = {
        title: `Test CAPA without JHAs ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      // Now get it by ID
      const capa = await caller.getById({ id: createdCapaId });

      expect(capa).toBeDefined();
      expect(capa.id).toBe(createdCapaId);
      expect(capa.title).toBe(mockCapaInput.title);

      // Verify no linked JHA data
      expect(capa.capaJhas).toEqual([]);
    });

    it('should get a capa with linked JHA that handles missing JHA data gracefully', async () => {
      // Create one valid JHA and one invalid instance ID
      const validJha = await createTestJha();
      const invalidInstanceId = randomUUID();

      // Create a CAPA with both valid and invalid JHA links
      const mockCapaInput = {
        title: `Test CAPA with missing JHA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
        linkedJhaInstanceIds: [validJha.instanceId!, invalidInstanceId],
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      // Now get it by ID and verify JHA details handling
      const capa = await caller.getById({ id: createdCapaId });

      expect(capa).toBeDefined();
      expect(capa.capaJhas).toHaveLength(2);

      // Check that we have one valid JHA and one with null JHA data
      const validLinks = capa.capaJhas.filter((link: any) => link.jha !== null);
      const invalidLinks = capa.capaJhas.filter((link: any) => link.jha === null);

      expect(validLinks).toHaveLength(1);
      expect(invalidLinks).toHaveLength(1);

      // Verify the valid JHA data
      const validLink = validLinks[0];
      expect(validLink.jhaInstanceId).toBe(validJha.instanceId);
      expect(validLink.jha).not.toBeNull();
      expect(validLink.jha!.title).toBe(validJha.title);
      expect(validLink.jha!.slug).toBe(validJha.slug);

      // Verify the invalid link has null JHA data but preserves the instance ID
      const invalidLink = invalidLinks[0];
      expect(invalidLink.jhaInstanceId).toBe(invalidInstanceId);
      expect(invalidLink.jha).toBeNull();
    });
  });

  describe('list', () => {
    it('should privateToAdmins CAPAs to a technician', async () => {
      // Create a private CAPA that technician shouldn't see
      await db.insert(capas).values({
        title: `Private CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id, // Admin-owned
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
        privateToAdmins: true,
        upkeepCompanyId: mockTechnicianUser.upkeepCompanyId,
        createdBy: mockUser.id,
        createdAt: new Date(),
      });

      // Create a non-private CAPA that technician should see (owned by technician)
      await db.insert(capas).values({
        title: `Non-Private CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockTechnicianUser.id, // Technician-owned
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
        privateToAdmins: false,
        upkeepCompanyId: mockTechnicianUser.upkeepCompanyId,
        createdBy: mockTechnicianUser.id,
        createdAt: new Date(),
      });

      // Setup technician context with needPartialCheck (technicians have partial permissions)
      const mockTechnicianContext = {
        ...createMockContext(mockTechnicianUser),
        needPartialCheck: true,
      };

      const caller = capaRouter.createCaller(mockTechnicianContext);
      const result = await caller.list({});

      // Technician should only see the non-private CAPA they own
      expect(result.result.length).toBe(1);
      expect(result.result[0].ownerId).toBe(mockTechnicianUser.id);
    });

    it('should list capas with default pagination', async () => {
      // First create a CAPA
      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
        privateToAdmins: true,
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      // Now list CAPAs
      const result = await caller.list({});

      expect(result).toBeDefined();
      expect(result.result).toBeDefined();
      expect(Array.isArray(result.result)).toBe(true);
      expect(result.result.length).toBeGreaterThan(0);
      expect(result.result[0].id).toBe(createdCapaId);

      // Verify user service was called
      expect(getUsersPublic).toHaveBeenCalledWith(
        expect.objectContaining({
          objectId: [mockUser.id],
        }),
      );
      expect(hasPermission).toHaveBeenCalled();
    });

    it('should list capas with filters', async () => {
      const caller = capaRouter.createCaller(mockContext);

      // Test with status filter
      const statusResult = await caller.list({
        status: [statusEnum.enumValues[0]],
      });
      expect(statusResult.result.every((capa) => capa.status === statusEnum.enumValues[0])).toBe(true);

      // Test with type filter
      const typeResult = await caller.list({
        type: [capaTypeEnum.enumValues[0]],
      });
      expect(typeResult.result.every((capa) => capa.type === capaTypeEnum.enumValues[0])).toBe(true);

      // Test with priority filter
      const priorityResult = await caller.list({
        priority: [capaPriorityEnum.enumValues[0]],
      });
      expect(priorityResult.result.every((capa) => capa.priority === capaPriorityEnum.enumValues[0])).toBe(true);

      // Verify user service was called for each filter
      expect(getUsersPublic).toHaveBeenCalledTimes(3);
      expect(hasPermission).toHaveBeenCalledTimes(3);
    });
  });

  describe('update', () => {
    it('should not allow technicians to update capas', async () => {
      const mockTechnicianContext = createMockContext(mockTechnicianUser);
      const caller = capaRouter.createCaller(mockTechnicianContext);

      await expect(caller.update({} as any)).rejects.toThrow();
    });

    it('should not allow view only users to update capas', async () => {
      const mockViewOnlyContext = createMockContext(mockViewOnlyUser);
      const caller = capaRouter.createCaller(mockViewOnlyContext);

      await expect(caller.update({} as any)).rejects.toThrow();
    });

    it('should update a capa', async () => {
      const { addJobToQueue } = await import('@server/queue/queue-utils');
      const { QUEUE_JOB_NAMES } = await import('@server/queue/job-names');

      // First create a CAPA
      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      // Now update it
      const updateInput = {
        id: createdCapaId,
        title: 'Updated CAPA Title',
        status: statusEnum.enumValues[1],
        priority: capaPriorityEnum.enumValues[1],
      };

      const updatedCapa = await caller.update(updateInput);

      expect(updatedCapa).toBeDefined();
      expect(updatedCapa.id).toBe(createdCapaId);
      expect(updatedCapa.title).toBe(updateInput.title);
      expect(updatedCapa.status).toBe(updateInput.status);
      expect(updatedCapa.priority).toBe(updateInput.priority);

      // Verify the update in the database
      const dbCapa = await db.query.capas.findFirst({
        where: eq(capas.id, createdCapaId),
      });

      expect(dbCapa).toBeDefined();
      expect(dbCapa?.title).toBe(updateInput.title);
      expect(dbCapa?.status).toBe(updateInput.status);
      expect(dbCapa?.priority).toBe(updateInput.priority);

      // Verify permission check was called
      expect(hasPermission).toHaveBeenCalled();

      // Verify queue job was added for notification
      expect(addJobToQueue).toHaveBeenCalledWith(
        QUEUE_JOB_NAMES.UPDATE_CAPA_NOTIFICATION,
        expect.objectContaining({
          capa: expect.objectContaining({
            id: createdCapaId,
            title: 'Updated CAPA Title',
          }),
          user: expect.objectContaining({
            id: mockUser.id,
          }),
          headers: expect.any(Object),
          needPartialCheck: false,
          actionPrefix: 'Updated',
        }),
        expect.objectContaining({
          jobId: expect.stringMatching(new RegExp(`capa-${createdCapaId}-update-\\d+`)),
        }),
      );
    });

    it('should throw error when updating non-existent capa', async () => {
      const caller = capaRouter.createCaller(mockContext);
      await expect(
        caller.update({
          id: 'non-existent-id',
          title: 'Updated Title',
        }),
      ).rejects.toThrow();
    });

    it('should update a capa and add linked JHA instance IDs', async () => {
      // First create a CAPA without JHA links
      const mockCapaInput = {
        title: `Test CAPA for JHA update ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      // Create test JHAs
      const testJhas = await createMultipleTestJhas(2);

      // Update the CAPA to add JHA links
      const updateInput = {
        id: createdCapaId,
        title: 'Updated CAPA with JHAs',
        linkedJhaInstanceIds: testJhas.map((jha) => jha.instanceId!),
      };

      const updatedCapa = await caller.update(updateInput);

      expect(updatedCapa).toBeDefined();
      expect(updatedCapa.id).toBe(createdCapaId);
      expect(updatedCapa.title).toBe(updateInput.title);

      // Verify JHA links were created
      const capaJhaLinks = await db.select().from(capasJhas).where(eq(capasJhas.capaId, createdCapaId));

      expect(capaJhaLinks).toHaveLength(2);
      expect(capaJhaLinks.map((link) => link.jhaInstanceId)).toEqual(
        expect.arrayContaining(testJhas.map((jha) => jha.instanceId)),
      );

      // Verify via getById that the links are returned
      const capaWithJhas = await caller.getById({ id: createdCapaId });
      expect(capaWithJhas.capaJhas).toHaveLength(2);
    });

    it('should update a capa and replace linked JHA instance IDs', async () => {
      // Create initial test JHAs
      const initialJhas = await createMultipleTestJhas(2);

      // Create a CAPA with initial JHA links
      const mockCapaInput = {
        title: `Test CAPA for JHA replacement ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
        linkedJhaInstanceIds: initialJhas.map((jha) => jha.instanceId!),
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      // Create new test JHAs
      const newJhas = await createMultipleTestJhas(3);

      // Update the CAPA to replace JHA links
      const updateInput = {
        id: createdCapaId,
        title: 'Updated CAPA with new JHAs',
        linkedJhaInstanceIds: newJhas.map((jha) => jha.instanceId!),
      };

      const updatedCapa = await caller.update(updateInput);

      expect(updatedCapa).toBeDefined();
      expect(updatedCapa.id).toBe(createdCapaId);

      // Verify old links were soft deleted and new links were created
      const allLinks = await db.select().from(capasJhas).where(eq(capasJhas.capaId, createdCapaId));

      const activeLinks = allLinks.filter((link) => link.deletedAt === null);
      const deletedLinks = allLinks.filter((link) => link.deletedAt !== null);

      expect(activeLinks).toHaveLength(3);
      expect(deletedLinks).toHaveLength(2);
      expect(activeLinks.map((link) => link.jhaInstanceId)).toEqual(
        expect.arrayContaining(newJhas.map((jha) => jha.instanceId)),
      );

      // Verify via getById that only active links are returned
      const capaWithJhas = await caller.getById({ id: createdCapaId });
      expect(capaWithJhas.capaJhas).toHaveLength(3);
    });

    it('should update a capa and remove all linked JHA instance IDs', async () => {
      // Create initial test JHAs
      const initialJhas = await createMultipleTestJhas(2);

      // Create a CAPA with initial JHA links
      const mockCapaInput = {
        title: `Test CAPA for JHA removal ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
        linkedJhaInstanceIds: initialJhas.map((jha) => jha.instanceId!),
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      // Update the CAPA to remove all JHA links
      const updateInput = {
        id: createdCapaId,
        title: 'Updated CAPA without JHAs',
        linkedJhaInstanceIds: [],
      };

      const updatedCapa = await caller.update(updateInput);

      expect(updatedCapa).toBeDefined();
      expect(updatedCapa.id).toBe(createdCapaId);

      // Verify all links were soft deleted
      const allLinks = await db.select().from(capasJhas).where(eq(capasJhas.capaId, createdCapaId));

      const activeLinks = allLinks.filter((link) => link.deletedAt === null);
      const deletedLinks = allLinks.filter((link) => link.deletedAt !== null);

      expect(activeLinks).toHaveLength(0);
      expect(deletedLinks).toHaveLength(2);

      // Verify via getById that no links are returned
      const capaWithJhas = await caller.getById({ id: createdCapaId });
      expect(capaWithJhas.capaJhas).toEqual([]);
    });

    it('should update a capa without affecting JHA links when not specified', async () => {
      // Create initial test JHAs
      const initialJhas = await createMultipleTestJhas(2);

      // Create a CAPA with initial JHA links
      const mockCapaInput = {
        title: `Test CAPA for JHA preservation ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
        linkedJhaInstanceIds: initialJhas.map((jha) => jha.instanceId!),
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      // Update the CAPA without specifying linkedJhaInstanceIds
      const updateInput = {
        id: createdCapaId,
        title: 'Updated CAPA title only',
        status: statusEnum.enumValues[1],
      };

      const updatedCapa = await caller.update(updateInput);

      expect(updatedCapa).toBeDefined();
      expect(updatedCapa.id).toBe(createdCapaId);
      expect(updatedCapa.title).toBe(updateInput.title);
      expect(updatedCapa.status).toBe(updateInput.status);

      // Verify JHA links were preserved
      const capaJhaLinks = await db.select().from(capasJhas).where(eq(capasJhas.capaId, createdCapaId));

      const activeLinks = capaJhaLinks.filter((link) => link.deletedAt === null);
      expect(activeLinks).toHaveLength(2);
      expect(activeLinks.map((link) => link.jhaInstanceId)).toEqual(
        expect.arrayContaining(initialJhas.map((jha) => jha.instanceId)),
      );

      // Verify via getById that links are still returned
      const capaWithJhas = await caller.getById({ id: createdCapaId });
      expect(capaWithJhas.capaJhas).toHaveLength(2);
    });
  });

  describe('toggleArchive', () => {
    it('should not allow technicians to toggle archive capas', async () => {
      const mockTechnicianContext = createMockContext(mockTechnicianUser);
      const caller = capaRouter.createCaller(mockTechnicianContext);

      await expect(caller.toggleArchive({} as any)).rejects.toThrow();
    });

    it('should not allow view only users to toggle archive capas', async () => {
      const mockViewOnlyContext = createMockContext(mockViewOnlyUser);
      const caller = capaRouter.createCaller(mockViewOnlyContext);

      await expect(caller.toggleArchive({} as any)).rejects.toThrow();
    });

    it('should archive an active capa', async () => {
      const { addJobToQueue } = await import('@server/queue/queue-utils');
      const { QUEUE_JOB_NAMES } = await import('@server/queue/job-names');

      // First create a CAPA
      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      // Verify it's initially not archived
      const initialCapa = await caller.getById({ id: createdCapaId });
      expect(initialCapa?.archivedAt).toBeNull();

      // Archive the CAPA
      const archivedResult = await caller.toggleArchive({ id: createdCapaId });

      expect(archivedResult).toBeDefined();
      expect(archivedResult?.id).toBe(createdCapaId);
      expect(archivedResult?.archivedAt).toBeTruthy();

      // Verify queue job was added for archive notification
      expect(addJobToQueue).toHaveBeenCalledWith(
        QUEUE_JOB_NAMES.UPDATE_CAPA_NOTIFICATION,
        expect.objectContaining({
          capa: expect.objectContaining({
            id: createdCapaId,
            archivedAt: expect.any(Date),
          }),
          user: expect.objectContaining({
            id: mockUser.id,
          }),
          headers: expect.any(Object),
          needPartialCheck: false,
          actionPrefix: 'Archived',
        }),
        expect.objectContaining({
          jobId: expect.stringMatching(new RegExp(`capa-${createdCapaId}-archive-\\d+`)),
        }),
      );
    });

    it('should unarchive an archived capa', async () => {
      const { addJobToQueue } = await import('@server/queue/queue-utils');
      const { QUEUE_JOB_NAMES } = await import('@server/queue/job-names');

      // First create a CAPA
      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      // First archive the CAPA
      const archivedResult = await caller.toggleArchive({ id: createdCapaId });
      expect(archivedResult?.archivedAt).toBeTruthy();

      // Clear previous mock calls
      vi.mocked(addJobToQueue).mockClear();

      // Then unarchive it
      const unarchivedResult = await caller.toggleArchive({ id: createdCapaId });

      expect(unarchivedResult).toBeDefined();
      expect(unarchivedResult?.id).toBe(createdCapaId);
      expect(unarchivedResult?.archivedAt).toBeNull();

      // Verify queue job was added for unarchive notification
      expect(addJobToQueue).toHaveBeenCalledWith(
        QUEUE_JOB_NAMES.UPDATE_CAPA_NOTIFICATION,
        expect.objectContaining({
          capa: expect.objectContaining({
            id: createdCapaId,
            archivedAt: null,
          }),
          user: expect.objectContaining({
            id: mockUser.id,
          }),
          headers: expect.any(Object),
          needPartialCheck: false,
          actionPrefix: 'Unarchived',
        }),
        expect.objectContaining({
          jobId: expect.stringMatching(new RegExp(`capa-${createdCapaId}-unarchive-\\d+`)),
        }),
      );
    });

    it('should preserve capa data when toggling archive status', async () => {
      // First create a CAPA
      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      // Archive the CAPA
      const archivedResult = await caller.toggleArchive({ id: createdCapaId });
      expect(archivedResult?.archivedAt).toBeTruthy();

      // Verify other fields remain unchanged by getting the full CAPA
      const archivedCapa = await caller.getById({ id: createdCapaId });
      expect(archivedCapa?.title).toBe(mockCapaInput.title);
      expect(archivedCapa?.type).toBe(mockCapaInput.type);
      expect(archivedCapa?.rcaFindings).toBe(mockCapaInput.rcaFindings);
      expect(archivedCapa?.ownerId).toBe(mockCapaInput.ownerId);
      expect(archivedCapa?.status).toBe(mockCapaInput.status);
      expect(archivedCapa?.priority).toBe(mockCapaInput.priority);

      // Unarchive and verify data is still preserved
      const unarchivedResult = await caller.toggleArchive({ id: createdCapaId });
      expect(unarchivedResult?.archivedAt).toBeNull();

      const unarchivedCapa = await caller.getById({ id: createdCapaId });
      expect(unarchivedCapa?.title).toBe(mockCapaInput.title);
      expect(unarchivedCapa?.type).toBe(mockCapaInput.type);
      expect(unarchivedCapa?.rcaFindings).toBe(mockCapaInput.rcaFindings);
      expect(unarchivedCapa?.ownerId).toBe(mockCapaInput.ownerId);
      expect(unarchivedCapa?.status).toBe(mockCapaInput.status);
      expect(unarchivedCapa?.priority).toBe(mockCapaInput.priority);
    });

    it('should handle toggle archive for capa with files', async () => {
      // First create a CAPA
      const mockCapaInput = {
        title: `Test CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const caller = capaRouter.createCaller(mockContext);
      const createdCapa = await caller.create(mockCapaInput);
      createdCapaId = createdCapa!.id;

      // Add a file to the CAPA
      await db.insert(files).values({
        id: 'test-file-capa-archive',
        upkeepCompanyId: mockUser.upkeepCompanyId,
        fileName: 'capa-archive-test.pdf',
        fileSize: 2048,
        mimeType: 'application/pdf',
        presignedUrl: 'https://test-bucket.s3.amazonaws.com/capa-archive-test.pdf',
        s3Key: 'test/capa-archive-test.pdf',
        s3Bucket: 'test-bucket',
        status: 'completed',
        entityType: 'capa',
        entityId: createdCapaId,
        uploadedBy: mockUser.id,
        expiresAt: new Date('2024-12-31'),
      });

      // Archive the CAPA
      const archivedResult = await caller.toggleArchive({ id: createdCapaId });
      expect(archivedResult?.archivedAt).toBeTruthy();

      // Verify the CAPA can still be retrieved with its files
      const capaWithFiles = await caller.getById({ id: createdCapaId });
      expect(capaWithFiles?.attachments).toBeDefined();
      expect(capaWithFiles?.attachments?.length).toBe(1);
      expect(capaWithFiles?.archivedAt).toBeTruthy();

      // Unarchive and verify files are still accessible
      const unarchivedResult = await caller.toggleArchive({ id: createdCapaId });
      expect(unarchivedResult?.archivedAt).toBeNull();

      const finalCapa = await caller.getById({ id: createdCapaId });
      expect(finalCapa?.attachments?.length).toBe(1);
      expect(finalCapa?.archivedAt).toBeNull();
    });

    it('should handle non-existent capa', async () => {
      const caller = capaRouter.createCaller(mockContext);

      // Try to toggle archive on a non-existent CAPA
      await expect(caller.toggleArchive({ id: 'non-existent-id' })).rejects.toThrow();
    });

    it('should validate required id parameter', async () => {
      const caller = capaRouter.createCaller(mockContext);

      // Test missing id field
      await expect(caller.toggleArchive({} as any)).rejects.toThrow();
    });

    it('should affect list results based on includeArchived filter', async () => {
      const caller = capaRouter.createCaller(mockContext);

      // Create two CAPAs
      const mockCapaInput1 = {
        title: 'Active CAPA',
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const mockCapaInput2 = {
        title: 'To Be Archived CAPA',
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const capa1 = await caller.create(mockCapaInput1);
      const capa2 = await caller.create(mockCapaInput2);

      // Archive one CAPA
      await caller.toggleArchive({ id: capa2!.id });

      // List without includeArchived should only show active CAPA
      const activeList = await caller.list({ includeArchived: false });
      expect(activeList.result.length).toBe(1);
      expect(activeList.result[0].title).toBe('Active CAPA');

      // List with includeArchived should show both CAPAs
      const allList = await caller.list({ includeArchived: true });
      expect(allList.result.length).toBe(2);

      const titles = allList.result.map((c) => c.title).sort();
      expect(titles).toEqual(['Active CAPA', 'To Be Archived CAPA']);
    });

    it('should allow technician to list CAPAs where they are assigned as implementer', async () => {
      // Create CAPA where technician is the implementer
      await db.insert(capas).values({
        title: `Technician Implementer CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id, // Admin owns it
        implementedBy: mockTechnicianUser.id, // Technician implements it
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
        upkeepCompanyId: mockTechnicianUser.upkeepCompanyId,
        createdBy: mockUser.id,
        createdAt: new Date(),
      });

      // Create another CAPA with no technician association
      await db.insert(capas).values({
        title: `Admin Only CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
        upkeepCompanyId: mockTechnicianUser.upkeepCompanyId,
        createdBy: mockUser.id,
        createdAt: new Date(),
      });

      const mockTechnicianContext = {
        ...createMockContext(mockTechnicianUser),
        needPartialCheck: true,
      };

      const caller = capaRouter.createCaller(mockTechnicianContext);
      const result = await caller.list({});

      // Technician should only see the CAPA they are assigned to implement
      expect(result.result.length).toBe(1);
      expect(result.result[0].title).toMatch(/Technician Implementer CAPA/);
    });

    it('should allow technician to list CAPAs where they are in teamMembersToNotify', async () => {
      // Create CAPA where technician is in team members to notify
      await db.insert(capas).values({
        title: `Technician Notified CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id, // Admin owns it
        teamMembersToNotify: [mockTechnicianUser.id], // Technician is notified
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
        upkeepCompanyId: mockTechnicianUser.upkeepCompanyId,
        createdBy: mockUser.id,
        createdAt: new Date(),
      });

      const mockTechnicianContext = {
        ...createMockContext(mockTechnicianUser),
        needPartialCheck: true,
      };

      const caller = capaRouter.createCaller(mockTechnicianContext);
      const result = await caller.list({});

      // Technician should see the CAPA they are notified about
      expect(result.result.length).toBeGreaterThanOrEqual(1);
      expect(result.result.some((capa: any) => capa.title.includes('Technician Notified CAPA'))).toBe(true);
    });

    it('should allow view-only user to list CAPAs where they are in teamMembersToNotify', async () => {
      // Create CAPA where view-only user is in team members to notify
      await db.insert(capas).values({
        title: `View-Only Notified CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id, // Admin owns it
        teamMembersToNotify: [mockViewOnlyUser.id], // View-only user is notified
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
        upkeepCompanyId: mockViewOnlyUser.upkeepCompanyId,
        createdBy: mockUser.id,
        createdAt: new Date(),
      });

      // Create another CAPA with no view-only user association
      await db.insert(capas).values({
        title: `Admin Only CAPA ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
        upkeepCompanyId: mockViewOnlyUser.upkeepCompanyId,
        createdBy: mockUser.id,
        createdAt: new Date(),
      });

      const mockViewOnlyContext = {
        ...createMockContext(mockViewOnlyUser),
        needPartialCheck: true,
      };

      const caller = capaRouter.createCaller(mockViewOnlyContext);
      const result = await caller.list({});

      // View-only user should only see the CAPA they are notified about
      expect(result.result.length).toBeGreaterThanOrEqual(1);
      expect(result.result.some((capa: any) => capa.title.includes('View-Only Notified CAPA'))).toBe(true);
      // Ensure all returned CAPAs have the view-only user in teamMembersToNotify
      expect(
        result.result.every(
          (capa: any) =>
            capa.teamMembersToNotify?.includes(mockViewOnlyUser.id) || capa.ownerId === mockViewOnlyUser.id,
        ),
      ).toBe(true);
    });
  });

  describe('listComments', () => {
    it('should list comments for a capa', async () => {
      const caller = capaRouter.createCaller(mockContext);
      const mockCapaInput = {
        title: `Test CAPA for comments ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const capa = await caller.create(mockCapaInput);

      // Create a comment directly in the database for testing
      const commentId = createId();
      await db
        .insert(comments)
        .values({
          id: commentId,
          entityType: 'capa',
          entityId: capa!.id,
          content: 'Test comment for list',
          userId: mockUser.id,
          upkeepCompanyId: mockUser.upkeepCompanyId,
        })
        .returning();

      const commentsList = await caller.listComments({
        entityId: capa!.id,
        options: { limit: 50, offset: 0 },
      });

      expect(commentsList).toBeDefined();
      expect(commentsList.length).toBe(1);
      expect(commentsList[0].content).toBe('Test comment for list');
    });
  });

  describe('getCommentById', () => {
    it('should get a comment by id', async () => {
      const caller = capaRouter.createCaller(mockContext);
      const mockCapaInput = {
        title: `Test CAPA for comment get ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const capa = await caller.create(mockCapaInput);

      // Create a comment directly in the database for testing
      const commentId = createId();
      await db
        .insert(comments)
        .values({
          id: commentId,
          entityType: 'capa',
          entityId: capa!.id,
          content: 'Test comment for get',
          userId: mockUser.id,
          upkeepCompanyId: mockUser.upkeepCompanyId,
        })
        .returning();

      const commentById = await caller.getCommentById({ id: commentId });

      expect(commentById).toBeDefined();
      expect(commentById?.id).toBe(commentId);
      expect(commentById?.content).toBe('Test comment for get');
    });
  });

  describe('deleteComment', () => {
    it('should delete a comment', async () => {
      const caller = capaRouter.createCaller(mockContext);
      const mockCapaInput = {
        title: `Test CAPA for comment delete ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const capa = await caller.create(mockCapaInput);

      // Create a comment directly in the database for testing
      const commentId = createId();
      await db
        .insert(comments)
        .values({
          id: commentId,
          entityType: 'capa',
          entityId: capa!.id,
          content: 'Test comment for delete',
          userId: mockUser.id,
          upkeepCompanyId: mockUser.upkeepCompanyId,
        })
        .returning();

      await caller.deleteComment({ id: commentId });
      const commentById = await caller.getCommentById({ id: commentId });

      expect(commentById).toBeNull();
    });
  });

  describe('createComment', () => {
    it('should create a new comment on a CAPA', async () => {
      const caller = capaRouter.createCaller(mockContext);
      const mockCapaInput = {
        title: `Test CAPA for comment ${Math.random().toString(36).substring(2, 5)}`,
        type: capaTypeEnum.enumValues[0],
        rcaFindings: 'Test findings',
        ownerId: mockUser.id,
        actionsToAddress: 'Test actions',
        status: statusEnum.enumValues[0],
        priority: capaPriorityEnum.enumValues[0],
        dueDate: new Date('2024-12-31'),
      };

      const capa = await caller.create(mockCapaInput);
      createdCapaId = capa!.id;

      const mockCommentInput = {
        content: 'Test comment for CAPA',
        entityId: capa!.id,
        entitySlug: capa!.slug,
        entityTitle: capa!.title,
        status: statusEnum.enumValues[0],
        entityType: entityTypeEnum.enumValues[1],
      };

      const comment = await caller.createComment(mockCommentInput);

      expect(comment).toBeDefined();
      expect(comment.content).toBe(mockCommentInput.content);
      expect(comment.id).toMatch(/^[a-z0-9]+$/); // CUID2 format
    });
  });
});
