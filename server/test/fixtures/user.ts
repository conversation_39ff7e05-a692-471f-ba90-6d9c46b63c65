import { USER_ACCOUNT_TYPES, generatePermissions } from '@shared/user-permissions';

export const mockUser = {
  id: '*********',
  username: 'testuser',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  fullName: 'Test User',
  upkeepCompanyId: '*********',
  groupName: 'Test Group',
  groupId: '*********',
  role: USER_ACCOUNT_TYPES.ADMIN,
  permissions: generatePermissions(USER_ACCOUNT_TYPES.ADMIN),
  featureFlags: {},
  hasEhsEnabled: true,
};

export const mockTechnicianUser = {
  id: 'tech123456',
  username: 'technician',
  email: '<EMAIL>',
  firstName: 'Technician',
  lastName: 'User',
  fullName: 'Technician User',
  upkeepCompanyId: '*********',
  groupName: 'Test Group',
  groupId: '*********',
  role: USER_ACCOUNT_TYPES.TECHNICIAN,
  permissions: generatePermissions(USER_ACCOUNT_TYPES.TECHNICIAN),
  featureFlags: {},
  hasEhsEnabled: true,
};

export const mockViewOnlyUser = {
  id: '*********',
  username: 'testuser',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  fullName: 'Test User',
  upkeepCompanyId: '*********',
  groupName: 'Test Group',
  groupId: '*********',
  role: USER_ACCOUNT_TYPES.VIEW_ONLY,
  permissions: generatePermissions(USER_ACCOUNT_TYPES.VIEW_ONLY),
  featureFlags: {},
  hasEhsEnabled: true,
};

export const mockPublicUser = {
  id: '*********',
  username: 'testuser',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  fullName: 'Test User',
};
