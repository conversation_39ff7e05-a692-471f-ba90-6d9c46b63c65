import { useCallback, useMemo, useState } from 'react';
import { trpc } from '@/providers/trpc';
import { useAppContext } from '@/contexts/app-context';
import { RouterOutputs } from '@shared/types/router.types';

type Notification = RouterOutputs['notification']['getNotifications'][number];

export const useNotifications = () => {
  const { user } = useAppContext();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isConnected, setIsConnected] = useState(false);

  // tRPC subscription for real-time notifications
  trpc.notification.onNotificationUpdate.useSubscription(undefined, {
    enabled: !!user,
    onData: (data) => {
      // Data is in the format { type: 'initial' | 'new' | 'update', notifications: Notification[] }
      if (data && data.notifications) {
        setNotifications(data.notifications);
      }
      setIsConnected(true);
    },
    onError: () => {
      setIsConnected(false);
    },
  });

  // tRPC mutations
  const markAsReadMutation = trpc.notification.markAsRead.useMutation();
  const markAllAsReadMutation = trpc.notification.markAllAsRead.useMutation();

  // Derive state from notifications
  const unreadCount = useMemo(() => {
    return notifications.filter((n: Notification) => !n.readAt && !n.archivedAt).length;
  }, [notifications]);

  const markAsRead = useCallback(
    (notificationId: string) => {
      markAsReadMutation.mutate({ notificationId });
    },
    [markAsReadMutation],
  );

  const markAllAsRead = useCallback(() => {
    markAllAsReadMutation.mutate();
  }, [markAllAsReadMutation]);

  return {
    notifications,
    unreadCount,
    isConnected,
    markAsRead,
    markAllAsRead,
  };
};
