import { useState, useCallback } from 'react';
import { trpc } from '@/providers/trpc';
import { isImage, TransientFileSchema } from '@shared/types/files.types';
import { entityTypeEnum } from '@shared/schema';
import { getFileSizeErrorMessage, MAX_FILE_SIZE_BYTES } from '@shared/files-utils';
import { z } from 'zod';

type ImageUploadOptions = {
  entityType: (typeof entityTypeEnum.enumValues)[number];
  entityId?: string;
};

type TransientFile = z.infer<typeof TransientFileSchema>;

export const useImageUpload = ({ isPublic, upkeepCompanyId }: { isPublic?: boolean; upkeepCompanyId?: string }) => {
  const [uploading, setUploading] = useState(false);
  const uploadMutation = trpc.image.createImageVariants.useMutation();
  const uploadMutationPublic = trpc.image.createImageVariantsPublic.useMutation();

  const fileToBase64 = useCallback((file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64 = reader.result as string;
        // Remove the data URL prefix (e.g., "data:image/jpeg;base64,")
        const base64Data = base64.split(',')[1];
        resolve(base64Data);
      };
      reader.readAsDataURL(file);
      reader.onerror = reject;
    });
  }, []);

  const uploadImage = useCallback(async (transientFile: TransientFile, options: ImageUploadOptions) => {
    if (!isImage(transientFile.type)) {
      throw new Error('Invalid file type. Please select an image file.');
    }

    if (transientFile.size > MAX_FILE_SIZE_BYTES) {
      throw new Error(getFileSizeErrorMessage(transientFile.name, transientFile.size));
    }

    try {
      setUploading(true);

      // Convert file to base64
      const fileData = await fileToBase64(transientFile.file!);

      // Note: The service behavior varies by image type:
      // - HEIC/HEIF: Processed synchronously, returned presigned URL is immediately usable
      // - Other images: Processing is queued, returned presigned URL is for DB record only
      // Components should use blob URLs for non-HEIC images until processing completes
      return isPublic
        ? await uploadMutationPublic.mutateAsync({
            fileData: fileData,
            fileName: transientFile.name,
            mimeType: transientFile.type,
            entityType: options.entityType,
            entityId: options.entityId,
            upkeepCompanyId: upkeepCompanyId!,
          })
        : await uploadMutation.mutateAsync({
            fileData: fileData,
            fileName: transientFile.name,
            mimeType: transientFile.type,
            entityType: options.entityType,
            entityId: options.entityId,
          });
    } catch (error) {
      console.error('Error uploading image:', error);
      throw error;
    } finally {
      setUploading(false);
    }
  }, []);

  return {
    uploadImage,
    uploading,
    isError: isPublic ? uploadMutationPublic.isError : uploadMutation.isError,
    error: isPublic ? uploadMutationPublic.error : uploadMutation.error,
  };
};
