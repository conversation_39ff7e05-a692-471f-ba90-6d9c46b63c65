import { AccessPointsFilters, AccessPointsFiltersSchema } from '@shared/types/access-points.types';
import { CapasFilters, CapasFiltersSchema } from '@shared/types/capas.types';
import { EventsFilters, EventsFiltersSchema } from '@shared/types/events.types';
import { JhaFilters, JhaFiltersSchema } from '@shared/types/jha.types';
import { SopFilters, SopFiltersSchema } from '@shared/types/sop.types';
import {
  OshaAgencyReportsFilters,
  OshaAgencyReportsFiltersSchema,
  OshaReportsFilters,
  OshaReportsFiltersSchema,
  OshaSummaryFilters,
  OshaSummaryFiltersSchema,
} from '@shared/types/osha.types';
import {
  ControlMeasuresFilters,
  ControlMeasuresFiltersSchema,
  HazardsFilters,
  HazardsFiltersSchema,
  OshaLocationsFilters,
  OshaLocationsFiltersSchema,
} from '@shared/types/settings.types';
import { useDebounce, useLocalStorage } from '@uidotdev/usehooks';

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useLocation, useSearch } from 'wouter';
import { z } from 'zod';

type FilterValue = string | number | boolean | Date | string[] | number[] | { from?: Date; to?: Date } | undefined;
type FilterType = 'string' | 'number' | 'boolean' | 'tristate' | 'array' | 'date' | 'dateRange';

interface FilterFieldConfig {
  type: FilterType;
  defaultValue?: FilterValue;
  itemType?: 'string' | 'number';
}

interface UseUrlFiltersConfig<T extends Record<string, FilterValue>> {
  schema: z.ZodType<T>;
  fields: Record<keyof T, FilterFieldConfig>;
  storageKey: string;
  defaultFilters: T;
  debounceMs?: number;
}

const serialize = (value: FilterValue, config: FilterFieldConfig): string | undefined => {
  if (value === undefined || value === null || value === config.defaultValue) return undefined;

  switch (config.type) {
    case 'string':
      return value === '' ? undefined : String(value);
    case 'number':
    case 'boolean':
    case 'tristate':
      return String(value);
    case 'array':
      return Array.isArray(value) && value.length > 0 ? value.join(',') : undefined;
    case 'date':
      return value instanceof Date ? value.toISOString() : String(value);
    case 'dateRange':
      if (typeof value === 'object' && value !== null && 'from' in value) {
        const { from, to } = value as { from?: Date; to?: Date };
        const parts: string[] = [];
        if (from) parts.push(`from:${from instanceof Date ? from.toISOString() : new Date(from).toISOString()}`);
        if (to) parts.push(`to:${to instanceof Date ? to.toISOString() : new Date(to).toISOString()}`);
        return parts.length > 0 ? parts.join('|') : undefined;
      }
      return undefined;
    default:
      return String(value);
  }
};

const deserialize = (value: string, config: FilterFieldConfig): FilterValue => {
  if (!value) return config.defaultValue;

  switch (config.type) {
    case 'string':
      return value;
    case 'number': {
      const num = Number(value);
      return isNaN(num) ? config.defaultValue : num;
    }
    case 'boolean':
      return value === 'true';
    case 'tristate':
      return value === 'true' ? true : value === 'false' ? false : config.defaultValue;
    case 'array': {
      const items = value.split(',').filter(Boolean);
      return config.itemType === 'number' ? items.map(Number).filter((n) => !isNaN(n)) : items;
    }
    case 'date': {
      const date = new Date(value);
      return isNaN(date.getTime()) ? config.defaultValue : date;
    }
    case 'dateRange': {
      const result: { from?: Date; to?: Date } = {};
      value.split('|').forEach((part) => {
        if (part.startsWith('from:')) {
          const date = new Date(part.substring(5));
          if (!isNaN(date.getTime())) result.from = date;
        } else if (part.startsWith('to:')) {
          const date = new Date(part.substring(3));
          if (!isNaN(date.getTime())) result.to = date;
        }
      });
      return Object.keys(result).length > 0 ? result : config.defaultValue;
    }
    default:
      return value;
  }
};

const isValueActive = (current: FilterValue, defaultValue: FilterValue): boolean => {
  if (Array.isArray(current) && Array.isArray(defaultValue)) {
    return current.length !== defaultValue.length || current.some((item, index) => item !== defaultValue[index]);
  }
  if (typeof current === 'object' && current !== null && typeof defaultValue === 'object' && defaultValue !== null) {
    return JSON.stringify(current) !== JSON.stringify(defaultValue);
  }
  return current !== defaultValue;
};

export function useUrlFilters<T extends Record<string, FilterValue>>(config: UseUrlFiltersConfig<T>) {
  const [location, navigate] = useLocation();
  const search = useSearch();
  const isInitializedRef = useRef(false);

  const [storedFilters, setStoredFilters] = useLocalStorage(`ehs/${config.storageKey}`, config.defaultFilters);

  const parsedFilters = useMemo(() => {
    const urlParams = new URLSearchParams(search || '');
    const parsed: Partial<T> = {};
    const hasUrlParams = urlParams.toString().length > 0;

    Object.entries(config.fields).forEach(([key, fieldConfig]) => {
      const paramValue = urlParams.get(key);
      try {
        let deserializedValue: FilterValue;

        if (paramValue !== null) {
          deserializedValue = deserialize(paramValue, fieldConfig);
        } else if (!hasUrlParams && config.storageKey && storedFilters[key as keyof T] !== undefined) {
          deserializedValue = storedFilters[key as keyof T];
        } else {
          deserializedValue = fieldConfig.defaultValue;
        }

        parsed[key as keyof T] = deserializedValue as T[keyof T];
      } catch (error) {
        console.warn(`Failed to parse URL parameter "${key}":`, error);
        parsed[key as keyof T] = fieldConfig.defaultValue as T[keyof T];
      }
    });

    try {
      return config.schema.parse({ ...config.defaultFilters, ...parsed }) as T;
    } catch (error) {
      console.warn('Invalid filters in URL, using defaults:', error);
      return { ...config.defaultFilters } as T;
    }
  }, [search, config.schema, config.defaultFilters, config.fields, config.storageKey, storedFilters]);

  const [filters, setFilters] = useState<T>(parsedFilters);
  const debouncedFilters = useDebounce(filters, config.debounceMs ?? 300);

  // Combined initialization and localStorage sync effect
  useEffect(() => {
    if (!isInitializedRef.current) {
      setFilters(parsedFilters);
      isInitializedRef.current = true;
    } else if (config.storageKey) {
      setStoredFilters(parsedFilters);

      // Sync localStorage to URL when no URL params exist
      const urlParams = new URLSearchParams(search || '');
      if (urlParams.toString().length === 0) {
        const hasStoredFilters = Object.keys(config.fields).some((key) =>
          isValueActive(storedFilters[key as keyof T], config.fields[key as keyof T].defaultValue),
        );

        if (hasStoredFilters) {
          const newUrlParams = new URLSearchParams();
          Object.entries(config.fields).forEach(([key, fieldConfig]) => {
            const serialized = serialize(storedFilters[key as keyof T], fieldConfig);
            if (serialized !== undefined) {
              newUrlParams.set(key, serialized);
            }
          });

          const newSearch = newUrlParams.toString();
          if (newSearch) {
            navigate(`${location.split('?')[0]}?${newSearch}`, { replace: true });
          }
        }
      }
    }
  }, [parsedFilters, config.storageKey, setStoredFilters, search, config.fields, storedFilters, location, navigate]);

  const updateUrl = useCallback(
    (newFilters: T) => {
      const urlParams = new URLSearchParams();

      Object.entries(config.fields).forEach(([key, fieldConfig]) => {
        const serialized = serialize(newFilters[key as keyof T], fieldConfig);
        if (serialized !== undefined) {
          urlParams.set(key, serialized);
        }
      });

      const newSearch = urlParams.toString();
      const currentPath = location.split('?')[0];
      const newUrl = newSearch ? `${currentPath}?${newSearch}` : currentPath;
      const currentFullUrl = search ? `${currentPath}?${search}` : currentPath;

      if (newUrl !== currentFullUrl) {
        navigate(newUrl, { replace: true });
      }

      if (config.storageKey) {
        setStoredFilters(newFilters);
      }
    },
    [location, navigate, search, config.fields, config.storageKey, setStoredFilters],
  );

  // Effect to update URL when debounced filters change
  useEffect(() => {
    if (isInitializedRef.current) {
      updateUrl(debouncedFilters);
    }
  }, [debouncedFilters, updateUrl]);

  const updateFilter = useCallback((key: keyof T, value: T[keyof T]) => {
    if (!isInitializedRef.current) return;
    setFilters((prev) => ({ ...prev, [key]: value }));
  }, []);

  const updateFilters = useCallback((updates: Partial<T>) => {
    if (!isInitializedRef.current) return;
    setFilters((prev) => ({ ...prev, ...updates }));
  }, []);

  const resetFilters = useCallback(() => {
    if (!isInitializedRef.current) return;
    setFilters(config.defaultFilters);

    // Clear URL
    navigate(location.split('?')[0], { replace: true });

    // Clear localStorage
    if (config.storageKey) {
      setStoredFilters(config.defaultFilters);
    }
  }, [config.defaultFilters, config.storageKey, location, navigate, setStoredFilters]);

  const hasActiveFilters = useMemo(
    () =>
      Object.keys(config.fields).some((key) =>
        isValueActive(filters[key as keyof T], config.fields[key as keyof T].defaultValue),
      ),
    [filters, config.fields],
  );

  const activeFilterCount = useMemo(
    () =>
      Object.keys(config.fields).reduce((count, key) => {
        const current = filters[key as keyof T];
        const fieldConfig = config.fields[key as keyof T];

        if (!isValueActive(current, fieldConfig.defaultValue)) return count;

        return count + (fieldConfig.type === 'array' && Array.isArray(current) ? current.length : 1);
      }, 0),
    [filters, config.fields],
  );

  return {
    filters: debouncedFilters, // For API calls
    immediateFilters: filters, // For UI inputs
    updateFilter,
    updateFilters,
    resetFilters,
    hasActiveFilters,
    activeFilterCount,
  };
}

const field = {
  array: (defaultValue: string[] = []): FilterFieldConfig => ({ type: 'array', itemType: 'string', defaultValue }),
  boolean: (defaultValue = false): FilterFieldConfig => ({ type: 'boolean', defaultValue }),
  tristate: (defaultValue?: boolean): FilterFieldConfig => ({ type: 'tristate', defaultValue }),
  string: (defaultValue?: string): FilterFieldConfig => ({ type: 'string', defaultValue }),
  number: (defaultValue: number): FilterFieldConfig => ({ type: 'number', defaultValue }),
  dateRange: (): FilterFieldConfig => ({ type: 'dateRange', defaultValue: undefined }),
};

export function useEventsUrlFilters() {
  return useUrlFilters<EventsFilters>({
    schema: EventsFiltersSchema,
    defaultFilters: {
      search: undefined,
      mustIncludeObjectIds: [],
      status: [],
      type: [],
      severity: [],
      oshaReportable: undefined,
      includeArchived: false,
      locationIds: [],
    },
    fields: {
      search: field.string(),
      status: field.array(),
      type: field.array(),
      severity: field.array(),
      oshaReportable: field.tristate(),
      includeArchived: field.boolean(),
      locationIds: field.array(),
      mustIncludeObjectIds: field.array(),
    },
    storageKey: 'events-filters',
  });
}

export function useCapasUrlFilters() {
  return useUrlFilters<CapasFilters>({
    schema: CapasFiltersSchema,
    defaultFilters: {
      search: undefined,
      mustIncludeObjectIds: [],
      status: [],
      type: [],
      priority: [],
      owner: [],
      dueDateRange: undefined,
      includeArchived: false,
      tags: [],
      eventId: undefined,
    },
    fields: {
      search: field.string(),
      mustIncludeObjectIds: field.array(),
      status: field.array(),
      type: field.array(),
      priority: field.array(),
      owner: field.array(),
      dueDateRange: field.dateRange(),
      includeArchived: field.boolean(),
      tags: field.array(),
      eventId: field.string(),
    },
    storageKey: 'capas-filters',
  });
}

export function useAccessPointsUrlFilters() {
  return useUrlFilters<AccessPointsFilters>({
    schema: AccessPointsFiltersSchema,
    defaultFilters: {
      includeArchived: false,
      locationId: [],
      status: [],
      createdBy: [],
      createdDateRange: undefined,
      search: undefined,
      mustIncludeObjectIds: [],
    },
    fields: {
      mustIncludeObjectIds: field.array(),
      search: field.string(),
      includeArchived: field.boolean(),
      locationId: field.array(),
      status: field.array(),
      createdBy: field.array(),
      createdDateRange: field.dateRange(),
    },
    storageKey: 'access-points-filters',
  });
}

export function useOshaReportsUrlFilters() {
  const currentYear = new Date().getFullYear();
  return useUrlFilters<OshaReportsFilters>({
    schema: OshaReportsFiltersSchema,
    defaultFilters: {
      search: undefined,
      mustIncludeObjectIds: [],
      caseType: [],
      includeArchived: undefined,
      year: currentYear,
      oshaLocationId: undefined,
    },
    fields: {
      search: field.string(),
      mustIncludeObjectIds: field.array(),
      caseType: field.array(),
      includeArchived: field.tristate(),
      year: field.number(currentYear),
      oshaLocationId: field.string(),
    },
    storageKey: 'osha-reports-filters',
  });
}

export function useOshaSummaryUrlFilters() {
  return useUrlFilters<OshaSummaryFilters>({
    schema: OshaSummaryFiltersSchema,
    defaultFilters: {
      year: new Date().getFullYear(),
      oshaLocationId: undefined,
    },
    fields: {
      year: field.number(new Date().getFullYear()),
      oshaLocationId: field.string(''),
    },
    storageKey: 'osha-summary-filters',
  });
}

export function useOshaAgencyReportsUrlFilters() {
  const currentYear = new Date().getFullYear();
  return useUrlFilters<OshaAgencyReportsFilters>({
    schema: OshaAgencyReportsFiltersSchema,
    defaultFilters: {
      search: undefined,
      mustIncludeObjectIds: [],
      oshaLocationId: undefined,
      typeOfIncident: [],
      dateRange: undefined,
      year: currentYear,
      showPrivacyCases: undefined,
      includeArchived: false,
    },
    fields: {
      search: field.string(),
      mustIncludeObjectIds: field.array(),
      oshaLocationId: field.string(),
      typeOfIncident: field.array(),
      dateRange: field.dateRange(),
      year: field.number(currentYear),
      showPrivacyCases: field.tristate(),
      includeArchived: field.boolean(),
    },
    storageKey: 'osha-agency-reports-filters',
  });
}

export function useOshaLocationsUrlFilters() {
  return useUrlFilters<OshaLocationsFilters>({
    schema: OshaLocationsFiltersSchema,
    defaultFilters: {
      search: undefined,
      mustIncludeObjectIds: [],
      createdBy: [],
      includeArchived: false,
      createdDateRange: undefined,
    },
    fields: {
      search: field.string(),
      mustIncludeObjectIds: field.array(),
      createdBy: field.array(),
      includeArchived: field.boolean(),
      createdDateRange: field.dateRange(),
    },
    storageKey: 'osha-locations-filters',
  });
}

export function useHazardsUrlFilters() {
  return useUrlFilters<HazardsFilters>({
    schema: HazardsFiltersSchema,
    defaultFilters: {
      search: undefined,
      mustIncludeObjectIds: [],
      type: [],
      createdBy: [],
      includeArchived: false,
      createdDateRange: undefined,
    },
    fields: {
      search: field.string(),
      mustIncludeObjectIds: field.array(),
      type: field.array(),
      createdBy: field.array(),
      includeArchived: field.boolean(),
      createdDateRange: field.dateRange(),
    },
    storageKey: 'hazards-filters',
  });
}

export function useJhaUrlFilters() {
  return useUrlFilters<JhaFilters>({
    schema: JhaFiltersSchema,
    defaultFilters: {
      search: undefined,
      mustIncludeObjectIds: [],
      status: [],
      ownerId: [],
      riskLevel: undefined,
      reviewStatus: undefined,
      includeArchived: false,
      locationIds: [],
      includeLocation: undefined,
    },
    fields: {
      search: field.string(undefined),
      mustIncludeObjectIds: field.array([]),
      status: field.array([]),
      ownerId: field.array([]),
      riskLevel: field.string(undefined),
      reviewStatus: field.string(undefined),
      includeArchived: field.boolean(false),
      locationIds: field.array([]),
      includeLocation: field.tristate(undefined),
    },
    storageKey: 'jha-filters',
  });
}

export function useControlMeasuresUrlFilters() {
  return useUrlFilters<ControlMeasuresFilters>({
    schema: ControlMeasuresFiltersSchema,
    defaultFilters: {
      search: undefined,
      mustIncludeObjectIds: [],
      includeArchived: false,
      type: [],
      createdBy: [],
      createdDateRange: undefined,
    },
    fields: {
      search: field.string(),
      mustIncludeObjectIds: field.array(),
      includeArchived: field.boolean(),
      type: field.array(),
      createdBy: field.array(),
      createdDateRange: field.dateRange(),
    },
    storageKey: 'control-measures-filters',
  });
}

export function useSopUrlFilters() {
  return useUrlFilters<SopFilters>({
    schema: SopFiltersSchema,
    defaultFilters: {
      search: undefined,
      mustIncludeObjectIds: [],
      status: [],
      ownerId: [],
      riskLevel: undefined,
      reviewStatus: undefined,
      includeArchived: false,
      locationIds: [],
      includeLocation: undefined,
    },
    fields: {
      search: field.string(undefined),
      mustIncludeObjectIds: field.array([]),
      status: field.array([]),
      ownerId: field.array([]),
      riskLevel: field.string(undefined),
      reviewStatus: field.string(undefined),
      includeArchived: field.boolean(false),
      locationIds: field.array([]),
      includeLocation: field.tristate(undefined),
    },
    storageKey: 'sop-filters',
  });
}
