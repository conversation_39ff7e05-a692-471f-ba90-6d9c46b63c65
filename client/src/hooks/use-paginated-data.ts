import { trpc } from '@/providers/trpc';
import { AccessPointsFilters } from '@shared/types/access-points.types';
import { CapasFilters } from '@shared/types/capas.types';
import { EventsFilters } from '@shared/types/events.types';
import { JhaFilters } from '@shared/types/jha.types';
import { SopFilters } from '@shared/types/sop.types';
import { OshaAgencyReportsFilters, OshaReportsFilters } from '@shared/types/osha.types';
import { RouterOutputs } from '@shared/types/router.types';
import { PublicSearchSchema } from '@shared/types/schema.types';
import { ControlMeasuresFilters, HazardsFilters, OshaLocationsFilters } from '@shared/types/settings.types';
import { useCallback, useMemo } from 'react';
import z from 'zod';

const TABLE_LIMIT = 20;

type BaseSearchParams = {
  enabled?: boolean;
  limit?: number;
};

type LocationSearchParams = BaseSearchParams & {
  search?: string;
  mustIncludeObjectIds?: string[];
};

type AssetSearchParams = BaseSearchParams & {
  locationId?: string;
  mustIncludeObjectIds?: string[];
};

type EventSearchParams = BaseSearchParams & {
  filters: EventsFilters;
};

type CapaSearchParams = BaseSearchParams & {
  filters: CapasFilters;
};

type AccessPointSearchParams = BaseSearchParams & {
  filters: AccessPointsFilters;
};

type OshaReportSearchParams = BaseSearchParams & {
  filters: OshaReportsFilters;
};

type AgencyReportSearchParams = BaseSearchParams & {
  filters: OshaAgencyReportsFilters;
};

type OshaLocationSearchParams = BaseSearchParams & {
  filters?: OshaLocationsFilters;
};

type HazardsSearchParams = BaseSearchParams & {
  filters?: HazardsFilters;
};

type ControlMeasuresSearchParams = BaseSearchParams & {
  filters?: ControlMeasuresFilters;
};

type JhaSearchParams = BaseSearchParams & {
  filters?: JhaFilters;
};

type SopSearchParams = BaseSearchParams & {
  filters?: SopFilters;
};

type WorkOrderSearchParams = BaseSearchParams & {
  search?: string;
};

type PublicLocationSearchParams = BaseSearchParams & {
  upkeepCompanyId: string;
  mustIncludeObjectIds?: string[];
  search?: string;
};

type PublicAssetSearchParams = {
  upkeepCompanyId: string;
  search?: string;
  locationId?: string;
  objectId?: string | string[];
  enabled?: boolean;
};

/**
 * Cursor-based hooks using useInfiniteQuery for efficient pagination
 */

/**
 * Hook for cursor-based location search with infinite loading
 */
export function useInfiniteLocations({ search = '', enabled = true, mustIncludeObjectIds }: LocationSearchParams = {}) {
  const response = trpc.location.search.useInfiniteQuery(
    { search, limit: TABLE_LIMIT, mustIncludeObjectIds },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['location']['search']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page: RouterOutputs['location']['search']) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

/**
 * Hook for cursor-based asset search with infinite loading
 */
export function useInfiniteAssets({ locationId, enabled = true, mustIncludeObjectIds }: AssetSearchParams) {
  const response = trpc.asset.search.useInfiniteQuery(
    { locationId, limit: TABLE_LIMIT, mustIncludeObjectIds },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['asset']['search']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page: RouterOutputs['asset']['search']) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

/**
 * Hook for cursor-based event list with infinite loading
 */
export function useInfiniteEvents({ filters, enabled = true }: EventSearchParams) {
  const response = trpc.event.list.useInfiniteQuery(
    { ...filters, limit: TABLE_LIMIT },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['event']['list']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

/**
 * Hook for cursor-based minimal event list with infinite loading
 */
export function useInfiniteMinimalEvents({ filters, enabled = true }: EventSearchParams) {
  const response = trpc.event.minimalList.useInfiniteQuery(
    { ...filters, limit: TABLE_LIMIT },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['event']['minimalList']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

/**
 * Hook for cursor-based CAPA list with infinite loading
 */
export function useInfiniteCapas({ filters, enabled = true }: CapaSearchParams) {
  const response = trpc.capa.list.useInfiniteQuery(
    { ...filters, limit: TABLE_LIMIT },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['capa']['list']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page: RouterOutputs['capa']['list']) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

export function useInfiniteSops({ filters, enabled = true }: SopSearchParams) {
  const response = trpc.sop.list.useInfiniteQuery(
    { ...filters, limit: TABLE_LIMIT },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['sop']['list']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page: RouterOutputs['sop']['list']) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

/**
 * Hook for cursor-based access points list with infinite loading
 */
export function useInfiniteAccessPoints({ enabled = true, filters }: AccessPointSearchParams) {
  const response = trpc.accessPoint.list.useInfiniteQuery(
    { ...filters, limit: TABLE_LIMIT },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['accessPoint']['list']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page: RouterOutputs['accessPoint']['list']) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

export function useInfiniteAgencyReports({ filters, enabled = true }: AgencyReportSearchParams) {
  const response = trpc.oshaAgencyReport.list.useInfiniteQuery(
    { ...filters, limit: TABLE_LIMIT },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['oshaAgencyReport']['list']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page: RouterOutputs['oshaAgencyReport']['list']) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

/**
 * Hook for cursor-based public location search with infinite loading
 */
export function useInfiniteLocationsPublic({
  upkeepCompanyId,
  search = '',
  mustIncludeObjectIds,
  enabled = true,
}: PublicLocationSearchParams) {
  const response = trpc.location.searchPublic.useInfiniteQuery(
    { upkeepCompanyId, search, mustIncludeObjectIds, limit: TABLE_LIMIT },
    {
      enabled: enabled && !!upkeepCompanyId,
      getNextPageParam: (lastPage: RouterOutputs['location']['searchPublic']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page: RouterOutputs['location']['searchPublic']) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

/**
 * Hook for cursor-based public asset search with infinite loading
 */
export function useInfiniteAssetsPublic({
  upkeepCompanyId,
  search = '',
  locationId,
  objectId,
  enabled = true,
}: PublicAssetSearchParams) {
  const response = trpc.asset.searchPublic.useInfiniteQuery(
    { upkeepCompanyId, search, locationId, objectId, limit: 50 },
    {
      enabled: enabled && !!upkeepCompanyId,
      getNextPageParam: (lastPage: RouterOutputs['asset']['searchPublic']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < 50) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page: RouterOutputs['asset']['searchPublic']) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

/**
 * Hook for cursor-based public user search with infinite loading
 */
export function useInfiniteUsersPublic({
  search = '',
  enabled = true,
  mustIncludeObjectIds,
  upkeepCompanyId,
  limit = TABLE_LIMIT,
  userAccountType,
  includeAllUserTypes,
}: z.infer<typeof PublicSearchSchema> & { enabled?: boolean }) {
  const response = trpc.user.getUsersPublic.useInfiniteQuery(
    { search, mustIncludeObjectIds, upkeepCompanyId, limit, userAccountType, includeAllUserTypes },
    {
      enabled: enabled && !!upkeepCompanyId,
      getNextPageParam: (lastPage: RouterOutputs['user']['getUsersPublic']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: false,
      refetchOnMount: true,
      initialCursor: 0,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page: RouterOutputs['user']['getUsersPublic']) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

/**
 * Hook for cursor-based event list with infinite loading
 */
export function useInfiniteOshaReports({ filters, enabled = true, limit = TABLE_LIMIT }: OshaReportSearchParams) {
  const response = trpc.oshaReport.list.useInfiniteQuery(
    { ...filters, limit, sortBy: 'createdAt', sortOrder: 'desc' },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['oshaReport']['list']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

/**
 * Hook for cursor-based OSHA locations list with infinite loading
 */
export function useInfiniteOshaLocations({ filters, enabled = true }: OshaLocationSearchParams) {
  const response = trpc.oshaLocation.list.useInfiniteQuery(
    { ...filters, limit: TABLE_LIMIT },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['oshaLocation']['list']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page: RouterOutputs['oshaLocation']['list']) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

/**
 * Hook for cursor-based minimal OSHA locations list with infinite loading
 */
export function useInfiniteMinimalOshaLocations({ filters, enabled = true }: OshaLocationSearchParams) {
  const response = trpc.oshaLocation.minimalList.useInfiniteQuery(
    { ...filters, limit: TABLE_LIMIT },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['oshaLocation']['minimalList']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page: RouterOutputs['oshaLocation']['minimalList']) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

/**
 * Hook for cursor-based hazards list with infinite loading
 */
export function useInfiniteHazards({ filters, enabled = true }: HazardsSearchParams) {
  const response = trpc.hazards.list.useInfiniteQuery(
    { ...filters, limit: TABLE_LIMIT },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['hazards']['list']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page: RouterOutputs['hazards']['list']) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

/**
 * Hook for cursor-based control measures list with infinite loading
 */
export function useInfiniteControlMeasures({ filters, enabled = true }: ControlMeasuresSearchParams) {
  const response = trpc.controlMeasures.list.useInfiniteQuery(
    { ...filters, limit: TABLE_LIMIT },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['controlMeasures']['list']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page: RouterOutputs['controlMeasures']['list']) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

/**
 * Hook for cursor-based JHA list with infinite loading
 */
export function useInfiniteJhas({ filters, enabled = true }: JhaSearchParams) {
  const response = trpc.jha.list.useInfiniteQuery(
    { ...filters, limit: TABLE_LIMIT },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['jha']['list']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page: RouterOutputs['jha']['list']) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

/**
 * Hook for cursor-based minimal JHA list with infinite loading
 */
export function useInfiniteMinimalJhas({ filters, enabled = true }: JhaSearchParams) {
  const response = trpc.jha.minimalList.useInfiniteQuery(
    { ...filters, limit: TABLE_LIMIT },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['jha']['minimalList']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page: RouterOutputs['jha']['minimalList']) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}

/**
 * Hook for cursor-based work order search with infinite loading
 */
export function useInfiniteWorkOrders({ search = '', enabled = true }: WorkOrderSearchParams = {}) {
  const response = trpc.workOrder.search.useInfiniteQuery(
    { search, limit: TABLE_LIMIT },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['workOrder']['search']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page: RouterOutputs['workOrder']['search']) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    ...response,
    data: allData,
    fetchNextPage: wrappedFetchNextPage,
  };
}
