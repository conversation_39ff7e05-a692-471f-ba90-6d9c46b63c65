import { useAppContext } from '@/contexts/app-context';
import { AllowedActions, Modules, PERMISSION_LEVELS } from '@shared/user-permissions';

export const usePermissions = () => {
  const { user } = useAppContext();

  const hasPermission = (module: Modules, action: AllowedActions, resourceOwnerId?: string) => {
    if (!user?.permissions) return false;

    const permission = user.permissions[module]?.[action];

    if (!permission) {
      return false;
    }

    if (permission === PERMISSION_LEVELS.FULL) {
      return true;
    }

    if (permission === PERMISSION_LEVELS.PARTIAL) {
      if (resourceOwnerId) {
        return resourceOwnerId === user.id;
      }
      return true;
    }

    return false;
  };

  return { hasPermission };
};
