import { trpc } from '@/providers/trpc';
import { GetPresignedUrlInputSchema, TransientFileSchema } from '@shared/types/files.types';
import { z } from 'zod';

type TransientFile = z.infer<typeof TransientFileSchema>;

export const useFileAssociation = (options?: { isPublic?: boolean; upkeepCompanyId?: string }) => {
  const privateUpdateMutation = trpc.file.update.useMutation();
  const publicUpdateMutation = trpc.file.updatePublic.useMutation();

  const associateFiles = async (
    files: TransientFile[],
    entityId: string,
    entityType: z.infer<typeof GetPresignedUrlInputSchema>['entityType'],
  ) => {
    if (!files.length) return;

    const filesToUpdate = files.filter((file) => file.id);

    if (!filesToUpdate.length) return;

    const updatePromises = filesToUpdate.map(async (file) => {
      try {
        if (options?.isPublic && options?.upkeepCompanyId) {
          return await publicUpdateMutation.mutateAsync({
            id: file.id!,
            status: 'completed',
            entityId,
            entityType,
            upkeepCompanyId: options.upkeepCompanyId,
          });
        } else {
          return await privateUpdateMutation.mutateAsync({
            id: file.id!,
            status: 'completed',
            entityId,
            entityType,
          });
        }
      } catch (error) {
        console.error(`Failed to associate file ${file.name}:`, error);
      }
    });

    await Promise.all(updatePromises);
  };

  return {
    associateFiles,
  };
};
