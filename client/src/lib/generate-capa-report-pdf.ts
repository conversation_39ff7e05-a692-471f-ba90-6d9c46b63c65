import { formatDate } from '@shared/date-utils';
import {
  CAPA_EFFECTIVENESS_STATUS_MAP,
  CAPA_PRIORITY_MAP,
  CAPA_TAGS_MAP,
  CAPA_TYPE_MAP,
  RCA_METHOD_MAP,
  ROOT_CAUSE_MAP,
} from '@shared/types/capas.types';
import {
  DocumentPreviewCapability,
  getDocumentPreviewCapability,
  getFileIcon,
  getMediaCategory,
  isSupportedMimeType,
  MediaCategory,
} from '@shared/types/files.types';
import { RouterOutputs } from '@shared/types/router.types';
import { STATUS_MAP } from '@shared/types/schema.types';
import { differenceInDays, format } from 'date-fns';

type CapaReport = RouterOutputs['capa']['getById'];

// Main export function
export const generateCapaReportPdf = (data: CapaReport): void => {
  // Create a printable div for the CAPA Report - professional layout
  const printDiv = document.createElement('div');
  printDiv.style.width = '8.5in';
  printDiv.style.minHeight = '11in';
  printDiv.style.margin = '0 auto';
  printDiv.style.padding = '0.5in';
  printDiv.style.fontFamily = '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif';
  printDiv.style.fontSize = '10pt';
  printDiv.style.position = 'relative';
  printDiv.style.lineHeight = '1.3';
  printDiv.style.backgroundColor = '#ffffff';
  printDiv.style.color = '#1a1a1a';
  printDiv.style.boxSizing = 'border-box';

  // Calculate overdue days
  const overdue = data.dueDate ? differenceInDays(new Date(), new Date(data.dueDate)) : 0;

  // CAPA Header Section - with professional styling
  const headerSection = document.createElement('div');
  headerSection.style.textAlign = 'center';
  headerSection.style.marginBottom = '0.2in';
  headerSection.style.borderBottom = '2px solid #2563eb';
  headerSection.style.paddingBottom = '0.1in';
  headerSection.innerHTML = `
    <table style="width: 100%; margin-bottom: 0.1in;">
      <tr>
        <td style="width: 120px; text-align: center; vertical-align: middle;">
          <div style="width: 100px; height: 60px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" style="width: 80px; height: 80px; fill: none; stroke: #2563eb; stroke-width: 2; stroke-linecap: round; stroke-linejoin: round;">
              <rect width="8" height="4" x="8" y="2" rx="1" ry="1"/>
              <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/>
              <path d="m9 14 2 2 4-4"/>
            </svg>
          </div>
        </td>
        <td style="text-align: left; vertical-align: middle; padding-left: 0.2in;">
          <h1 style="font-size: 16pt; margin: 0; color: #2563eb; font-weight: 700;">${data.title || 'CAPA Report'}</h1>
          <h2 style="font-size: 11pt; margin: 0; color: #333; font-weight: 500;">Corrective and Preventive Action</h2>
        </td>
      </tr>
    </table>
    <div style="background: #f8f9fa; padding: 0.08in; border-radius: 4px; border: 1px solid #dee2e6;">
      <p style="margin: 0; font-size: 8pt; color: #666; font-style: italic;">
        This report documents the corrective and preventive actions taken to address identified issues.
      </p>
      <p style="margin: 0.05in 0 0 0; font-size: 12pt; color: #2563eb; font-weight: 600;">CAPA ID: ${data.slug || 'N/A'}</p>
    </div>
  `;
  printDiv.appendChild(headerSection);

  // CAPA Basic Information Section
  const basicInfoSection = document.createElement('div');
  basicInfoSection.style.marginBottom = '0.15in';
  basicInfoSection.style.background = '#f8f9fa';
  basicInfoSection.style.border = '1px solid #dee2e6';
  basicInfoSection.style.borderRadius = '4px';
  basicInfoSection.style.padding = '0.1in';
  basicInfoSection.innerHTML = `
    <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #2563eb; font-weight: 600; border-bottom: 1px solid #2563eb; padding-bottom: 0.03in;">
      CAPA Information
    </h3>
    <table style="width: 100%; font-size: 9pt; border-collapse: collapse;">
      <tr>
        <td style="width: 50%; vertical-align: top; padding-right: 0.1in;">
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #2563eb; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Title:</strong><br>
            <span style="color: #212529; font-size: 10pt; font-weight: 600;">${data.title || 'Not specified'}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #2563eb; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Type:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.type ? CAPA_TYPE_MAP[data.type] : 'Not specified'}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #2563eb; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Priority:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.priority ? CAPA_PRIORITY_MAP[data.priority] : 'Not specified'}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #2563eb;">
            <strong style="color: #495057;">Status:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.status ? STATUS_MAP[data.status] : 'Not specified'}</span>
          </div>
        </td>
        <td style="width: 50%; vertical-align: top; padding-left: 0.1in;">
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #28a745; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Owner:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.owner?.fullName || 'Not assigned'}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #28a745; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Due Date:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.dueDate ? formatDate(data.dueDate) : 'Not specified'}</span>
            ${
              data.dueDate && new Date(data.dueDate) < new Date() && overdue > 0
                ? `<br><span style="color: #dc3545; font-size: 8pt; font-weight: 600;">(Overdue by ${overdue} days)</span>`
                : ''
            }
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #28a745; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Location:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.location?.name || 'Not specified'}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #28a745;">
            <strong style="color: #495057;">Created:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.createdAt ? formatDate(data.createdAt) : 'Not specified'}</span>
          </div>
        </td>
      </tr>
    </table>
  `;
  printDiv.appendChild(basicInfoSection);

  // Linked Event Section - moved to appear right after basic info
  if (data.event || data.eventId) {
    const linkedEventSection = document.createElement('div');
    linkedEventSection.style.marginBottom = '0.15in';
    linkedEventSection.style.background = '#f8f9fa';
    linkedEventSection.style.border = '1px solid #dee2e6';
    linkedEventSection.style.borderRadius = '4px';
    linkedEventSection.style.padding = '0.1in';
    linkedEventSection.innerHTML = `
      <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #2563eb; font-weight: 600; border-bottom: 1px solid #2563eb; padding-bottom: 0.03in;">
        Linked Safety Event
      </h3>
      <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #28a745;">
        <span style="color: #212529; font-size: 9pt; line-height: 1.4;">
          <strong style="color: #495057;">Event ID:</strong> ${data.eventSlug || data.event?.slug || 'N/A'}<br>
          <strong style="color: #495057;">Event Title:</strong> ${data.event?.title || 'Not specified'}<br>
          <strong style="color: #495057;">Event Type:</strong> ${data.event?.type || 'Not specified'}<br>
          <strong style="color: #495057;">Reported Date:</strong> ${data.event?.reportedAt ? formatDate(data.event.reportedAt) : 'Not specified'}<br>
          <strong style="color: #495057;">Event Status:</strong> ${data.event?.status ? STATUS_MAP[data.event.status] : 'Not specified'}
        </span>
      </div>
    `;
    printDiv.appendChild(linkedEventSection);
  }

  // Summary Section
  if (data.summary) {
    const summarySection = document.createElement('div');
    summarySection.style.marginBottom = '0.15in';
    summarySection.style.background = '#f8f9fa';
    summarySection.style.border = '1px solid #dee2e6';
    summarySection.style.borderRadius = '4px';
    summarySection.style.padding = '0.1in';
    summarySection.innerHTML = `
      <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #2563eb; font-weight: 600; border-bottom: 1px solid #2563eb; padding-bottom: 0.03in;">
        Summary
      </h3>
      <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #17a2b8;">
        <span style="color: #212529; font-size: 9pt; line-height: 1.4;">${data.summary}</span>
      </div>
    `;
    printDiv.appendChild(summarySection);
  }

  // Proposed Actions Section
  const proposedActionsSection = document.createElement('div');
  proposedActionsSection.style.marginBottom = '0.15in';
  proposedActionsSection.style.background = '#f8f9fa';
  proposedActionsSection.style.border = '1px solid #dee2e6';
  proposedActionsSection.style.borderRadius = '4px';
  proposedActionsSection.style.padding = '0.1in';
  proposedActionsSection.innerHTML = `
    <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #2563eb; font-weight: 600; border-bottom: 1px solid #2563eb; padding-bottom: 0.03in;">
      Proposed Actions
    </h3>
    <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #28a745;">
      <span style="color: #212529; font-size: 9pt; line-height: 1.4; white-space: pre-wrap;">${data.actionsToAddress || 'No proposed actions provided'}</span>
    </div>
  `;
  printDiv.appendChild(proposedActionsSection);

  // Root Cause Analysis Section
  const rcaSection = document.createElement('div');
  rcaSection.style.marginBottom = '0.15in';
  rcaSection.style.background = '#f8f9fa';
  rcaSection.style.border = '1px solid #dee2e6';
  rcaSection.style.borderRadius = '4px';
  rcaSection.style.padding = '0.1in';

  // Check if RCA Findings contains numbered list patterns
  const hasNumberedList =
    data.rcaFindings &&
    (data.rcaFindings.includes('1.') ||
      data.rcaFindings.includes('1)') ||
      /\d+\.\s/.test(data.rcaFindings) ||
      /\d+\)\s/.test(data.rcaFindings));

  rcaSection.innerHTML = `
    <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #2563eb; font-weight: 600; border-bottom: 1px solid #2563eb; padding-bottom: 0.03in;">
      Root Cause Analysis
    </h3>
    ${
      hasNumberedList
        ? `
      <!-- RCA Findings takes full width when it's a list -->
      <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #6f42c1; margin-bottom: 0.05in;">
        <strong style="color: #495057;">RCA Findings:</strong><br>
        <span style="color: #212529; font-size: 9pt; line-height: 1.4; white-space: pre-wrap;">${data.rcaFindings || 'No findings provided'}</span>
      </div>
      <table style="width: 100%; font-size: 9pt; border-collapse: collapse;">
        <tr>
          <td style="width: 50%; vertical-align: top; padding-right: 0.05in;">
            <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #fd7e14;">
              <strong style="color: #495057;">RCA Method:</strong><br>
              <span style="color: #212529; font-size: 10pt;">${data.rcaMethod ? RCA_METHOD_MAP[data.rcaMethod] : 'Not specified'}</span>
            </div>
          </td>
          <td style="width: 50%; vertical-align: top; padding-left: 0.05in;">
            <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #fd7e14;">
              <strong style="color: #495057;">Root Causes:</strong><br>
              <span style="color: #212529; font-size: 10pt;">
                ${
                  data.rootCauses && data.rootCauses.length > 0
                    ? data.rootCauses.map((cause) => ROOT_CAUSE_MAP[cause] || cause.replace(/_/g, ' ')).join(', ')
                    : 'Not specified'
                }
              </span>
              ${data.otherRootCause ? `<br><span style="color: #666; font-size: 8pt;">Other: ${data.otherRootCause}</span>` : ''}
            </div>
          </td>
        </tr>
      </table>
    `
        : `
      <!-- Original layout when RCA Findings is not a list -->
      <table style="width: 100%; font-size: 9pt; border-collapse: collapse;">
        <tr>
          <td style="width: 50%; vertical-align: top; padding-right: 0.1in;">
            <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #fd7e14; margin-bottom: 0.05in;">
              <strong style="color: #495057;">RCA Method:</strong><br>
              <span style="color: #212529; font-size: 10pt;">${data.rcaMethod ? RCA_METHOD_MAP[data.rcaMethod] : 'Not specified'}</span>
            </div>
            <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #fd7e14;">
              <strong style="color: #495057;">Root Causes:</strong><br>
              <span style="color: #212529; font-size: 10pt;">
                ${
                  data.rootCauses && data.rootCauses.length > 0
                    ? data.rootCauses.map((cause) => ROOT_CAUSE_MAP[cause] || cause.replace(/_/g, ' ')).join(', ')
                    : 'Not specified'
                }
              </span>
              ${data.otherRootCause ? `<br><span style="color: #666; font-size: 8pt;">Other: ${data.otherRootCause}</span>` : ''}
            </div>
          </td>
          <td style="width: 50%; vertical-align: top; padding-left: 0.1in;">
            <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #6f42c1;">
              <strong style="color: #495057;">RCA Findings:</strong><br>
              <span style="color: #212529; font-size: 9pt; line-height: 1.4;">${data.rcaFindings || 'No findings provided'}</span>
            </div>
          </td>
        </tr>
      </table>
    `
    }
  `;
  printDiv.appendChild(rcaSection);

  // Actual Actions Taken Section
  if (data.actionsImplemented) {
    const actualActionsSection = document.createElement('div');
    actualActionsSection.style.marginBottom = '0.15in';
    actualActionsSection.style.background = '#f8f9fa';
    actualActionsSection.style.border = '1px solid #dee2e6';
    actualActionsSection.style.borderRadius = '4px';
    actualActionsSection.style.padding = '0.1in';
    actualActionsSection.innerHTML = `
      <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #2563eb; font-weight: 600; border-bottom: 1px solid #2563eb; padding-bottom: 0.03in;">
        Actual Actions Taken
      </h3>
      <table style="width: 100%; font-size: 9pt; border-collapse: collapse;">
        <tr>
          <td style="width: 70%; vertical-align: top; padding-right: 0.1in;">
            <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #dc3545;">
              <strong style="color: #495057;">Actions Implemented:</strong><br>
              <span style="color: #212529; font-size: 9pt; line-height: 1.4; white-space: pre-wrap;">${data.actionsImplemented}</span>
            </div>
          </td>
          <td style="width: 30%; vertical-align: top; padding-left: 0.1in;">
            <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #ffc107; margin-bottom: 0.05in;">
              <strong style="color: #495057;">Implementation Date:</strong><br>
              <span style="color: #212529; font-size: 10pt;">${data.implementationDate ? formatDate(data.implementationDate) : 'Not specified'}</span>
            </div>
            <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #ffc107;">
              <strong style="color: #495057;">Implemented By:</strong><br>
              <span style="color: #212529; font-size: 10pt;">${data.implementedBy?.fullName || 'Not specified'}</span>
            </div>
          </td>
        </tr>
      </table>
    `;
    printDiv.appendChild(actualActionsSection);
  }

  // Verification of Effectiveness Section
  if (data.verificationFindings || data.voePerformedBy) {
    const voeSection = document.createElement('div');
    voeSection.style.marginBottom = '0.15in';
    voeSection.style.background = '#f8f9fa';
    voeSection.style.border = '1px solid #dee2e6';
    voeSection.style.borderRadius = '4px';
    voeSection.style.padding = '0.1in';
    voeSection.innerHTML = `
      <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #2563eb; font-weight: 600; border-bottom: 1px solid #2563eb; padding-bottom: 0.03in;">
        Verification of Effectiveness (VoE)
      </h3>
      <table style="width: 100%; font-size: 9pt; border-collapse: collapse;">
        <tr>
          <td style="width: 50%; vertical-align: top; padding-right: 0.1in;">
            <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #17a2b8; margin-bottom: 0.05in;">
              <strong style="color: #495057;">VoE Performed By:</strong><br>
              <span style="color: #212529; font-size: 10pt;">${data.voePerformedBy?.fullName || 'Not specified'}</span>
            </div>
            <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #17a2b8;">
              <strong style="color: #495057;">VoE Date:</strong><br>
              <span style="color: #212529; font-size: 10pt;">${data.voeDate ? formatDate(data.voeDate) : 'Not completed'}</span>
            </div>
          </td>
          <td style="width: 50%; vertical-align: top; padding-left: 0.1in;">
            <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #6f42c1; margin-bottom: 0.05in;">
              <strong style="color: #495057;">Effectiveness Status:</strong><br>
              <span style="color: #212529; font-size: 10pt; font-weight: 600;">${data.effectivenessStatus ? CAPA_EFFECTIVENESS_STATUS_MAP[data.effectivenessStatus] : 'Not evaluated'}</span>
            </div>
            <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #6f42c1;">
              <strong style="color: #495057;">Verification Findings:</strong><br>
              <span style="color: #212529; font-size: 9pt; line-height: 1.4;">${data.verificationFindings || 'No findings documented'}</span>
            </div>
          </td>
        </tr>
      </table>
    `;
    printDiv.appendChild(voeSection);
  }

  // Tags Section
  if (data.tags && data.tags.length > 0) {
    const tagsSection = document.createElement('div');
    tagsSection.style.marginBottom = '0.15in';
    tagsSection.style.background = '#f8f9fa';
    tagsSection.style.border = '1px solid #dee2e6';
    tagsSection.style.borderRadius = '4px';
    tagsSection.style.padding = '0.1in';
    tagsSection.innerHTML = `
      <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #2563eb; font-weight: 600; border-bottom: 1px solid #2563eb; padding-bottom: 0.03in;">
        Tags
      </h3>
      <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #fd7e14;">
        <span style="color: #212529; font-size: 9pt;">
          ${data.tags.map((tag) => CAPA_TAGS_MAP[tag] || tag).join(', ')}
        </span>
      </div>
    `;
    printDiv.appendChild(tagsSection);
  }

  // Attachments Section (if any)
  if (data.attachments && data.attachments.length > 0) {
    const attachmentsSection = document.createElement('div');
    attachmentsSection.style.marginBottom = '0.15in';
    attachmentsSection.style.background = '#f8f9fa';
    attachmentsSection.style.border = '1px solid #dee2e6';
    attachmentsSection.style.borderRadius = '4px';
    attachmentsSection.style.padding = '0.1in';
    // Add gentle page break handling for attachments section - only avoid breaking within
    attachmentsSection.style.pageBreakInside = 'avoid';

    // Categorize attachment files using the proper type system
    const images = data.attachments.filter((file) => getMediaCategory(file.type) === MediaCategory.IMAGE);
    const documents = data.attachments.filter((file) => getMediaCategory(file.type) === MediaCategory.DOCUMENT);
    const videos = data.attachments.filter((file) => getMediaCategory(file.type) === MediaCategory.VIDEO);
    const otherFiles = data.attachments.filter((file) => getMediaCategory(file.type) === MediaCategory.UNKNOWN);

    let attachmentsContent = `
      <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #2563eb; font-weight: 600; border-bottom: 1px solid #2563eb; padding-bottom: 0.03in;" class="attachments-header">
        Attachments (${data.attachments.length} files)
      </h3>
    `;

    // Display images inline
    if (images.length > 0) {
      attachmentsContent += `
        <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #28a745; margin-bottom: 0.08in;">
          <strong style="color: #495057; font-size: 9pt;">📸 Images (${images.length}):</strong>
          <div style="margin-top: 0.05in; display: flex; flex-wrap: wrap; gap: 0.05in;">
      `;

      images.forEach((image) => {
        const fileSize = image.size ? `${(image.size / 1024 / 1024).toFixed(2)} MB` : 'Unknown size';
        attachmentsContent += `
          <div style="margin-bottom: 0.08in; text-align: center; max-width: 2.5in; page-break-inside: avoid;">
            <img src="${image.url}" alt="${image.name}" style="max-width: 2.5in; max-height: 2in; border: 1px solid #dee2e6; border-radius: 3px; margin-bottom: 0.03in;" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
            <div style="display: none; padding: 0.2in; background: #f8f9fa; border: 1px dashed #dee2e6; border-radius: 3px; color: #666; font-size: 8pt;">
              📷 Image preview unavailable<br>
              <a href="${image.url}" target="_blank" style="color: #2563eb; text-decoration: none;">View Image</a>
            </div>
            <div style="font-size: 8pt; color: #666; line-height: 1.2;">
              <strong>${image.name}</strong><br>
              ${fileSize}
            </div>
          </div>
        `;
      });

      attachmentsContent += `
          </div>
        </div>
      `;
    }

    // Display document links with preview capability information
    if (documents.length > 0) {
      attachmentsContent += `
        <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #fd7e14; margin-bottom: 0.08in;">
          <strong style="color: #495057; font-size: 9pt;">📄 Documents (${documents.length}):</strong>
          <div style="margin-top: 0.05in;">
      `;

      documents.forEach((doc) => {
        const fileSize = doc.size ? `${(doc.size / 1024 / 1024).toFixed(2)} MB` : 'Unknown size';
        const fileIcon = getFileIcon(doc.type || '');
        const previewCapability = getDocumentPreviewCapability(doc.type);
        const isPreviewable =
          previewCapability === DocumentPreviewCapability.OBJECT_EMBED ||
          previewCapability === DocumentPreviewCapability.TEXT_CONTENT;

        attachmentsContent += `
          <div style="margin-bottom: 0.05in; padding: 0.05in; background: #f8f9fa; border-radius: 3px; font-size: 8pt;">
            <span style="margin-right: 0.05in;">${fileIcon}</span>
            <strong><a href="${doc.url}" target="_blank" style="color: #2563eb; text-decoration: none;">${doc.name}</a></strong>
            <span style="color: #666; margin-left: 0.1in;">(${fileSize})</span>
            ${isPreviewable ? '<span style="color: #28a745; font-size: 7pt; margin-left: 0.05in;">• Previewable</span>' : ''}
          </div>
        `;
      });

      attachmentsContent += `
          </div>
        </div>
      `;
    }

    // Display video links
    if (videos.length > 0) {
      attachmentsContent += `
        <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #dc2626; margin-bottom: 0.08in;">
          <strong style="color: #495057; font-size: 9pt;">🎥 Videos (${videos.length}):</strong>
          <div style="margin-top: 0.05in;">
      `;

      videos.forEach((video) => {
        const fileSize = video.size ? `${(video.size / 1024 / 1024).toFixed(2)} MB` : 'Unknown size';
        attachmentsContent += `
          <div style="margin-bottom: 0.05in; padding: 0.05in; background: #f8f9fa; border-radius: 3px; font-size: 8pt;">
            <span style="margin-right: 0.05in;">🎬</span>
            <strong><a href="${video.url}" target="_blank" style="color: #2563eb; text-decoration: none;">${video.name}</a></strong>
            <span style="color: #666; margin-left: 0.1in;">(${fileSize})</span>
            <div style="font-size: 7pt; color: #888; margin-top: 0.02in;">
              📹 Video file - Click link to view
            </div>
          </div>
        `;
      });

      attachmentsContent += `
          </div>
        </div>
      `;
    }

    // Display unsupported/unknown files
    if (otherFiles.length > 0) {
      attachmentsContent += `
        <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #6c757d;">
          <strong style="color: #495057; font-size: 9pt;">📎 Other Files (${otherFiles.length}):</strong>
          <div style="margin-top: 0.05in;">
      `;

      otherFiles.forEach((file) => {
        const fileSize = file.size ? `${(file.size / 1024 / 1024).toFixed(2)} MB` : 'Unknown size';
        const fileIcon = getFileIcon(file.type || '');
        attachmentsContent += `
          <div style="margin-bottom: 0.05in; padding: 0.05in; background: #f8f9fa; border-radius: 3px; font-size: 8pt;">
            <span style="margin-right: 0.05in;">${fileIcon}</span>
            <strong><a href="${file.url}" target="_blank" style="color: #2563eb; text-decoration: none;">${file.name}</a></strong>
            <span style="color: #666; margin-left: 0.1in;">(${fileSize})</span>
            <div style="font-size: 7pt; color: #888; margin-top: 0.02in;">
              Type: ${file.type || 'Unknown'} ${!isSupportedMimeType(file.type || '') ? '• Unsupported format' : ''}
            </div>
          </div>
        `;
      });

      attachmentsContent += `
          </div>
        </div>
      `;
    }

    attachmentsSection.innerHTML = attachmentsContent;
    printDiv.appendChild(attachmentsSection);
  }

  // Footer Section
  const footerSection = document.createElement('div');
  footerSection.style.marginTop = '0.2in';
  footerSection.style.paddingTop = '0.1in';
  footerSection.style.borderTop = '1px solid #dee2e6';
  footerSection.style.fontSize = '8pt';
  footerSection.style.color = '#666';
  footerSection.innerHTML = `
    <div style="display: flex; justify-content: space-between; align-items: center;">
      <div>
        <strong>Report Created:</strong> ${formatDate(data.createdAt)}<br>
        ${data.updatedAt ? `<strong>Last Updated:</strong> ${formatDate(data.updatedAt)}` : ''}
      </div>
      <div style="text-align: right;">
        <strong>CAPA Report - Corrective and Preventive Action</strong><br>
        Generated on ${formatDate(new Date())}
      </div>
    </div>
  `;
  printDiv.appendChild(footerSection);

  // Create a new window/tab with the PDF content
  const newWindow = window.open('', '_blank');

  if (!newWindow) {
    // Fallback to print if popup is blocked
    document.body.appendChild(printDiv);
    window.print();
    document.body.removeChild(printDiv);
    return;
  }

  // Set up the new window with proper HTML structure and print styles
  const htmlContent = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>CAPA_Report_${data.slug || 'Report'}_${format(new Date(), 'yyyy-MM-dd')}</title>
      <style>
        body {
          margin: 0;
          padding: 0;
          font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
          background: #f5f5f5;
        }
        
        .print-container {
          max-width: 8.5in;
          margin: 20px auto;
          background: white;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          border-radius: 8px;
          overflow: hidden;
        }
        
        .print-actions {
          background: #2563eb;
          color: white;
          padding: 15px 20px;
          text-align: center;
          position: sticky;
          top: 0;
          z-index: 100;
        }
        
        .print-actions button {
          background: white;
          color: #2563eb;
          border: none;
          padding: 8px 16px;
          margin: 0 5px;
          border-radius: 4px;
          cursor: pointer;
          font-weight: 600;
          transition: all 0.2s;
          min-width: 100px;
          font-size: 14px;
        }
        
        .print-actions button:hover {
          background: #f8f9fa;
          transform: translateY(-1px);
        }
        
        @media print {
          body {
            background: white;
          }
          .print-container {
            max-width: none;
            margin: 0;
            box-shadow: none;
            border-radius: 0;
          }
          .print-actions {
            display: none;
          }
          @page {
            margin: 0.5in;
            size: letter;
          }
          * {
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
          }
          table {
            page-break-inside: avoid;
          }
          h3 {
            page-break-after: avoid;
          }
          /* Image containers should stay together */
          div[style*="text-align: center"][style*="max-width: 2.5in"] {
            page-break-inside: avoid;
            break-inside: avoid;
          }
          /* Ensure attachments section has enough space - but don't force page breaks */
          div[style*="page-break-inside: avoid"] {
            orphans: 2;
            widows: 2;
          }
        }
        
        @media screen {
          .print-content {
            padding: 20px;
          }
        }
      </style>
    </head>
    <body>
      <div class="print-container">
        <div class="print-actions">
          <h3 style="margin: 0 0 10px 0;">CAPA Report - Corrective and Preventive Action</h3>
          <button onclick="window.print()">🖨️ Print PDF</button>
          <button onclick="window.close()">❌ Close</button>
        </div>
        <div class="print-content">
          ${printDiv.innerHTML}
        </div>
      </div>
    </body>
    </html>
  `;

  newWindow.document.write(htmlContent);
  newWindow.document.close();

  // Focus the new window
  newWindow.focus();
};
