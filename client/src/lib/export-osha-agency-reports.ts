import { handleExportCSVFile } from '@/lib/download-file';
import { formatDate } from '@shared/date-utils';
import { OSHA_AGENCY_REPORT_TYPE_MAP } from '@shared/types/osha.types';
import { RouterOutputs } from '@shared/types/router.types';

export const exportOshaAgencyReportsCSV = (agencyReports: RouterOutputs['oshaAgencyReport']['export']) => {
  // CSV Headers for OSHA agency reports export
  const headers = [
    'Report ID',
    'Date of Incident',
    'Type of Incident',
    'Location',
    'Description',
    'Employees Involved',
    'Company Contact Person',
    'Contact Phone',
    'Number Affected',
    'Date Prepared',
    'Created By',
    'Created Date',
    'Archived',
  ];

  // Convert data to CSV rows
  const rows = agencyReports.map((report) => {
    // Format dates
    const dateOfIncident = report.dateOfIncident ? formatDate(report.dateOfIncident) : '';
    const datePrepared = report.datePrepared ? formatDate(report.datePrepared) : '';
    const createdDate = report.createdAt ? formatDate(report.createdAt) : '';

    // Map type enum to human-readable string
    const incidentType = report.typeOfIncident ? OSHA_AGENCY_REPORT_TYPE_MAP[report.typeOfIncident] : '';

    // Get location name
    const locationName = report.oshaLocation?.name || '';

    // Get created by information (user ID since user details aren't enriched in export)
    const createdBy = report.createdBy || '';

    // Format archived status
    const archivedStatus = report.archivedAt ? 'Yes' : 'No';

    // Clean up text fields for CSV (remove line breaks and extra spaces)
    const cleanDescription = (report.description || '').replace(/\r?\n/g, ' ').replace(/\s+/g, ' ').trim();
    const cleanEmployeesInvolved = (report.employeesInvolved || '').replace(/\r?\n/g, ' ').replace(/\s+/g, ' ').trim();

    return [
      `"${report.slug || ''}"`,
      `"${dateOfIncident}"`,
      `"${incidentType}"`,
      `"${locationName}"`,
      `"${cleanDescription}"`,
      `"${cleanEmployeesInvolved}"`,
      `"${report.companyContactPerson || ''}"`,
      `"${report.contactPersonPhone || ''}"`,
      `"${report.affectedCount || 0}"`,
      `"${datePrepared}"`,
      `"${createdBy}"`,
      `"${createdDate}"`,
      `"${archivedStatus}"`,
    ];
  });

  // Combine headers and rows
  const csvContent = [headers.join(','), ...rows.map((row) => row.join(','))].join('\n');

  handleExportCSVFile({ content: csvContent, fileName: `osha_agency_reports_export` });
};
