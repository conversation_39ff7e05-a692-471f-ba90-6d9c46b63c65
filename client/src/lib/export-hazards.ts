import { handleExportCSVFile } from '@/lib/download-file';
import { formatDate } from '@shared/date-utils';
import { RouterOutputs } from '@shared/types/router.types';

const hazardTypeLabels: Record<string, string> = {
  chemical: 'Chemical',
  electrical: 'Electrical',
  physical: 'Physical',
  environmental: 'Environmental',
  ergonomic: 'Ergonomic',
  fall: 'Fall',
  biological: 'Biological',
  fire: 'Fire',
  mechanical: 'Mechanical',
  radiation: 'Radiation',
  noise: 'Noise',
  thermal: 'Thermal',
  atmospheric: 'Atmospheric',
  spill: 'Spill',
  transportation: 'Transportation',
  violence: 'Violence',
  other: 'Other',
};

export const exportHazardsCSV = (hazards: RouterOutputs['hazards']['export']) => {
  // CSV Headers for hazards export
  const headers = ['Hazard ID', 'Name', 'Type', 'Status', 'Created By', 'Created Date', 'Last Updated'];

  // Convert data to CSV rows
  const rows = hazards.map((hazard) => {
    // Format dates
    const createdDate = hazard.createdAt ? formatDate(hazard.createdAt) : '';
    const lastUpdated = hazard.updatedAt ? formatDate(hazard.updatedAt) : '';

    // Determine status based on archivedAt
    const status = hazard.archivedAt ? 'Archived' : 'Active';

    // Get hazard type label
    const typeLabel = hazardTypeLabels[hazard.type] || hazard.type;

    // Get created by information
    const createdBy = typeof hazard.createdBy === 'object' && hazard.createdBy?.fullName
      ? `${hazard.createdBy.fullName} (${hazard.createdBy.email || hazard.createdBy.username || ''})`.trim()
      : hazard.createdBy || '';

    return [
      `"${hazard.id || ''}"`,
      `"${hazard.name || ''}"`,
      `"${typeLabel}"`,
      `"${status}"`,
      `"${createdBy}"`,
      `"${createdDate}"`,
      `"${lastUpdated}"`,
    ];
  });

  // Combine headers and rows
  const csvContent = [headers.join(','), ...rows.map((row) => row.join(','))].join('\n');

  handleExportCSVFile({ content: csvContent, fileName: `hazards_export` });
};
