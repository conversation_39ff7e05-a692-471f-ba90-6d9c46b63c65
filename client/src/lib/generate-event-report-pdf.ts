import { formatDate } from '@shared/date-utils';
import {
  DocumentPreviewCapability,
  getDocumentPreviewCapability,
  getFileIcon,
  getMediaCategory,
  isSupportedMimeType,
  MediaCategory,
} from '@shared/types/files.types';
import { RouterOutputs } from '@shared/types/router.types';
import { REPORT_TYPE_MAP, SEVERITY_MAP, STATUS_MAP } from '@shared/types/schema.types';
import { format } from 'date-fns';

type EventReport = RouterOutputs['event']['getById'];

// Main export function
export const generateEventReportPdf = (data: EventReport): void => {
  // Create a printable div for the Event Report - professional layout
  const printDiv = document.createElement('div');
  printDiv.style.width = '8.5in';
  printDiv.style.minHeight = '11in';
  printDiv.style.margin = '0 auto';
  printDiv.style.padding = '0.5in';
  printDiv.style.fontFamily = '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif';
  printDiv.style.fontSize = '10pt';
  printDiv.style.position = 'relative';
  printDiv.style.lineHeight = '1.3';
  printDiv.style.backgroundColor = '#ffffff';
  printDiv.style.color = '#1a1a1a';
  printDiv.style.boxSizing = 'border-box';

  // Event Header Section - with professional styling
  const headerSection = document.createElement('div');
  headerSection.style.textAlign = 'center';
  headerSection.style.marginBottom = '0.2in';
  headerSection.style.borderBottom = '2px solid #2563eb';
  headerSection.style.paddingBottom = '0.1in';
  headerSection.innerHTML = `
    <table style="width: 100%; margin-bottom: 0.1in;">
      <tr>
        <td style="width: 120px; text-align: center; vertical-align: middle;">
          <div style="width: 100px; height: 60px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" style="width: 80px; height: 80px; fill: none; stroke: #2563eb; stroke-width: 2; stroke-linecap: round; stroke-linejoin: round;">
              <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3"/>
              <path d="M12 9v4"/>
              <path d="M12 17h.01"/>
            </svg>
          </div>
        </td>
        <td style="text-align: left; vertical-align: middle; padding-left: 0.2in;">
          <h1 style="font-size: 16pt; margin: 0; color: #2563eb; font-weight: 700;">${data.title || 'Safety Event Report'}</h1>
          <h2 style="font-size: 11pt; margin: 0; color: #333; font-weight: 500;">Incident and Safety Event Documentation</h2>
        </td>
      </tr>
    </table>
    <div style="background: #f8f9fa; padding: 0.08in; border-radius: 4px; border: 1px solid #dee2e6;">
      <p style="margin: 0.05in 0 0 0; font-size: 12pt; color: #2563eb; font-weight: 600;">Event ID: ${data.slug || 'N/A'}</p>
    </div>
  `;
  printDiv.appendChild(headerSection);

  // Event Basic Information Section
  const basicInfoSection = document.createElement('div');
  basicInfoSection.style.marginBottom = '0.15in';
  basicInfoSection.style.background = '#f8f9fa';
  basicInfoSection.style.border = '1px solid #dee2e6';
  basicInfoSection.style.borderRadius = '4px';
  basicInfoSection.style.padding = '0.1in';
  basicInfoSection.innerHTML = `
    <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #2563eb; font-weight: 600; border-bottom: 1px solid #2563eb; padding-bottom: 0.03in;">
      Event Information
    </h3>
    <table style="width: 100%; font-size: 9pt; border-collapse: collapse;">
      <tr>
        <td style="width: 50%; vertical-align: top; padding-right: 0.1in;">
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #dc2626; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Title:</strong><br>
            <span style="color: #212529; font-size: 10pt; font-weight: 600;">${data.title || 'Not specified'}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #dc2626; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Type:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.type ? REPORT_TYPE_MAP[data.type] : 'Not specified'}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #dc2626; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Severity:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.severity ? SEVERITY_MAP[data.severity] : 'Not specified'}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #dc2626;">
            <strong style="color: #495057;">Status:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.status ? STATUS_MAP[data.status] : 'Not specified'}</span>
          </div>
        </td>
        <td style="width: 50%; vertical-align: top; padding-left: 0.1in;">
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #28a745; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Reported By:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.reportedByUser?.fullName || data.reportedByName || 'Not specified'}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #28a745; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Reported Date:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.reportedAt ? formatDate(data.reportedAt) : 'Not specified'}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #28a745; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Location:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.location?.name || 'Not specified'}</span>
          </div>
        </td>
      </tr>
    </table>
  `;
  printDiv.appendChild(basicInfoSection);

  // Hazard Category Section
  if (data.category) {
    const hazardSection = document.createElement('div');
    hazardSection.style.marginBottom = '0.15in';
    hazardSection.style.background = '#f8f9fa';
    hazardSection.style.border = '1px solid #dee2e6';
    hazardSection.style.borderRadius = '4px';
    hazardSection.style.padding = '0.1in';
    hazardSection.innerHTML = `
          <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #2563eb; font-weight: 600; border-bottom: 1px solid #2563eb; padding-bottom: 0.03in;">
      Hazard Category
    </h3>
      <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #fd7e14;">
        <span style="color: #212529; font-size: 9pt; line-height: 1.4;">
          <strong style="color: #495057;">Category:</strong> ${data.category}<br>
          <strong style="color: #495057;">OSHA Reportable:</strong> ${data.oshaReportable ? 'Yes' : 'No'}
        </span>
      </div>
    `;
    printDiv.appendChild(hazardSection);
  }

  // Description Section
  if (data.description) {
    const descriptionSection = document.createElement('div');
    descriptionSection.style.marginBottom = '0.15in';
    descriptionSection.style.background = '#f8f9fa';
    descriptionSection.style.border = '1px solid #dee2e6';
    descriptionSection.style.borderRadius = '4px';
    descriptionSection.style.padding = '0.1in';
    descriptionSection.innerHTML = `
      <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #2563eb; font-weight: 600; border-bottom: 1px solid #2563eb; padding-bottom: 0.03in;">
        Description
      </h3>
      <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #17a2b8;">
        <span style="color: #212529; font-size: 9pt; line-height: 1.4; white-space: pre-wrap;">${data.description}</span>
      </div>
    `;
    printDiv.appendChild(descriptionSection);
  }

  // Immediate Actions Section
  if (data.immediateActions) {
    const immediateActionsSection = document.createElement('div');
    immediateActionsSection.style.marginBottom = '0.15in';
    immediateActionsSection.style.background = '#f8f9fa';
    immediateActionsSection.style.border = '1px solid #dee2e6';
    immediateActionsSection.style.borderRadius = '4px';
    immediateActionsSection.style.padding = '0.1in';
    immediateActionsSection.innerHTML = `
      <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #2563eb; font-weight: 600; border-bottom: 1px solid #2563eb; padding-bottom: 0.03in;">
        Immediate Actions Taken
      </h3>
      <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #28a745;">
        <span style="color: #212529; font-size: 9pt; line-height: 1.4; white-space: pre-wrap;">${data.immediateActions}</span>
      </div>
    `;
    printDiv.appendChild(immediateActionsSection);
  }

  // Customer Information Section (for customer events)
  if (data.type === 'customer_incident' && (data.customerName || data.customerPhoneNumber || data.customerAddress)) {
    const customerSection = document.createElement('div');
    customerSection.style.marginBottom = '0.15in';
    customerSection.style.background = '#f8f9fa';
    customerSection.style.border = '1px solid #dee2e6';
    customerSection.style.borderRadius = '4px';
    customerSection.style.padding = '0.1in';
    customerSection.innerHTML = `
      <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #2563eb; font-weight: 600; border-bottom: 1px solid #2563eb; padding-bottom: 0.03in;">
        Customer Information
      </h3>
      <table style="width: 100%; font-size: 9pt; border-collapse: collapse;">
        <tr>
          <td style="width: 50%; vertical-align: top; padding-right: 0.1in;">
            ${
              data.customerName
                ? `
              <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #6f42c1; margin-bottom: 0.05in;">
                <strong style="color: #495057;">Customer Name:</strong><br>
                <span style="color: #212529; font-size: 10pt;">${data.customerName}</span>
              </div>
            `
                : ''
            }
            ${
              data.customerAddress
                ? `
              <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #6f42c1;">
                <strong style="color: #495057;">Customer Address:</strong><br>
                <span style="color: #212529; font-size: 10pt;">${data.customerAddress}</span>
              </div>
            `
                : ''
            }
          </td>
          <td style="width: 50%; vertical-align: top; padding-left: 0.1in;">
            ${
              data.customerPhoneNumber
                ? `
              <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #ffc107;">
                <strong style="color: #495057;">Customer Phone:</strong><br>
                <span style="color: #212529; font-size: 10pt;">${data.customerPhoneNumber}</span>
              </div>
            `
                : ''
            }
          </td>
        </tr>
      </table>
    `;
    printDiv.appendChild(customerSection);
  }

  // Media Section (if any)
  if (data.media && data.media.length > 0) {
    const mediaSection = document.createElement('div');
    mediaSection.style.marginBottom = '0.15in';
    mediaSection.style.background = '#f8f9fa';
    mediaSection.style.border = '1px solid #dee2e6';
    mediaSection.style.borderRadius = '4px';
    mediaSection.style.padding = '0.1in';
    // Add gentle page break handling for media section - only avoid breaking within
    mediaSection.style.pageBreakInside = 'avoid';

    // Categorize media files using the proper type system
    const images = data.media.filter((file) => getMediaCategory(file.type) === MediaCategory.IMAGE);
    const documents = data.media.filter((file) => getMediaCategory(file.type) === MediaCategory.DOCUMENT);
    const videos = data.media.filter((file) => getMediaCategory(file.type) === MediaCategory.VIDEO);
    const otherFiles = data.media.filter((file) => getMediaCategory(file.type) === MediaCategory.UNKNOWN);

    let mediaContent = `
      <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #2563eb; font-weight: 600; border-bottom: 1px solid #2563eb; padding-bottom: 0.03in;" class="media-header">
        Media Attachments (${data.media.length} files)
      </h3>
    `;

    // Display images inline
    if (images.length > 0) {
      mediaContent += `
        <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #28a745; margin-bottom: 0.08in;">
          <strong style="color: #495057; font-size: 9pt;">📸 Images (${images.length}):</strong>
          <div style="margin-top: 0.05in; display: flex; flex-wrap: wrap; gap: 0.05in;">
      `;

      images.forEach((image) => {
        const fileSize = image.size ? `${(image.size / 1024 / 1024).toFixed(2)} MB` : 'Unknown size';
        mediaContent += `
          <div style="margin-bottom: 0.08in; text-align: center; max-width: 2.5in; page-break-inside: avoid;">
            <img src="${image.url}" alt="${image.name}" style="max-width: 2.5in; max-height: 2in; border: 1px solid #dee2e6; border-radius: 3px; margin-bottom: 0.03in;" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
            <div style="display: none; padding: 0.2in; background: #f8f9fa; border: 1px dashed #dee2e6; border-radius: 3px; color: #666; font-size: 8pt;">
              📷 Image preview unavailable<br>
              <a href="${image.url}" target="_blank" style="color: #2563eb; text-decoration: none;">View Image</a>
            </div>
            <div style="font-size: 8pt; color: #666; line-height: 1.2;">
              <strong>${image.name}</strong><br>
              ${fileSize}
            </div>
          </div>
        `;
      });

      mediaContent += `
          </div>
        </div>
      `;
    }

    // Display document links with preview capability information
    if (documents.length > 0) {
      mediaContent += `
        <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #fd7e14; margin-bottom: 0.08in;">
          <strong style="color: #495057; font-size: 9pt;">📄 Documents (${documents.length}):</strong>
          <div style="margin-top: 0.05in;">
      `;

      documents.forEach((doc) => {
        const fileSize = doc.size ? `${(doc.size / 1024 / 1024).toFixed(2)} MB` : 'Unknown size';
        const fileIcon = getFileIcon(doc.type || '');
        const previewCapability = getDocumentPreviewCapability(doc.type);
        const isPreviewable =
          previewCapability === DocumentPreviewCapability.OBJECT_EMBED ||
          previewCapability === DocumentPreviewCapability.TEXT_CONTENT;

        mediaContent += `
          <div style="margin-bottom: 0.05in; padding: 0.05in; background: #f8f9fa; border-radius: 3px; font-size: 8pt;">
            <span style="margin-right: 0.05in;">${fileIcon}</span>
            <strong><a href="${doc.url}" target="_blank" style="color: #2563eb; text-decoration: none;">${doc.name}</a></strong>
            <span style="color: #666; margin-left: 0.1in;">(${fileSize})</span>
            ${isPreviewable ? '<span style="color: #28a745; font-size: 7pt; margin-left: 0.05in;">• Previewable</span>' : ''}
          </div>
        `;
      });

      mediaContent += `
          </div>
        </div>
      `;
    }

    // Display video links
    if (videos.length > 0) {
      mediaContent += `
        <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #dc2626; margin-bottom: 0.08in;">
          <strong style="color: #495057; font-size: 9pt;">🎥 Videos (${videos.length}):</strong>
          <div style="margin-top: 0.05in;">
      `;

      videos.forEach((video) => {
        const fileSize = video.size ? `${(video.size / 1024 / 1024).toFixed(2)} MB` : 'Unknown size';
        mediaContent += `
          <div style="margin-bottom: 0.05in; padding: 0.05in; background: #f8f9fa; border-radius: 3px; font-size: 8pt;">
            <span style="margin-right: 0.05in;">🎬</span>
            <strong><a href="${video.url}" target="_blank" style="color: #2563eb; text-decoration: none;">${video.name}</a></strong>
            <span style="color: #666; margin-left: 0.1in;">(${fileSize})</span>
            <div style="font-size: 7pt; color: #888; margin-top: 0.02in;">
              📹 Video file - Click link to view
            </div>
          </div>
        `;
      });

      mediaContent += `
          </div>
        </div>
      `;
    }

    // Display unsupported/unknown files
    if (otherFiles.length > 0) {
      mediaContent += `
        <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #6c757d;">
          <strong style="color: #495057; font-size: 9pt;">📎 Other Files (${otherFiles.length}):</strong>
          <div style="margin-top: 0.05in;">
      `;

      otherFiles.forEach((file) => {
        const fileSize = file.size ? `${(file.size / 1024 / 1024).toFixed(2)} MB` : 'Unknown size';
        const fileIcon = getFileIcon(file.type || '');
        mediaContent += `
          <div style="margin-bottom: 0.05in; padding: 0.05in; background: #f8f9fa; border-radius: 3px; font-size: 8pt;">
            <span style="margin-right: 0.05in;">${fileIcon}</span>
            <strong><a href="${file.url}" target="_blank" style="color: #2563eb; text-decoration: none;">${file.name}</a></strong>
            <span style="color: #666; margin-left: 0.1in;">(${fileSize})</span>
            <div style="font-size: 7pt; color: #888; margin-top: 0.02in;">
              Type: ${file.type || 'Unknown'} ${!isSupportedMimeType(file.type || '') ? '• Unsupported format' : ''}
            </div>
          </div>
        `;
      });

      mediaContent += `
          </div>
        </div>
      `;
    }

    mediaSection.innerHTML = mediaContent;
    printDiv.appendChild(mediaSection);
  }

  // Linked Assets Section
  if (data.assets && data.assets.length > 0) {
    const assetsSection = document.createElement('div');
    assetsSection.style.marginBottom = '0.15in';
    assetsSection.style.background = '#f8f9fa';
    assetsSection.style.border = '1px solid #dee2e6';
    assetsSection.style.borderRadius = '4px';
    assetsSection.style.padding = '0.1in';
    assetsSection.innerHTML = `
      <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #2563eb; font-weight: 600; border-bottom: 1px solid #2563eb; padding-bottom: 0.03in;">
        Linked Assets
      </h3>
      <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #fd7e14;">
        <span style="color: #212529; font-size: 9pt; line-height: 1.4;">
          ${data.assets.map((asset) => asset.name).join(', ')}
        </span>
      </div>
    `;
    printDiv.appendChild(assetsSection);
  }

  // Footer Section
  const footerSection = document.createElement('div');
  footerSection.style.marginTop = '0.2in';
  footerSection.style.paddingTop = '0.1in';
  footerSection.style.borderTop = '1px solid #dee2e6';
  footerSection.style.fontSize = '8pt';
  footerSection.style.color = '#666';
  footerSection.innerHTML = `
    <div style="display: flex; justify-content: space-between; align-items: center;">
      <div>
        <strong>Reported At:</strong> ${formatDate(data.reportedAt)}<br>
        ${data.updatedAt ? `<strong>Last Updated:</strong> ${formatDate(data.updatedAt)}` : ''}
      </div>
      <div style="text-align: right;">
        <strong>Safety Event Report - Incident Documentation</strong><br>
        Generated on ${formatDate(new Date())}
      </div>
    </div>
  `;
  printDiv.appendChild(footerSection);

  // Create a new window/tab with the PDF content
  const newWindow = window.open('', '_blank');

  if (!newWindow) {
    // Fallback to print if popup is blocked
    document.body.appendChild(printDiv);
    window.print();
    document.body.removeChild(printDiv);
    return;
  }

  // Set up the new window with proper HTML structure and print styles
  const htmlContent = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Safety_Event_Report_${data.slug || 'Report'}_${format(new Date(), 'yyyy-MM-dd')}</title>
      <style>
        body {
          margin: 0;
          padding: 0;
          font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
          background: #f5f5f5;
        }
        
        .print-container {
          max-width: 8.5in;
          margin: 20px auto;
          background: white;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          border-radius: 8px;
          overflow: hidden;
        }
        
        .print-actions {
          background: #2563eb;
          color: white;
          padding: 15px 20px;
          text-align: center;
          position: sticky;
          top: 0;
          z-index: 100;
        }
        
        .print-actions button {
          background: white;
          color: #2563eb;
          border: none;
          padding: 8px 16px;
          margin: 0 5px;
          border-radius: 4px;
          cursor: pointer;
          font-weight: 600;
          transition: all 0.2s;
          min-width: 100px;
          font-size: 14px;
        }
        
        .print-actions button:hover {
          background: #f8f9fa;
          transform: translateY(-1px);
        }
        
        @media print {
          body {
            background: white;
          }
          .print-container {
            max-width: none;
            margin: 0;
            box-shadow: none;
            border-radius: 0;
          }
          .print-actions {
            display: none;
          }
          @page {
            margin: 0.5in;
            size: letter;
          }
          * {
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
          }
          table {
            page-break-inside: avoid;
          }
          h3 {
            page-break-after: avoid;
          }
          /* Image containers should stay together */
          div[style*="text-align: center"][style*="max-width: 2.5in"] {
            page-break-inside: avoid;
            break-inside: avoid;
          }
          /* Ensure media section has enough space - but don't force page breaks */
          div[style*="page-break-inside: avoid"] {
            orphans: 2;
            widows: 2;
          }
        }
        
        @media screen {
          .print-content {
            padding: 20px;
          }
        }
      </style>
    </head>
    <body>
      <div class="print-container">
        <div class="print-actions">
          <h3 style="margin: 0 0 10px 0;">Safety Event Report - Incident Documentation</h3>
          <button onclick="window.print()">🖨️ Print PDF</button>
          <button onclick="window.close()">❌ Close</button>
        </div>
        <div class="print-content">
          ${printDiv.innerHTML}
        </div>
      </div>
    </body>
    </html>
  `;

  newWindow.document.write(htmlContent);
  newWindow.document.close();

  // Focus the new window
  newWindow.focus();
};
