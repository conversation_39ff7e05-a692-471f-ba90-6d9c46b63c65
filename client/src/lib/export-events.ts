import { handleExportCSVFile } from '@/lib/download-file';
import { formatDate } from '@shared/date-utils';
import { RouterOutputs } from '@shared/types/router.types';
import { CATEGORY_MAP, REPORT_TYPE_MAP, SEVERITY_MAP, STATUS_MAP } from '@shared/types/schema.types';

export const exportEventsCSV = (events: RouterOutputs['event']['export']) => {
  // CSV Headers for comprehensive event export
  const headers = [
    'Event ID',
    'Title',
    'Type',
    'Category',
    'Status',
    'Severity',
    'Reported Date',
    'Reported By',
    'Location',
    'OSHA Reportable',
    'Customer Name',
    'Customer Phone',
    'Customer Address',
    'Asset IDs',
    'Archived',
    'Last Updated',
  ];

  // Convert data to CSV rows
  const rows = events.map((event) => {
    // Format the reported date
    const reportedDate = event.reportedAt ? formatDate(event.reportedAt) : '';

    // Format the last updated date
    const lastUpdated = event.updatedAt ? formatDate(event.updatedAt) : '';

    // Map enum values to human-readable strings
    const eventType = event.type ? REPORT_TYPE_MAP[event.type] : '';
    const eventStatus = event.status ? STATUS_MAP[event.status] : '';
    const eventSeverity = event.severity ? SEVERITY_MAP[event.severity] : '';

    // Get location name or fallback
    const locationName = event.location?.name || '';

    // Format OSHA reportable as Yes/No
    const oshaReportable = event.oshaReportable ? 'Yes' : 'No';

    // Format asset IDs as comma-separated string
    const assetIds = event.assetIds?.join(', ') || '';

    // Format archived status
    const archivedStatus = event.archivedAt ? 'Yes' : 'No';

    // Get reported by name
    const reportedBy = event.reportedByName || event.reportedByEmail || '';

    return [
      `"${event.slug || ''}"`,
      `"${event.title || ''}"`,
      `"${eventType}"`,
      `"${event.category ? CATEGORY_MAP[event.category] : ''}"`,
      `"${eventStatus}"`,
      `"${eventSeverity}"`,
      `"${reportedDate}"`,
      `"${reportedBy}"`,
      `"${locationName}"`,
      `"${oshaReportable}"`,
      `"${event.customerName || ''}"`,
      `"${event.customerPhoneNumber || ''}"`,
      `"${event.customerAddress || ''}"`,
      `"${assetIds}"`,
      `"${archivedStatus}"`,
      `"${lastUpdated}"`,
    ];
  });

  // Combine headers and rows
  const csvContent = [headers.join(','), ...rows.map((row) => row.join(','))].join('\n');

  handleExportCSVFile({ content: csvContent, fileName: `safety_events_export` });
};
