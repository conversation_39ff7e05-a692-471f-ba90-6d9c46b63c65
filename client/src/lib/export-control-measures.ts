import { handleExportCSVFile } from '@/lib/download-file';
import { formatDate } from '@shared/date-utils';
import { RouterOutputs } from '@shared/types/router.types';

const controlMeasureTypeLabels: Record<string, string> = {
  engineering_controls: 'Engineering Controls',
  administrative_controls: 'Administrative Controls',
  personal_protective_equipment: 'Personal Protective Equipment',
  specialized_advanced_controls: 'Specialized Advanced Controls',
  other: 'Other',
};

export const exportControlMeasuresCSV = (controlMeasures: RouterOutputs['controlMeasures']['export']) => {
  // CSV Headers for control measures export
  const headers = ['Control Measure ID', 'Name', 'Type', 'Status', 'Created By', 'Created Date', 'Last Updated'];

  // Convert data to CSV rows
  const rows = controlMeasures.map((controlMeasure) => {
    // Format dates
    const createdDate = controlMeasure.createdAt ? formatDate(controlMeasure.createdAt) : '';
    const lastUpdated = controlMeasure.updatedAt ? formatDate(controlMeasure.updatedAt) : '';

    // Determine status based on archivedAt
    const status = controlMeasure.archivedAt ? 'Archived' : 'Active';

    // Get control measure type label
    const typeLabel = controlMeasureTypeLabels[controlMeasure.type] || controlMeasure.type;

    // Get created by information
    const createdBy = typeof controlMeasure.createdBy === 'object' && controlMeasure.createdBy?.fullName
      ? `${controlMeasure.createdBy.fullName} (${controlMeasure.createdBy.email || controlMeasure.createdBy.username || ''})`.trim()
      : controlMeasure.createdBy || '';

    return [
      `"${controlMeasure.id || ''}"`,
      `"${controlMeasure.name || ''}"`,
      `"${typeLabel}"`,
      `"${status}"`,
      `"${createdBy}"`,
      `"${createdDate}"`,
      `"${lastUpdated}"`,
    ];
  });

  // Combine headers and rows
  const csvContent = [headers.join(','), ...rows.map((row) => row.join(','))].join('\n');

  handleExportCSVFile({ content: csvContent, fileName: `control_measures_export` });
};
