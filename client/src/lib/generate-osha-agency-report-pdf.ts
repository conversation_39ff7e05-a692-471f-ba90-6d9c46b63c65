import { format } from 'date-fns';
import { RouterOutputs } from '@shared/types/router.types';
import { formatDate } from '@shared/date-utils';
import { OSHA_AGENCY_REPORT_TYPE_MAP } from '@shared/types/osha.types';

type OshaAgencyReport = NonNullable<RouterOutputs['oshaAgencyReport']['getById']>;

// Helper function for safe date formatting
const safeFormatDate = (
  date: string | Date | null | undefined,
  formatStr: string,
  fallback: string = 'Not specified',
): string => {
  if (!date) return fallback;
  try {
    return format(new Date(date), formatStr);
  } catch {
    return fallback;
  }
};

// Main export function
export const generateOshaAgencyReportPdf = (data: OshaAgencyReport): void => {
  // Create a printable div for the Agency Report - professional layout
  const printDiv = document.createElement('div');
  printDiv.style.width = '8.5in';
  printDiv.style.minHeight = '11in';
  printDiv.style.margin = '0 auto';
  printDiv.style.padding = '0.5in';
  printDiv.style.fontFamily = '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif';
  printDiv.style.fontSize = '10pt';
  printDiv.style.position = 'relative';
  printDiv.style.lineHeight = '1.3';
  printDiv.style.backgroundColor = '#ffffff';
  printDiv.style.color = '#1a1a1a';
  printDiv.style.boxSizing = 'border-box';

  // OSHA Header Section - with official logo
  const headerSection = document.createElement('div');
  headerSection.style.textAlign = 'center';
  headerSection.style.marginBottom = '0.2in';
  headerSection.style.borderBottom = '2px solid #dc3545';
  headerSection.style.paddingBottom = '0.1in';
  headerSection.innerHTML = `
    <table style="width: 100%; margin-bottom: 0.1in;">
      <tr>
        <td style="width: 120px; text-align: center; vertical-align: middle;">
          <div style="width: 100px; height: 60px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
            <svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" viewBox="0 0 720.002 207.213" style="width: 100px; height: auto;">
              <path d="M314.039 48.588c-1.656-13.246-2.76-26.492-2.76-39.465-8.557-1.102-32.014-4.139-39.738-4.139-35.602 0-60.988 21.25-60.988 57.127 0 66.51 81.131 51.332 81.131 100.18 0 19.594-15.178 31.461-33.943 31.461-26.217 0-39.463-20.422-42.773-43.879l-7.729 1.654c1.381 14.074 3.314 27.875 4.691 41.949 13.523 6.9 28.426 12.42 43.881 12.42 35.322 0 66.783-19.873 66.783-57.955 0-66.236-82.789-52.436-82.789-98.248 0-21.805 12.143-34.773 33.941-34.773 19.043 0 30.635 17.111 32.564 35.049"/>
              <path d="M372.752 109.027h100.176v68.719c0 14.904-11.592 15.73-21.523 15.73h-5.246v8.279c12.143 0 29.805-1.104 44.707-1.104 14.074 0 30.633 1.104 41.67 1.104v-8.279h-3.861c-9.936 0-20.422-1.381-20.422-15.73V33.133c0-14.348 10.486-15.729 20.422-15.729h3.861V9.123c-11.037 0-27.596 1.105-41.67 1.105-14.902 0-32.564-1.105-44.707-1.105v8.281h5.246c9.932 0 21.523.826 21.523 15.729v60.439H372.752V33.133c0-14.902 11.592-15.729 21.525-15.729h5.244V9.123c-12.143 0-29.805 1.105-44.709 1.105-14.072 0-30.629-1.105-41.668-1.105v8.281h3.861c9.936 0 20.422 1.381 20.422 15.729v144.613c0 14.35-10.486 15.73-20.422 15.73h-3.861v8.279c11.039 0 27.596-1.104 41.668-1.104 14.904 0 32.566 1.104 44.709 1.104v-8.279h-5.244c-9.934 0-21.525-.826-21.525-15.73"/>
              <path d="M581.742 133.59h62.645l10.762 32.012c3.037 8.832 5.52 16.283 5.52 19.873 0 6.898-10.762 8.002-17.109 8.002h-3.035v8.279c14.076-.551 27.875-1.104 41.395-1.104 13.246 0 25.664.553 38.084 1.104v-8.279h-1.656c-9.105 0-14.9-3.311-18.213-11.039-3.588-8.279-6.898-18.49-10.211-28.15L639.145 9.951c-.83-2.207-1.656-4.691-2.484-6.898-.551-.83-1.104-.83-1.932-.83s-1.379.279-2.207.553c-5.244 3.312-16.283 8.832-25.113 11.867-1.654 10.213-6.623 24.012-10.211 34.223L553.042 175.54c-4.139 11.59-13.244 17.938-24.008 17.938h-1.654v8.279c9.934-.551 19.867-1.104 29.805-1.104 11.035 0 22.352.553 33.391 1.104v-8.279h-3.035c-9.109 0-20.699-1.381-20.699-9.66 0-4.967 3.312-12.143 6.07-21.525m65.68-44.158h-51.605l25.664-78.102h.555l25.386 78.102z"/>
              <path fill="#939598" d="M56.324 71.451c10.143-14.01 26.633-23.123 45.25-23.123 30.836 0 55.832 24.996 55.832 55.834 0 30.836-24.996 55.832-55.832 55.832-22.824 0-42.449-13.695-51.105-33.32 6.953 7.992 17.197 13.043 28.623 13.043 20.947 0 37.928-16.982 37.928-37.93s-16.98-37.928-37.928-37.928a37.774 37.774 0 0 0-22.768 7.592"/>
              <path fill="#0082C4" d="M0 103.941C0 60.33 35.352 24.975 78.963 24.975c28.4 0 53.299 14.996 67.213 37.502-11.141-11.916-27.002-19.363-44.602-19.363-33.713 0-61.045 27.332-61.045 61.049s27.332 61.049 61.045 61.049c17.168 0 32.68-7.086 43.771-18.494-14.057 21.775-38.537 36.189-66.383 36.189C35.352 182.906 0 147.553 0 103.941"/>
              <path d="M5.836 61.889C21.92 25.688 58.189.443 100.357.443c57.094 0 103.379 46.285 103.379 103.383 0 57.1-46.285 103.387-103.379 103.387-41.775 0-77.764-24.781-94.068-60.441 14.674 24.846 41.727 41.512 72.674 41.512 46.58 0 84.34-37.762 84.34-84.342 0-46.584-37.76-84.344-84.34-84.344-31.268 0-58.563 17.013-73.127 42.291M664.98 19.18C664.98 8.592 673.572 0 684.262 0c10.639 0 19.18 8.592 19.18 19.18 0 10.689-8.541 19.281-19.18 19.281A19.23 19.23 0 0 1 664.98 19.18m35.084 0c0-8.695-7.059-15.805-15.803-15.805-8.797 0-15.908 7.109-15.908 15.805 0 8.797 7.111 15.906 15.908 15.906 8.745 0 15.803-7.109 15.803-15.906zm-19.894 1.277v9.771h-3.529V7.621h7.314c4.141 0 8.438 1.123 8.438 6.24 0 2.607-1.584 4.652-4.604 5.266v.104c3.121.613 3.479 1.994 3.838 4.449.307 2.146.562 4.502 1.328 6.549h-4.502c-.254-1.279-.613-2.713-.768-4.041-.252-1.945-.252-3.736-1.277-4.809-.867-.922-2.045-.818-3.273-.922h-2.965zm3.73-3.527c3.326-.104 4.092-1.484 4.092-3.223 0-1.689-.766-2.557-3.578-2.557h-4.244v5.779h3.73z"/>
            </svg>
          </div>
        </td>
        <td style="text-align: left; vertical-align: middle; padding-left: 0.2in;">
          <h1 style="font-size: 16pt; margin: 0; color: #dc3545; font-weight: 700;">Serious Incident Report</h1>
          <h2 style="font-size: 11pt; margin: 0; color: #333; font-weight: 500;">Regulatory Agency Notification</h2>
        </td>
      </tr>
    </table>
    <div style="background: #f8f9fa; padding: 0.08in; border-radius: 4px; border: 1px solid #dee2e6;">
      <p style="margin: 0; font-size: 8pt; color: #666; font-style: italic;">
        This report documents serious workplace incidents requiring regulatory agency notification.
      </p>
      <p style="margin: 0.05in 0 0 0; font-size: 12pt; color: #dc3545; font-weight: 600;">Report ID: ${data.slug || 'AR-' + (data.id?.slice(-4) || 'N/A')}</p>
    </div>
  `;
  printDiv.appendChild(headerSection);

  // Report Information Section
  const reportInfoSection = document.createElement('div');
  reportInfoSection.style.marginBottom = '0.15in';
  reportInfoSection.style.background = '#f8f9fa';
  reportInfoSection.style.border = '1px solid #dee2e6';
  reportInfoSection.style.borderRadius = '4px';
  reportInfoSection.style.padding = '0.1in';
  reportInfoSection.innerHTML = `
    <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #dc3545; font-weight: 600; border-bottom: 1px solid #dc3545; padding-bottom: 0.03in;">
      Report Information
    </h3>
    <table style="width: 100%; font-size: 9pt; border-collapse: collapse;">
      <tr>
        <td style="width: 50%; vertical-align: top; padding-right: 0.1in;">
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #dc3545; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Report ID:</strong><br>
            <span style="color: #212529; font-size: 10pt; font-weight: 600;">
              ${data.slug || `AR-${safeFormatDate(data.createdAt, 'yyyy-MM-dd', 'UNKNOWN')}`}
            </span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #dc3545; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Report Prepared:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${safeFormatDate(data.datePrepared || data.createdAt, 'MMM d, yyyy h:mm a')}</span>
          </div>
        </td>
        <td style="width: 50%; vertical-align: top; padding-left: 0.1in;">
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #28a745; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Generated:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${format(new Date(), 'MMM d, yyyy h:mm a')}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #28a745;">
            <strong style="color: #495057;">Last Updated:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${safeFormatDate(data.updatedAt || data.createdAt, 'MMM d, yyyy h:mm a')}</span>
          </div>
        </td>
      </tr>
    </table>
  `;
  printDiv.appendChild(reportInfoSection);

  // Incident Details Section
  const incidentSection = document.createElement('div');
  incidentSection.style.marginBottom = '0.15in';
  incidentSection.style.background = '#f8f9fa';
  incidentSection.style.border = '1px solid #dee2e6';
  incidentSection.style.borderRadius = '4px';
  incidentSection.style.padding = '0.1in';
  incidentSection.innerHTML = `
    <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #dc3545; font-weight: 600; border-bottom: 1px solid #dc3545; padding-bottom: 0.03in;">
      Incident Details
    </h3>
    <table style="width: 100%; font-size: 9pt; border-collapse: collapse;">
      <tr>
        <td style="width: 50%; vertical-align: top; padding-right: 0.1in;">
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #fd7e14; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Date of Incident:</strong><br>
            <span style="color: #212529; font-size: 10pt; font-weight: 600;">${safeFormatDate(data.dateOfIncident, 'MMM d, yyyy')}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #fd7e14; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Time of Incident:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${safeFormatDate(data.dateOfIncident, 'HH:mm')}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #fd7e14;">
            <strong style="color: #495057;">Location:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.oshaLocation?.name || 'Not specified'}</span>
          </div>
        </td>
        <td style="width: 50%; vertical-align: top; padding-left: 0.1in;">
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #6f42c1; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Incident Type:</strong><br>
            <span style="color: #212529; font-size: 10pt; font-weight: 600;">${OSHA_AGENCY_REPORT_TYPE_MAP[data.typeOfIncident]}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #6f42c1;">
            <strong style="color: #495057;">Number Affected:</strong><br>
            <span style="color: #212529; font-size: 10pt; font-weight: 600;">${data.affectedCount}</span>
          </div>
        </td>
      </tr>
    </table>
    ${
      data.description
        ? `
      <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #17a2b8; margin-top: 0.05in;">
        <strong style="color: #495057;">Description:</strong><br>
        <span style="color: #212529; font-size: 9pt; line-height: 1.4;">${data.description}</span>
      </div>
    `
        : ''
    }
  `;
  printDiv.appendChild(incidentSection);

  // Severity Classification Section
  const severitySection = document.createElement('div');
  severitySection.style.marginBottom = '0.15in';
  severitySection.innerHTML = `
    <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #dc3545; font-weight: 600; border-bottom: 1px solid #dc3545; padding-bottom: 0.03in;">
      Severity Classification
    </h3>
    <table style="width: 100%; border-collapse: collapse; font-size: 9pt; border: 1px solid #dee2e6;">
      <thead>
        <tr style="background: #dc3545; color: white;">
          <th style="padding: 0.08in; text-align: left; font-weight: 600; border-right: 1px solid #c82333;">Classification</th>
          <th style="padding: 0.08in; text-align: center; font-weight: 600; width: 20%;">Count</th>
        </tr>
      </thead>
      <tbody>
        <tr style="background: ${data.typeOfIncident === 'fatality' ? '#ffebee' : '#f8f9fa'};">
          <td style="padding: 0.06in; border-right: 1px solid #dee2e6; border-bottom: 1px solid #dee2e6;">
            <strong>Fatalities</strong><br>
            <span style="font-size: 8pt; color: #666;">Employee deaths from work-related incidents</span>
          </td>
          <td style="padding: 0.06in; text-align: center; font-weight: 700; font-size: 12pt; color: ${data.typeOfIncident === 'fatality' ? '#dc3545' : '#28a745'}; border-bottom: 1px solid #dee2e6;">
            ${data.typeOfIncident === 'fatality' ? data.affectedCount : 0}
          </td>
        </tr>
        <tr style="background: ${data.typeOfIncident === 'hospitalization' ? '#fff3e0' : '#f8f9fa'};">
          <td style="padding: 0.06in; border-right: 1px solid #dee2e6; border-bottom: 1px solid #dee2e6;">
            <strong>Hospitalizations</strong><br>
            <span style="font-size: 8pt; color: #666;">Employees requiring overnight hospital stays</span>
          </td>
          <td style="padding: 0.06in; text-align: center; font-weight: 700; font-size: 12pt; color: ${data.typeOfIncident === 'hospitalization' ? '#fd7e14' : '#28a745'}; border-bottom: 1px solid #dee2e6;">
            ${data.typeOfIncident === 'hospitalization' ? data.affectedCount : 0}
          </td>
        </tr>
        <tr style="background: ${data.typeOfIncident === 'amputation' ? '#fffde7' : '#f8f9fa'};">
          <td style="padding: 0.06in; border-right: 1px solid #dee2e6; border-bottom: 1px solid #dee2e6;">
            <strong>Amputations</strong><br>
            <span style="font-size: 8pt; color: #666;">Loss of body parts or limbs</span>
          </td>
          <td style="padding: 0.06in; text-align: center; font-weight: 700; font-size: 12pt; color: ${data.typeOfIncident === 'amputation' ? '#ffc107' : '#28a745'}; border-bottom: 1px solid #dee2e6;">
            ${data.typeOfIncident === 'amputation' ? data.affectedCount : 0}
          </td>
        </tr>
        <tr style="background: ${data.typeOfIncident === 'eye_loss' ? '#f3e5f5' : '#f8f9fa'};">
          <td style="padding: 0.06in; border-right: 1px solid #dee2e6;">
            <strong>Eye Loss</strong><br>
            <span style="font-size: 8pt; color: #666;">Loss of vision or eye injuries</span>
          </td>
          <td style="padding: 0.06in; text-align: center; font-weight: 700; font-size: 12pt; color: ${data.typeOfIncident === 'eye_loss' ? '#6f42c1' : '#28a745'};">
            ${data.typeOfIncident === 'eye_loss' ? data.affectedCount : 0}
          </td>
        </tr>
      </tbody>
    </table>
  `;
  printDiv.appendChild(severitySection);

  // Employees Involved Section
  const employeeSection = document.createElement('div');
  employeeSection.style.marginBottom = '0.15in';
  employeeSection.style.background = '#f8f9fa';
  employeeSection.style.border = '1px solid #dee2e6';
  employeeSection.style.borderRadius = '4px';
  employeeSection.style.padding = '0.1in';
  employeeSection.innerHTML = `
    <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #dc3545; font-weight: 600; border-bottom: 1px solid #dc3545; padding-bottom: 0.03in;">
      Employees Involved
    </h3>
    <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #17a2b8;">
      <strong style="color: #495057;">Affected Employees:</strong><br>
      <span style="color: #212529; font-size: 9pt; line-height: 1.4;">
        ${data.employeesInvolved?.trim() || 'No employees specified'}
      </span>
    </div>
  `;
  printDiv.appendChild(employeeSection);

  // Contact Information Section
  const contactSection = document.createElement('div');
  contactSection.style.marginBottom = '0.15in';
  contactSection.style.background = '#f8f9fa';
  contactSection.style.border = '1px solid #dee2e6';
  contactSection.style.borderRadius = '4px';
  contactSection.style.padding = '0.1in';
  contactSection.innerHTML = `
    <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #dc3545; font-weight: 600; border-bottom: 1px solid #dc3545; padding-bottom: 0.03in;">
      Company Contact Information
    </h3>
    <table style="width: 100%; font-size: 9pt; border-collapse: collapse;">
      <tr>
        <td style="width: 50%; vertical-align: top; padding-right: 0.1in;">
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #28a745;">
            <strong style="color: #495057;">Contact Person:</strong><br>
            <span style="color: #212529; font-size: 10pt; font-weight: 600;">${data.companyContactPerson || 'Not specified'}</span>
          </div>
        </td>
        <td style="width: 50%; vertical-align: top; padding-left: 0.1in;">
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #28a745;">
            <strong style="color: #495057;">Phone Number:</strong><br>
            <span style="color: #212529; font-size: 10pt; font-weight: 600;">${data.contactPersonPhone || 'Not specified'}</span>
          </div>
        </td>
      </tr>
    </table>
  `;
  printDiv.appendChild(contactSection);

  // Regulatory Notice Section
  const noticeSection = document.createElement('div');
  noticeSection.style.marginBottom = '0.15in';
  noticeSection.style.background = '#fff3cd';
  noticeSection.style.border = '1px solid #ffeaa7';
  noticeSection.style.borderRadius = '4px';
  noticeSection.style.padding = '0.1in';
  noticeSection.innerHTML = `
    <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #856404; font-weight: 600; border-bottom: 1px solid #856404; padding-bottom: 0.03in;">
      ⚠️ Regulatory Notice
    </h3>
    <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #ffc107;">
      <span style="color: #212529; font-size: 9pt; line-height: 1.4;">
        <strong>Important:</strong> This report has been prepared for regulatory notification purposes. 
        Actual agency notification must be completed through official OSHA channels within the required timeframe. 
        Serious incidents typically must be reported within 8 or 24 hours depending on the type of incident.
      </span>
    </div>
  `;
  printDiv.appendChild(noticeSection);

  // Footer Section
  const footerSection = document.createElement('div');
  footerSection.style.marginTop = '0.2in';
  footerSection.style.paddingTop = '0.1in';
  footerSection.style.borderTop = '1px solid #dee2e6';
  footerSection.style.fontSize = '8pt';
  footerSection.style.color = '#666';
  footerSection.innerHTML = `
    <div style="display: flex; justify-content: space-between; align-items: center;">
      <div>
        <strong>Report Created:</strong> ${formatDate(data.createdAt)}<br>
        ${data.updatedAt ? `<strong>Last Updated:</strong> ${formatDate(data.updatedAt)}` : ''}
      </div>
      <div style="text-align: right;">
        <strong>OSHA Serious Incident Report</strong><br>
        Generated on ${formatDate(new Date())}
      </div>
    </div>
  `;
  printDiv.appendChild(footerSection);

  // Create a new window/tab with the PDF content
  const newWindow = window.open('', '_blank');

  if (!newWindow) {
    // Fallback to print if popup is blocked
    document.body.appendChild(printDiv);
    window.print();
    document.body.removeChild(printDiv);
    return;
  }

  // Set up the new window with proper HTML structure and print styles
  const reportId = data.slug || `AR-${safeFormatDate(data.createdAt, 'yyyy-MM-dd', 'UNKNOWN')}`;
  const htmlContent = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>OSHA_Agency_Report_${reportId}_${format(new Date(), 'yyyy-MM-dd')}</title>
      <style>
        body {
          margin: 0;
          padding: 0;
          font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
          background: #f5f5f5;
        }
        
        .print-container {
          max-width: 8.5in;
          margin: 20px auto;
          background: white;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          border-radius: 8px;
          overflow: hidden;
        }
        
        .print-actions {
          background: #dc3545;
          color: white;
          padding: 15px 20px;
          text-align: center;
          position: sticky;
          top: 0;
          z-index: 100;
        }
        
        .print-actions button {
          background: white;
          color: #dc3545;
          border: none;
          padding: 8px 16px;
          margin: 0 5px;
          border-radius: 4px;
          cursor: pointer;
          font-weight: 600;
          transition: all 0.2s;
          min-width: 100px;
          font-size: 14px;
        }
        
        .print-actions button:hover {
          background: #f8f9fa;
          transform: translateY(-1px);
        }
        
        @media print {
          body {
            background: white;
          }
          .print-container {
            max-width: none;
            margin: 0;
            box-shadow: none;
            border-radius: 0;
          }
          .print-actions {
            display: none;
          }
          @page {
            margin: 0.5in;
            size: letter;
          }
          * {
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
          }
          table {
            page-break-inside: avoid;
          }
          h3 {
            page-break-after: avoid;
          }
        }
        
        @media screen {
          .print-content {
            padding: 20px;
          }
        }
      </style>
    </head>
    <body>
      <div class="print-container">
        <div class="print-actions">
          <h3 style="margin: 0 0 10px 0;">OSHA Serious Incident Report - Regulatory Agency Notification</h3>
          <button onclick="window.print()">🖨️ Print PDF</button>
          <button onclick="window.close()">❌ Close</button>
        </div>
        <div class="print-content">
          ${printDiv.innerHTML}
        </div>
      </div>
    </body>
    </html>
  `;

  newWindow.document.write(htmlContent);
  newWindow.document.close();

  // Focus the new window
  newWindow.focus();
};
