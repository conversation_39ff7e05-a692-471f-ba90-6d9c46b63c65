import { formatDate } from '@shared/date-utils';
import { RouterOutputs } from '@shared/types/router.types';
import { SOP_STATUS_MAP, getSopRiskLevel, SOP_SECTION_TYPE_MAP } from '@shared/types/sop.types';
import { format } from 'date-fns';

type SopReport = RouterOutputs['sop']['getByInstanceId'];

// Helper function to calculate risk score
const calculateRiskScore = (severity: number, likelihood: number): number => {
  return severity * likelihood;
};

// Helper function to get risk level color for PDF styling
// Maps the Tailwind classes from getSopRiskLevel to hex colors for PDF
const getRiskLevelColor = (score: number): string => {
  const riskData = getSopRiskLevel(score, 1); // likelihood defaults to 1 for color mapping

  // Extract color mapping based on the Tailwind classes returned by getSopRiskLevel
  if (riskData.color.includes('text-red-800')) return '#dc2626'; // red-600 equivalent
  if (riskData.color.includes('text-yellow-800')) return '#f59e0b'; // amber-500 equivalent
  if (riskData.color.includes('text-green-800')) return '#16a34a'; // green-600 equivalent

  return '#16a34a'; // default green
};

// Main export function
export const generateSopReportPdf = (data: SopReport): void => {
  // Create a printable div for the SOP Report - professional layout
  const printDiv = document.createElement('div');
  printDiv.style.width = '8.5in';
  printDiv.style.minHeight = '11in';
  printDiv.style.margin = '0 auto';
  printDiv.style.padding = '0.5in';
  printDiv.style.fontFamily = '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif';
  printDiv.style.fontSize = '10pt';
  printDiv.style.position = 'relative';
  printDiv.style.lineHeight = '1.3';
  printDiv.style.backgroundColor = '#ffffff';
  printDiv.style.color = '#1a1a1a';
  printDiv.style.boxSizing = 'border-box';

  // SOP Header Section - with professional styling
  const headerSection = document.createElement('div');
  headerSection.style.textAlign = 'center';
  headerSection.style.marginBottom = '0.2in';
  headerSection.style.borderBottom = '2px solid #2563eb';
  headerSection.style.paddingBottom = '0.1in';
  headerSection.innerHTML = `
    <table style="width: 100%; margin-bottom: 0.1in;">
      <tr>
        <td style="width: 120px; text-align: center; vertical-align: middle;">
          <div style="width: 100px; height: 60px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" style="width: 80px; height: 80px; fill: none; stroke: #2563eb; stroke-width: 2; stroke-linecap: round; stroke-linejoin: round;">
              <path d="M10 10V5a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v5"/>
              <path d="M14 6a6 6 0 0 1 6 6v3"/>
              <path d="M4 15v-3a6 6 0 0 1 6-6"/>
              <rect x="2" y="15" width="20" height="4" rx="1"/>
            </svg>
          </div>
        </td>
        <td style="text-align: left; vertical-align: middle; padding-left: 0.2in;">
          <h1 style="font-size: 16pt; margin: 0; color: #2563eb; font-weight: 700;">${data.title || 'Standard Operating Procedure (SOP) Report'}</h1>
          <h2 style="font-size: 11pt; margin: 0; color: #333; font-weight: 500;">Standardized Work Process Documentation</h2>
        </td>
      </tr>
    </table>
    <div style="background: #f8f9fa; padding: 0.08in; border-radius: 4px; border: 1px solid #dee2e6;">
      <p style="margin: 0.05in 0 0 0; font-size: 12pt; color: #2563eb; font-weight: 600;">SOP ID: ${data.slug || 'N/A'} | Version: ${data.version}</p>
    </div>
  `;
  printDiv.appendChild(headerSection);

  // SOP Basic Information Section
  const basicInfoSection = document.createElement('div');
  basicInfoSection.style.marginBottom = '0.15in';
  basicInfoSection.style.background = '#f8f9fa';
  basicInfoSection.style.border = '1px solid #dee2e6';
  basicInfoSection.style.borderRadius = '4px';
  basicInfoSection.style.padding = '0.1in';
  basicInfoSection.innerHTML = `
    <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #2563eb; font-weight: 600; border-bottom: 1px solid #2563eb; padding-bottom: 0.03in;">
      SOP Information
    </h3>
    <table style="width: 100%; font-size: 9pt; border-collapse: collapse;">
      <tr>
        <td style="width: 50%; vertical-align: top; padding-right: 0.1in;">
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #dc2626; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Title:</strong><br>
            <span style="color: #212529; font-size: 10pt; font-weight: 600;">${data.title || 'Not specified'}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #dc2626; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Status:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.status ? SOP_STATUS_MAP[data.status] : 'Not specified'}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #dc2626; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Owner:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.owner?.fullName || 'Not specified'}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #dc2626;">
            <strong style="color: #495057;">Approver:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.approver?.fullName || 'Not specified'}</span>
          </div>
        </td>
        <td style="width: 50%; vertical-align: top; padding-left: 0.1in;">
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #28a745; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Created By:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.owner?.fullName || 'Not specified'}</span>
          </div>
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #28a745; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Created Date:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.createdAt ? formatDate(data.createdAt) : 'Not specified'}</span>
          </div>
          ${
            data.reviewDate
              ? `
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #28a745; margin-bottom: 0.05in;">
            <strong style="color: #495057;">Review Date:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${formatDate(data.reviewDate)}</span>
          </div>
          `
              : ''
          }
          ${
            data.location?.name
              ? `
          <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #28a745;">
            <strong style="color: #495057;">Location:</strong><br>
            <span style="color: #212529; font-size: 10pt;">${data.location.name}</span>
          </div>
          `
              : ''
          }
        </td>
      </tr>
    </table>
  `;
  printDiv.appendChild(basicInfoSection);

  // SOP Purpose & Responsibilities Section
  if (data.purpose || data.responsibilities) {
    const purposeSection = document.createElement('div');
    purposeSection.style.marginBottom = '0.15in';
    purposeSection.style.background = '#f8f9fa';
    purposeSection.style.border = '1px solid #dee2e6';
    purposeSection.style.borderRadius = '4px';
    purposeSection.style.padding = '0.1in';
    purposeSection.innerHTML = `
      <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #2563eb; font-weight: 600; border-bottom: 1px solid #2563eb; padding-bottom: 0.03in;">
        Purpose & Responsibilities
      </h3>
      ${
        data.purpose
          ? `
      <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #17a2b8; margin-bottom: 0.08in;">
        <strong style="color: #495057; font-size: 9pt;">Purpose:</strong><br>
        <span style="color: #212529; font-size: 9pt; line-height: 1.4; white-space: pre-wrap;">${data.purpose}</span>
      </div>
      `
          : ''
      }
      ${
        data.responsibilities
          ? `
      <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #fd7e14;">
        <strong style="color: #495057; font-size: 9pt;">Responsibilities:</strong><br>
        <span style="color: #212529; font-size: 9pt; line-height: 1.4; white-space: pre-wrap;">${data.responsibilities}</span>
      </div>
      `
          : ''
      }
    `;
    printDiv.appendChild(purposeSection);
  }

  // Risk Summary Section
  if (data.sections && data.sections.length > 0) {
    const stepSections = data.sections.filter((section) => section.sectionType === 'step' && section.severity);
    if (stepSections.length > 0) {
      const highestRiskScore = Math.max(...stepSections.map((section) => calculateRiskScore(section.severity || 1, 1))); // likelihood not stored in sections
      const riskSummarySection = document.createElement('div');
      riskSummarySection.style.marginBottom = '0.15in';
      riskSummarySection.style.background = '#f8f9fa';
      riskSummarySection.style.border = '1px solid #dee2e6';
      riskSummarySection.style.borderRadius = '4px';
      riskSummarySection.style.padding = '0.1in';
      riskSummarySection.innerHTML = `
        <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #2563eb; font-weight: 600; border-bottom: 1px solid #2563eb; padding-bottom: 0.03in;">
          Risk Summary
        </h3>
        <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid ${getRiskLevelColor(highestRiskScore)};">
          <span style="color: #212529; font-size: 9pt; line-height: 1.4;">
            <strong style="color: #495057;">Highest Risk Score:</strong> ${highestRiskScore} (${getSopRiskLevel(highestRiskScore, 1).level})<br>
            <strong style="color: #495057;">Total Sections:</strong> ${data.sections.length} sections<br>
            <strong style="color: #495057;">Risk Sections:</strong> ${stepSections.length} steps with risk assessment<br>
            <strong style="color: #495057;">Overall Risk Level:</strong> ${data.highestSeverity ? getSopRiskLevel(data.highestSeverity, 1).level : getSopRiskLevel(highestRiskScore, 1).level}
          </span>
        </div>
      `;
      printDiv.appendChild(riskSummarySection);
    }
  }

  // SOP Sections
  if (data.sections && data.sections.length > 0) {
    const stepsSection = document.createElement('div');
    stepsSection.style.marginBottom = '0.15in';
    stepsSection.style.background = '#f8f9fa';
    stepsSection.style.border = '1px solid #dee2e6';
    stepsSection.style.borderRadius = '4px';
    stepsSection.style.padding = '0.1in';
    stepsSection.style.pageBreakInside = 'avoid';

    let stepsContent = `
      <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #2563eb; font-weight: 600; border-bottom: 1px solid #2563eb; padding-bottom: 0.03in;">
        SOP Sections (${data.sections.length} sections)
      </h3>
    `;

    // Group sections by type for better organization
    const groupedSections = data.sections.reduce(
      (acc, section) => {
        if (!acc[section.sectionType]) {
          acc[section.sectionType] = [];
        }
        acc[section.sectionType].push(section);
        return acc;
      },
      {} as Record<string, typeof data.sections>,
    );

    // Sort each group by serial number
    Object.keys(groupedSections).forEach((key) => {
      groupedSections[key].sort((a, b) => a.serial - b.serial);
    });

    // Define section type order for display
    const sectionTypeOrder = ['general', 'pre_procedure', 'procedure', 'step', 'post_procedure', 'emergency'];

    sectionTypeOrder.forEach((sectionType) => {
      const sections = groupedSections[sectionType];
      if (!sections || sections.length === 0) return;

      stepsContent += `
        <div style="background: #f1f5f9; padding: 0.08in; border-radius: 3px; margin-bottom: 0.1in; border-left: 4px solid #2563eb;">
          <h4 style="margin: 0; font-size: 10pt; font-weight: 600; color: #2563eb;">
            ${SOP_SECTION_TYPE_MAP[sectionType as keyof typeof SOP_SECTION_TYPE_MAP]} (${sections.length} section${sections.length > 1 ? 's' : ''})
          </h4>
        </div>
      `;

      sections.forEach((section, _index) => {
        const hasRiskAssessment = section.sectionType === 'step' && section.severity;
        const riskScore = hasRiskAssessment ? calculateRiskScore(section.severity || 1, 1) : 0;
        const riskData = hasRiskAssessment ? getSopRiskLevel(section.severity || 1, 1) : null;
        const riskColor = hasRiskAssessment ? getRiskLevelColor(riskScore) : '#6b7280';

        stepsContent += `
          <div style="background: white; padding: 0.1in; border-radius: 3px; border: 1px solid #dee2e6; margin-bottom: 0.1in; page-break-inside: avoid;">
            <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 0.08in; border-bottom: 1px solid #f1f5f9; padding-bottom: 0.05in;">
              <div style="display: flex; align-items: center; gap: 0.1in;">
                <div style="display: flex; align-items: center; justify-content: center; width: 0.3in; height: 0.3in; background: #f8f9fa; border-radius: 50%; font-size: 9pt; font-weight: 600;">
                  ${section.serial}
                </div>
                <div>
                  <h4 style="margin: 0; font-size: 10pt; font-weight: 600; color: #212529;">${section.label || 'Section ' + section.serial}</h4>
                  <p style="margin: 0; font-size: 8pt; color: #6b7280;">${SOP_SECTION_TYPE_MAP[section.sectionType as keyof typeof SOP_SECTION_TYPE_MAP]} - Section ${section.serial}</p>
                </div>
              </div>
              ${
                hasRiskAssessment
                  ? `
              <div style="display: flex; align-items: center; gap: 0.05in;">
                <span style="background: ${riskColor}; color: white; padding: 0.02in 0.05in; border-radius: 3px; font-size: 8pt; font-weight: 600;">
                  Severity: ${section.severity}/4
                </span>
                <span style="background: ${riskColor}20; color: ${riskColor}; padding: 0.02in 0.05in; border-radius: 3px; font-size: 8pt; font-weight: 600;">
                  ${riskData?.level}
                </span>
              </div>
              `
                  : ''
              }
            </div>

            <div style="margin-bottom: 0.08in; padding: 0.05in; background: #f8f9fa; border-radius: 3px;">
              <span style="font-size: 9pt; color: #212529; line-height: 1.4; white-space: pre-wrap;">${section.value}</span>
            </div>

            ${
              hasRiskAssessment && (section.hazardIds?.length || section.controlMeasureIds?.length)
                ? `
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.1in; margin-top: 0.05in;">
              <!-- Hazards Column -->
              <div>
                <div style="display: flex; align-items: center; gap: 0.03in; margin-bottom: 0.05in;">
                  <div style="width: 0.08in; height: 0.08in; background: #dc2626; border-radius: 50%;"></div>
                  <strong style="font-size: 8pt; color: #495057;">Hazards</strong>
                </div>
                ${
                  section.hazardIds && section.hazardIds.length > 0
                    ? '<p style="font-size: 8pt; color: #6b7280;">Associated hazards: ' +
                      section.hazardIds.length +
                      '</p>'
                    : '<p style="font-size: 8pt; color: #6b7280; font-style: italic;">No hazards identified</p>'
                }
              </div>

              <!-- Control Measures Column -->
              <div>
                <div style="display: flex; align-items: center; gap: 0.03in; margin-bottom: 0.05in;">
                  <div style="width: 0.08in; height: 0.08in; background: #16a34a; border-radius: 50%;"></div>
                  <strong style="font-size: 8pt; color: #495057;">Control Measures</strong>
                </div>
                ${
                  section.controlMeasureIds && section.controlMeasureIds.length > 0
                    ? '<p style="font-size: 8pt; color: #6b7280;">Associated control measures: ' +
                      section.controlMeasureIds.length +
                      '</p>'
                    : '<p style="font-size: 8pt; color: #6b7280; font-style: italic;">No control measures defined</p>'
                }
              </div>
            </div>
            `
                : ''
            }
          </div>
        `;
      });
    });

    stepsSection.innerHTML = stepsContent;
    printDiv.appendChild(stepsSection);
  }

  // Additional Notes Section
  if (data.notes) {
    const notesSection = document.createElement('div');
    notesSection.style.marginBottom = '0.15in';
    notesSection.style.background = '#f8f9fa';
    notesSection.style.border = '1px solid #dee2e6';
    notesSection.style.borderRadius = '4px';
    notesSection.style.padding = '0.1in';
    notesSection.innerHTML = `
      <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #2563eb; font-weight: 600; border-bottom: 1px solid #2563eb; padding-bottom: 0.03in;">
        Additional Notes & Considerations
      </h3>
      <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #17a2b8;">
        <span style="color: #212529; font-size: 9pt; line-height: 1.4; white-space: pre-wrap;">${data.notes}</span>
      </div>
    `;
    printDiv.appendChild(notesSection);
  }

  // Linked Assets Section
  if (data.assets && data.assets.length > 0) {
    const assetsSection = document.createElement('div');
    assetsSection.style.marginBottom = '0.15in';
    assetsSection.style.background = '#f8f9fa';
    assetsSection.style.border = '1px solid #dee2e6';
    assetsSection.style.borderRadius = '4px';
    assetsSection.style.padding = '0.1in';
    assetsSection.innerHTML = `
      <h3 style="font-size: 11pt; margin: 0 0 0.08in 0; color: #2563eb; font-weight: 600; border-bottom: 1px solid #2563eb; padding-bottom: 0.03in;">
        Linked Assets
      </h3>
      <div style="background: white; padding: 0.08in; border-radius: 3px; border-left: 3px solid #fd7e14;">
        <span style="color: #212529; font-size: 9pt; line-height: 1.4;">
          ${data.assets.map((asset) => asset.name).join(', ')}
        </span>
      </div>
    `;
    printDiv.appendChild(assetsSection);
  }

  // Footer Section
  const footerSection = document.createElement('div');
  footerSection.style.marginTop = '0.2in';
  footerSection.style.paddingTop = '0.1in';
  footerSection.style.borderTop = '1px solid #dee2e6';
  footerSection.style.fontSize = '8pt';
  footerSection.style.color = '#666';
  footerSection.innerHTML = `
    <div style="display: flex; justify-content: space-between; align-items: center;">
      <div>
        <strong>Created At:</strong> ${formatDate(data.createdAt)}<br>
        ${data.updatedAt ? `<strong>Last Updated:</strong> ${formatDate(data.updatedAt)}` : ''}
        ${data.archivedAt ? `<br><strong>Archived:</strong> ${formatDate(data.archivedAt)}` : ''}
      </div>
      <div style="text-align: right;">
        <strong>Standard Operating Procedure Report</strong><br>
        Generated on ${formatDate(new Date())}
      </div>
    </div>
  `;
  printDiv.appendChild(footerSection);

  // Create a new window/tab with the PDF content
  const newWindow = window.open('', '_blank');

  if (!newWindow) {
    // Fallback to print if popup is blocked
    document.body.appendChild(printDiv);
    window.print();
    document.body.removeChild(printDiv);
    return;
  }

  // Set up the new window with proper HTML structure and print styles
  const htmlContent = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>SOP_Report_${data.slug || 'Report'}_${format(new Date(), 'yyyy-MM-dd')}</title>
      <style>
        body {
          margin: 0;
          padding: 0;
          font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
          background: #f5f5f5;
        }
        
        .print-container {
          max-width: 8.5in;
          margin: 20px auto;
          background: white;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          border-radius: 8px;
          overflow: hidden;
        }
        
        .print-actions {
          background: #2563eb;
          color: white;
          padding: 15px 20px;
          text-align: center;
          position: sticky;
          top: 0;
          z-index: 100;
        }
        
        .print-actions button {
          background: white;
          color: #2563eb;
          border: none;
          padding: 8px 16px;
          margin: 0 5px;
          border-radius: 4px;
          cursor: pointer;
          font-weight: 600;
          transition: all 0.2s;
          min-width: 100px;
          font-size: 14px;
        }
        
        .print-actions button:hover {
          background: #f8f9fa;
          transform: translateY(-1px);
        }
        
        @media print {
          body {
            background: white;
          }
          .print-container {
            max-width: none;
            margin: 0;
            box-shadow: none;
            border-radius: 0;
          }
          .print-actions {
            display: none;
          }
          @page {
            margin: 0.5in;
            size: letter;
          }
          * {
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
          }
          table {
            page-break-inside: avoid;
          }
          h3 {
            page-break-after: avoid;
          }
          /* Step containers should stay together */
          div[style*="page-break-inside: avoid"] {
            page-break-inside: avoid;
            break-inside: avoid;
            orphans: 2;
            widows: 2;
          }
        }
        
        @media screen {
          .print-content {
            padding: 20px;
          }
        }
      </style>
    </head>
    <body>
      <div class="print-container">
        <div class="print-actions">
          <h3 style="margin: 0 0 10px 0;">Standard Operating Procedure Report - Work Process Documentation</h3>
          <button onclick="window.print()">🖨️ Print PDF</button>
          <button onclick="window.close()">❌ Close</button>
        </div>
        <div class="print-content">
          ${printDiv.innerHTML}
        </div>
      </div>
    </body>
    </html>
  `;

  newWindow.document.write(htmlContent);
  newWindow.document.close();

  // Focus the new window
  newWindow.focus();
};
