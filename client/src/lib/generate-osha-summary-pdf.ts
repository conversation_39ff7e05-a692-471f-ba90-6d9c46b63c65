import { RouterOutputs } from '@shared/types/router.types';

type OshaSummary = RouterOutputs['oshaSummary']['getOshaCasesSummary'];
type OshaSummaryEstablishmentInfo = RouterOutputs['oshaSummary']['getEstablishmentInformation'];

export const generateOshaSummaryPdf = ({
  summary,
  establishmentInfo,
  year,
}: {
  summary: OshaSummary;
  establishmentInfo?: OshaSummaryEstablishmentInfo;
  year: number;
}) => {
  // Create a printable div for the OSHA Form 300A - optimized for A4 size
  const printDiv = document.createElement('div');
  printDiv.style.width = '8.27in'; // A4 width
  printDiv.style.minHeight = '11.69in'; // A4 height
  printDiv.style.margin = '0 auto';
  printDiv.style.padding = '0.4in'; // Balanced padding for A4
  printDiv.style.fontFamily = '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif';
  printDiv.style.fontSize = '9.5pt'; // Slightly larger than before
  printDiv.style.position = 'relative';
  printDiv.style.lineHeight = '1.25'; // Slightly more breathing room
  printDiv.style.backgroundColor = '#ffffff';
  printDiv.style.color = '#1a1a1a';
  printDiv.style.boxSizing = 'border-box';

  // OSHA Header Section - balanced for A4
  const headerSection = document.createElement('div');
  headerSection.style.textAlign = 'center';
  headerSection.style.marginBottom = '0.12in'; // Slightly more space
  headerSection.style.borderBottom = '2px solid #0066cc';
  headerSection.style.paddingBottom = '0.06in';
  headerSection.innerHTML = `
    <table style="width: 100%; margin-bottom: 0.06in;">
      <tr>
        <td style="width: 110px; text-align: center; vertical-align: middle;">
          <div style="width: 90px; height: 54px; margin: 0 auto; display: flex; align-items: center; justify-content: center;"> <!-- Slightly larger -->
            <svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" viewBox="0 0 720.002 207.213" style="width: 90px; height: auto;">
              <path d="M314.039 48.588c-1.656-13.246-2.76-26.492-2.76-39.465-8.557-1.102-32.014-4.139-39.738-4.139-35.602 0-60.988 21.25-60.988 57.127 0 66.51 81.131 51.332 81.131 100.18 0 19.594-15.178 31.461-33.943 31.461-26.217 0-39.463-20.422-42.773-43.879l-7.729 1.654c1.381 14.074 3.314 27.875 4.691 41.949 13.523 6.9 28.426 12.42 43.881 12.42 35.322 0 66.783-19.873 66.783-57.955 0-66.236-82.789-52.436-82.789-98.248 0-21.805 12.143-34.773 33.941-34.773 19.043 0 30.635 17.111 32.564 35.049"/>
              <path d="M372.752 109.027h100.176v68.719c0 14.904-11.592 15.73-21.523 15.73h-5.246v8.279c12.143 0 29.805-1.104 44.707-1.104 14.074 0 30.633 1.104 41.67 1.104v-8.279h-3.861c-9.936 0-20.422-1.381-20.422-15.73V33.133c0-14.348 10.486-15.729 20.422-15.729h3.861V9.123c-11.037 0-27.596 1.105-41.67 1.105-14.902 0-32.564-1.105-44.707-1.105v8.281h5.246c9.932 0 21.523.826 21.523 15.729v60.439H372.752V33.133c0-14.902 11.592-15.729 21.525-15.729h5.244V9.123c-12.143 0-29.805 1.105-44.709 1.105-14.072 0-30.629-1.105-41.668-1.105v8.281h3.861c9.936 0 20.422 1.381 20.422 15.729v144.613c0 14.35-10.486 15.73-20.422 15.73h-3.861v8.279c11.039 0 27.596-1.104 41.668-1.104 14.904 0 32.566 1.104 44.709 1.104v-8.279h-5.244c-9.934 0-21.525-.826-21.525-15.73"/>
              <path d="M581.742 133.59h62.645l10.762 32.012c3.037 8.832 5.52 16.283 5.52 19.873 0 6.898-10.762 8.002-17.109 8.002h-3.035v8.279c14.076-.551 27.875-1.104 41.395-1.104 13.246 0 25.664.553 38.084 1.104v-8.279h-1.656c-9.105 0-14.9-3.311-18.213-11.039-3.588-8.279-6.898-18.49-10.211-28.15L639.145 9.951c-.83-2.207-1.656-4.691-2.484-6.898-.551-.83-1.104-.83-1.932-.83s-1.379.279-2.207.553c-5.244 3.312-16.283 8.832-25.113 11.867-1.654 10.213-6.623 24.012-10.211 34.223L553.042 175.54c-4.139 11.59-13.244 17.938-24.008 17.938h-1.654v8.279c9.934-.551 19.867-1.104 29.805-1.104 11.035 0 22.352.553 33.391 1.104v-8.279h-3.035c-9.109 0-20.699-1.381-20.699-9.66 0-4.967 3.312-12.143 6.07-21.525m65.68-44.158h-51.605l25.664-78.102h.555l25.386 78.102z"/>
              <path fill="#939598" d="M56.324 71.451c10.143-14.01 26.633-23.123 45.25-23.123 30.836 0 55.832 24.996 55.832 55.834 0 30.836-24.996 55.832-55.832 55.832-22.824 0-42.449-13.695-51.105-33.32 6.953 7.992 17.197 13.043 28.623 13.043 20.947 0 37.928-16.982 37.928-37.93s-16.98-37.928-37.928-37.928a37.774 37.774 0 0 0-22.768 7.592"/>
              <path fill="#0082C4" d="M0 103.941C0 60.33 35.352 24.975 78.963 24.975c28.4 0 53.299 14.996 67.213 37.502-11.141-11.916-27.002-19.363-44.602-19.363-33.713 0-61.045 27.332-61.045 61.049s27.332 61.049 61.045 61.049c17.168 0 32.68-7.086 43.771-18.494-14.057 21.775-38.537 36.189-66.383 36.189C35.352 182.906 0 147.553 0 103.941"/>
              <path d="M5.836 61.889C21.92 25.688 58.189.443 100.357.443c57.094 0 103.379 46.285 103.379 103.383 0 57.1-46.285 103.387-103.379 103.387-41.775 0-77.764-24.781-94.068-60.441 14.674 24.846 41.727 41.512 72.674 41.512 46.58 0 84.34-37.762 84.34-84.342 0-46.584-37.76-84.344-84.34-84.344-31.268 0-58.563 17.013-73.127 42.291M664.98 19.18C664.98 8.592 673.572 0 684.262 0c10.639 0 19.18 8.592 19.18 19.18 0 10.689-8.541 19.281-19.18 19.281A19.23 19.23 0 0 1 664.98 19.18m35.084 0c0-8.695-7.059-15.805-15.803-15.805-8.797 0-15.908 7.109-15.908 15.805 0 8.797 7.111 15.906 15.908 15.906 8.745 0 15.803-7.109 15.803-15.906zm-19.894 1.277v9.771h-3.529V7.621h7.314c4.141 0 8.438 1.123 8.438 6.24 0 2.607-1.584 4.652-4.604 5.266v.104c3.121.613 3.479 1.994 3.838 4.449.307 2.146.562 4.502 1.328 6.549h-4.502c-.254-1.279-.613-2.713-.768-4.041-.252-1.945-.252-3.736-1.277-4.809-.867-.922-2.045-.818-3.273-.922h-2.965zm3.73-3.527c3.326-.104 4.092-1.484 4.092-3.223 0-1.689-.766-2.557-3.578-2.557h-4.244v5.779h3.73z"/>
            </svg>
          </div>
        </td>
        <td style="text-align: left; vertical-align: middle; padding-left: 0.16in;">
          <h1 style="font-size: 15pt; margin: 0; color: #0066cc; font-weight: 700;">Form 300A</h1> <!-- Slightly larger -->
          <h2 style="font-size: 10.5pt; margin: 0; color: #333; font-weight: 500;">Summary of Work-Related Injuries and Illnesses</h2>
        </td>
      </tr>
    </table>
    <div style="background: #f8f9fa; padding: 0.06in; border-radius: 4px; border: 1px solid #dee2e6;">
      <p style="margin: 0; font-size: 7.5pt; color: #666; font-style: italic;"> <!-- Slightly larger -->
        All establishments covered by Part 1904 must complete this Summary page, even if no injuries or illnesses occurred during the year.
      </p>
      <p style="margin: 0.04in 0 0 0; font-size: 11.5pt; color: #0066cc; font-weight: 600;">Calendar Year: ${year}</p>
    </div>
  `;
  printDiv.appendChild(headerSection);

  // Establishment Information Section - balanced spacing
  const estInfo = document.createElement('div');
  estInfo.style.marginBottom = '0.1in'; // Balanced margin
  estInfo.style.background = '#f8f9fa';
  estInfo.style.border = '1px solid #dee2e6';
  estInfo.style.borderRadius = '4px';
  estInfo.style.padding = '0.07in'; // Balanced padding
  estInfo.innerHTML = `
    <h3 style="font-size: 10.5pt; margin: 0 0 0.06in 0; color: #0066cc; font-weight: 600; border-bottom: 1px solid #0066cc; padding-bottom: 0.025in;">
      Establishment Information
    </h3>
    <table style="width: 100%; font-size: 8.5pt; border-collapse: collapse;"> <!-- Slightly larger font -->
      <tr>
        <td style="width: 50%; vertical-align: top; padding-right: 0.09in;">
          <div style="background: white; padding: 0.06in; border-radius: 3px; border-left: 3px solid #0066cc; margin-bottom: 0.04in;">
            <strong style="color: #495057;">Company Name:</strong><br>
            <span style="color: #212529; font-size: 9.5pt;">${establishmentInfo?.companyName || 'Not specified'}</span>
          </div>
          <div style="background: white; padding: 0.06in; border-radius: 3px; border-left: 3px solid #0066cc; margin-bottom: 0.04in;">
            <strong style="color: #495057;">Facility ID:</strong><br>
            <span style="color: #212529; font-size: 9.5pt;">${establishmentInfo?.companyFacilityId || 'Not specified'}</span>
          </div>
          <div style="background: white; padding: 0.06in; border-radius: 3px; border-left: 3px solid #0066cc;">
            <strong style="color: #495057;">NAICS Code:</strong><br>
            <span style="color: #212529; font-size: 9.5pt;">${establishmentInfo?.companyNAICSCode || 'Not specified'}</span>
          </div>
        </td>
        <td style="width: 50%; vertical-align: top; padding-left: 0.09in;">
          <div style="background: white; padding: 0.06in; border-radius: 3px; border-left: 3px solid #28a745; margin-bottom: 0.04in;">
            <strong style="color: #495057;">Annual Average Employees:</strong><br>
            <span style="color: #212529; font-size: 9.5pt; font-weight: 600;">${establishmentInfo?.companyAnnualAverageNumberOfEmployees || 'Not specified'}</span>
          </div>
          <div style="background: white; padding: 0.06in; border-radius: 3px; border-left: 3px solid #28a745;">
            <strong style="color: #495057;">Total Hours Worked:</strong><br>
            <span style="color: #212529; font-size: 9.5pt; font-weight: 600;">${establishmentInfo?.companyTotalHoursWorked ? Number(establishmentInfo.companyTotalHoursWorked).toLocaleString() : 'Not specified'}</span>
          </div>
        </td>
      </tr>
    </table>
  `;
  printDiv.appendChild(estInfo);

  // Summary Statistics - balanced table
  const summaryTable = document.createElement('div');
  summaryTable.style.marginBottom = '0.1in';
  summaryTable.innerHTML = `
    <h3 style="font-size: 10.5pt; margin: 0 0 0.06in 0; color: #0066cc; font-weight: 600; border-bottom: 1px solid #0066cc; padding-bottom: 0.025in;">
      Summary of Cases
    </h3>
    <table style="width: 100%; border-collapse: collapse; font-size: 8.5pt; border: 1px solid #dee2e6;"> <!-- Slightly larger font -->
      <thead>
        <tr style="background: #0066cc; color: white;">
          <th style="padding: 0.06in; text-align: left; font-weight: 600; border-right: 1px solid #004499;">Type of Case</th>
          <th style="padding: 0.06in; text-align: center; font-weight: 600; width: 20%;">Count</th>
        </tr>
      </thead>
      <tbody>
        <tr style="background: #fff5f5;">
          <td style="padding: 0.05in; border-right: 1px solid #dee2e6; border-bottom: 1px solid #dee2e6;">
            <strong>Deaths</strong><br>
            <span style="font-size: 7.5pt; color: #666;">Fatalities from work-related injuries</span>
          </td>
          <td style="padding: 0.05in; text-align: center; font-weight: 700; font-size: 11.5pt; color: ${summary.deaths ? '#dc3545' : '#28a745'}; border-bottom: 1px solid #dee2e6;">
            ${summary.deaths}
          </td>
        </tr>
        <tr style="background: #fff8e1;">
          <td style="padding: 0.05in; border-right: 1px solid #dee2e6; border-bottom: 1px solid #dee2e6;">
            <strong>Cases with days away from work</strong><br>
            <span style="font-size: 7.5pt; color: #666;">Employee unable to work due to injury</span>
          </td>
          <td style="padding: 0.05in; text-align: center; font-weight: 700; font-size: 11.5pt; color: ${summary.totalDaysAway ? '#fd7e14' : '#28a745'}; border-bottom: 1px solid #dee2e6;">
            ${summary.totalDaysAway}
          </td>
        </tr>
        <tr style="background: #e8f4fd;">
          <td style="padding: 0.05in; border-right: 1px solid #dee2e6; border-bottom: 1px solid #dee2e6;">
            <strong>Cases with job transfer or restriction</strong><br>
            <span style="font-size: 7.5pt; color: #666;">Modified work assignments due to injury</span>
          </td>
          <td style="padding: 0.05in; text-align: center; font-weight: 700; font-size: 11.5pt; color: ${summary.totalDaysRestricted ? '#fd7e14' : '#28a745'}; border-bottom: 1px solid #dee2e6;">
            ${summary.totalDaysRestricted}
          </td>
        </tr>
        <tr style="background: #f0f8ff;">
          <td style="padding: 0.05in; border-right: 1px solid #dee2e6; border-bottom: 1px solid #dee2e6;">
            <strong>Other recordable cases</strong><br>
            <span style="font-size: 7.5pt; color: #666;">Medical treatment beyond first aid</span>
          </td>
          <td style="padding: 0.05in; text-align: center; font-weight: 700; font-size: 11.5pt; color: ${summary.otherCases ? '#6f42c1' : '#28a745'}; border-bottom: 1px solid #dee2e6;">
            ${summary.otherCases}
          </td>
        </tr>
        <tr style="background: #f8f9fa; border-top: 2px solid #0066cc;">
          <td style="padding: 0.06in; font-weight: 700; font-size: 9.5pt; color: #0066cc; border-right: 1px solid #dee2e6;">
            TOTAL RECORDABLE CASES
          </td>
          <td style="padding: 0.06in; text-align: center; font-weight: 700; font-size: 13pt; color: #0066cc;">
            ${summary.totalCases}
          </td>
        </tr>
      </tbody>
    </table>
  `;
  printDiv.appendChild(summaryTable);

  // Injury Rates Section - balanced spacing
  const ratesSection = document.createElement('div');
  ratesSection.style.marginBottom = '0.1in';
  ratesSection.innerHTML = `
    <h3 style="font-size: 10.5pt; margin: 0 0 0.06in 0; color: #0066cc; font-weight: 600; border-bottom: 1px solid #0066cc; padding-bottom: 0.025in;">
      Safety Performance Metrics
    </h3>
    <div style="overflow: hidden; margin-bottom: 0.06in;">
      <div style="width: 48%; float: left; margin-right: 4%;">
        <div style="background: #e3f2fd; border: 1px solid #2196f3; border-radius: 4px; padding: 0.06in; text-align: center;">
          <div style="font-size: 7.5pt; color: #1565c0; font-weight: 600; margin-bottom: 0.025in;">TOTAL RECORDABLE CASE RATE</div>
          <div style="font-size: 17pt; font-weight: 700; color: #0d47a1; margin-bottom: 0.025in;">${summary.trcRate.toFixed(2)}</div>
          <div style="font-size: 7.5pt; color: #1565c0;">per 200,000 hours worked</div>
        </div>
      </div>
      <div style="width: 48%; float: right;">
        <div style="background: #f3e5f5; border: 1px solid #9c27b0; border-radius: 4px; padding: 0.06in; text-align: center;">
          <div style="font-size: 7.5pt; color: #7b1fa2; font-weight: 600; margin-bottom: 0.025in;">DART RATE</div>
          <div style="font-size: 17pt; font-weight: 700; color: #4a148c; margin-bottom: 0.025in;">${summary.dartRate.toFixed(2)}</div>
          <div style="font-size: 7.5pt; color: #7b1fa2;">Days Away, Restricted, or Transferred</div>
        </div>
      </div>
    </div>
    <div style="clear: both; background: #f8f9fa; padding: 0.05in; border-radius: 3px; border-left: 3px solid #17a2b8;">
      <p style="margin: 0; font-size: 7.5pt; color: #666; font-style: italic;">
        <strong>Note:</strong> Rates are calculated per 200,000 hours worked (equivalent to 100 full-time employees working 40 hours per week for 50 weeks).
      </p>
    </div>
  `;
  printDiv.appendChild(ratesSection);

  // Certification Section - balanced spacing
  const certSection = document.createElement('div');
  certSection.style.marginBottom = '0.1in';
  certSection.style.background = '#f8f9fa';
  certSection.style.border = '1px solid #dee2e6';
  certSection.style.borderRadius = '4px';
  certSection.style.padding = '0.07in';
  certSection.style.pageBreakBefore = 'auto';
  certSection.style.pageBreakInside = 'avoid';
  certSection.innerHTML = `
    <h3 style="font-size: 10.5pt; margin: 0 0 0.06in 0; color: #0066cc; font-weight: 600; border-bottom: 1px solid #0066cc; padding-bottom: 0.025in;">
      Company Executive Certification
    </h3>
    <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 3px; padding: 0.05in; margin-bottom: 0.06in;">
      <p style="margin: 0; font-size: 7.5pt; color: #856404; font-weight: 500;">
        I certify that I have examined this document and that to the best of my knowledge the entries are true, accurate, and complete.
      </p>
    </div>
    <table style="width: 100%; font-size: 8.5pt; border-collapse: collapse;">
      <tr>
        <td style="width: 50%; vertical-align: top; padding-right: 0.09in;">
          <div style="background: white; padding: 0.06in; border-radius: 3px; border-left: 3px solid #0066cc; margin-bottom: 0.04in;">
            <strong style="color: #495057;">Executive Name:</strong><br>
            <span style="color: #212529; font-size: 9.5pt; font-weight: 600;">${establishmentInfo?.executiveName || 'Not specified'}</span>
          </div>
          <div style="background: white; padding: 0.06in; border-radius: 3px; border-left: 3px solid #0066cc;">
            <strong style="color: #495057;">Title:</strong><br>
            <span style="color: #212529; font-size: 9.5pt;">${establishmentInfo?.executiveTitle || 'Not specified'}</span>
          </div>
        </td>
        <td style="width: 50%; vertical-align: top; padding-left: 0.09in;">
          <div style="background: white; padding: 0.06in; border-radius: 3px; border-left: 3px solid #28a745; margin-bottom: 0.04in;">
            <strong style="color: #495057;">Digital Signature:</strong><br>
            <div style="border: 1px dashed #dee2e6; border-radius: 3px; width: 150px; height: 45px; margin: 0.025in 0; padding: 2px; background: #fafafa; text-align: center; line-height: 41px;"> <!-- Balanced size -->
              ${
                establishmentInfo?.digitalSignature
                  ? `<img src="${establishmentInfo?.digitalSignature}" style="max-width: 100%; max-height: 100%; vertical-align: middle;" />`
                  : '<span style="color: #6c757d; font-size: 7.5pt; font-style: italic;">No signature provided</span>'
              }
            </div>
          </div>
          <div style="background: white; padding: 0.06in; border-radius: 3px; border-left: 3px solid #28a745;">
            <strong style="color: #495057;">Date Certified:</strong><br>
            <span style="color: #212529; font-size: 9.5pt;">${establishmentInfo?.dateCertified || 'Not specified'}</span>
          </div>
        </td>
      </tr>
    </table>
  `;
  printDiv.appendChild(certSection);

  // Create a new window/tab with the PDF content
  const newWindow = window.open('', '_blank');

  if (!newWindow) {
    // Fallback to print if popup is blocked
    document.body.appendChild(printDiv);
    window.print();
    document.body.removeChild(printDiv);
    return;
  }

  // Set up the new window with proper HTML structure and print styles
  const htmlContent = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>OSHA_Form_300A_Summary_${year}_${establishmentInfo?.companyName?.replace(/[^a-zA-Z0-9]/g, '_') || 'Company'}</title>
      <style>
        body {
          margin: 0;
          padding: 0;
          font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
          background: #f5f5f5;
        }
        
        .print-container {
          max-width: 8.5in;
          margin: 20px auto;
          background: white;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          border-radius: 8px;
          overflow: hidden;
        }
        
        .print-actions {
          background: #0066cc;
          color: white;
          padding: 15px 20px;
          text-align: center;
          position: sticky;
          top: 0;
          z-index: 100;
        }
        
        .print-actions button {
          background: white;
          color: #0066cc;
          border: none;
          padding: 8px 16px;
          margin: 0 5px;
          border-radius: 4px;
          cursor: pointer;
          font-weight: 600;
          transition: all 0.2s;
          min-width: 100px;
          font-size: 14px;
        }
        
        .print-actions button:hover {
          background: #f8f9fa;
          transform: translateY(-1px);
        }
        
        @media print {
          body {
            background: white;
          }
          .print-container {
            max-width: none;
            margin: 0;
            box-shadow: none;
            border-radius: 0;
          }
          .print-actions {
            display: none;
          }
          @page {
            margin: 0.5in;
            size: letter;
          }
          * {
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
          }
          table {
            page-break-inside: avoid;
          }
          h3 {
            page-break-after: avoid;
          }
          div[style*="page-break-inside: avoid"] {
            page-break-inside: avoid;
          }
        }
        
        @media screen {
          .print-content {
            padding: 20px;
          }
        }
      </style>
    </head>
    <body>
      <div class="print-container">
        <div class="print-actions">
          <h3 style="margin: 0 0 10px 0;">OSHA Form 300A - Summary of Work-Related Injuries and Illnesses</h3>
          <button onclick="window.print()">🖨️ Print PDF</button>
          <button onclick="window.close()">❌ Close</button>
        </div>
        <div class="print-content">
          ${printDiv.innerHTML}
        </div>
      </div>
    </body>
    </html>
  `;

  newWindow.document.write(htmlContent);
  newWindow.document.close();

  // Focus the new window
  newWindow.focus();
};
