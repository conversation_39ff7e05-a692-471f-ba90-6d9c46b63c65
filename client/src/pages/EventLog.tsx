import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { EventsEmpty } from '@/components/events/list/events-empty';
import { EventsError } from '@/components/events/list/events-error';
import { Filters } from '@/components/events/list/events-filters';
import { EventsLoading } from '@/components/events/list/events-loading';
import { MobileFilters } from '@/components/events/list/events-mobile-filters';
import { EventsMobileView } from '@/components/events/list/events-mobile-view';
import { EventsTable } from '@/components/events/list/events-table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { useAnalytics } from '@/hooks/use-analytics';
import { useIsMobile } from '@/hooks/use-mobile';
import { useInfiniteEvents } from '@/hooks/use-paginated-data';
import { usePermissions } from '@/hooks/use-permissions';
import { useEventsUrlFilters } from '@/hooks/use-url-filters';
import { exportEventsCSV } from '@/lib/export-events';
import { trpc } from '@/providers/trpc';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { ReportTypeSchema, SeveritySchema, StatusSchema } from '@shared/types/schema.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { FileDown, Search, X } from 'lucide-react';
import { useCallback, useEffect } from 'react';
import { toast } from 'sonner';
import { useLocation } from 'wouter';
import z from 'zod';

export default function EventLog() {
  const [_, navigate] = useLocation();
  const isMobile = useIsMobile();
  const { hasPermission } = usePermissions();
  const analytics = useAnalytics();

  // Use the URL filters hook instead of manual state management
  const { filters, immediateFilters, updateFilter, updateFilters, resetFilters, activeFilterCount } =
    useEventsUrlFilters();

  const {
    data: events,
    isLoading,
    error,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
    isFetchedAfterMount,
  } = useInfiniteEvents({
    filters,
    enabled: true,
  });

  const { mutateAsync: exportEvents, isPending: isExporting } = trpc.event.export.useMutation();

  useEffect(() => {
    if (error?.data?.code === 'FORBIDDEN') {
      navigate(ROUTES.NOT_FOUND);
    }
  }, [error]);

  // Track page view on component mount (only once)
  useEffect(() => {
    // since sorting is not applied yet in events list page, keeping static values for now
    if (isFetchedAfterMount) {
      analytics.track(ANALYTICS_EVENTS.EVENT.LOG_VIEW_OPENED, {
        default_sort_by: 'reportedAt',
        default_sort_order: 'desc',
      });
    }
  }, [isFetchedAfterMount]);

  // Track search when debounced search term changes
  useEffect(() => {
    if (filters.search?.trim() && events) {
      analytics.track(ANALYTICS_EVENTS.EVENT.SEARCH_PERFORMED, {
        search_term: filters.search,
        result_count: events.length,
      });
    }
  }, [filters.search, events]);

  const toggleFilter = useCallback(
    (
      type: 'status' | 'type' | 'severity',
      value: z.infer<typeof StatusSchema> | z.infer<typeof ReportTypeSchema> | z.infer<typeof SeveritySchema>,
    ) => {
      const currentFilters = [...(immediateFilters[type] ?? [])];
      const index = currentFilters.indexOf(value);

      let newFilters;
      if (index > -1) {
        currentFilters.splice(index, 1);
        newFilters = currentFilters;
      } else {
        currentFilters.push(value);
        newFilters = currentFilters;
      }

      // Track filter applied
      analytics.track(ANALYTICS_EVENTS.EVENT.FILTER_APPLIED, {
        filter_name: type,
        filter_value: value,
        include_archived_toggle_state: filters.includeArchived,
      });

      updateFilter(type, newFilters);
    },
    [immediateFilters, updateFilter],
  );

  const trackFilterApplied = (
    type: 'status' | 'type' | 'severity' | 'locationIds' | 'oshaReportable' | 'includeArchived',
    value: string,
  ) => {
    analytics.track(ANALYTICS_EVENTS.EVENT.FILTER_APPLIED, {
      filter_name: type,
      filter_value: value,
      include_archived_toggle_state: filters.includeArchived,
    });
  };

  // Enhanced reset filters that also clears search
  const handleResetFilters = () => {
    // Track filter reset
    analytics.track(ANALYTICS_EVENTS.EVENT.FILTER_RESET, {});

    resetFilters();
  };

  const handleExport = async () => {
    toast.info('Exporting events started...');

    try {
      const events = await exportEvents(filters);

      exportEventsCSV(events);

      toast.success('Exporting events completed...');
    } catch (error) {
      console.error(error);
      toast.error('Error exporting events');
    }
  };

  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <div className="flex flex-col gap-4 justify-between items-start lg:flex-row ">
        <div>
          <h1 className="text-2xl font-bold  md:mb-0">Safety Events</h1>
          <p className="text-muted-foreground text-sm mt-1">Track and manage all safety events</p>
        </div>
        <div className="w-full flex flex-col-reverse items-start pb-4 gap-4 md:pb-0 md:flex-row md:gap-2 md:w-auto">
          <div className="relative flex-1 md:w-64 w-full">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search events..."
              className="pl-8 pr-4"
              value={immediateFilters.search ?? ''}
              onChange={(e) => updateFilter('search', e.target.value)}
            />
            {immediateFilters.search && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0 "
                onClick={() => updateFilter('search', '')}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          <div className="flex items-start gap-4 w-full md:flex-row md:gap-2 md:w-auto">
            {hasPermission(MODULES.EHS_EVENT, ALLOWED_ACTIONS.CREATE) && (
              <Button
                onClick={() => {
                  analytics.track(ANALYTICS_EVENTS.EVENT.FORM_VIEWED, {
                    form_entry_point: 'Event Log',
                  });
                  navigate(ROUTES.EVENT_NEW);
                }}
                className="flex-1"
              >
                + Create Safety Event
              </Button>
            )}
            {hasPermission(MODULES.EHS_EVENT, ALLOWED_ACTIONS.EXPORT) && (
              <Tooltip delayDuration={500}>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" disabled={isExporting} onClick={handleExport}>
                    <FileDown className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent align="end">
                  Export events (maximum 500 records). <br />
                  Only the first 500 records matching your <br /> current filters will be included in the export.
                </TooltipContent>
              </Tooltip>
            )}
            <MobileFilters
              toggleFilter={toggleFilter}
              filters={immediateFilters}
              updateFilters={updateFilters}
              activeFilterCount={activeFilterCount}
              resetFilters={handleResetFilters}
              trackFilterApplied={trackFilterApplied}
            />
          </div>
        </div>
      </div>

      {/* Filters bar */}
      <Filters
        toggleFilter={toggleFilter}
        filters={immediateFilters}
        updateFilters={updateFilters}
        activeFilterCount={activeFilterCount}
        resetFilters={handleResetFilters}
        trackFilterApplied={trackFilterApplied}
      />
      {/* Error state */}
      {error ? <EventsError /> : null}

      {/* Loading state */}
      {isLoading ? <EventsLoading /> : null}

      {/* Empty state - when no events are found and not loading */}
      {events && events.length === 0 && !isLoading && !error && (
        <EventsEmpty hasActiveFilters={activeFilterCount > 0} onResetFilters={handleResetFilters} />
      )}

      {/* Desktop Table View */}
      {!isMobile && events && events.length > 0 ? <EventsTable events={events} /> : null}

      {/* Mobile Card View */}
      {isMobile && events && events.length > 0 ? <EventsMobileView events={events} /> : null}

      {/* Load More Button for Infinite Scrolling */}
      {events && events.length > 0 && hasNextPage && (
        <div className="flex justify-center mt-6">
          <Button onClick={() => fetchNextPage()} disabled={isFetchingNextPage} variant="outline" className="px-6">
            {isFetchingNextPage ? 'Loading more...' : `Load More (${events.length} loaded)`}
          </Button>
        </div>
      )}
    </div>
  );
}
