import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { AccessPointInvalid } from '@/components/access-points/access-point-invalid';
import { AiCompose } from '@/components/composite/ai-compose';
import { AsyncAssetMultiSelect } from '@/components/composite/async-asset-multi-select';
import { AsyncLocationSelect } from '@/components/composite/async-location-select';
import { SuccessModal } from '@/components/composite/success-modal';
import { VoiceInputRef } from '@/components/composite/voice-input';
import { PublicEventError } from '@/components/events/public/public-event-error';
import { PublicEventLoading } from '@/components/events/public/public-event-loading';
import { MediaUpload } from '@/components/media/media-upload';
import { Button } from '@/components/ui/button';
import { DateTimePicker } from '@/components/ui/date-time-picker';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useAnalytics } from '@/hooks/use-analytics';
import { useFileAssociation } from '@/hooks/use-file-association';
import { trpc } from '@/providers/trpc';
import { zodResolver } from '@hookform/resolvers/zod';
import { accessPointStatusEnum, hazardCategoryEnum, reportTypeEnum, severityEnum } from '@shared/schema';
import { CreateEventFormPublicSchema } from '@shared/types/events.types';
import { TransientFileSchema } from '@shared/types/files.types';
import { RouterOutputs } from '@shared/types/router.types';
import { CATEGORY_MAP, REPORT_TYPE_MAP, SEVERITY_MAP } from '@shared/types/schema.types';
import { Loader2, Sparkles } from 'lucide-react';
import { motion } from 'motion/react';
import { useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { useSearchParams } from 'wouter';
import { z } from 'zod';

type CreatedEvent = RouterOutputs['event']['createPublic'];

const FormSchema = CreateEventFormPublicSchema;

export default function NewEventReport() {
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [autoFilledFields, setAutoFilledFields] = useState<string[]>([]);
  const [showSuccessModal, setShowSuccessModal] = useState<boolean>(false);
  const [createdEvent, setCreatedEvent] = useState<CreatedEvent>();
  const voiceInputRef = useRef<VoiceInputRef>(null);
  // Analytics tracking state
  const [formStartTime] = useState(() => Date.now());
  const [isVoiceUsed, setIsVoiceUsed] = useState<boolean>(false);
  // Track when voice analysis completed for abandonment tracking
  const [voiceCompletedAt, setVoiceCompletedAt] = useState<number | undefined>();
  // Track last field user interacted with for abandonment tracking
  const [lastFieldInteracted, setLastFieldInteracted] = useState<string>('');
  const [isAccessPointValid, setIsAccessPointValid] = useState<boolean>(false);

  const { track } = useAnalytics();
  const utils = trpc.useUtils();

  const [searchParams] = useSearchParams();

  const accessPointId = searchParams.get('accessPointId');
  const upkeepCompanyId = searchParams.get('upkeepCompanyId');
  const assetId = searchParams.get('assetId');

  const { associateFiles } = useFileAssociation({ isPublic: true, upkeepCompanyId: upkeepCompanyId || undefined });

  const {
    data: accessPoint,
    isLoading: isLoadingAccessPoint,
    isError: isErrorAccessPoint,
    isSuccess: isSuccessAccessPoint,
  } = trpc.accessPoint.getByIdPublic.useQuery(
    {
      id: accessPointId!,
      upkeepCompanyId: upkeepCompanyId!,
    },
    {
      enabled: Boolean(accessPointId && upkeepCompanyId),
    },
  );

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      name: '',
      email: '',
      type: reportTypeEnum.enumValues[0],
      title: '',
      reportedAt: new Date(),
      media: [],
      category: undefined,
      severity: undefined,
      description: '',
      immediateActions: '',
      assetIds: [],
      locationId: undefined,
      upkeepCompanyId: upkeepCompanyId!,
      customerName: '',
      customerPhoneNumber: '',
      customerAddress: '',
      accessPointCreatedBy: accessPoint?.createdBy,
    },
    mode: 'onSubmit',
  });

  useEffect(() => {
    if (accessPoint?.createdBy) {
      form.setValue('accessPointCreatedBy', accessPoint.createdBy);
    }
  }, [accessPoint?.createdBy]);

  // Form View Tracking
  useEffect(() => {
    if (isSuccessAccessPoint && accessPoint) {
      track(ANALYTICS_EVENTS.EVENT.FORM_VIEWED, {
        form_entry_point: 'QR Code',
        access_point_id: accessPoint.id!,
        location_id: accessPoint.locationId,
        asset_id: accessPoint.assetId,
        is_prefilled_from_qr: !!accessPoint.locationId,
        is_asset_specific: !!accessPoint.assetId,
        platform: 'web',
        role: 'Public',
        organization_id: accessPoint.upkeepCompanyId,
        component: 'Events',
        access_scope: 'public',
      });
    }
  }, [accessPoint, isSuccessAccessPoint]);

  // Form Abandonment Tracking
  useEffect(() => {
    const handleBeforeUnload = () => {
      const formDuration = Math.floor((Date.now() - formStartTime) / 1000);

      // Check for voice-to-form abandonment (voice was used successfully but form abandoned)
      if (isVoiceUsed && voiceCompletedAt && !isSubmitting) {
        const voiceDuration = Date.now() - voiceCompletedAt;
        track(ANALYTICS_EVENTS.EVENT.VOICE_TO_FORM_ABANDONED, {
          duration_ms: voiceDuration,
          last_field_interacted: lastFieldInteracted,
          access_point_id: accessPointId ?? undefined,
          platform: 'web',
          role: 'Public',
          organization_id: accessPoint?.upkeepCompanyId,
          component: 'Events',
          access_scope: 'public',
        });
      }
      // General form abandonment tracking
      else if (formDuration > 30 && !isSubmitting) {
        track(ANALYTICS_EVENTS.EVENT.FORM_ABANDONED, {
          form_entry_point: 'QR Code',
          duration_on_form_seconds: formDuration,
          access_point_id: accessPointId ?? undefined,
          asset_id: assetId,
          platform: 'web',
          role: 'Public',
          organization_id: accessPoint?.upkeepCompanyId,
          component: 'Events',
          access_scope: 'public',
        });
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [formStartTime, isSubmitting, accessPointId, accessPoint?.upkeepCompanyId]);

  useEffect(() => {
    if (accessPoint?.status === accessPointStatusEnum.enumValues[0] && !accessPoint?.archivedAt) {
      setIsAccessPointValid(true);
    }

    if (accessPoint?.locationId) {
      form.setValue('locationId', accessPoint.locationId);
    }

    if (accessPoint?.assetId) {
      form.setValue('assetIds', [accessPoint.assetId]);
    }
  }, [accessPoint]);

  const { mutateAsync: removeFiles } = trpc.file.removeFilesPublic.useMutation();

  const { mutateAsync: createEvent } = trpc.event.createPublic.useMutation({
    onSuccess: () => {
      utils.event.list.invalidate();
    },
    onError: (error) => {
      console.error('Error creating safety event', error);
      toast.error('Error creating safety event', {
        description: 'There was a problem creating your safety event. Please try again.',
      });
    },
  });

  if (!accessPointId || !upkeepCompanyId || isErrorAccessPoint) {
    return (
      <PublicEventError
        accessPointId={accessPointId}
        upkeepCompanyId={upkeepCompanyId}
        isErrorAccessPoint={isErrorAccessPoint}
      />
    );
  }

  if (isLoadingAccessPoint) {
    return <PublicEventLoading />;
  }

  const onFileRemoval = async (file: z.infer<typeof TransientFileSchema>) => {
    try {
      if (!file.id) {
        return;
      }

      setIsUploading(true);

      const fileId = file.id;
      await removeFiles([fileId]);
      const updatedMedias = form.getValues('media')?.filter((file) => file.id !== fileId);

      form.setValue('media', updatedMedias);
      toast.success('File removed', {
        description: 'The file has been successfully removed.',
      });
    } catch (error) {
      console.error('Error removing file:', error);
      toast.error('Error removing file', {
        description: 'There was a problem removing the file. Please try again.',
      });
    } finally {
      setIsUploading(false);
    }
  };

  // Separate function to handle the actual form submission
  const onSubmit = async (values: z.infer<typeof FormSchema>) => {
    if (!upkeepCompanyId) {
      // Form Validation Failed Tracking
      const errorMessage = 'Role ID is required but missing';
      track(ANALYTICS_EVENTS.EVENT.FORM_VALIDATION_FAILED, {
        validation_errors: [errorMessage],
        error_message: errorMessage,
        access_point_id: accessPointId,
        platform: 'web',
        role: 'Public',
        organization_id: accessPoint?.upkeepCompanyId,
        component: 'Events',
        access_scope: 'public',
      });
      toast.error('Error reporting safety event', {
        description: 'There was a problem submitting your safety event. Please try again.',
      });
      return;
    }

    try {
      setIsSubmitting(true);

      const createdEvent = await createEvent({
        ...values,
        upkeepCompanyId,
        media: undefined,
      });

      setCreatedEvent(createdEvent);

      // Associate all uploaded files with the newly created Event
      if (createdEvent?.id && values.media?.length) {
        await associateFiles(values.media, createdEvent.id, 'event');
      }

      // Form Submission Tracking
      track(ANALYTICS_EVENTS.EVENT.FORM_SUBMITTED, {
        event_id: createdEvent?.id,
        report_type: REPORT_TYPE_MAP[values.type as keyof typeof REPORT_TYPE_MAP],
        severity_level: values.severity
          ? ((values.severity.charAt(0).toUpperCase() + values.severity.slice(1)) as
              | 'Low'
              | 'Medium'
              | 'High'
              | 'Critical')
          : undefined,
        location: values.locationId || '',
        asset: values.assetIds?.length ? values.assetIds[0] : '',
        hazard_category: values.category || '',
        media_attached_count: values.media?.length || 0,
        is_ai_assisted: isVoiceUsed,
        first_name: values.name?.split(' ')[0] || '',
        last_name: values.name?.split(' ').slice(1).join(' ') || '',
        reporter_email: values.email,
        form_entry_point: 'QR Code',
        is_prefilled_from_qr: !!accessPoint?.locationId,
        access_point_id: accessPointId,
        organization_id: accessPoint?.upkeepCompanyId,
        platform: 'web',
        role: 'Public',
        component: 'Events',
        access_scope: 'public',
      });

      // QR Code Specific Analytics - tracking form submission via QR code
      if (accessPointId && accessPoint) {
        const timeFromScanToSubmit = Math.floor((Date.now() - formStartTime) / 1000);
        track(ANALYTICS_EVENTS.ACCESS_POINT.EVENT_SUBMITTED_VIA_QR, {
          access_point_id: accessPointId,
          location_id: accessPoint.locationId,
          time_from_scan_to_submit: timeFromScanToSubmit,
          platform: 'web',
          role: 'Public',
          organization_id: accessPoint.upkeepCompanyId,
          component: 'Events',
          access_scope: 'public',
        });
      }

      // Show success modal
      setShowSuccessModal(true);

      // Reset form
      form.reset();

      // Clear any auto-filled fields
      setAutoFilledFields([]);

      // Reset media files
      form.setValue('media', []);

      // Reset voice input
      voiceInputRef.current?.resetTranscript();
    } catch (error) {
      console.error('Error submitting safety event', error);
      // Form Validation Failed Tracking for submission errors
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      track(ANALYTICS_EVENTS.EVENT.FORM_VALIDATION_FAILED, {
        validation_errors: [errorMessage],
        error_message: errorMessage,
        access_point_id: accessPointId,
        platform: 'web',
        role: 'Public',
        organization_id: accessPoint?.upkeepCompanyId,
        component: 'Events',
        access_scope: 'public',
      });
      toast.error('Error reporting safety event', {
        description: 'There was a problem submitting your safety event. Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const onReset = () => {
    setShowSuccessModal(false);
    voiceInputRef.current?.resetTranscript();
    form.reset();
    setAutoFilledFields([]);
    form.setValue('media', []);
    if (accessPoint?.createdBy) {
      form.setValue('accessPointCreatedBy', accessPoint.createdBy);
    }

    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Handle the extracted data from voice analysis
  const handleVoiceAnalysis = async (data: RouterOutputs['ai']['analyzeEvent']) => {
    // Clear any previous animations
    setAutoFilledFields([]);

    // Set AI assistance flag when voice analysis starts
    setIsVoiceUsed(true);

    // Track each field that gets populated
    const trackFieldPopulation = (fieldName: string, value: string | Date | undefined) => {
      if (value) {
        // Track individual field AI population
        track(ANALYTICS_EVENTS.EVENT.FIELD_AI_POPULATED, {
          field_name: fieldName,
          access_point_id: accessPointId,
          platform: 'web',
          role: 'Public',
          organization_id: accessPoint?.upkeepCompanyId,
          component: 'Events',
          access_scope: 'public',
        });
      }
    };

    // Animate each field separately with staggered timing
    const animateField = (field: keyof z.infer<typeof FormSchema>, value: string | Date | undefined, delay: number) => {
      setTimeout(() => {
        form.setValue(field, value);
        trackFieldPopulation(field, value);
        setAutoFilledFields((prev) => [...prev, field]);
      }, delay);
    };

    // Base delay between animations and running counter
    const baseDelay = 300;
    let currentDelay = 300;

    // Apply animations in sequence for first section of form
    if (data?.type) {
      animateField('type', data.type, currentDelay);
      currentDelay += baseDelay;
    }

    if (data?.title) {
      animateField('title', data.title, currentDelay);
      currentDelay += baseDelay;
    }

    if (data && 'category' in data && data.category) {
      animateField('category', data.category, currentDelay);
      currentDelay += baseDelay;
    }

    if (data && 'reportedAt' in data && data.reportedAt) {
      animateField('reportedAt', new Date(data.reportedAt), currentDelay);
      currentDelay += baseDelay;
    }

    if (data && 'severity' in data && data.severity) {
      animateField('severity', data.severity, currentDelay);
      currentDelay += baseDelay;
    }

    if (data && 'description' in data && data.description) {
      animateField('description', data.description, currentDelay);
      currentDelay += baseDelay;
    }

    if (data && 'immediateActions' in data && data.immediateActions) {
      animateField('immediateActions', data.immediateActions, currentDelay);
      currentDelay += baseDelay;
    }

    // Mark when voice analysis completed for abandonment tracking
    setVoiceCompletedAt(Date.now());

    toast.success('🪄 AI Voice Analysis Complete', {
      description: "We've filled out the form with your spoken event report",
    });
  };

  if (!isAccessPointValid && isSuccessAccessPoint) {
    return <AccessPointInvalid />;
  }

  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 md:px-8 lg:px-10 py-6">
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        entity="publicEvent"
        data={createdEvent}
        onSecondaryActionClick={onReset}
      />

      <div>
        <div className="mb-6">
          <p className="text-muted-foreground text-base sm:text-lg">
            Use this form to report safety events or near misses that occurred in your facility.
          </p>
        </div>

        <div className="mb-5">
          <div className="p-3 bg-blue-50 rounded-lg border border-blue-100 flex flex-col gap-2">
            <h1 className="text-lg sm:text-xl font-semibold text-gray-800">Describe the issue</h1>
            <AiCompose
              methods={['voice']}
              entity="event"
              onVoiceComplete={handleVoiceAnalysis}
              voiceInputRef={voiceInputRef}
              isPublic
            />
          </div>
        </div>

        {/* Media Upload Component */}
        <div className="mt-5 mb-10 w-full px-0 sm:px-0">
          <MediaUpload
            className="w-full max-w-full"
            files={form.watch('media') ?? []}
            entityType={'event'}
            isPublic
            upkeepCompanyId={upkeepCompanyId}
            setFiles={(tFiles) => {
              form.setValue('media', tFiles);
            }}
            onUploadStateChange={(state) => {
              setIsUploading(state.isUploading);
            }}
            onFileRemove={onFileRemoval}
          />
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            {/* Name */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>
                      Name <span className="text-red-500">*</span>
                    </FormLabel>
                  </div>
                  <FormControl>
                    <Input
                      placeholder="Enter your full name"
                      className={autoFilledFields.includes('name') ? 'border-indigo-300' : ''}
                      {...field}
                      onFocus={() => setLastFieldInteracted('name')}
                    />
                  </FormControl>
                  <FormDescription>Your full name as it appears in company records</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Email */}
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>
                      Email <span className="text-red-500">*</span>
                    </FormLabel>
                  </div>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="Enter your email address"
                      className={autoFilledFields.includes('email') ? 'border-indigo-300' : ''}
                      {...field}
                      onFocus={() => setLastFieldInteracted('email')}
                    />
                  </FormControl>
                  <FormDescription>Your company email address </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Report Type */}
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>
                      Report Type <span className="text-red-500">*</span>
                    </FormLabel>
                    {autoFilledFields.includes('reportType') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      setLastFieldInteracted('type');
                    }}
                    value={field.value} // Use value instead of defaultValue to keep it updated
                  >
                    <FormControl>
                      <SelectTrigger className={autoFilledFields.includes('reportType') ? 'border-indigo-300' : ''}>
                        <SelectValue placeholder="Select report type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {reportTypeEnum.enumValues.map((type) => {
                        return (
                          <SelectItem key={type} value={type}>
                            {REPORT_TYPE_MAP[type]}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    An event is an event that caused injury or damage. A near miss is an event that could have caused
                    injury or damage but didn't. An observation is an event that did not cause injury or damage. A
                    customer event involves a customer in a safety-related event.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Customer Event Specific Fields - Conditional Display */}
            {form.watch('type') === 'customer_incident' && (
              <>
                {/* Customer Name */}
                <FormField
                  control={form.control}
                  name="customerName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Customer Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter customer's full name" {...field} value={field.value ?? ''} />
                      </FormControl>
                      <FormDescription>The name of the customer involved in this event</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Customer Phone Number */}
                <FormField
                  control={form.control}
                  name="customerPhoneNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Customer Phone Number</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter customer's phone number" {...field} value={field.value ?? ''} />
                      </FormControl>
                      <FormDescription>Phone number to reach the customer for follow-up</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Customer Address */}
                <FormField
                  control={form.control}
                  name="customerAddress"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Customer Address</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter customer's address"
                          className="min-h-[80px]"
                          {...field}
                          value={field.value ?? ''}
                        />
                      </FormControl>
                      <FormDescription>The customer's address for documentation purposes</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}

            {/* Title */}
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>
                      Title <span className="text-red-500">*</span>
                    </FormLabel>
                    {autoFilledFields.includes('title') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <FormControl>
                    <Input
                      placeholder="Briefly describe what happened"
                      className={autoFilledFields.includes('title') ? 'border-indigo-300' : ''}
                      {...field}
                      onFocus={() => setLastFieldInteracted('title')}
                    />
                  </FormControl>
                  <FormDescription>
                    A short description of the safety event (e.g., "Fall from ladder in warehouse")
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Date and Time */}
            <FormField
              control={form.control}
              name="reportedAt"
              render={({ field }) => (
                <FormItem className={`flex flex-col`}>
                  <div className="flex items-center gap-2">
                    <FormLabel>
                      Date and Time <span className="text-red-500">*</span>
                    </FormLabel>
                    {autoFilledFields.includes('reportedAt') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <FormControl>
                    <DateTimePicker
                      selected={field.value}
                      onSelect={field.onChange}
                      disabled={{ after: new Date() }}
                      onFocus={() => setLastFieldInteracted('reportedAt')}
                    />
                  </FormControl>
                  <FormDescription>When did the safety event occur?</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Location */}
            <FormField
              control={form.control}
              name="locationId"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>Location</FormLabel>
                    {autoFilledFields.includes('location') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <FormControl>
                    <AsyncLocationSelect
                      value={field.value}
                      onChange={(value) => {
                        field.onChange(value);
                        // clears assetIds if the user is changing locationId, keep if they're clearing
                        if (value) {
                          form.setValue('assetIds', []);
                        }

                        setLastFieldInteracted('locationId');
                      }}
                      mustIncludeObjectIds={accessPoint?.locationId ? [accessPoint.locationId] : undefined}
                      placeholder="Where did it happen?"
                      upkeepCompanyId={upkeepCompanyId}
                    />
                  </FormControl>
                  <FormDescription>
                    Specific area, building, or equipment where the safety event occurred
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Assets */}
            <FormField
              control={form.control}
              name="assetIds"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>Assets</FormLabel>
                    {autoFilledFields.includes('assetIds') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <FormControl>
                    <AsyncAssetMultiSelect
                      {...field}
                      value={field.value}
                      onChange={(value) => {
                        field.onChange(value);
                        setLastFieldInteracted('assetIds');
                      }}
                      locationId={form.watch('locationId') || undefined}
                      placeholder="Select assets"
                      upkeepCompanyId={upkeepCompanyId}
                    />
                  </FormControl>
                  <FormDescription>
                    {/* Equipment or asset involved (AI will suggest based on selected location) */}
                    Select the assets that were involved in the safety event. If the safety event involved multiple
                    assets, select all that apply.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Hazard Category */}
            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>Hazard Category</FormLabel>
                    {autoFilledFields.includes('category') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      setLastFieldInteracted('category');
                    }}
                    value={field.value ?? ''}
                  >
                    <FormControl>
                      <SelectTrigger className={autoFilledFields.includes('category') ? 'border-indigo-300' : ''}>
                        <SelectValue placeholder="Select hazard category" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {hazardCategoryEnum.enumValues.map((category) => (
                        <SelectItem key={category} value={category}>
                          {CATEGORY_MAP[category]}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>Examples: Chemical, Electrical, Ergonomic, Fall, Fire, Mechanical</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Severity Level */}
            <FormField
              control={form.control}
              name="severity"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>Severity Level</FormLabel>
                    {autoFilledFields.includes('severityLevel') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      setLastFieldInteracted('severity');
                    }}
                    value={field.value ?? ''}
                  >
                    <FormControl>
                      <SelectTrigger className={autoFilledFields.includes('severityLevel') ? 'border-indigo-300' : ''}>
                        <SelectValue placeholder="Select severity" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {severityEnum.enumValues.map((level) => (
                        <SelectItem key={level} value={level}>
                          {SEVERITY_MAP[level]}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    How serious was this safety event or how serious could it have been?
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>Description</FormLabel>
                    {autoFilledFields.includes('description') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <FormControl>
                    <Textarea
                      placeholder="Describe what happened in detail"
                      className={`min-h-[120px] ${autoFilledFields.includes('description') ? 'border-indigo-300' : ''}`}
                      {...field}
                      value={field.value ?? ''}
                      onFocus={() => setLastFieldInteracted('description')}
                    />
                  </FormControl>
                  <FormDescription>
                    Provide a detailed account of what happened, what led to the safety event, and any relevant
                    circumstances
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Immediate Actions */}
            <FormField
              control={form.control}
              name="immediateActions"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>Immediate Actions Taken</FormLabel>
                    {autoFilledFields.includes('immediateActions') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <FormControl>
                    <Textarea
                      placeholder="What actions were taken immediately after the safety event?"
                      className={`min-h-[80px] ${autoFilledFields.includes('immediateActions') ? 'border-indigo-300' : ''}`}
                      {...field}
                      value={field.value ?? ''}
                      onFocus={() => setLastFieldInteracted('immediateActions')}
                    />
                  </FormControl>
                  <FormDescription>
                    Describe any immediate steps taken to address the situation, treat injuries, or prevent further harm
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-4 pt-4">
              <Button variant="outline" type="button" onClick={onReset}>
                Reset
              </Button>

              <Button type="submit" disabled={isSubmitting || isUploading}>
                {isSubmitting || isUploading ? <Loader2 className="size-4 animate-spin" /> : null}
                {isUploading ? 'Processing Files...' : isSubmitting ? 'Submitting...' : 'Submit Report'}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
