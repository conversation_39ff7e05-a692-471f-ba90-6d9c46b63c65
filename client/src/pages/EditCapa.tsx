import { CapaTags } from '@/components/capas/details/tags';
import { EditCapaError } from '@/components/capas/edit/edit-capa-error';
import { EditCapaLoading } from '@/components/capas/edit/edit-capa-loading';
import { RootCauseSelector } from '@/components/capas/root-cause-selector';
import { AsyncAssetSelect } from '@/components/composite/async-asset-select';
import { AsyncEventsSelect } from '@/components/composite/async-events-select';
import { AsyncJhaMultiSelect } from '@/components/composite/async-jha-multi-select';
import { AsyncLocationSelect } from '@/components/composite/async-location-select';
import { AsyncUserMultiSelect } from '@/components/composite/async-user-multi-select';
import { AsyncUserSelect } from '@/components/composite/async-user-select';
import { AsyncWorkOrderMultiSelect } from '@/components/composite/async-work-order-multi-select';
import { Back } from '@/components/composite/back';
import { MediaUpload } from '@/components/media/media-upload';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DateTimePicker } from '@/components/ui/date-time-picker';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useFileAssociation } from '@/hooks/use-file-association';
import { usePermissions } from '@/hooks/use-permissions';
import { trpc } from '@/providers/trpc';
import { zodResolver } from '@hookform/resolvers/zod';
import { MAX_FILE_SIZE_MB } from '@shared/files-utils';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { capaEffectivenessStatusEnum, capaPriorityEnum, capaTypeEnum, rcaMethodEnum, statusEnum } from '@shared/schema';
import {
  CAPA_EFFECTIVENESS_STATUS_MAP,
  CAPA_PRIORITY_MAP,
  CAPA_TYPE_MAP,
  CapaValidations,
  EditCapasFormSchema,
  RCA_METHOD_MAP,
} from '@shared/types/capas.types';
import { TransientFileSchema } from '@shared/types/files.types';
import { STATUS_MAP } from '@shared/types/schema.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { addYears } from 'date-fns';
import { Info, Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { useLocation } from 'wouter';
import { z } from 'zod';

const FormSchema = EditCapasFormSchema.extend(CapaValidations);

type FormValues = z.infer<typeof FormSchema>;

export default function EditCapa({ params }: { params: { id: string } }) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [_, navigate] = useLocation();
  const utils = trpc.useUtils();

  const { hasPermission } = usePermissions();

  // Fetch the existing CAPA data
  const capaId = params.id;

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      actionsImplemented: '',
      actionsToAddress: '',
      assetId: null,
      attachments: [],
      dueDate: undefined,
      effectivenessStatus: 'not_effective',
      eventId: null,
      implementationDate: undefined,
      implementedBy: '',
      locationId: null,
      linkedJhaInstanceIds: [],
      otherRootCause: '',
      ownerId: '',
      priority: capaPriorityEnum.enumValues[0],
      privateToAdmins: false,
      rcaFindings: '',
      rcaMethod: rcaMethodEnum.enumValues[0],
      rootCauses: [],
      status: statusEnum.enumValues[0],
      tags: [],
      teamMembersToNotify: [],
      title: '',
      type: capaTypeEnum.enumValues[0],
      verificationFindings: '',
      voeDate: undefined,
      voePerformedBy: '',
      workOrderIds: [],
    },
    mode: 'onSubmit',
  });

  const {
    data: capa,
    isPending: isCapaLoading,
    error: capaError,
  } = trpc.capa.getByIdForEdit.useQuery({
    id: capaId,
  });

  useEffect(() => {
    if (capaError?.data?.code === 'FORBIDDEN') {
      navigate(ROUTES.NOT_FOUND);
    }
  }, [capaError]);

  const { mutateAsync: updateCapa } = trpc.capa.update.useMutation({
    onSuccess: () => {
      utils.auditTrail.get.invalidate({ entityType: 'capa', entityId: capaId });
      utils.capa.getById.invalidate({ id: capaId });
    },
    onError: (error) => {
      console.error(error);

      toast.success('Error updating CAPA', {
        description: 'There was a problem saving your changes.',
      });
    },
  });

  const { mutateAsync: removeFiles } = trpc.file.removeFiles.useMutation();
  const { associateFiles } = useFileAssociation();

  // Handle file removal - called when X button is clicked on existing files
  const onFileRemoval = async (file: z.infer<typeof TransientFileSchema>) => {
    try {
      if (!file.id) {
        return;
      }

      const fileId = file.id;

      await removeFiles([fileId]);

      const updatedAttachments = form.getValues('attachments')?.filter((file) => file.id !== fileId);

      form.setValue('attachments', updatedAttachments);

      toast.success('File removed', {
        description: 'The file has been successfully removed.',
      });
    } catch (error) {
      console.error('Error removing file:', error);
      toast.error('Error removing file', {
        description: 'There was a problem removing the file. Please try again.',
      });
    }
  };

  // Populate form with CAPA data when it loads
  useEffect(() => {
    if (capa && !isCapaLoading && !capaError) {
      // Set form values from CAPA data
      form.reset({
        id: capa.id,
        actionsImplemented: capa.actionsImplemented ?? undefined,
        actionsToAddress: capa.actionsToAddress ?? undefined,
        archivedAt: capa.archivedAt,
        attachments: capa.attachments || [],
        dueDate: capa.dueDate ? new Date(capa.dueDate) : undefined,
        effectivenessStatus: capa.effectivenessStatus ?? 'not_effective',
        eventId: capa.eventId || null,
        implementationDate: capa.implementationDate ? new Date(capa.implementationDate) : undefined,
        implementedBy: capa.implementedBy ?? undefined,
        locationId: capa.locationId || null,
        assetId: capa.assetId || null,
        otherRootCause: capa.rootCauses?.includes('other') ? (capa.otherRootCause ?? undefined) : undefined,
        ownerId: capa.ownerId ?? '',
        priority: capa.priority ?? 'medium',
        privateToAdmins: capa.privateToAdmins ?? false,
        rcaFindings: capa.rcaFindings || '',
        rcaMethod: capa.rcaMethod ?? '5_whys',
        rootCauses: capa.rootCauses || [],
        status: capa.status,
        tags: capa.tags ?? [],
        teamMembersToNotify: capa.teamMembersToNotify || [],
        title: capa.title,
        type: capa.type,
        verificationFindings: capa.verificationFindings || null,
        voeDate: capa.voeDate ? new Date(capa.voeDate) : undefined,
        voePerformedBy: capa.voePerformedBy ?? undefined,
        // Transform the CAPA-JHA relationship objects into a simple array of JHA instance IDs
        // for the form. The backend returns capaJhas as objects with { id, jhaInstanceId, jha: { slug, title } }
        // but the form expects just an array of strings (JHA instance IDs)
        linkedJhaInstanceIds:
          capa.capaJhas?.map((capaJha) => capaJha.jhaInstanceId).filter((id): id is string => Boolean(id)) ?? [],
        // Use work order IDs from the CAPA data
        workOrderIds: capa.workOrderIds || [],
      });
    }
  }, [capa, isCapaLoading, capaError]);

  // Handle form submission
  async function onSubmit(values: FormValues) {
    setIsSubmitting(true);

    const toUpdate = {};

    for (const field in form.formState.dirtyFields) {
      if (form.formState.dirtyFields[field as keyof typeof form.formState.dirtyFields]) {
        (toUpdate as Record<string, unknown>)[field] = values[field as keyof FormValues];
      }
    }

    try {
      // Only upload new files (files with file object)
      const newFiles = values.attachments?.filter((item) => item.file) || [];

      await updateCapa({
        ...toUpdate,
        id: capaId,
        attachments: undefined,
      });

      // Associate uploaded images with the updated CAPA
      if (newFiles.length) {
        await associateFiles(newFiles, capaId, 'capa');
      }

      // Files are uploaded on selection now; nothing to upload here.

      toast.success('CAPA updated', {
        description: 'Your changes have been saved successfully.',
      });

      // Navigate back to the CAPA details page
      navigate(ROUTES.BUILD_CAPA_DETAILS_PATH(capaId));
    } catch (error) {
      console.error('Error updating CAPA', error);
      toast.error('Error updating CAPA', {
        description: 'There was a problem saving your changes. Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  if (isCapaLoading) {
    return <EditCapaLoading />;
  }

  // If error, show error message
  if (capaError || !capa) {
    return <EditCapaError />;
  }

  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 md:px-8 lg:px-10 py-6">
      {/* Header section with back button and title */}
      <div className="flex items-center justify-between mb-8">
        <Back />
        <h1 className="text-2xl font-bold text-neutral-black">Edit #{capa.slug}</h1>
      </div>

      {/* Main layout container - full width form */}
      <div>
        <div>
          {capa && (
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
                {/* CAPA Summary Section */}
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold border-b pb-2">CAPA Summary</h2>

                  {/* Title */}
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Title <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="Brief, descriptive title" {...field} />
                        </FormControl>
                        <FormDescription>Provide a clear, concise title that describes the CAPA.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="flex flex-col md:flex-row md:items-start gap-2">
                    {/* CAPA Type */}
                    <FormField
                      control={form.control}
                      name="type"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>
                            CAPA Type <span className="text-red-500">*</span>
                          </FormLabel>
                          <FormControl>
                            <Select key={field.value} onValueChange={field.onChange} defaultValue={field.value}>
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="Select CAPA type" />
                              </SelectTrigger>
                              <SelectContent>
                                {capaTypeEnum.enumValues.map((type) => (
                                  <SelectItem key={type} value={type}>
                                    {CAPA_TYPE_MAP[type]}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormDescription>
                            Select whether this is a corrective action, preventive action, or both.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Status */}
                    <FormField
                      control={form.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>
                            Status <span className="text-red-500">*</span>
                          </FormLabel>
                          <FormControl>
                            <Select
                              key={field.value}
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                              disabled={!hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.CREATE)}
                            >
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="Select status" />
                              </SelectTrigger>
                              <SelectContent>
                                {statusEnum.enumValues.map((status) => (
                                  <SelectItem key={status} value={status}>
                                    {STATUS_MAP[status]}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormDescription>
                            Current status of this CAPA. Status changes are logged in the audit trail.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <div className="flex flex-col md:flex-row md:items-start gap-2">
                  {/* Location */}
                  <FormField
                    control={form.control}
                    name="locationId"
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormLabel>Location</FormLabel>
                        <FormControl>
                          <AsyncLocationSelect
                            placeholder="Where did it happen?"
                            {...field}
                            value={field.value}
                            onChange={(newValue) => {
                              field.onChange(newValue);
                              // clears assetId if the user is changing locationId, keep if they're clearing
                              if (newValue) {
                                form.setValue('assetId', null);
                              }
                            }}
                            mustIncludeObjectIds={capa.locationId ? [capa.locationId] : undefined}
                          />
                        </FormControl>
                        <FormDescription>
                          Specific area, building, or equipment where the CAPA will be implemented
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Assets */}
                  <FormField
                    control={form.control}
                    name="assetId"
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormLabel>Asset (Optional)</FormLabel>
                        <FormControl>
                          <AsyncAssetSelect
                            {...field}
                            placeholder="Select an asset"
                            value={field.value}
                            onChange={field.onChange}
                            locationId={form.watch('locationId') ?? undefined}
                          />
                        </FormControl>
                        <FormDescription>Specific equipment or asset related to this CAPA</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Action Details Section */}
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold border-b pb-2">Action Details</h2>

                  {/* Root Cause */}
                  <RootCauseSelector
                    control={form.control}
                    setValue={form.setValue}
                    rootCausesFieldName="rootCauses"
                    otherRootCauseFieldName="otherRootCause"
                  />

                  {/* Actions to Address */}
                  <FormField
                    control={form.control}
                    name="actionsToAddress"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Proposed Actions
                          <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Based on the RCA, what specific corrective and preventive actions are planned to eliminate the root cause and prevent recurrence?"
                            className="min-h-[150px]"
                            {...field}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormDescription>
                          List specific actions to take. Each line will be automatically formatted as a numbered item.
                          Include both immediate actions and long-term preventive measures
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* RCA Method */}
                  <FormField
                    control={form.control}
                    name="rcaMethod"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>RCA Method</FormLabel>
                        <Select key={field.value} onValueChange={field.onChange} defaultValue={field.value || '5_whys'}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select RCA method" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {rcaMethodEnum.enumValues.map((method) => (
                              <SelectItem key={method} value={method}>
                                {RCA_METHOD_MAP[method]}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>Method used for root cause analysis.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* RCA Findings */}
                  <FormField
                    control={form.control}
                    name="rcaFindings"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          RCA Findings
                          <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Document the findings from your root cause analysis"
                            className="min-h-[120px]"
                            {...field}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormDescription>Document the detailed findings from your root cause analysis.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="tags"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tags</FormLabel>
                        <FormControl>
                          <CapaTags {...field} />
                        </FormControl>
                        <FormDescription>
                          Add tags to categorize this CAPA (e.g., Training, Procedure, Equipment).
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Actual Actions Taken */}
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold border-b pb-2">Actual Actions Taken</h2>

                  {/* VoE Findings */}
                  <FormField
                    control={form.control}
                    name="actionsImplemented"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Actions Implemented</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Document the actions implemented"
                            className="min-h-[120px]"
                            {...field}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormDescription>Document the detailed actions implemented.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="flex flex-col md:flex-row md:items-start gap-2">
                    {/* Implemented By */}
                    <FormField
                      control={form.control}
                      name="implementedBy"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Implemented By</FormLabel>
                          <FormControl>
                            <AsyncUserSelect
                              placeholder="Select a person"
                              {...field}
                              value={field.value}
                              mustIncludeObjectIds={capa.implementedBy ? [capa.implementedBy] : undefined}
                            />
                          </FormControl>
                          <FormDescription>Person responsible for implementing this CAPA.</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* VoE Date */}
                    <FormField
                      control={form.control}
                      name="implementationDate"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Implementation Date</FormLabel>
                          <FormControl>
                            <DateTimePicker
                              selected={field.value ?? undefined}
                              onSelect={field.onChange}
                              onlyDate
                              placeholder="Select implementation date"
                              startMonth={new Date(capa.createdAt)}
                              endMonth={addYears(new Date(capa.createdAt), 5)}
                            />
                          </FormControl>
                          <FormDescription>Date this CAPA was implemented.</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* VoE Section */}
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold border-b pb-2">Verification of Effectiveness (VoE)</h2>

                  <div className="flex flex-col md:flex-row md:items-start gap-2">
                    {/* VoE Performed By */}
                    <FormField
                      control={form.control}
                      name="voePerformedBy"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>VoE Performed By</FormLabel>
                          <FormControl>
                            <AsyncUserSelect
                              placeholder="Select a person"
                              {...field}
                              value={field.value}
                              mustIncludeObjectIds={capa.voePerformedBy ? [capa.voePerformedBy] : undefined}
                            />
                          </FormControl>
                          <FormDescription>Person responsible for performing this VoE.</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* VoE Date */}
                    <FormField
                      control={form.control}
                      name="voeDate"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>VoE Date</FormLabel>
                          <FormControl>
                            <DateTimePicker
                              selected={field.value ?? undefined}
                              onSelect={field.onChange}
                              onlyDate
                              placeholder="Select VoE date"
                              startMonth={new Date(capa.createdAt)}
                              endMonth={addYears(new Date(capa.createdAt), 5)}
                            />
                          </FormControl>
                          <FormDescription>Date this VoE was completed.</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* VoE Findings */}
                  <FormField
                    control={form.control}
                    name="verificationFindings"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>VoE Findings</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Document the findings from your VoE"
                            className="min-h-[120px]"
                            {...field}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormDescription>Document the detailed findings from your VoE.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Effectiveness Status */}
                  <FormField
                    control={form.control}
                    name="effectivenessStatus"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Effectiveness Status</FormLabel>
                        <FormControl>
                          <Select
                            key={field.value}
                            onValueChange={field.onChange}
                            defaultValue={field.value ?? undefined}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select effectiveness status" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {capaEffectivenessStatusEnum.enumValues.map((status) => (
                                <SelectItem key={status} value={status}>
                                  {CAPA_EFFECTIVENESS_STATUS_MAP[status]}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormDescription>Status of this VoE.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Assignment Section */}
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold border-b pb-2">Assignment</h2>
                  <div className="flex flex-col md:flex-row md:items-start gap-2">
                    {/* Owner */}
                    <FormField
                      control={form.control}
                      name="ownerId"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>
                            Owner <span className="text-red-500">*</span>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger>
                                  <Info className="h-3 w-3" />
                                </TooltipTrigger>
                                <TooltipContent>
                                  <div className="font-bold mb-1">Can't find a team member?</div>
                                  <div>
                                    Only active users in your UpKeep EHS system can be assigned. <br /> To add new team
                                    members, please contact your UpKeep Administrator for assistance.
                                  </div>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </FormLabel>

                          <FormControl>
                            <AsyncUserSelect
                              placeholder="Select an owner"
                              {...field}
                              value={field.value}
                              mustIncludeObjectIds={capa.ownerId ? [capa.ownerId] : undefined}
                            />
                          </FormControl>
                          <FormDescription>Person responsible for implementing this CAPA.</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Due Date */}
                    <FormField
                      control={form.control}
                      name="dueDate"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Due Date</FormLabel>
                          <FormControl>
                            <DateTimePicker
                              selected={field.value ?? undefined}
                              onSelect={field.onChange}
                              disabled={{
                                before: new Date(),
                              }}
                              onlyDate
                              placeholder="Select due date"
                              startMonth={new Date()}
                              endMonth={addYears(new Date(), 5)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Priority */}
                  <FormField
                    control={form.control}
                    name="priority"
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormLabel>
                          Priority <span className="text-red-500">*</span>
                        </FormLabel>
                        <Select key={field.value} onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select priority" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {capaPriorityEnum.enumValues.map((priority) => (
                              <SelectItem key={priority} value={priority}>
                                {CAPA_PRIORITY_MAP[priority]}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>Set the priority level for this CAPA.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Linkage Section */}
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold border-b pb-2">Linkage</h2>

                  {/* Linked Safety Event */}
                  <FormField
                    control={form.control}
                    name="eventId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Link to Safety Event</FormLabel>
                        <div className="flex flex-col gap-3">
                          <AsyncEventsSelect
                            {...field}
                            onChange={field.onChange}
                            value={field.value}
                            placeholder="Search and select a safety event..."
                            mustIncludeObjectIds={capa.eventId ? [capa.eventId] : undefined}
                          />
                        </div>
                        <FormDescription>Connect this CAPA to a related safety event.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Linked Documents Section */}
                <Card className="border-gray-200 shadow-none">
                  <CardHeader>
                    <CardTitle className="text-md font-medium flex items-center gap-2">
                      <Info className="size-4" />
                      Linked Documents
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-3">
                    <FormField
                      control={form.control}
                      name="linkedJhaInstanceIds"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Job Hazard Analyses (JHAs)</FormLabel>
                          <FormControl>
                            <AsyncJhaMultiSelect
                              placeholder="Search and select relevant JHAs..."
                              value={field.value}
                              onChange={field.onChange}
                              disabled={false}
                            />
                          </FormControl>
                          <FormDescription>Associate relevant Job Hazard Analyses with this CAPA.</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="workOrderIds"
                      render={({ field }) => (
                        <FormItem className="mt-4">
                          <FormLabel>Work Orders</FormLabel>
                          <FormControl>
                            <AsyncWorkOrderMultiSelect
                              placeholder="Search and select relevant work orders..."
                              value={field.value}
                              onChange={field.onChange}
                            />
                          </FormControl>
                          <FormDescription>Associate relevant Work Orders with this CAPA.</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>

                {/* Attachments Section */}
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold border-b pb-2">Attachments</h2>

                  {/* File Uploads */}
                  <div>
                    <FormLabel>Upload Files</FormLabel>
                    <div className="mt-2">
                      <MediaUpload
                        maxFiles={5}
                        maxSize={MAX_FILE_SIZE_MB}
                        entityType={'capa'}
                        entityId={capaId}
                        className="bg-white"
                        files={form.watch('attachments') ?? []}
                        setFiles={(tFiles) => {
                          form.setValue('attachments', tFiles);
                        }}
                        onFileRemove={onFileRemoval}
                        onUploadStateChange={(state) => {
                          setIsUploading(state.isUploading);
                        }}
                      />
                    </div>
                  </div>
                </div>

                {/* Team Notifications Section */}
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold border-b pb-2">Team Notifications</h2>

                  {/* Private to Admins Toggle */}
                  {/* TODO: REMOVED FROM V1 */}
                  {/* <FormField
                    control={form.control}
                    name="privateToAdmins"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Private to Admins Only</FormLabel>
                          <FormDescription>When enabled, only administrators can view this CAPA.</FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value || false} onCheckedChange={field.onChange} />
                        </FormControl>
                      </FormItem>
                    )}
                  /> */}

                  {/* Team Members to Notify */}
                  <FormField
                    control={form.control}
                    name="teamMembersToNotify"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Team Members to Notify
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger>
                                <Info className="h-3 w-3" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <div className="font-bold mb-1">Can't find a team member?</div>
                                <div>
                                  Only active users in your UpKeep EHS system can be notified. <br /> To add new team
                                  members, please contact your UpKeep Administrator for assistance.
                                </div>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </FormLabel>
                        <FormControl>
                          <AsyncUserMultiSelect
                            placeholder="Select team members to notify"
                            {...field}
                            value={field.value}
                            mustIncludeObjectIds={
                              capa.teamMembersToNotify && capa.teamMembersToNotify.length > 0
                                ? capa.teamMembersToNotify
                                : undefined
                            }
                          />
                        </FormControl>
                        <FormDescription>
                          Select team members who should be notified about this CAPA update.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Submit and Cancel Buttons */}
                <div className="flex justify-end gap-3 pt-6 border-t border-neutral-700">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate(ROUTES.BUILD_CAPA_DETAILS_PATH(capaId))}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isSubmitting || isUploading || !form.formState.isDirty}>
                    {isSubmitting || isUploading ? <Loader2 className="size-4 animate-spin" /> : null}
                    {isUploading ? 'Processing Files...' : isSubmitting ? 'Saving...' : 'Save Changes'}
                  </Button>
                </div>
              </form>
            </Form>
          )}
        </div>
      </div>
    </div>
  );
}
