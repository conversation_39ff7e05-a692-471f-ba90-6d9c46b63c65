import { UpsertHazardModal } from '@/components/hazards/upsert-hazard-modal';
import { HazardsEmpty } from '@/components/hazards/hazards-empty';
import { HazardsError } from '@/components/hazards/hazards-error';
import { Filters } from '@/components/hazards/hazards-filters';
import { HazardsLoading } from '@/components/hazards/hazards-loading';
import { MobileFilters } from '@/components/hazards/hazards-mobile-filters';
import { HazardsMobileView } from '@/components/hazards/hazards-mobile-view';
import { HazardsTable } from '@/components/hazards/hazards-table';
import { SeedHazardsModal } from '@/components/hazards/seed-hazards-modal';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { useIsMobile } from '@/hooks/use-mobile';
import { useInfiniteHazards } from '@/hooks/use-paginated-data';
import { usePermissions } from '@/hooks/use-permissions';
import { useHazardsUrlFilters } from '@/hooks/use-url-filters';
import { exportHazardsCSV } from '@/lib/export-hazards';
import { trpc } from '@/providers/trpc';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { FileDown, Search, Sprout, X } from 'lucide-react';
import { AnimatePresence, motion } from 'motion/react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { useLocation } from 'wouter';
import { useLocalStorage } from '@uidotdev/usehooks';

export default function HazardsLog() {
  const [_, navigate] = useLocation();
  const isMobile = useIsMobile();
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [isSeedModalOpen, setIsSeedModalOpen] = useState<boolean>(false);

  const [location] = useLocation();
  const { hasPermission } = usePermissions();

  // Track if user has seen the seed button highlight
  const [hasSeenSeedHighlight, setHasSeenSeedHighlight] = useLocalStorage('ehs/hazards-seed-highlight-seen', false);
  const [showSeedHighlight, setShowSeedHighlight] = useState(false);

  const { filters, immediateFilters, updateFilter, resetFilters, activeFilterCount } = useHazardsUrlFilters();

  const {
    data: hazards,
    isLoading,
    error,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
  } = useInfiniteHazards({ filters });

  const { mutateAsync: exportHazards, isPending: isExporting } = trpc.hazards.export.useMutation();

  useEffect(() => {
    if (error?.data?.code === 'FORBIDDEN') {
      navigate(ROUTES.NOT_FOUND);
    }
  }, [error]);

  // Show seed button highlight on first visit
  useEffect(() => {
    if (!hasSeenSeedHighlight && hazards && !isLoading) {
      const timer = setTimeout(() => {
        setShowSeedHighlight(true);
      }, 1000);
      return () => clearTimeout(timer);
    } else {
      setShowSeedHighlight(false);
    }
  }, [hasSeenSeedHighlight, hazards, isLoading]);

  // Check for create query parameter and open modal
  useEffect(() => {
    if (location === ROUTES.HAZARDS_NEW) {
      setIsModalOpen(true);
    } else {
      // Close modal if navigating away from the new route
      setIsModalOpen(false);
    }
  }, [location]);

  const handleExport = async () => {
    try {
      toast.info('Exporting hazards...');
      const hazards = await exportHazards(filters);

      exportHazardsCSV(hazards);
      toast.success('Hazards exported successfully');
    } catch (error) {
      console.error(error);
      toast.error('Error exporting hazards');
    }
  };

  const handleSeedButtonClick = () => {
    setIsSeedModalOpen(true);
    // Mark as seen and stop the highlight animation
    if (showSeedHighlight) {
      setShowSeedHighlight(false);
      setHasSeenSeedHighlight(true);
    }
  };

  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <div className="flex flex-col md:flex-row justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Hazards</h1>
        </div>

        <div className="flex flex-col-reverse md:flex-row items-center gap-2 w-full md:w-auto">
          <div className="relative flex-1 md:w-64 w-full">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search hazards..."
              className="pl-8 pr-4"
              value={immediateFilters.search ?? ''}
              onChange={(e) => updateFilter('search', e.target.value)}
            />
            {immediateFilters.search && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0 "
                onClick={() => updateFilter('search', '')}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          <div className="flex items-center gap-2 w-full md:w-auto">
            {hasPermission(MODULES.HAZARDS, ALLOWED_ACTIONS.CREATE) && (
              <Button
                onClick={() => {
                  setIsModalOpen(true);
                }}
                className="flex-1"
              >
                + Create Hazard
              </Button>
            )}
            {hasPermission(MODULES.HAZARDS, ALLOWED_ACTIONS.CREATE) && (
              <Tooltip delayDuration={500}>
                <TooltipTrigger asChild>
                  <motion.div
                    className="relative"
                    animate={showSeedHighlight ? { scale: [1, 1.02, 1] } : {}}
                    transition={{
                      duration: 2,
                      repeat: showSeedHighlight ? Infinity : 0,
                      ease: 'easeInOut',
                    }}
                  >
                    <AnimatePresence>
                      {showSeedHighlight && (
                        <motion.div
                          className="absolute -inset-1 rounded-md bg-blue-400/20"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: [0.2, 0.4, 0.2] }}
                          exit={{ opacity: 0 }}
                          transition={{
                            duration: 2,
                            repeat: Infinity,
                            ease: 'easeInOut',
                          }}
                        />
                      )}
                    </AnimatePresence>

                    <Button
                      variant="outline"
                      size="icon"
                      onClick={handleSeedButtonClick}
                      className={`relative z-10 transition-all duration-300 ${
                        showSeedHighlight ? 'bg-blue-50 border-blue-300 hover:bg-blue-100' : ''
                      }`}
                    >
                      <motion.div
                        animate={showSeedHighlight ? { rotate: [0, 15, -15, 10, -5, 0] } : {}}
                        transition={{
                          duration: 1.5,
                          repeat: showSeedHighlight ? Infinity : 0,
                          ease: 'linear',
                          times: [0, 0.2, 0.4, 0.6, 0.8, 1],
                        }}
                      >
                        <Sprout
                          className={`h-4 w-4 transition-colors duration-300 ${
                            showSeedHighlight ? 'text-blue-600' : ''
                          }`}
                        />
                      </motion.div>
                    </Button>
                  </motion.div>
                </TooltipTrigger>
                <TooltipContent align="end">Seed default hazards</TooltipContent>
              </Tooltip>
            )}
            {hasPermission(MODULES.HAZARDS, ALLOWED_ACTIONS.EXPORT) && (
              <Tooltip delayDuration={500}>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" disabled={isExporting} onClick={handleExport}>
                    <FileDown className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent align="end">
                  Export hazards (maximum 500 records). <br />
                  Only the first 500 records matching your <br /> current filters will be included in the export.
                </TooltipContent>
              </Tooltip>
            )}
            {/* Mobile filters */}
            <MobileFilters
              activeFilterCount={activeFilterCount}
              filters={immediateFilters}
              updateFilter={updateFilter}
              resetFilters={resetFilters}
            />
          </div>
        </div>
      </div>

      {/* Desktop Filters bar */}
      <Filters
        filters={immediateFilters}
        updateFilter={updateFilter}
        activeFilterCount={activeFilterCount}
        resetFilters={resetFilters}
      />

      {/* Create Hazard Modal */}
      {hasPermission(MODULES.HAZARDS, ALLOWED_ACTIONS.CREATE) && (
        <UpsertHazardModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} />
      )}

      {/* Seed Hazards Modal */}
      {hasPermission(MODULES.HAZARDS, ALLOWED_ACTIONS.CREATE) && (
        <SeedHazardsModal isOpen={isSeedModalOpen} onClose={() => setIsSeedModalOpen(false)} />
      )}

      {/* Error state */}
      {error && <HazardsError />}

      {/* Loading state */}
      {isLoading && <HazardsLoading />}

      {/* Empty state - when no hazards are found and not loading */}
      {hazards && hazards.length === 0 && !isLoading && !error && (
        <HazardsEmpty
          hasActiveFilters={activeFilterCount > 0}
          onResetFilters={resetFilters}
          onCreateHazard={() => {
            setIsModalOpen(true);
          }}
          onSeedHazard={handleSeedButtonClick}
        />
      )}

      {/* Desktop Table View */}
      {!isMobile && hazards && hazards.length > 0 && <HazardsTable hazards={hazards} />}

      {/* Mobile Card View */}
      {isMobile && hazards && hazards.length > 0 && <HazardsMobileView hazards={hazards} />}

      {/* Load More Button for Infinite Scrolling */}
      {hazards && hazards.length > 0 && hasNextPage && (
        <div className="flex justify-center mt-6">
          <Button onClick={() => fetchNextPage()} disabled={isFetchingNextPage} variant="outline" className="px-6">
            {isFetchingNextPage ? 'Loading more...' : `Load More (${hazards.length} loaded)`}
          </Button>
        </div>
      )}
    </div>
  );
}
