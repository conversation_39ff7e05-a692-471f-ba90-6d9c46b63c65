import { StepFooter } from '@/components/sop/step-footer';
import { StepIndicator } from '@/components/sop/step-indicator';
import { StepProgress } from '@/components/sop/step-progress';
import { Scoped } from '@/components/sop/stepper';
import { SopSteps } from '@/components/sop/steps';
import { EditSopError } from '@/components/sop/upsert/edit-sop-error';
import { EditSopLoading } from '@/components/sop/upsert/edit-sop-loading';
import { SopReasonForRevision } from '@/components/sop/upsert/sop-reason-for-revision';
import { Form } from '@/components/ui/form';
import { usePermissions } from '@/hooks/use-permissions';
import { trpc } from '@/providers/trpc';
import { zodResolver } from '@hookform/resolvers/zod';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { approvalStatusEnum } from '@shared/schema';
import { UpdateSopFormSchema, UpdateSopType } from '@shared/types/sop.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { ClipboardList } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { Redirect, useLocation } from 'wouter';

const FormSchema = UpdateSopFormSchema;
type FormType = UpdateSopType;

export default function EditSop({ params }: { params: { id: string } }) {
  const sopInstanceId = params.id;
  const [_, navigate] = useLocation();

  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const [showReasonForRevisionModal, setShowReasonForRevisionModal] = useState<boolean>(false);
  const [reasonForRevision, setReasonForRevision] = useState<string>('');

  const utils = trpc.useUtils();

  const mode = 'edit';

  const {
    data: sop,
    isLoading,
    error: sopError,
  } = trpc.sop.getByInstanceIdForEdit.useQuery({
    id: sopInstanceId,
  });

  const isInitiatingRevision = useMemo(() => sop?.status === approvalStatusEnum.enumValues[2], [sop?.status]);

  const form = useForm<FormType>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      sop: {
        title: undefined,
        ownerId: undefined,
        approverId: undefined,
        reviewDate: undefined,
        locationId: undefined,
        notes: '',
        isPublic: false,
      },
      sections: [
        {
          sectionType: 'step',
          serial: 1,
          label: '',
          value: '',
          hazardIds: [],
          controlMeasureIds: [],
          hazardsToCreate: [],
          controlMeasuresToCreate: [],
          severity: 1,
          likelihood: 1,
        },
      ],
    },
    mode: 'onSubmit',
  });

  useEffect(() => {
    if (sop) {
      form.reset({
        sop: {
          ...sop,
        },
        sections: sop.sections.map((section) => ({
          id: section.id,
          serial: section.serial,
          sectionType: section.sectionType as
            | 'general'
            | 'emergency'
            | 'step'
            | 'pre_procedure'
            | 'procedure'
            | 'post_procedure',
          label: section.label,
          value: section.value,
          hazardIds: section.hazardIds,
          controlMeasureIds: section.controlMeasureIds,
          hazardsToCreate: [],
          controlMeasuresToCreate: [],
          severity: section.severity,
          likelihood: 1, // Default likelihood since it might not exist in older data
        })),
      });
    }
  }, [sop]);

  const { mutate: createSop } = trpc.sop.create.useMutation({
    onSuccess: () => {
      utils.sop.invalidate?.();
      toast.success('SOP updated successfully', {
        description: 'Your Standard Operating Procedure has been updated and is ready for review.',
      });
    },
    onError: (error) => {
      console.error('Error updating SOP', error);
      toast.error('Error updating SOP', {
        description: 'There was a problem updating your SOP. Please try again.',
      });
    },
  });

  const { mutateAsync: updateSop } = trpc.sop.update.useMutation({
    onSuccess: () => {
      utils.sop.invalidate?.();
      toast.success('SOP updated successfully', {
        description: 'Your Standard Operating Procedure has been updated and is ready for review.',
      });
    },
    onError: (error) => {
      console.error('Error updating SOP', error);
      toast.error('Error updating SOP', {
        description: 'There was a problem updating your SOP. Please try again.',
      });
    },
  });

  const onSubmit = async (values: FormType) => {
    if (isInitiatingRevision && !reasonForRevision) {
      toast.error('Error updating SOP', {
        description: 'Please add a reason for revision.',
      });
      setShowReasonForRevisionModal(true);
      return;
    }

    if (!isInitiatingRevision && !sop?.id) {
      toast.error('Error updating SOP', {
        description: 'There was a problem updating your SOP. Please try again.',
      });
      return;
    }

    const mutation = isInitiatingRevision ? createSop : updateSop;

    try {
      await mutation({
        sop: {
          ...values.sop,
          instanceId: sop?.instanceId,
          ...(isInitiatingRevision && reasonForRevision && { reasonForRevision }),
          status: isInitiatingRevision ? approvalStatusEnum.enumValues[0] : sop?.status,
        },
        sections: values.sections.map((section) => ({
          ...section,
          serial: section.serial || 1, // Ensure serial is always a number
        })),
      });

      // Reset form and navigate to SOP details
      form.reset();
      navigate(ROUTES.BUILD_SOP_DETAILS_PATH(sop?.instanceId!));
    } catch (error) {
      console.error('Error submitting SOP', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.log('SOP form validation failed:', {
        validation_errors: [errorMessage],
        error_message: errorMessage,
      });
      toast.error('Error updating SOP', {
        description: 'There was a problem updating your SOP. Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const { hasPermission: checkPermission } = usePermissions();

  if (!checkPermission(MODULES.SOP, ALLOWED_ACTIONS.EDIT)) {
    return <Redirect to={ROUTES.SOP_LIST} />;
  }

  if (isLoading) {
    return <EditSopLoading />;
  }

  if (sopError || !sop) {
    return <EditSopError error={sopError} />;
  }

  return (
    <>
      <SopReasonForRevision
        isOpen={showReasonForRevisionModal}
        onClose={() => setShowReasonForRevisionModal(false)}
        onSave={(reason) => {
          setReasonForRevision(reason);
          setShowReasonForRevisionModal(false);
        }}
        isLoading={isSubmitting}
      />

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <Scoped>
            <div className="max-w-6xl mx-auto px-4 sm:px-6 md:px-8 lg:px-10 py-6">
              <div className="space-y-8">
                <div className="flex items-center gap-5">
                  <div className="flex flex-col">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <ClipboardList className="w-6 h-6 text-blue-600" />
                      </div>
                      <h1 className="text-2xl font-bold text-gray-900">Edit SOP</h1>
                    </div>
                    <StepIndicator />
                  </div>
                </div>

                <StepProgress />
                <SopSteps mode={mode} />
                <StepFooter
                  isSubmitting={isSubmitting}
                  mode={mode}
                  isInitiatingRevision={isInitiatingRevision}
                  setShowReasonForRevisionModal={setShowReasonForRevisionModal}
                  reasonForRevision={reasonForRevision}
                />
              </div>
            </div>
          </Scoped>
        </form>
      </Form>
    </>
  );
}
