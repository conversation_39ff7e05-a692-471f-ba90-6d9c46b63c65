import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { SopEmpty } from '@/components/sop/list/sop-empty';
import { SopError } from '@/components/sop/list/sop-error';
import { Filters } from '@/components/sop/list/sop-filters';
import { SopLoading } from '@/components/sop/list/sop-loading';
import { MobileFilters } from '@/components/sop/list/sop-mobile-filters';
import { SopMobileView } from '@/components/sop/list/sop-mobile-view';
import { SopTable } from '@/components/sop/list/sop-table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAnalytics } from '@/hooks/use-analytics';
import { useIsMobile } from '@/hooks/use-mobile';
import { useInfiniteSops } from '@/hooks/use-paginated-data';
import { usePermissions } from '@/hooks/use-permissions';
import { useSopUrlFilters } from '@/hooks/use-url-filters';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { approvalStatusEnum } from '@shared/schema';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { Search, X } from 'lucide-react';
import { useCallback, useEffect } from 'react';
import { useLocation } from 'wouter';

export const SopLog = () => {
  const [_, navigate] = useLocation();
  const isMobile = useIsMobile();
  const { hasPermission } = usePermissions();
  const analytics = useAnalytics();

  const { filters, immediateFilters, updateFilter, updateFilters, resetFilters, activeFilterCount } =
    useSopUrlFilters();

  const {
    data: sops,
    isLoading,
    error,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
    isFetchedAfterMount,
  } = useInfiniteSops({
    filters,
    enabled: true,
  });

  useEffect(() => {
    if (error?.data?.code === 'FORBIDDEN') {
      navigate(ROUTES.NOT_FOUND);
    }
  }, [error]);

  // Track page view on component mount (only once)
  useEffect(() => {
    if (isFetchedAfterMount) {
      analytics.track(ANALYTICS_EVENTS.SOP.LOG_VIEW_OPENED, {
        default_sort_by: 'createdAt',
        default_sort_order: 'desc',
      });
    }
  }, [isFetchedAfterMount]);

  // Track search when debounced search term changes
  useEffect(() => {
    if (filters.search?.trim() && sops) {
      analytics.track(ANALYTICS_EVENTS.SOP.SEARCH_PERFORMED, {
        search_term: filters.search,
        result_count: sops.length,
      });
    }
  }, [filters.search, sops]);

  const toggleStatus = useCallback(
    (status: (typeof approvalStatusEnum.enumValues)[number]) => {
      const currentFilters = [...(immediateFilters.status ?? [])];
      const index = currentFilters.indexOf(status);

      let newFilters;
      if (index > -1) {
        currentFilters.splice(index, 1);
        newFilters = currentFilters;
      } else {
        currentFilters.push(status);
        newFilters = currentFilters;
      }

      // Track filter applied
      analytics.track(ANALYTICS_EVENTS.SOP.FILTER_APPLIED, {
        filter_name: 'status',
        filter_value: status,
        include_archived_toggle_state: filters.includeArchived,
      });

      updateFilter('status', newFilters);
    },
    [immediateFilters, updateFilter, analytics, filters.includeArchived],
  );

  const trackFilterApplied = (
    type: 'status' | 'ownerId' | 'reviewStatus' | 'locationIds' | 'includeArchived',
    value: string,
  ) => {
    analytics.track(ANALYTICS_EVENTS.SOP.FILTER_APPLIED, {
      filter_name: type,
      filter_value: value,
      include_archived_toggle_state: filters.includeArchived,
    });
  };

  // Enhanced reset filters that also clears search
  const handleResetFilters = () => {
    // Track filter reset
    analytics.track(ANALYTICS_EVENTS.SOP.FILTER_RESET, {});
    resetFilters();
  };

  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <div className="flex flex-col gap-4 justify-between items-start lg:flex-row ">
        <div className="mb-4 md:mb-0">
          <h1 className="text-2xl font-bold md:mb-0">Standard Operating Procedures</h1>
          <p className="text-muted-foreground text-sm mt-1">
            Create and manage standard operating procedures to ensure safety and consistency
          </p>
        </div>
        <div className="w-full flex flex-col-reverse items-start pb-4 md:pb-0 gap-4 md:flex-row md:gap-2 md:w-auto">
          <div className="relative flex-1 md:w-64 w-full">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search SOPs..."
              className="pl-8 pr-4"
              value={immediateFilters.search ?? ''}
              onChange={(e) => updateFilter('search', e.target.value)}
            />
            {immediateFilters.search && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0"
                onClick={() => updateFilter('search', '')}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          <div className="flex items-center gap-2 w-full md:w-auto">
            {hasPermission(MODULES.SOP, ALLOWED_ACTIONS.CREATE) && (
              <Button
                onClick={() => {
                  analytics.track(ANALYTICS_EVENTS.SOP.FORM_VIEWED, {
                    source: 'sop_log_row_action',
                  });
                  navigate(ROUTES.SOP_NEW);
                }}
                className="flex-1"
              >
                + Create SOP
              </Button>
            )}
            <MobileFilters
              toggleStatus={toggleStatus}
              filters={immediateFilters}
              updateFilters={updateFilters}
              activeFilterCount={activeFilterCount}
              resetFilters={handleResetFilters}
              trackFilterApplied={trackFilterApplied}
            />
          </div>
        </div>
      </div>

      {/* Filters bar */}
      <Filters
        toggleStatus={toggleStatus}
        filters={immediateFilters}
        updateFilters={updateFilters}
        activeFilterCount={activeFilterCount}
        resetFilters={handleResetFilters}
        trackFilterApplied={trackFilterApplied}
      />

      {/* Error state */}
      {error ? <SopError /> : null}

      {/* Loading state */}
      {isLoading ? <SopLoading /> : null}

      {/* Empty state - when no SOPs are found and not loading */}
      {sops && sops.length === 0 && !isLoading && !error && (
        <SopEmpty hasActiveFilters={activeFilterCount > 0} onResetFilters={handleResetFilters} />
      )}

      {/* Desktop Table View */}
      {!isMobile && sops && sops.length > 0 ? <SopTable sops={sops} /> : null}

      {/* Mobile Card View */}
      {isMobile && sops && sops.length > 0 ? <SopMobileView sops={sops} /> : null}

      {/* Load More Button for Infinite Scrolling */}
      {sops && sops.length > 0 && hasNextPage && (
        <div className="flex justify-center mt-6">
          <Button onClick={() => fetchNextPage()} disabled={isFetchingNextPage} variant="outline" className="px-6">
            {isFetchingNextPage ? 'Loading more...' : `Load More (${sops.length} loaded)`}
          </Button>
        </div>
      )}
    </div>
  );
};
