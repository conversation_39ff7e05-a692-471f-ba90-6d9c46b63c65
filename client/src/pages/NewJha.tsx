import { AiCompose } from '@/components/composite/ai-compose';
import { AsyncAssetMultiSelect } from '@/components/composite/async-asset-multi-select';
import { AsyncLocationSelect } from '@/components/composite/async-location-select';
import { AsyncUserSelect } from '@/components/composite/async-user-select';
import { VoiceInputRef } from '@/components/composite/voice-input';
import { AiDisclaimerModal } from '@/components/composite/ai-disclaimer-modal';
import { JhaSteps } from '@/components/jha/steps';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { DateTimePicker } from '@/components/ui/date-time-picker';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { usePermissions } from '@/hooks/use-permissions';
import { trpc } from '@/providers/trpc';
import { zodResolver } from '@hookform/resolvers/zod';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { approvalStatusEnum } from '@shared/schema';
import { CreateJhaFormSchema, CreateJhaType } from '@shared/types/jha.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { addYears } from 'date-fns';
import { InfoIcon, Save } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { Redirect, useLocation } from 'wouter';

const FormSchema = CreateJhaFormSchema;

type FormType = CreateJhaType;

export default function NewJha() {
  const [_, navigate] = useLocation();

  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [status, setStatus] = useState<(typeof approvalStatusEnum.enumValues)[number]>(
    approvalStatusEnum.enumValues[0],
  );
  const [isAiGenerated, setIsAiGenerated] = useState<boolean>(false);
  const [disclaimerAccepted, setDisclaimerAccepted] = useState<boolean>(false);
  const [showDisclaimerModal, setShowDisclaimerModal] = useState<boolean>(false);

  const utils = trpc.useUtils();

  const voiceInputRef = useRef<VoiceInputRef>(null);

  const form = useForm<FormType>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      jha: {
        title: undefined,
        ownerId: undefined,
        approverId: undefined,
        reviewDate: undefined,
        locationId: undefined,
        assetIds: undefined,
        notes: '',
        isPublic: false,
        status: approvalStatusEnum.enumValues[0],
      },
      steps: [
        {
          serial: 1,
          title: '',
          hazardIds: [],
          controlMeasureIds: [],
          severity: 1,
          likelihood: 1,
          description: '',
        },
      ],
    },
    mode: 'onSubmit',
  });

  const onAnalysisComplete = (jhaData: CreateJhaType) => {
    form.reset(jhaData);
    setIsAiGenerated(true);
    setDisclaimerAccepted(false);
  };

  const { mutateAsync: createJha } = trpc.jha.create.useMutation({
    onSuccess: () => {
      utils.jha.invalidate?.(); // Invalidate JHA queries if they exist
    },
    onError: (error) => {
      console.error('Error creating JHA', error);
      toast.error('Error creating JHA', {
        description: 'There was a problem creating your JHA. Please try again.',
      });
    },
  });

  useEffect(() => {
    if (form.watch('jha.locationId')) {
      form.setValue('jha.assetIds', []);
      utils.asset.search.invalidate();
    }
  }, [form.watch('jha.locationId'), form, utils.asset.search]);

  const onSubmit = async (values: FormType) => {
    // If AI generated content and disclaimer not accepted, show disclaimer modal
    if (isAiGenerated && !disclaimerAccepted) {
      setShowDisclaimerModal(true);
      return;
    }

    // Otherwise, proceed with submission
    setIsSubmitting(true);

    try {
      const submissionData = {
        ...values,
        jha: { ...values.jha, status },
        isAiGenerated,
        disclaimerAccepted: isAiGenerated ? disclaimerAccepted : undefined,
      };

      const createdJha = await createJha(submissionData);

      // Track JHA creation
      console.log('JHA created:', {
        jha_id: createdJha?.id,
        steps_count: values.steps.length,
        location: values.jha.locationId || '',
        assets_count: values.jha.assetIds?.length || 0,
      });

      toast.success('JHA created successfully', {
        description: 'Your Job Hazard Analysis has been created and is ready for review.',
      });

      form.reset();
      setIsAiGenerated(false);
      setDisclaimerAccepted(false);

      if (createdJha?.instanceId) {
        navigate(ROUTES.BUILD_JHA_DETAILS_PATH(createdJha?.instanceId));
      } else {
        navigate(ROUTES.JHA_LIST);
      }
    } catch (error) {
      console.error('Error submitting JHA', error);
      // Track form validation failed
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.log('JHA form validation failed:', {
        validation_errors: [errorMessage],
        error_message: errorMessage,
      });
      toast.error('Error creating JHA', {
        description: 'There was a problem creating your JHA. Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDisclaimerAccept = () => {
    setDisclaimerAccepted(true);
    setShowDisclaimerModal(false);
  };

  const handleDisclaimerCancel = () => {
    setShowDisclaimerModal(false);
  };

  const { hasPermission: checkPermission } = usePermissions();

  if (!checkPermission(MODULES.EHS_JHA, ALLOWED_ACTIONS.CREATE)) {
    return <Redirect to={ROUTES.JHA_LIST} />;
  }

  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 md:px-8 lg:px-10 py-6">
      <div>
        <div className="flex items-center gap-5">
          <div className="flex flex-col">
            <h1 className="text-2xl font-bold text-primary-600">Create Job Hazard Analysis</h1>
            <p className="text-muted-foreground text-base sm:text-lg">
              Create a comprehensive job hazard analysis to identify workplace hazards and establish control measures.
            </p>
          </div>
        </div>

        <div className="border-b mb-4">
          <AiCompose
            methods={['voice', 'document']}
            entity="jha"
            onVoiceComplete={onAnalysisComplete}
            onDocumentComplete={onAnalysisComplete}
            voiceInputRef={voiceInputRef}
          />
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            {/* Basic Information */}
            <div className="space-y-6">
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Basic Information</h2>
                <p className="text-sm text-gray-600 mt-1">Provide the basic details about this Job Hazard Analysis.</p>
              </div>

              {/* JHA Title - Full width */}
              <FormField
                control={form.control}
                name="jha.title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      JHA Title <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="Enter the job or task title" {...field} value={field.value || ''} />
                    </FormControl>
                    <FormDescription>
                      A clear, descriptive title for the job or task being analyzed (e.g., "Operating Forklift in
                      Warehouse")
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Owner and Approver - Responsive grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="jha.ownerId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Owner <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <AsyncUserSelect
                          {...field}
                          placeholder="Select JHA owner"
                          value={field.value}
                          onChange={field.onChange}
                          excludeViewOnly={true}
                        />
                      </FormControl>
                      <FormDescription>The person responsible for creating and maintaining this JHA</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="jha.approverId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Approver <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <AsyncUserSelect
                          placeholder="Select JHA approver"
                          value={field.value}
                          onChange={field.onChange}
                          excludeViewOnly={true}
                        />
                      </FormControl>
                      <FormDescription>The supervisor or manager who will approve this JHA</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Location, Assets, and Review Date - Responsive grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 items-start">
                <FormField
                  control={form.control}
                  name="jha.locationId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Location</FormLabel>
                      <FormControl>
                        <AsyncLocationSelect
                          value={field.value}
                          onChange={field.onChange}
                          placeholder="Where is this job performed?"
                        />
                      </FormControl>
                      <FormDescription>
                        Specific area, building, or department where the job is performed
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="jha.assetIds"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Equipment/Assets</FormLabel>
                      <FormControl>
                        <AsyncAssetMultiSelect
                          {...field}
                          value={field.value || []}
                          onChange={field.onChange}
                          placeholder="Select equipment used"
                          locationId={form.watch('jha.locationId') ?? undefined}
                        />
                      </FormControl>
                      <FormDescription>Equipment, tools, or assets involved in this job</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="jha.reviewDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Next Review Date</FormLabel>
                      <FormControl>
                        <DateTimePicker
                          selected={field.value || undefined}
                          onSelect={field.onChange}
                          disabled={{ before: new Date() }}
                          onlyDate
                          startMonth={new Date()}
                          endMonth={addYears(new Date(), 5)}
                        />
                      </FormControl>
                      <FormDescription>When should this JHA be reviewed next?</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Task Steps & Risk Assessment */}
            <div>
              <div className="pb-4">
                <h2 className="text-xl font-semibold text-gray-900">Task Steps & Risk Assessment</h2>
                <p className="text-sm text-gray-600 mt-1">
                  List each step of the job in the order it's performed. For each step, identify the hazard, estimate
                  its severity and likelihood, then define the control that reduces risk. Drag steps to re-order. Risk
                  Score updates automatically.
                </p>
              </div>

              <JhaSteps defaultOpen />
            </div>

            {/* Additional Notes */}
            <div className="space-y-6">
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Additional Notes</h2>
                <p className="text-sm text-gray-600 mt-1">
                  Add any additional context, requirements, or information about this job hazard analysis.
                </p>
              </div>

              <FormField
                control={form.control}
                name="jha.notes"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Textarea
                        placeholder="Any additional information about this JHA"
                        className="min-h-[80px]"
                        {...field}
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="flex justify-between items-center border-t pt-8">
              <div>
                <FormField
                  control={form.control}
                  name="jha.isPublic"
                  render={({ field }) => (
                    <FormItem className="flex items-center gap-2">
                      <FormControl>
                        <div className="flex items-center gap-2">
                          <Checkbox name={field.name} checked={field.value ?? false} onCheckedChange={field.onChange} />
                          <Label htmlFor={field.name}>
                            Make Public
                            <Tooltip>
                              <TooltipTrigger>
                                <InfoIcon className="w-4 h-4" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <strong>Public Visibility</strong> <br /> Checking this box will make this JHA
                                accessible in a read-only format to <br /> all active users within your organization,
                                including those with 'View-Only' access. <br /> This allows broader access to general
                                safety procedures.
                                <br />
                                <br />
                                <strong>Note:</strong> This will not affect the visibility of the JHA to the owner or
                                approver.
                              </TooltipContent>
                            </Tooltip>
                          </Label>
                        </div>
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
              <div className="flex justify-end space-x-4">
                <Button
                  variant="outline"
                  type="button"
                  onClick={() => {
                    form.reset();
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                  }}
                >
                  Reset
                </Button>

                {isAiGenerated && !disclaimerAccepted ? (
                  <>
                    <Button type="submit" disabled={isSubmitting} variant="outline">
                      <Save className="h-4 w-4" />
                      Accept Disclaimer & Save Draft
                    </Button>
                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      onClick={() => setStatus(approvalStatusEnum.enumValues[1])}
                    >
                      Accept Disclaimer & Submit for Review
                    </Button>
                  </>
                ) : (
                  <>
                    <Button type="submit" disabled={isSubmitting} variant="outline">
                      <Save className="h-4 w-4" />
                      {isSubmitting ? 'Creating JHA...' : 'Save Draft'}
                    </Button>
                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      onClick={() => setStatus(approvalStatusEnum.enumValues[1])}
                    >
                      {isSubmitting ? 'Creating JHA...' : 'Submit for Review'}
                    </Button>
                  </>
                )}
              </div>
            </div>
          </form>
        </Form>
      </div>

      {/* AI Disclaimer Modal */}
      <AiDisclaimerModal
        open={showDisclaimerModal}
        onAccept={handleDisclaimerAccept}
        onCancel={handleDisclaimerCancel}
      />
    </div>
  );
}
