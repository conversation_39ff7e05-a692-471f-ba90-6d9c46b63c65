import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { Filters } from '@/components/capas/list/capa-filters';
import { CapaMobileFilters } from '@/components/capas/list/capa-mobile-filters';
import { CapaMobileView } from '@/components/capas/list/capa-mobile-view';
import { CapaTable } from '@/components/capas/list/capa-table';
import { CapasEmpty } from '@/components/capas/list/capas-empty';
import { CapasError } from '@/components/capas/list/capas-error';
import { CapasLoading } from '@/components/capas/list/capas-loading';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { useAnalytics } from '@/hooks/use-analytics';
import { useIsMobile } from '@/hooks/use-mobile';
import { useInfiniteCapas } from '@/hooks/use-paginated-data';
import { usePermissions } from '@/hooks/use-permissions';
import { useCapasUrlFilters } from '@/hooks/use-url-filters';
import { exportCapasCSV } from '@/lib/export-capas';
import { trpc } from '@/providers/trpc';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { FileDown, Search, X } from 'lucide-react';
import { useCallback, useEffect } from 'react';
import { toast } from 'sonner';
import { useLocation } from 'wouter';

export default function CapaLog() {
  const isMobile = useIsMobile();
  const [_, navigate] = useLocation();
  const analytics = useAnalytics();

  const { hasPermission } = usePermissions();

  const { filters, immediateFilters, updateFilter, resetFilters, hasActiveFilters, activeFilterCount } =
    useCapasUrlFilters();

  // Fetch capas data
  const {
    data: capas,
    isLoading,
    error,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
  } = useInfiniteCapas({
    filters,
    enabled: true,
  });

  const { mutateAsync: exportCapas, isPending: isExporting } = trpc.capa.export.useMutation();

  useEffect(() => {
    if (error?.data?.code === 'FORBIDDEN') {
      navigate(ROUTES.NOT_FOUND);
    }
  }, [error]);

  // Track page view on component mount
  useEffect(() => {
    analytics.track(ANALYTICS_EVENTS.CAPA.TRACKER_VIEW_OPENED, {
      default_sort_by: 'createdAt',
      default_sort_order: 'desc',
    });
  }, [analytics]);

  // Track search when debounced search term changes
  useEffect(() => {
    if (filters.search?.trim() && capas) {
      analytics.track(ANALYTICS_EVENTS.CAPA.SEARCH_PERFORMED, {
        search_term: filters.search,
        result_count: capas.length,
      });
    }
  }, [filters.search, capas, analytics]);

  // Handle adding or removing a filter
  const toggleFilter = useCallback(
    (type: 'status' | 'type' | 'priority' | 'owner' | 'tags', value: string) => {
      const currentFilters = [...(immediateFilters[type] ?? [])];
      const index = currentFilters.indexOf(value);

      if (index > -1) {
        currentFilters.splice(index, 1);
      } else {
        currentFilters.push(value);
      }

      updateFilter(type, currentFilters);
    },
    [immediateFilters, updateFilter],
  );

  const trackFilterApplied = (
    type: 'status' | 'type' | 'priority' | 'owner' | 'tags' | 'dueDateRange' | 'includeArchived',
    value: string,
  ) => {
    analytics.track(ANALYTICS_EVENTS.CAPA.FILTER_APPLIED, {
      filter_name: type,
      filter_value: value,
      include_archived_toggle_state: filters.includeArchived,
    });
  };

  // Reset filters with analytics tracking
  const handleResetFilters = () => {
    analytics.track(ANALYTICS_EVENTS.CAPA.FILTER_RESET, {});
    resetFilters();
  };

  const handleExport = async () => {
    try {
      toast.info('Exporting CAPAs...');
      const capas = await exportCapas(filters);
      exportCapasCSV(capas);
      toast.success('Exporting CAPAs completed...');
    } catch (error) {
      console.error(error);
      toast.error('Error exporting CAPAs');
    }
  };

  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <div className="flex flex-col gap-4 justify-between items-start lg:flex-row ">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">CAPA Tracker</h1>
          <p className="text-muted-foreground text-sm mt-1">Track and manage all corrective and preventive actions</p>
        </div>

        <div className="w-full flex flex-col-reverse items-start pb-4 md:pb-0 gap-4 md:flex-row md:gap-2 md:w-auto">
          <div className="relative flex-1 md:w-64 w-full">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search CAPAs..."
              className="pl-8 pr-4"
              value={immediateFilters.search ?? ''}
              onChange={(e) => updateFilter('search', e.target.value)}
            />
            {immediateFilters.search && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0 "
                onClick={() => updateFilter('search', '')}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          <div className="flex md:flex-row items-start gap-4 md:gap-2 w-full md:w-auto">
            {hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.CREATE) && (
              <Button onClick={() => navigate(ROUTES.CAPA_NEW)} className="flex-1">
                + Create CAPA
              </Button>
            )}
            {hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.EXPORT) && (
              <Tooltip delayDuration={500}>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" disabled={isExporting} onClick={handleExport}>
                    <FileDown className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent align="end">
                  Export CAPAs (maximum 500 records). <br />
                  Only the first 500 records matching your <br /> current filters will be included in the export.
                </TooltipContent>
              </Tooltip>
            )}
            {/* Mobile filters */}
            <CapaMobileFilters
              filters={immediateFilters}
              toggleFilter={toggleFilter}
              activeFilterCount={activeFilterCount}
              resetFilters={handleResetFilters}
              updateFilter={updateFilter}
              trackFilterApplied={trackFilterApplied}
            />
          </div>
        </div>
      </div>

      {/* Desktop Dropdown filters section */}
      <Filters
        filters={immediateFilters}
        toggleFilter={toggleFilter}
        activeFilterCount={activeFilterCount}
        resetFilters={handleResetFilters}
        updateFilter={updateFilter}
        trackFilterApplied={trackFilterApplied}
      />

      {error ? <CapasError /> : null}

      {isLoading ? <CapasLoading /> : null}

      {/* Empty state */}
      {capas && capas.length === 0 && !isLoading && !error && (
        <CapasEmpty hasActiveFilters={hasActiveFilters} onResetFilters={handleResetFilters} />
      )}

      {!isMobile && capas && capas.length > 0 ? <CapaTable capas={capas} /> : null}

      {isMobile && capas && capas.length > 0 ? (
        <CapaMobileView capas={capas} activeFilterCount={activeFilterCount} resetFilters={handleResetFilters} />
      ) : null}

      {/* Load More Button for Infinite Scrolling */}
      {capas && capas.length > 0 && hasNextPage && (
        <div className="flex justify-center mt-6">
          <Button onClick={() => fetchNextPage()} disabled={isFetchingNextPage} variant="outline" className="px-6">
            {isFetchingNextPage ? 'Loading more...' : `Load More (${capas.length} loaded)`}
          </Button>
        </div>
      )}
    </div>
  );
}
