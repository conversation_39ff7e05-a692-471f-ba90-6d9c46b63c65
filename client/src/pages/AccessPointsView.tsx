import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { AccessPointBulkActionsDropdown } from '@/components/access-points/access-point-bulk-actions-dropdown';
import { AccessPointBulkImport } from '@/components/access-points/access-point-bulk-import';
import { AccessPointsEmpty } from '@/components/access-points/access-points-empty';
import { AccessPointsError } from '@/components/access-points/access-points-error';
import { Filters } from '@/components/access-points/access-points-filters';
import { AccessPointsLoading } from '@/components/access-points/access-points-loading';
import { MobileFilters } from '@/components/access-points/access-points-mobile-filters';
import { AccessPointsMobileView } from '@/components/access-points/access-points-mobile-view';
import { AccessPointsTable } from '@/components/access-points/access-points-table';
import { BulkImportSummaryModal } from '@/components/access-points/bulk-import-summary-modal';
import { CreateAccessPointModal } from '@/components/access-points/create-access-point-modal';
import { QrViewingModal } from '@/components/access-points/qr-viewing-modal';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { useAnalytics } from '@/hooks/use-analytics';
import { useIsMobile } from '@/hooks/use-mobile';
import { useInfiniteAccessPoints } from '@/hooks/use-paginated-data';
import { usePermissions } from '@/hooks/use-permissions';
import { useAccessPointsUrlFilters } from '@/hooks/use-url-filters';
import { exportAccessPointsCSV } from '@/lib/export-access-points';
import { generatePublicEventUrl } from '@/lib/utils';
import { trpc } from '@/providers/trpc';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { TransientAccessPoint } from '@shared/types/access-points.types';
import { RouterOutputs } from '@shared/types/router.types';
import { BulkImportResult } from '@shared/types/schema.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { FileDown, Search, X } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { toast } from 'sonner';
import { useLocation } from 'wouter';

export default function AccessPointsView() {
  const [_, navigate] = useLocation();
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [isBulkImportOpen, setIsBulkImportOpen] = useState<boolean>(false);
  const [isQrModalOpen, setIsQrModalOpen] = useState<boolean>(false);
  const [selectedAccessPoint, setSelectedAccessPoint] = useState<TransientAccessPoint | undefined>(undefined);
  const [isSummaryModalOpen, setIsSummaryModalOpen] = useState<boolean>(false);
  const [bulkImportResult, setBulkImportResult] = useState<BulkImportResult | null>(null);

  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState<boolean>(false);

  const [location] = useLocation();
  const { hasPermission } = usePermissions();
  const { track } = useAnalytics();
  const isMobile = useIsMobile();

  // Use the URL filters hook instead of manual state management
  const { filters, immediateFilters, updateFilter, updateFilters, resetFilters, activeFilterCount } =
    useAccessPointsUrlFilters();

  const {
    data: accessPoints,
    isLoading,
    error,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
    isFetchedAfterMount,
  } = useInfiniteAccessPoints({ filters });

  const { mutateAsync: exportAccessPoints, isPending: isExporting } = trpc.accessPoint.export.useMutation();

  useEffect(() => {
    if (error?.data?.code === 'FORBIDDEN') {
      navigate(ROUTES.NOT_FOUND);
    }
  }, [error]);

  // Track page view on component mount
  useEffect(() => {
    if (isFetchedAfterMount) {
      track(ANALYTICS_EVENTS.ACCESS_POINT.TABLE_VIEW_OPENED, {});
    }
  }, [isFetchedAfterMount]);

  // Track search when debounced search term changes
  useEffect(() => {
    if (filters?.search?.trim() && accessPoints) {
      track(ANALYTICS_EVENTS.ACCESS_POINT.SEARCH_PERFORMED, {
        search_term: filters.search,
        result_count: accessPoints.length,
      });
    }
  }, [filters.search, accessPoints]);

  // Check for create query parameter and open modal
  useEffect(() => {
    if (location === ROUTES.ACCESS_POINTS_NEW) {
      setIsModalOpen(true);
      // Track create initiated when modal opens via URL
      track(ANALYTICS_EVENTS.ACCESS_POINT.CREATE_INITIATED, {});
    } else {
      // Close modal if navigating away from the new route
      setIsModalOpen(false);
    }
  }, [location]);

  // Handle adding or removing a filter (similar to EventLog)
  const toggleFilter = useCallback(
    (type: 'status' | 'locationId' | 'createdBy', value: string) => {
      const currentFilters = [...(immediateFilters[type] ?? [])];
      const index = currentFilters.indexOf(value);

      let newFilters;
      if (index > -1) {
        currentFilters.splice(index, 1);
        newFilters = currentFilters;
      } else {
        currentFilters.push(value);
        newFilters = currentFilters;
      }

      // Track filter applied
      track(ANALYTICS_EVENTS.ACCESS_POINT.FILTER_APPLIED, {
        filter_name: type as string,
        filter_value: value,
      });

      updateFilter(type, newFilters);
    },
    [immediateFilters, updateFilter],
  );

  const trackFilterApplied = (
    type: 'status' | 'locationId' | 'createdBy' | 'createdDateRange' | 'includeArchived',
    value: string,
  ) => {
    track(ANALYTICS_EVENTS.ACCESS_POINT.FILTER_APPLIED, {
      filter_name: type as string,
      filter_value: value,
    });
  };

  // Enhanced reset filters that also clears search
  const handleResetFilters = () => {
    resetFilters();
  };

  // Handle bulk import success
  const handleBulkImportSuccess = (result: BulkImportResult) => {
    track(ANALYTICS_EVENTS.ACCESS_POINT.BULK_IMPORT_SUCCESS, {
      total_count: result.total,
      success_count: result.created,
      error_count: result.failed,
    });
    setBulkImportResult(result);
    setIsBulkImportOpen(false);
    setIsSummaryModalOpen(true);
  };

  const handleExport = async () => {
    try {
      toast.info('Exporting access points...');
      const accessPoints = await exportAccessPoints(filters);
      exportAccessPointsCSV(accessPoints);
      toast.success('Exporting access points completed...');
    } catch (error) {
      console.error(error);
      toast.error('Error exporting access points');
    }
  };

  // Handle viewing QR code for an existing access point
  const handleViewQRCode = async (accessPoint: TransientAccessPoint, qrCodeUrl: string) => {
    setSelectedAccessPoint(accessPoint);

    if (qrCodeUrl) {
      // If the access point already has a QR code URL, just show it
      setQrCodeUrl(qrCodeUrl);
      setIsQrModalOpen(true);
      return;
    }

    // Otherwise, generate a QR code URL for this access point
    setIsGenerating(true);
    try {
      const url = generatePublicEventUrl(accessPoint);

      // Store the QR code URL
      setQrCodeUrl(url);
      setIsQrModalOpen(true);
    } catch (error) {
      console.error('Error generating QR code:', error);
      // TODO: Show error message to user via toast or alert
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle form submission - generate QR code
  const handleGenerateQR = async (createdAccessPoint: RouterOutputs['accessPoint']['create']) => {
    setIsGenerating(true);

    try {
      const url = generatePublicEventUrl(createdAccessPoint);

      // Store the QR code URL
      setQrCodeUrl(url);
      // After successful generation, close the creation modal
      setIsModalOpen(false);

      // Track access point created
      track(ANALYTICS_EVENTS.ACCESS_POINT.CREATED, {
        access_point_id: createdAccessPoint.id,
        access_point_name: createdAccessPoint.name,
        location_id: createdAccessPoint.locationId || undefined,
        has_label_text: Boolean(createdAccessPoint.description),
      });

      // Set the created access point as selected and show QR modal immediately
      setSelectedAccessPoint(createdAccessPoint);
      setIsQrModalOpen(true);
    } catch (error) {
      console.error('Error generating QR code:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <div className="flex flex-col gap-4 justify-between items-start lg:flex-row ">
        <div>
          <h1 className="text-2xl font-bold md:mb-0">Access Points</h1>
          <p className="text-muted-foreground text-sm mt-1">Track and manage all access points</p>
        </div>

        <div className="w-full flex flex-col-reverse items-start pb-4 md:pb-0 gap-4 md:flex-row md:gap-2 md:w-auto">
          <div className="relative flex-1 w-full md:w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search access points..."
              className="pl-8 pr-4"
              value={immediateFilters.search ?? ''}
              onChange={(e) => updateFilter('search', e.target.value)}
            />
            {immediateFilters.search && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0 "
                onClick={() => updateFilter('search', '')}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
          <div className="flex md:flex-row items-start gap-4 md:gap-2 w-full md:w-auto">
            {hasPermission(MODULES.EHS_ACCESS_POINT, ALLOWED_ACTIONS.CREATE) && (
              <>
                <Button
                  onClick={() => {
                    setIsModalOpen(true);
                    track(ANALYTICS_EVENTS.ACCESS_POINT.CREATE_INITIATED, {});
                  }}
                  className="flex-1"
                >
                  + Create Access Point
                </Button>
                <AccessPointBulkActionsDropdown onImportClick={() => setIsBulkImportOpen(true)} />
              </>
            )}
            {hasPermission(MODULES.EHS_ACCESS_POINT, ALLOWED_ACTIONS.EXPORT) && (
              <Tooltip delayDuration={500}>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" disabled={isExporting} onClick={handleExport}>
                    <FileDown className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent align="end">
                  Export access points (maximum 500 records). <br />
                  Only the first 500 records matching your <br /> current filters will be included in the export.
                </TooltipContent>
              </Tooltip>
            )}
            <MobileFilters
              activeFilterCount={activeFilterCount}
              filters={immediateFilters}
              updateFilters={updateFilters}
              toggleFilter={toggleFilter}
              resetFilters={handleResetFilters}
              trackFilterApplied={trackFilterApplied}
            />
          </div>
        </div>
      </div>

      {/* Desktop Filters bar */}
      <Filters
        filters={immediateFilters}
        updateFilters={updateFilters}
        toggleFilter={toggleFilter}
        activeFilterCount={activeFilterCount}
        resetFilters={handleResetFilters}
        trackFilterApplied={trackFilterApplied}
      />

      {/* Error state */}
      {error && <AccessPointsError />}

      {/* Loading state */}
      {isLoading && <AccessPointsLoading />}

      <QrViewingModal
        isQrModalOpen={isQrModalOpen}
        setIsQrModalOpen={setIsQrModalOpen}
        accessPoint={selectedAccessPoint}
        qrCodeUrl={qrCodeUrl}
      />

      {/* Create Access Point Modal */}
      {hasPermission(MODULES.EHS_ACCESS_POINT, ALLOWED_ACTIONS.CREATE) && (
        <CreateAccessPointModal
          isModalOpen={isModalOpen}
          setIsModalOpen={setIsModalOpen}
          isGenerating={isGenerating}
          handleGenerateQR={handleGenerateQR}
        />
      )}

      {/* Bulk Import Modal */}
      <AccessPointBulkImport
        isOpen={isBulkImportOpen}
        onClose={() => setIsBulkImportOpen(false)}
        onSuccess={handleBulkImportSuccess}
      />

      {/* Bulk Import Summary Modal */}
      <BulkImportSummaryModal
        isOpen={isSummaryModalOpen}
        onClose={() => setIsSummaryModalOpen(false)}
        result={bulkImportResult}
      />

      {/* Desktop Table View */}
      {!isMobile && !isLoading && !error && accessPoints && accessPoints.length > 0 && (
        <AccessPointsTable accessPoints={accessPoints} onViewQRCode={handleViewQRCode} qrCodeUrl={qrCodeUrl} />
      )}

      {/* Mobile Card View */}
      {isMobile && !isLoading && !error && accessPoints && accessPoints.length > 0 && (
        <AccessPointsMobileView accessPoints={accessPoints} onViewQRCode={handleViewQRCode} qrCodeUrl={qrCodeUrl} />
      )}

      {/* Empty state - when no access points are found and not loading */}
      {!isLoading && !error && accessPoints && accessPoints.length === 0 && (
        <AccessPointsEmpty
          hasActiveFilters={activeFilterCount > 0}
          onResetFilters={handleResetFilters}
          onCreateAccessPoint={() => {
            setIsModalOpen(true);
            track(ANALYTICS_EVENTS.ACCESS_POINT.CREATE_INITIATED, {});
          }}
        />
      )}

      {/* Load More Button for Infinite Scrolling */}
      {accessPoints && accessPoints.length > 0 && hasNextPage && (
        <div className="flex justify-center mt-6">
          <Button onClick={() => fetchNextPage()} disabled={isFetchingNextPage} variant="outline" className="px-6">
            {isFetchingNextPage ? 'Loading more...' : `Load More (${accessPoints.length} loaded)`}
          </Button>
        </div>
      )}
    </div>
  );
}
