import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { CreateCapaLoading } from '@/components/capas/create/create-capa-loading';
import { CapaTags } from '@/components/capas/details/tags';
import { RootCauseSelector } from '@/components/capas/root-cause-selector';
import { AiCompose } from '@/components/composite/ai-compose';
import { AsyncAssetSelect } from '@/components/composite/async-asset-select';
import { AsyncEventsSelect } from '@/components/composite/async-events-select';
import { AsyncJhaMultiSelect } from '@/components/composite/async-jha-multi-select';
import { AsyncLocationSelect } from '@/components/composite/async-location-select';
import { AsyncUserMultiSelect } from '@/components/composite/async-user-multi-select';
import { AsyncUserSelect } from '@/components/composite/async-user-select';
import { AsyncWorkOrderMultiSelect } from '@/components/composite/async-work-order-multi-select';
import { SuccessModal } from '@/components/composite/success-modal';
import { VoiceInputRef } from '@/components/composite/voice-input';
import { MediaUpload } from '@/components/media/media-upload';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DateTimePicker } from '@/components/ui/date-time-picker';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useAnalytics } from '@/hooks/use-analytics';
import { useFileAssociation } from '@/hooks/use-file-association';
import { usePermissions } from '@/hooks/use-permissions';
import { trpc } from '@/providers/trpc';
import { zodResolver } from '@hookform/resolvers/zod';
import { MAX_FILE_SIZE_MB } from '@shared/files-utils';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { capaPriorityEnum, capaTypeEnum, rcaMethodEnum, statusEnum } from '@shared/schema';
import { CAPA_TYPE_MAP, CreateCapasFormSchema, RCA_METHOD_MAP } from '@shared/types/capas.types';
import { TransientFileSchema } from '@shared/types/files.types';
import { RouterOutputs } from '@shared/types/router.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { addYears } from 'date-fns';
import { Check, Info, Loader2 } from 'lucide-react';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { Redirect, useLocation, useSearchParams } from 'wouter';
import { z } from 'zod';

type CreatedCapa = RouterOutputs['capa']['create'];

const FormSchema = CreateCapasFormSchema;

export default function NewCapa() {
  const [_, navigate] = useLocation();
  const { track } = useAnalytics();
  const { hasPermission } = usePermissions();
  const { associateFiles } = useFileAssociation();

  // States for UI
  const [autoFilledFields, setAutoFilledFields] = useState<string[]>([]);
  const [showSuccessModal, setShowSuccessModal] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [createdCapa, setCreatedCapa] = useState<CreatedCapa>();

  const [isVoiceUsed, setIsVoiceUsed] = useState(false);
  const [aiSummary, setAiSummary] = useState<string>('');

  const [searchParams] = useSearchParams();
  const utils = trpc.useUtils();

  const sourceCapaId = searchParams.get('sourceCapaId');

  const voiceInputRef = useRef<VoiceInputRef>(null);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      actionsImplemented: '',
      actionsToAddress: '',
      assetId: null,
      attachments: [],
      dueDate: undefined,
      effectivenessStatus: 'not_effective',
      eventId: null,
      implementationDate: undefined,
      implementedBy: '',
      locationId: null,
      linkedJhaInstanceIds: [],
      otherRootCause: '',
      ownerId: '',
      priority: capaPriorityEnum.enumValues[0],
      privateToAdmins: false,
      rcaFindings: '',
      rcaMethod: rcaMethodEnum.enumValues[0],
      rootCauses: [],
      status: statusEnum.enumValues[0],
      tags: [],
      teamMembersToNotify: [],
      title: '',
      type: capaTypeEnum.enumValues[0],
      verificationFindings: '',
      voeDate: undefined,
      voePerformedBy: '',
      workOrderIds: [],
    },
    mode: 'onSubmit',
  });

  const eventId = useMemo(
    () => searchParams.get('eventId') || form.watch('eventId') || undefined,
    [searchParams.get('eventId'), form.watch('eventId')],
  );

  const { data: event } = trpc.event.getById.useQuery(
    {
      id: eventId || '',
    },
    {
      enabled: !!eventId,
    },
  );

  const { mutateAsync: removeFiles } = trpc.file.removeFiles.useMutation();
  const { mutateAsync: createCapa } = trpc.capa.create.useMutation({
    onSuccess: () => {
      utils.event.getById.invalidate({ id: eventId });
      utils.capa.list.invalidate();
    },
  });

  const onFileRemoval = async (file: z.infer<typeof TransientFileSchema>) => {
    try {
      if (!file.id) {
        return;
      }

      const fileId = file.id;
      await removeFiles([fileId]);
      const updatedAttachments = form.getValues('attachments')?.filter((file) => file.id !== fileId);

      form.setValue('attachments', updatedAttachments);
      toast.success('File removed', {
        description: 'The file has been successfully removed.',
      });
    } catch (error) {
      console.error('Error removing file:', error);
      toast.error('Error removing file', {
        description: 'There was a problem removing the file. Please try again.',
      });
    }
  };

  const {
    data: capaFromId,
    isPending: isCapaFromIdLoading,
    error: capaFromIdError,
  } = trpc.capa.getByIdForDuplicate.useQuery(
    {
      id: sourceCapaId!,
    },
    { enabled: !!sourceCapaId },
  );

  useEffect(() => {
    // needed to reset the form if we're at this route with sourceCapaId query param and navigate to the same route without sourceCapaId.
    if (!sourceCapaId && isCapaFromIdLoading) {
      resetState();
    }
  }, [sourceCapaId, isCapaFromIdLoading]);

  const isLoadingCapaFromId = sourceCapaId && isCapaFromIdLoading && !capaFromIdError;

  useEffect(() => {
    if (capaFromId && !isCapaFromIdLoading && !capaFromIdError) {
      // Set form values from CAPA data
      form.reset(
        {
          actionsImplemented: capaFromId.actionsImplemented ?? undefined,
          actionsToAddress: capaFromId.actionsToAddress ?? undefined,
          attachments: capaFromId.attachments || [],
          dueDate: capaFromId.dueDate ? new Date(capaFromId.dueDate) : undefined,
          effectivenessStatus: capaFromId.effectivenessStatus ?? 'not_effective',
          eventId: capaFromId.eventId || null,
          implementationDate: capaFromId.implementationDate ? new Date(capaFromId.implementationDate) : undefined,
          implementedBy: capaFromId.implementedBy ?? undefined,
          locationId: capaFromId.locationId || null,
          assetId: capaFromId.assetId || null,
          otherRootCause: capaFromId.rootCauses?.includes('other')
            ? (capaFromId.otherRootCause ?? undefined)
            : undefined,
          ownerId: capaFromId.ownerId ?? '',
          priority: capaFromId.priority ?? 'medium',
          privateToAdmins: capaFromId.privateToAdmins ?? false,
          rcaFindings: capaFromId.rcaFindings || '',
          rcaMethod: capaFromId.rcaMethod ?? '5_whys',
          rootCauses: capaFromId.rootCauses || [],
          status: statusEnum.enumValues[0],
          tags: capaFromId.tags ?? [],
          teamMembersToNotify: capaFromId.teamMembersToNotify || [],
          title: capaFromId.title,
          type: capaFromId.type,
          verificationFindings: capaFromId.verificationFindings || null,
          voeDate: capaFromId.voeDate ? new Date(capaFromId.voeDate) : undefined,
          voePerformedBy: capaFromId.voePerformedBy ?? undefined,
          // Transform the CAPA-JHA relationship objects into a simple array of JHA instance IDs
          // for the form. The backend returns capaJhas as objects with { id, jhaInstanceId, jha: { slug, title } }
          // but the form expects just an array of strings (JHA instance IDs)
          linkedJhaInstanceIds:
            capaFromId.capaJhas?.map((capaJha) => capaJha.jhaInstanceId).filter((id): id is string => Boolean(id)) ??
            [],
          workOrderIds: [],
        },
        {
          keepDefaultValues: true, // don't overwrite default values
        },
      );
    }
  }, [capaFromId, isCapaFromIdLoading, capaFromIdError]);

  // Track form view and create initiated on component mount
  useEffect(() => {
    // Determine source based on URL params or referrer - inline logic
    const getCapaSource = ():
      | 'global_create'
      | 'event_detail_page'
      | 'event_log_row_action'
      | 'capa_log_row_duplicate' => {
      const search = window.location?.search || '';
      const eventId = search.includes('eventId=');

      if (eventId) {
        if (document.referrer.includes('/events/')) {
          return 'event_detail_page';
        } else if (document.referrer.includes('/events')) {
          return 'event_log_row_action';
        }
      }

      if (search.includes('sourceCapaId=')) {
        return 'capa_log_row_duplicate';
      }

      return 'global_create';
    };

    const source = getCapaSource();

    // Track create initiated
    track(ANALYTICS_EVENTS.CAPA.CREATE_INITIATED, {
      source,
    });

    // Track form viewed
    track(ANALYTICS_EVENTS.CAPA.FORM_VIEWED, {
      source,
    });
  }, []);

  useEffect(() => {
    if (eventId && event) {
      // Update form with actual safety event data
      form.setValue('title', `CAPA for: ${event.title}`);
      form.setValue('eventId', eventId);

      // Pre-fill location from safety event
      if (event.location) {
        form.setValue('locationId', event.location.id);
      }

      // Set appropriate priority based on hazard category
      if (event.category && ['chemical', 'fire', 'electrical'].includes(event.category)) {
        form.setValue('priority', 'high');
      }

      // Set default due date (2 weeks from now)
      const dueDate = new Date();
      dueDate.setDate(dueDate.getDate() + 14);
      form.setValue('dueDate', dueDate);
    }
  }, [event, eventId]);

  // Separate useEffect to handle setting asset after location is set
  useEffect(() => {
    if (event && event.assetIds?.length === 1) {
      form.setValue('assetId', event.assetIds[0]);
    }
  }, [event, form.watch('locationId')]);

  // Handle the voice analysis results from the VoiceInput component
  const handleVoiceAnalysis = async (data: RouterOutputs['ai']['analyzeCapa']) => {
    setIsVoiceUsed(true);
    setAutoFilledFields([]); // Clear any previous animations

    // Store the AI summary
    if (data?.summary) {
      setAiSummary(data.summary);
    }

    // Animate each field separately with staggered timing
    const animateField = (
      field: keyof z.infer<typeof FormSchema>,
      value: string | Date | undefined | null | string[],
      delay: number,
    ) => {
      setTimeout(() => {
        form.setValue(field, value);
        setAutoFilledFields((prev) => [...prev, field]);
      }, delay);
    };

    const baseDelay = 300;
    let currentDelay = 0;

    if (!eventId) {
      animateField('title', data?.title, currentDelay);
      currentDelay += baseDelay;
    }

    if (data.rcaMethod) {
      animateField('rcaMethod', data.rcaMethod, currentDelay);
      currentDelay += baseDelay;
    }

    animateField('rcaFindings', data.rcaFindings, currentDelay);
    currentDelay += baseDelay;

    animateField('actionsToAddress', data.actionsToAddress, currentDelay);
    currentDelay += baseDelay;

    animateField('rootCauses', data.rootCauses, currentDelay);
    currentDelay += baseDelay;

    animateField('type', data?.type, currentDelay);
    currentDelay += baseDelay;

    if (data.otherRootCause && data.rootCauses?.includes('other')) {
      animateField('otherRootCause', data?.otherRootCause, currentDelay);
      currentDelay += baseDelay;
    }

    animateField('priority', data.priority, currentDelay);
    currentDelay += baseDelay;

    if (data.tags) {
      animateField('tags', data?.tags, currentDelay);
      form.setValue('tags', data?.tags);
      currentDelay += baseDelay;
    }

    if (data.dueDate) {
      animateField('dueDate', new Date(data?.dueDate), currentDelay);
      currentDelay += baseDelay;
    }

    toast.success('✨ AI Analysis Complete', {
      description:
        'Form has been populated based on your voice description. Feel free to edit any field before submitting.',
    });
  };

  // Reset State - used when creating another CAPA
  const resetState = () => {
    form.reset();
    setAutoFilledFields([]);
    setShowSuccessModal(false);
    setIsSubmitting(false);
    setIsUploading(false);
    setCreatedCapa(undefined);
    setIsVoiceUsed(false);
    setAiSummary('');
  };

  // Form submission handler
  const onSubmit = async (values: z.infer<typeof FormSchema>) => {
    setIsSubmitting(true);

    try {
      const newCapa = await createCapa({ ...values, attachments: undefined, summary: aiSummary });

      // Associate all uploaded files with the newly created CAPA
      if (newCapa?.id && values.attachments?.length) {
        await associateFiles(values.attachments, newCapa.id, 'capa');
      }

      // Track CAPA creation
      track(ANALYTICS_EVENTS.CAPA.FORM_SUBMITTED, {
        capa_id: newCapa?.id,
        event_id: values.eventId || undefined,
        capa_type: values.type === 'corrective' ? 'Corrective' : 'Preventive',
        location: values.locationId || '',
        owner_id: values.ownerId || '',
        priority: values.priority || '',
        tags: values.tags || [],
        is_private_to_admins: values.privateToAdmins || false,
        media_attached_count: values.attachments?.length || 0,
        is_ai_assisted: isVoiceUsed,
      });

      // Track CAPA created from event (if linked to an event)
      if (values.eventId) {
        track(ANALYTICS_EVENTS.EVENT.CAPA_CREATED_FROM_EVENT, {
          event_id: values.eventId,
          capa_id: newCapa?.id,
        });
      }

      // Track CAPA created from another CAPA (aka duplicated)
      if (sourceCapaId) {
        track(ANALYTICS_EVENTS.EVENT.CAPA_CREATED_FROM_CAPA, {
          from_capa_id: sourceCapaId,
          capa_id: newCapa?.id,
        });
      }

      setCreatedCapa({ ...form.getValues(), ...newCapa });

      form.reset();

      // Scroll to top of the page
      window.scrollTo({ top: 0, behavior: 'smooth' });

      // Clear any auto-filled fields
      setAutoFilledFields([]);

      toast.success('CAPA created', {
        description: 'Your CAPA has been created successfully',
      });

      // Show the success modal and let user decide when to close it
      setShowSuccessModal(true);
    } catch (error) {
      console.error('Error submitting root cause analysis', error);
      // Track form validation failed
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      track(ANALYTICS_EVENTS.CAPA.FORM_VALIDATION_FAILED, {
        validation_errors: [errorMessage],
      });
      toast.error('Error reporting root cause analysis', {
        description: 'There was a problem submitting your root cause analysis. Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.CREATE)) {
    return <Redirect to={ROUTES.CAPA_LIST} />;
  }

  if (isLoadingCapaFromId) {
    return <CreateCapaLoading />;
  }

  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 md:px-8 lg:px-10 py-6">
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        entity="capa"
        data={createdCapa}
        onSecondaryActionClick={resetState}
      />

      <div className="flex items-center gap-5 mb-4">
        <h1 className="text-2xl font-bold text-primary-600">Create CAPA</h1>
        {eventId && event && <div className="text-sm text-gray-500">Linked to Safety Event: {event.title}</div>}
      </div>

      {/* CAPA Form */}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="space-y-6 pt-5">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Title <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Brief title for this CAPA" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* CAPA Type and Linked Event - Responsive layout */}
            <div className="grid grid-cols-1 sm:grid-cols-12 gap-4">
              <div className="sm:col-span-4">
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        CAPA Type <span className="text-red-500">*</span>
                      </FormLabel>
                      <Select key={field.value} onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select CAPA type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {capaTypeEnum.enumValues.map((type) => (
                            <SelectItem key={type} value={type}>
                              {CAPA_TYPE_MAP[type]}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Select whether this is a corrective action, preventive action, or both.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Linked Event - Mobile Optimized Dropdown */}
              <div className="sm:col-span-8">
                <FormField
                  control={form.control}
                  name="eventId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Link to Safety Event</FormLabel>
                      <AsyncEventsSelect
                        {...field}
                        onChange={field.onChange}
                        value={field.value}
                        placeholder="Search and select a safety event..."
                        mustIncludeObjectIds={eventId ? [eventId] : undefined}
                      />
                      <FormDescription>
                        {eventId ? (
                          <span className="text-green-600 flex items-center gap-1">
                            <Check className="size-4" />
                            This CAPA is linked to a safety event
                          </span>
                        ) : (
                          'Optional: Link this CAPA to a safety event. Linked CAPAs will show on the safety event record.'
                        )}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Location and Asset - Responsive layout */}
            <div className="grid grid-cols-1 sm:grid-cols-12 gap-4">
              <div className="sm:col-span-6">
                <FormField
                  control={form.control}
                  name="locationId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Location</FormLabel>
                      <FormControl>
                        <AsyncLocationSelect
                          {...field}
                          placeholder="Select a location"
                          value={field.value}
                          onChange={(newValue) => {
                            field.onChange(newValue);
                            // clears assetId if the user is changing locationId, keep if they're clearing
                            if (newValue) {
                              form.setValue('assetId', null);
                            }
                          }}
                          mustIncludeObjectIds={event?.locationId ? [event.locationId] : undefined}
                        />
                      </FormControl>
                      <FormDescription>Specific area where the CAPA will be implemented</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="sm:col-span-6">
                <FormField
                  control={form.control}
                  name="assetId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Asset (Optional)</FormLabel>
                      <FormControl>
                        <AsyncAssetSelect
                          {...field}
                          placeholder="Select an asset"
                          locationId={form.watch('locationId') || undefined}
                        />
                      </FormControl>
                      <FormDescription>Specific equipment or asset related to this CAPA</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Root Cause Analysis Method - New section */}
            <Card className="border-blue-100 shadow-none mb-4">
              <CardHeader>
                <CardTitle className="text-md font-medium flex items-center gap-2">
                  <Info className="size-4" />
                  Root Cause Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-3">
                {/* AI Assist Voice Input for RCA Section */}
                <div className="p-3 bg-blue-50 rounded-lg border border-blue-100 flex flex-col gap-2">
                  <p className="text-sm text-blue-700">
                    Use your voice to describe the safety event investigation, the RCA method used, and the proposed
                    actions to address the root cause(s).
                  </p>
                  <AiCompose
                    methods={['voice']}
                    entity="capa"
                    onVoiceComplete={handleVoiceAnalysis}
                    voiceInputRef={voiceInputRef}
                  />
                </div>

                <div className="my-4">
                  <FormField
                    control={form.control}
                    name="rcaMethod"
                    render={({ field }) => (
                      <FormItem className={autoFilledFields.includes('rcaMethod') ? 'relative' : ''}>
                        <FormLabel>RCA Method Used</FormLabel>
                        <Select key={field.value} onValueChange={field.onChange} defaultValue={field.value || '5_whys'}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select analysis method" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {rcaMethodEnum.enumValues.map((method) => (
                              <SelectItem key={method} value={method}>
                                {RCA_METHOD_MAP[method]}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>Choose the method used to analyze the root cause</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* RCA Findings - New field */}
                <FormField
                  control={form.control}
                  name="rcaFindings"
                  render={({ field }) => (
                    <FormItem className={autoFilledFields.includes('rcaFindings') ? 'relative' : ''}>
                      {autoFilledFields.includes('rcaFindings') && (
                        <div className="absolute -inset-1 rounded-md bg-indigo-50/50 border border-indigo-200 -z-10" />
                      )}
                      <FormLabel>
                        RCA Findings & Conclusion
                        <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          {...field}
                          placeholder="Document your findings and root cause analysis conclusion here. If using 5 Whys, include all questions and answers."
                          className="min-h-[120px]"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Root Cause Category moved into RCA section and renamed */}
                <div className="mt-4">
                  <RootCauseSelector
                    control={form.control}
                    setValue={form.setValue}
                    rootCausesFieldName="rootCauses"
                    otherRootCauseFieldName="otherRootCause"
                    autoFilledFields={autoFilledFields}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Actions to Address - Renamed from Recommended Action */}
            <FormField
              control={form.control}
              name="actionsToAddress"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Proposed Actions
                    <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Based on the RCA, what specific corrective and preventive actions are planned to eliminate the root cause and prevent recurrence?"
                      className="min-h-[120px]"
                      value={field.value || ''}
                    />
                  </FormControl>
                  <FormDescription>Include both immediate actions and long-term preventive measures</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Attachments Section */}
            <div className="pt-2 pb-3">
              <div className="mb-2">
                <MediaUpload
                  maxFiles={5}
                  maxSize={MAX_FILE_SIZE_MB}
                  entityType={'capa'}
                  className="bg-white"
                  files={form.watch('attachments') ?? []}
                  setFiles={(tFiles) => {
                    form.setValue('attachments', tFiles);
                  }}
                  onUploadStateChange={(state) => {
                    setIsUploading(state.isUploading);
                  }}
                  onFileRemove={onFileRemoval}
                />
              </div>
            </div>

            {/* Linked Documents Section */}
            <Card className="border-gray-200 shadow-none">
              <CardHeader>
                <CardTitle className="text-md font-medium flex items-center gap-2">
                  <Info className="size-4" />
                  Linked Documents
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-3">
                <FormField
                  control={form.control}
                  name="linkedJhaInstanceIds"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Job Hazard Analyses (JHAs)</FormLabel>
                      <FormControl>
                        <AsyncJhaMultiSelect
                          placeholder="Search and select relevant JHAs..."
                          value={field.value}
                          onChange={field.onChange}
                          disabled={false}
                        />
                      </FormControl>
                      <FormDescription>Associate relevant Job Hazard Analyses with this CAPA.</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="workOrderIds"
                  render={({ field }) => (
                    <FormItem className="mt-4">
                      <FormLabel>Work Orders</FormLabel>
                      <FormControl>
                        <AsyncWorkOrderMultiSelect
                          placeholder="Search and select relevant work orders..."
                          value={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                      <FormDescription>Associate relevant Work Orders with this CAPA.</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Owner, Due Date, and Priority - Responsive Layout */}
            <div className="flex flex-col md:flex-row md:items-start gap-4">
              {/* Owner Select */}
              <FormField
                control={form.control}
                name="ownerId"
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormLabel>
                      Owner <span className="text-red-500">*</span>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger>
                            <Info className="h-3 w-3" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="font-bold mb-1">Can't find a team member?</div>
                            <div>
                              Only active users in your UpKeep EHS system can be assigned. <br /> To add new team
                              members, please contact your UpKeep Administrator for assistance.
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </FormLabel>

                    <FormControl>
                      <AsyncUserSelect placeholder="Select an owner" {...field} value={field.value} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Due Date */}
              <FormField
                control={form.control}
                name="dueDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Due Date</FormLabel>
                    <FormControl>
                      <DateTimePicker
                        selected={field.value ?? undefined}
                        onSelect={field.onChange}
                        disabled={{
                          before: new Date(),
                        }}
                        onlyDate
                        placeholder="Select due date"
                        startMonth={new Date()}
                        endMonth={addYears(new Date(), 5)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Priority Radio Select */}
              <FormField
                control={form.control}
                name="priority"
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormLabel>Priority</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className="flex flex-col sm:flex-row sm:space-x-4 sm:mt-2"
                      >
                        <FormItem className="flex items-center">
                          <FormControl>
                            <RadioGroupItem value="low" />
                          </FormControl>
                          <FormLabel className="font-normal cursor-pointer">Low</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center">
                          <FormControl>
                            <RadioGroupItem value="medium" />
                          </FormControl>
                          <FormLabel className="font-normal cursor-pointer">Medium</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center">
                          <FormControl>
                            <RadioGroupItem value="high" />
                          </FormControl>
                          <FormLabel className="font-normal cursor-pointer">High</FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Tags - Responsive Grid */}
            <FormField
              control={form.control}
              name="tags"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tags</FormLabel>
                  <FormControl>
                    <CapaTags {...field} />
                  </FormControl>
                  <FormDescription>
                    Add tags to categorize this CAPA (e.g., Training, Procedure, Equipment).
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Private to Admins Checkbox */}
            {/* TODO: REMOVED FROM V1 */}
            {/* <FormField
              control={form.control}
              name="privateToAdmins"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                  <FormControl>
                    <Checkbox checked={field.value || false} onCheckedChange={field.onChange} />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Private to Administrators</FormLabel>
                    <FormDescription>If checked, this CAPA will only be visible to administrators.</FormDescription>
                  </div>
                </FormItem>
              )}
            /> */}

            {/* Team Members to Notify */}
            <FormField
              control={form.control}
              name="teamMembersToNotify"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Team Members to Notify
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          <Info className="h-3 w-3" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <div className="font-bold mb-1">Can't find a team member?</div>
                          <div>
                            Only active users in your UpKeep EHS system can be notified. <br /> To add new team members,
                            please contact your UpKeep Administrator for assistance.
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </FormLabel>
                  <FormControl>
                    <AsyncUserMultiSelect placeholder="Select team members to notify" {...field} value={field.value} />
                  </FormControl>
                  <FormDescription>Select team members who should be notified about this CAPA update.</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Submit Button */}
            <div className="flex justify-end gap-3 pt-6 border-t border-neutral-700">
              <Button type="button" variant="outline" onClick={() => navigate(ROUTES.CAPA_LIST)}>
                Cancel
              </Button>

              {/* Primary Submit Button */}
              <Button type="submit" disabled={isSubmitting || isUploading} className="min-w-[100px]">
                {isSubmitting || isUploading ? <Loader2 className="size-4 animate-spin" /> : null}
                {isUploading ? 'Processing Files...' : isSubmitting ? 'Creating...' : 'Create CAPA'}
              </Button>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
}
