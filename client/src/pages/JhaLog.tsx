import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { HazardAndControlMeasuresIntroModal } from '@/components/composite/hazard-and-control-measures-intro-modal';
import { JhaEmpty } from '@/components/jha/list/jha-empty';
import { JhaError } from '@/components/jha/list/jha-error';
import { Filters } from '@/components/jha/list/jha-filters';
import { JhaLoading } from '@/components/jha/list/jha-loading';
import { MobileFilters } from '@/components/jha/list/jha-mobile-filters';
import { JhaMobileView } from '@/components/jha/list/jha-mobile-view';
import { JhaTable } from '@/components/jha/list/jha-table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAnalytics } from '@/hooks/use-analytics';
import { useIsMobile } from '@/hooks/use-mobile';
import { useInfiniteJhas } from '@/hooks/use-paginated-data';
import { usePermissions } from '@/hooks/use-permissions';
import { useJhaUrlFilters } from '@/hooks/use-url-filters';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { approvalStatusEnum } from '@shared/schema';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { useLocalStorage } from '@uidotdev/usehooks';
import { Search, X } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { useLocation } from 'wouter';

export default function JhaLog() {
  const [_, navigate] = useLocation();
  const isMobile = useIsMobile();
  const { hasPermission } = usePermissions();
  const analytics = useAnalytics();

  // Modal state for hazard and control measures intro
  const [showIntroModal, setShowIntroModal] = useState(false);
  const [hasSeenIntro, setHasSeenIntro] = useLocalStorage('ehs/jha-intro-modal-seen', false);

  // Use the URL filters hook
  const { filters, immediateFilters, updateFilter, updateFilters, resetFilters, activeFilterCount } =
    useJhaUrlFilters();

  const {
    data: jhas,
    isLoading,
    error,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
    isFetchedAfterMount,
  } = useInfiniteJhas({
    filters,
    enabled: true,
  });

  useEffect(() => {
    if (error?.data?.code === 'FORBIDDEN') {
      navigate(ROUTES.NOT_FOUND);
    }
  }, [error]);

  // Show intro modal on first visit
  useEffect(() => {
    const canCreateHazards = hasPermission(MODULES.HAZARDS, ALLOWED_ACTIONS.CREATE);
    const canCreateControlMeasures = hasPermission(MODULES.CONTROL_MEASURES, ALLOWED_ACTIONS.CREATE);

    if (!hasSeenIntro && isFetchedAfterMount && canCreateHazards && canCreateControlMeasures) {
      setShowIntroModal(true);
    }
  }, [hasSeenIntro, isFetchedAfterMount]);

  // Track page view on component mount (only once)
  useEffect(() => {
    if (isFetchedAfterMount) {
      analytics.track(ANALYTICS_EVENTS.JHA.LOG_VIEW_OPENED, {
        default_sort_by: 'createdAt',
        default_sort_order: 'desc',
      });
    }
  }, [isFetchedAfterMount]);

  // Track search when debounced search term changes
  useEffect(() => {
    if (filters.search?.trim() && jhas) {
      analytics.track(ANALYTICS_EVENTS.JHA.SEARCH_PERFORMED, {
        search_term: filters.search,
        result_count: jhas.length,
      });
    }
  }, [filters.search, jhas]);

  const toggleFilter = useCallback(
    (type: 'status', value: (typeof approvalStatusEnum.enumValues)[number]) => {
      const currentFilters = [...(immediateFilters[type] ?? [])];
      const index = currentFilters.indexOf(value);

      let newFilters;
      if (index > -1) {
        currentFilters.splice(index, 1);
        newFilters = currentFilters;
      } else {
        currentFilters.push(value);
        newFilters = currentFilters;
      }

      // Track filter applied
      analytics.track(ANALYTICS_EVENTS.JHA.FILTER_APPLIED, {
        filter_name: type,
        filter_value: value,
        include_archived_toggle_state: filters.includeArchived,
      });

      updateFilter(type, newFilters);
    },
    [immediateFilters, updateFilter, analytics, filters.includeArchived],
  );

  const trackFilterApplied = (
    type: 'status' | 'ownerId' | 'riskLevel' | 'reviewStatus' | 'locationIds' | 'includeArchived',
    value: string,
  ) => {
    analytics.track(ANALYTICS_EVENTS.JHA.FILTER_APPLIED, {
      filter_name: type,
      filter_value: value,
      include_archived_toggle_state: filters.includeArchived,
    });
  };

  // Enhanced reset filters that also clears search
  const handleResetFilters = () => {
    // Track filter reset
    analytics.track(ANALYTICS_EVENTS.JHA.FILTER_RESET, {});
    resetFilters();
  };

  // Handle closing the intro modal
  const handleCloseIntroModal = () => {
    setShowIntroModal(false);
    setHasSeenIntro(true);
  };

  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <div className="flex flex-col gap-4 justify-between items-start lg:flex-row ">
        <div className="mb-4 md:mb-0">
          <h1 className="text-2xl font-bold md:mb-0">Job Hazard Analyses</h1>
          <p className="text-muted-foreground text-sm mt-1">
            Create and manage job hazard analyses to identify and control workplace hazards
          </p>
        </div>
        <div className="w-full flex flex-col-reverse items-start pb-4 md:pb-0 gap-4 md:flex-row md:gap-2 md:w-auto">
          <div className="relative flex-1 md:w-64 w-full">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search JHAs..."
              className="pl-8 pr-4"
              value={immediateFilters.search ?? ''}
              onChange={(e) => updateFilter('search', e.target.value)}
            />
            {immediateFilters.search && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0"
                onClick={() => updateFilter('search', '')}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          <div className="flex items-center gap-2 w-full md:w-auto">
            {hasPermission(MODULES.EHS_JHA, ALLOWED_ACTIONS.CREATE) && (
              <Button
                onClick={() => {
                  analytics.track(ANALYTICS_EVENTS.JHA.FORM_VIEWED, {
                    source: 'jha_log',
                  });
                  navigate(ROUTES.JHA_NEW);
                }}
                className="flex-1"
              >
                + Create JHA
              </Button>
            )}
            <MobileFilters
              toggleFilter={toggleFilter}
              filters={immediateFilters}
              updateFilters={updateFilters}
              activeFilterCount={activeFilterCount}
              resetFilters={handleResetFilters}
              trackFilterApplied={trackFilterApplied}
            />
          </div>
        </div>
      </div>

      {/* Filters bar */}
      <Filters
        toggleFilter={toggleFilter}
        filters={immediateFilters}
        updateFilters={updateFilters}
        activeFilterCount={activeFilterCount}
        resetFilters={handleResetFilters}
        trackFilterApplied={trackFilterApplied}
      />

      {/* Error state */}
      {error ? <JhaError /> : null}

      {/* Loading state */}
      {isLoading ? <JhaLoading /> : null}

      {/* Empty state - when no JHAs are found and not loading */}
      {jhas && jhas.length === 0 && !isLoading && !error && (
        <JhaEmpty hasActiveFilters={activeFilterCount > 0} onResetFilters={handleResetFilters} />
      )}

      {/* Desktop Table View */}
      {!isMobile && jhas && jhas.length > 0 ? <JhaTable jhas={jhas} /> : null}

      {/* Mobile Card View */}
      {isMobile && jhas && jhas.length > 0 ? <JhaMobileView jhas={jhas} /> : null}

      {/* Load More Button for Infinite Scrolling */}
      {jhas && jhas.length > 0 && hasNextPage && (
        <div className="flex justify-center mt-6">
          <Button onClick={() => fetchNextPage()} disabled={isFetchingNextPage} variant="outline" className="px-6">
            {isFetchingNextPage ? 'Loading more...' : `Load More (${jhas.length} loaded)`}
          </Button>
        </div>
      )}

      {/* Hazard and Control Measures Intro Modal */}
      <HazardAndControlMeasuresIntroModal isOpen={showIntroModal} onClose={handleCloseIntroModal} />
    </div>
  );
}
