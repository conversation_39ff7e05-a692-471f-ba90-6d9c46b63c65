import { ArchiveConfirmationDialog } from '@/components/composite/archive-confirmation-dialog';
import { AsyncOshaLocationFilter } from '@/components/composite/async-osha-location-filter';
import { YearSelect } from '@/components/composite/year-select';
import { CasesSummary } from '@/components/osha-summary/cases-summary';
import { CompanyExecutiveCertificationDetails } from '@/components/osha-summary/company-executive-certification-details';
import { CompanyExecutiveCertificationForm } from '@/components/osha-summary/company-executive-certification-form';
import { EstablishmentInformationDetails } from '@/components/osha-summary/establishment-information-details';
import { EstablishmentInformationForm } from '@/components/osha-summary/establishment-information-form';
import { OshaLocationEmpty } from '@/components/osha-summary/osha-location-empty';
import { OshaSummaryConfirmationDialog } from '@/components/osha-summary/osha-summary-confirmation-dialog';
import { OshaSummaryLoading } from '@/components/osha-summary/osha-summary-loading';
import { OshaSummarySuccessDialog } from '@/components/osha-summary/osha-summary-success-dialog';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { useOshaSummaryUrlFilters } from '@/hooks/use-url-filters';
import { generateOshaSummaryReport } from '@/lib/download-osha-summary-report';
import { generateOshaSummaryPdf } from '@/lib/generate-osha-summary-pdf';
import { trpc } from '@/providers/trpc';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { Archive, Download, FileBarChart } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { useLocation } from 'wouter';

export default function OshaSummary() {
  const [_, navigate] = useLocation();

  const { filters, updateFilter } = useOshaSummaryUrlFilters();
  const [showArchiveConfirmDialog, setShowArchiveConfirmDialog] = useState(false);
  const [showOshaSummaryConfirmDialog, setShowOshaSummaryConfirmDialog] = useState(false);
  const [showOshaSummarySuccessDialog, setShowOshaSummarySuccessDialog] = useState(false);

  const [isEditingCompanyInformation, setIsEditingCompanyInformation] = useState(false);
  const [isEditingCompanyExecutiveCertification, setIsEditingCompanyExecutiveCertification] = useState(false);

  const {
    data: establishmentInfo,
    error: establishmentInfoError,
    isLoading: isEstablishmentInfoLoading,
  } = trpc.oshaSummary.getEstablishmentInformation.useQuery({
    year: filters.year,
    oshaLocationId: filters.oshaLocationId,
  });

  const {
    data: summary,
    error: summaryError,
    isLoading: isSummaryLoading,
  } = trpc.oshaSummary.getOshaCasesSummary.useQuery({
    year: filters.year,
    oshaLocationId: filters.oshaLocationId,
  });

  const isLoading = isEstablishmentInfoLoading || isSummaryLoading;

  useEffect(() => {
    if (establishmentInfoError?.data?.code === 'FORBIDDEN' || summaryError?.data?.code === 'FORBIDDEN') {
      navigate(ROUTES.NOT_FOUND);
    }
  }, [establishmentInfoError, summaryError]);

  const { mutateAsync: createOshaAuditTrail } = trpc.oshaAuditTrail.create.useMutation();

  const handleExportPDF = () => {
    if (!summary || !establishmentInfo) {
      toast.error('Failed to generate PDF', {
        description: 'Please try again later',
      });
      return;
    }

    generateOshaSummaryPdf({
      summary,
      establishmentInfo,
      year: filters.year,
    });
  };

  const onDownload = async () => {
    await createOshaAuditTrail({
      entityId: establishmentInfo!.id,
      entityType: 'osha_company_information',
      action: 'downloaded',
    });

    setShowOshaSummarySuccessDialog(true);
  };

  const onGenerateAndDownloadReport = async () => {
    setShowOshaSummaryConfirmDialog(false);

    if (!establishmentInfo || !summary) {
      toast.error('Failed to generate report', {
        description: 'Please try again later',
      });
      return;
    }

    generateOshaSummaryReport({
      establishmentInfo,
      summary,
      onDownload,
    });
  };

  if (isLoading) {
    return <OshaSummaryLoading />;
  }

  return (
    <div className="container mx-auto px-4 py-6 md:px-6">
      <ArchiveConfirmationDialog
        archived={!!establishmentInfo?.archivedAt}
        showDialog={showArchiveConfirmDialog}
        setShowDialog={setShowArchiveConfirmDialog}
        entityId={establishmentInfo?.id ?? ''}
        entityType="oshaSummary"
      />

      <OshaSummaryConfirmationDialog
        showDialog={showOshaSummaryConfirmDialog}
        setShowDialog={setShowOshaSummaryConfirmDialog}
        establishmentInfo={establishmentInfo}
        onGenerateAndDownloadReport={onGenerateAndDownloadReport}
      />

      <OshaSummarySuccessDialog
        showDialog={showOshaSummarySuccessDialog}
        setShowDialog={setShowOshaSummarySuccessDialog}
        establishmentInfo={establishmentInfo}
      />

      {establishmentInfo?.archivedAt && (
        <Alert variant="destructive" className="mb-6 border-red-200 bg-red-50">
          <AlertTitle>
            <div className="flex items-center gap-2">
              <Archive className="h-4 w-4 text-red-600" />
              <span>ARCHIVED - This year's OSHA logs are archived and read-only</span>
            </div>
          </AlertTitle>
          <AlertDescription>
            These records cannot be modified. To edit current year data, please navigate back to the main view.
            <Button variant="outline" size="sm" onClick={() => setShowArchiveConfirmDialog(true)} className="mt-1">
              Restore Year
            </Button>
          </AlertDescription>
        </Alert>
      )}
      <div className="flex items-center">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">OSHA Summary</h1>
          <p className="text-muted-foreground mt-1 text-sm">
            OSHA Form 300A Summary of Work-Related Injuries and Illnesses
          </p>
        </div>
      </div>

      {/* Controls Section */}
      <div className="flex flex-col gap-2 py-6 md:sticky md:top-0 md:z-10 md:bg-gray-50 lg:flex-row lg:items-center lg:justify-between">
        <div className="flex flex-col gap-2 sm:flex-row sm:items-center">
          <AsyncOshaLocationFilter
            selected={filters.oshaLocationId}
            onSelect={(oshaLocationId) => updateFilter('oshaLocationId', oshaLocationId)}
          />
          {/* Year Filter */}
          <YearSelect
            value={filters.year}
            onChange={(year) => updateFilter('year', year)}
            className="w-full md:w-auto"
          />
        </div>

        {/* Action Buttons */}

        {/* Secondary Actions */}
        <div className="flex flex-col gap-2 sm:flex-row">
          {!establishmentInfo?.archivedAt ? (
            <Button
              size="sm"
              variant="outline"
              disabled={!establishmentInfo?.id}
              onClick={() => setShowArchiveConfirmDialog(true)}
              className="w-full border-gray-300 font-medium hover:bg-gray-50 sm:w-auto"
            >
              <Archive className="h-4 w-4" />
              Archive Year
            </Button>
          ) : (
            <Button
              size="sm"
              variant="outline"
              onClick={() => setShowArchiveConfirmDialog(true)}
              disabled={!establishmentInfo?.id}
              className="w-full border-gray-300 font-medium hover:bg-gray-50 sm:w-auto"
            >
              <Archive className="h-4 w-4" />
              Restore Year
            </Button>
          )}

          {filters.oshaLocationId && (
            <>
              <Button
                size="sm"
                variant="outline"
                onClick={handleExportPDF}
                disabled={!establishmentInfo?.id}
                className="w-full border-gray-300 font-medium hover:bg-gray-50 sm:w-auto"
              >
                <Download className="h-4 w-4" />
                Download PDF
              </Button>
              {/* Primary Action - Submit OSHA Report */}

              <Button
                size="sm"
                onClick={() => setShowOshaSummaryConfirmDialog(true)}
                className="w-full sm:w-auto"
                disabled={!establishmentInfo?.id}
              >
                <FileBarChart className="h-4 w-4" />
                Submit OSHA Report
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Content Grid */}
      <div className="mt-8 space-y-4 lg:space-y-6">
        {!filters.oshaLocationId ? (
          <OshaLocationEmpty />
        ) : (
          <>
            {/* Top Row - Company Info and Cases Summary */}
            <div className="grid grid-cols-1 gap-2 lg:gap-6 xl:grid-cols-2">
              {/* Establishment Information Card */}
              <div>
                {isEditingCompanyInformation ? (
                  <EstablishmentInformationForm
                    filters={filters}
                    establishmentInfo={establishmentInfo}
                    onCancel={() => setIsEditingCompanyInformation(false)}
                    onSave={() => setIsEditingCompanyInformation(false)}
                  />
                ) : (
                  <EstablishmentInformationDetails
                    establishmentInfo={establishmentInfo}
                    onEdit={() => setIsEditingCompanyInformation(true)}
                  />
                )}
              </div>

              {/* Cases Summary Card */}
              <div>
                <CasesSummary summary={summary} year={filters.year} />
              </div>

              {/* Certification Section - Full width */}
              <div>
                {isEditingCompanyExecutiveCertification ? (
                  <CompanyExecutiveCertificationForm
                    onSave={() => setIsEditingCompanyExecutiveCertification(false)}
                    onCancel={() => setIsEditingCompanyExecutiveCertification(false)}
                    establishmentInfo={establishmentInfo}
                  />
                ) : (
                  <CompanyExecutiveCertificationDetails
                    onEdit={() => setIsEditingCompanyExecutiveCertification(true)}
                    establishmentInfo={establishmentInfo}
                  />
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
