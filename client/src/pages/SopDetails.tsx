import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { ArchiveConfirmationDialog } from '@/components/composite/archive-confirmation-dialog';
import { ArchivedBadge } from '@/components/composite/archived-badge';
import { Back } from '@/components/composite/back';
import { SlugBadge } from '@/components/composite/slug-badge';
import { SopStatusBadge } from '@/components/composite/sop-status-badge';
import { Timeline } from '@/components/composite/timeline';
import { SopConfirmationDialog } from '@/components/sop/details/sop-confirmation-dialog';
import { SopDetailsError } from '@/components/sop/details/sop-details-error';
import { SopDetailsLoading } from '@/components/sop/details/sop-details-loading';
import { SopVersionHistory } from '@/components/sop/details/sop-version-history';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useAppContext } from '@/contexts/app-context';
import { useAnalytics } from '@/hooks/use-analytics';
import { usePermissions } from '@/hooks/use-permissions';
import { generateSopReportPdf } from '@/lib/generate-sop-report-pdf';
import { trpc } from '@/providers/trpc';
import { formatDate } from '@shared/date-utils';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { approvalStatusEnum } from '@shared/schema';
import { CONTROL_MEASURE_TYPE_MAP } from '@shared/types/jha.types';
import { CATEGORY_MAP } from '@shared/types/schema.types';
import { SOP_SEVERITY_LEVELS } from '@shared/types/sop.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import {
  AlertTriangle,
  Archive,
  ArrowLeft,
  Calendar,
  Check,
  Clock,
  Download,
  Edit,
  History,
  MapPin,
  MoreVertical,
  Send,
  Shield,
  User,
  X,
} from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { toast } from 'sonner';
import { useLocation, useSearchParams } from 'wouter';

export default function SopDetails({ params }: { params: { id: string } }) {
  const { user } = useAppContext();
  const { hasPermission } = usePermissions();
  const analytics = useAnalytics();
  const [, navigate] = useLocation();
  const [searchParams] = useSearchParams();

  const sopInstanceId = params.id;
  const versionId = searchParams.get('version');

  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const [showStatusConfirm, setShowStatusConfirm] = useState(false);
  const [statusAction, setStatusAction] = useState<(typeof approvalStatusEnum.enumValues)[number] | null>(null);

  // Fetch SOP by instance id (route param is the SOP instance id)
  const { data: sop, isLoading, error } = trpc.sop.getByInstanceId.useQuery({ id: sopInstanceId, versionId });

  const isArchived = Boolean(sop?.archivedAt);
  const isViewingOlderVersion = !!versionId;

  useEffect(() => {
    if (error?.data?.code === 'FORBIDDEN') {
      navigate(ROUTES.NOT_FOUND);
    }
  }, [error]);

  useEffect(() => {
    if (sop && !isLoading) {
      analytics.track(ANALYTICS_EVENTS.SOP.DETAIL_VIEW_OPENED, {
        sop_id: sop.id,
        instance_id: sop.instanceId!,
        current_status: sop.status,
        version: sop.version,
      });
    }
  }, [sop?.id, sop?.instanceId, sop?.status, sop?.version, sop?.highestSeverity, versionId, isLoading]);

  const sectionGroups = useMemo(() => {
    if (!sop?.sections || !Array.isArray(sop?.sections))
      return {
        general: [],
        emergency: [],
        step: [],
        pre_procedure: [],
        procedure: [],
        post_procedure: [],
      };
    return sop.sections.reduce(
      (acc, section) => {
        const type = section.sectionType;

        if (!acc[type]) {
          acc[type] = [];
        }

        acc[type].push(section);

        return acc;
      },
      {} as Record<string, typeof sop.sections>,
    );
  }, [sop?.sections]);

  const {
    general: generalSections = [],
    emergency: emergencySections = [],
    step: stepSections = [],
    pre_procedure: preSections = [],
    procedure: procedureSections = [],
    post_procedure: postSections = [],
  } = sectionGroups;

  const rolePermissions = useMemo(() => {
    if (!user || !sop) return { isApprover: false, isOwner: false, canEdit: false };

    return {
      isApprover: user.id === sop.approverId,
      isOwner: user.id === sop.ownerId,
      canEdit: hasPermission(MODULES.SOP, ALLOWED_ACTIONS.EDIT, sop.createdBy),
    };
  }, [user?.id, sop?.approverId, sop?.ownerId, sop?.createdBy, hasPermission]);

  const { isApprover, isOwner, canEdit } = rolePermissions;


  // Action handlers
  const handleApprove = useCallback(() => {
    analytics.track(ANALYTICS_EVENTS.SOP.APPROVED, {
      sop_id: sop?.id,
      instance_id: sop?.instanceId!,
      approver_id: sop?.approverId,
      version: sop?.version,
    });
    setStatusAction(approvalStatusEnum.enumValues[2]);
    setShowStatusConfirm(true);
  }, [sop?.id, sop?.instanceId, sop?.version]);

  const handleReject = useCallback(() => {
    analytics.track(ANALYTICS_EVENTS.SOP.REJECTED, {
      sop_id: sop?.id,
      instance_id: sop?.instanceId!,
      approver_id: sop?.approverId,
      version: sop?.version,
    });
    setStatusAction(approvalStatusEnum.enumValues[0]);
    setShowStatusConfirm(true);
  }, [sop?.id, sop?.instanceId, sop?.version]);

  const handleSubmitForReview = useCallback(() => {
    analytics.track(ANALYTICS_EVENTS.SOP.REVIEW_REQUESTED, {
      sop_id: sop?.id,
      instance_id: sop?.instanceId!,
      version: sop?.version,
    });
    setStatusAction(approvalStatusEnum.enumValues[1]);
    setShowStatusConfirm(true);
  }, [sop?.id, sop?.instanceId, sop?.version]);

  const handleInitiateRevision = useCallback(() => {
    analytics.track(ANALYTICS_EVENTS.SOP.REVISION_REQUESTED, {
      sop_id: sop?.id,
      instance_id: sop?.instanceId!,
      version: sop?.version,
    });
    sop?.id ? navigate(ROUTES.BUILD_SOP_EDIT_PATH(sop.instanceId!)) : null;
  }, [sop?.id, sop?.instanceId, sop?.version]);

  const handleArchiveClick = useCallback(() => {
    analytics.track(ANALYTICS_EVENTS.SOP.ARCHIVED, {
      sop_id: sop?.id,
      instance_id: sop?.instanceId!,
      version: sop?.version,
    });
    setShowArchiveConfirm(true);
  }, [sop?.id, sop?.instanceId, sop?.version]);

  const handleDownloadPdf = useCallback(() => {
    if (!sop) {
      toast.error('Something went wrong', {
        description: 'Please try again',
      });
      return;
    }
    analytics.track(ANALYTICS_EVENTS.SOP.PRINT_TRIGGERED, {
      sop_id: sop?.id,
      instance_id: sop?.instanceId!,
      version: sop?.version,
    });

    generateSopReportPdf(sop);
  }, [sop]);

  if (isLoading || !sop) {
    return <SopDetailsLoading />;
  }

  if (error) {
    return <SopDetailsError />;
  }

  return (
    <div className="container mx-auto py-4 px-4">
      {/* Historical Version Alert */}
      {isViewingOlderVersion && (
        <Alert className="mb-4 bg-amber-50 border-amber-200">
          <History className="h-4 w-4" />
          <AlertDescription>
            Viewing Historical Version {sop.version} - Read Only
            <br />
            This is a historical version from {formatDate(sop.createdAt)} by {sop.owner?.fullName}. No editing is
            available.
            <Button
              className="justifyEnd"
              variant="outline"
              size="sm"
              onClick={() => navigate(ROUTES.BUILD_SOP_DETAILS_PATH(sopInstanceId))}
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back to Latest Version</span>
            </Button>
          </AlertDescription>
        </Alert>
      )}

      <>
        {/* SOP Header */}
        <div className="flex flex-col md:flex-row justify-between items-start">
          {/* Desktop View */}
          <div className="hidden md:block w-full">
            <div className="flex flex-wrap items-center gap-2 mb-2">
              <Back />
              {sop.slug && <SlugBadge slug={sop.slug} />}
              <SopStatusBadge status={sop.status} />
              <Badge variant="outline" className="bg-slate-50 text-slate-700 border-slate-200">
                v{sop.version}
              </Badge>
              {isArchived && <ArchivedBadge />}
            </div>
            <h1 className="text-2xl md:text-3xl font-bold mb-2">{sop.title}</h1>
          </div>

          {/* Mobile View - with menu in title area */}
          <div className="md:hidden w-full">
            <div className="flex flex-wrap items-center gap-2 mb-2">
              <Back />
              {sop.slug && <SlugBadge slug={sop.slug} />}
              <SopStatusBadge status={sop.status} />
              <Badge className="capitalize bg-slate-50 text-slate-700 border border-slate-200" variant="outline">
                v{sop.version}
              </Badge>
              {isArchived && <ArchivedBadge />}
            </div>

            {/* Mobile title row with menu */}
            <div className="flex items-center justify-between">
              <h1 className="text-2xl font-bold mb-2 pr-3 flex-1">{sop.title}</h1>

              <div className="flex items-center gap-2">
                {/* Mobile menu in the title row */}
                {hasPermission(MODULES.SOP, ALLOWED_ACTIONS.EDIT) && !isViewingOlderVersion && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-9 w-9 p-0 flex items-center justify-center"
                        aria-label="Open actions menu"
                      >
                        <MoreVertical className="h-5 w-5" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-56">
                      <DropdownMenuLabel>SOP Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />

                      {/* Submit for Review - for owners of draft SOPs */}
                      {(canEdit || isOwner) && sop.status === approvalStatusEnum.enumValues[0] && (
                        <DropdownMenuItem className="cursor-pointer" onClick={handleSubmitForReview}>
                          <Send className="mr-2 h-4 w-4" />
                          <span>Submit for Review</span>
                        </DropdownMenuItem>
                      )}

                      {/* Approve / Reject buttons - for approvers */}
                      {isApprover && sop.status === approvalStatusEnum.enumValues[1] && (
                        <>
                          <DropdownMenuItem className="cursor-pointer" onClick={handleApprove}>
                            <Check className="mr-2 h-4 w-4" />
                            <span>Approve</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem className="cursor-pointer" onClick={handleReject}>
                            <X className="mr-2 h-4 w-4" />
                            <span>Reject</span>
                          </DropdownMenuItem>
                        </>
                      )}

                      {/* Edit - for draft SOPs */}
                      {sop.status === approvalStatusEnum.enumValues[0] && (
                        <DropdownMenuItem
                          className="cursor-pointer"
                          onClick={() => navigate(ROUTES.BUILD_SOP_EDIT_PATH(sop.instanceId!))}
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          <span>Edit SOP</span>
                        </DropdownMenuItem>
                      )}

                      {/* Initiate Revision - for owners of approved SOPs */}
                      {(canEdit || isOwner) && sop.status === approvalStatusEnum.enumValues[2] && (
                        <DropdownMenuItem className="cursor-pointer" onClick={handleInitiateRevision}>
                          <History className="mr-2 h-4 w-4" />
                          <span>Initiate Revision</span>
                        </DropdownMenuItem>
                      )}

                      <SopVersionHistory sopInstanceId={sop.instanceId ?? ''} sopTitle={sop.title} />

                      {/* PDF Generation for approved SOPs */}
                      {sop.status === approvalStatusEnum.enumValues[2] && (
                        <DropdownMenuItem className="cursor-pointer" onClick={handleDownloadPdf}>
                          <Download className="mr-2 h-4 w-4" />
                          <span>Generate PDF Report</span>
                        </DropdownMenuItem>
                      )}

                      <DropdownMenuSeparator />

                      <DropdownMenuItem className="cursor-pointer" onClick={handleArchiveClick}>
                        <Archive className="mr-2 h-4 w-4" />
                        <span>{isArchived ? 'Unarchive SOP' : 'Archive SOP'}</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </div>
            </div>
          </div>

          {/* Desktop buttons - only shown on desktop */}
          {!isViewingOlderVersion && (
            <div className="hidden md:flex gap-2 self-start">
              {hasPermission(MODULES.SOP, ALLOWED_ACTIONS.EDIT) &&
                isApprover &&
                sop.status === approvalStatusEnum.enumValues[1] && (
                  <>
                    <Button
                      size="sm"
                      onClick={handleApprove}
                      variant="outline"
                      className="bg-emerald-100 hover:bg-emerald-200"
                    >
                      <Check className="h-4 w-4" />
                      <span>Approve</span>
                    </Button>
                    <Button size="sm" onClick={handleReject} variant="outline" className="bg-red-100 hover:bg-red-200">
                      <X className="h-4 w-4" />
                      <span>Reject</span>
                    </Button>
                  </>
                )}

              {(hasPermission(MODULES.SOP, ALLOWED_ACTIONS.EDIT, sop.createdBy) || isOwner) &&
                sop.status === approvalStatusEnum.enumValues[0] && (
                  <Button size="sm" onClick={handleSubmitForReview}>
                    <Send className="h-4 w-4" />
                    <span>Submit for Review</span>
                  </Button>
                )}

              {hasPermission(MODULES.SOP, ALLOWED_ACTIONS.EDIT, sop.createdBy) &&
                sop.status === approvalStatusEnum.enumValues[0] && (
                  <Button size="sm" onClick={() => navigate(ROUTES.BUILD_SOP_EDIT_PATH(sop.instanceId!))}>
                    <Edit className="h-4 w-4" />
                    <span>Edit</span>
                  </Button>
                )}

              {(hasPermission(MODULES.SOP, ALLOWED_ACTIONS.EDIT, sop.createdBy) || isOwner) &&
                sop.status === approvalStatusEnum.enumValues[2] && (
                  <Button size="sm" onClick={handleInitiateRevision}>
                    <History className="h-4 w-4" />
                    <span>Initiate Revision</span>
                  </Button>
                )}

              {/* Version History Button - always visible */}
              <SopVersionHistory sopInstanceId={sop.instanceId ?? ''} sopTitle={sop.title} />

              {/* Desktop-only Options Menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="flex items-center gap-1">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Options</DropdownMenuLabel>
                  {sop.status === approvalStatusEnum.enumValues[2] && (
                    <DropdownMenuItem onClick={handleDownloadPdf}>
                      <Download className="mr-2 h-4 w-4" />
                      <span>Generate PDF Report</span>
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuSeparator />

                  {hasPermission(MODULES.SOP, ALLOWED_ACTIONS.EDIT) && (
                    <DropdownMenuItem onClick={handleArchiveClick}>
                      <Archive className="mr-2 h-4 w-4" />
                      <span>{isArchived ? 'Unarchive SOP' : 'Archive SOP'}</span>
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}
        </div>

        {/* Context bar with metadata */}
        <div className="flex flex-wrap items-center text-sm text-muted-foreground mb-4 gap-y-2">
          <div className="flex items-center">
            <User className="h-4 w-4 mr-1.5" />
            <span>Owner: {sop.owner?.fullName || 'Not assigned'}</span>
          </div>

          <div className="hidden sm:block text-muted-foreground mx-2">•</div>

          <div className="flex items-center">
            <User className="h-4 w-4 mr-1.5" />
            <span>Approver: {sop.approver?.fullName || 'Not assigned'}</span>
          </div>

          <div className="hidden sm:block text-muted-foreground mx-2">•</div>

          <div className="flex items-center">
            <Clock className="h-4 w-4 mr-1.5" />
            <span>Last Reviewed: {sop.updatedAt ? formatDate(sop.updatedAt, true) : 'Unknown'}</span>
          </div>

          {sop.reviewDate && (
            <>
              <div className="hidden sm:block text-muted-foreground mx-2">•</div>
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-1.5" />
                <span>Review Date: {formatDate(sop.reviewDate, true)}</span>
              </div>
            </>
          )}
        </div>

        {/* Main content grid */}
        <div
          className={`grid grid-cols-1 md:grid-cols-3 gap-4 ${
            sop.archivedAt ? 'p-3 bg-amber-50/30 border border-amber-200 rounded-lg' : ''
          }`}
        >
          {/* Left column - main content */}
          <div className="md:col-span-2 space-y-4">
            {/* Procedure Content */}
            <Card className="overflow-hidden">
              <CardHeader className="bg-gray-50 border-b">
                <CardTitle>Procedure Content</CardTitle>
              </CardHeader>
              <CardContent>
                {/* General Information */}
                {(sop.purpose || sop.responsibilities || generalSections.length > 0) && (
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-base font-semibold text-gray-900 mb-4">General Information</h3>
                      <div className="space-y-3">
                        {/* Purpose */}
                        {sop.purpose && (
                          <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                            <h4 className="font-medium text-gray-800 mb-2">Purpose</h4>
                            <p className="text-gray-700 text-sm leading-relaxed whitespace-pre-line">{sop.purpose}</p>
                          </div>
                        )}
                        
                        {/* Responsibilities */}
                        {sop.responsibilities && (
                          <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                            <h4 className="font-medium text-gray-800 mb-2">Responsibilities</h4>
                            <p className="text-gray-700 text-sm leading-relaxed whitespace-pre-line">{sop.responsibilities}</p>
                          </div>
                        )}
                        
                        {/* Other general sections */}
                        {generalSections.map((section) => (
                          <div key={section.id} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                            {section.label && <h4 className="font-medium text-gray-800 mb-2">{section.label}</h4>}
                            <p className="text-gray-700 text-sm leading-relaxed whitespace-pre-line">{section.value}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Hazards & Controls */}
                {stepSections.length > 0 && (
                  <div className="mt-6 space-y-6">
                    <h3 className="text-base font-semibold text-gray-900">Hazards & Controls</h3>
                    <div className="space-y-4">
                      {stepSections.map((section, idx) => {
                        const severityMeta = SOP_SEVERITY_LEVELS.find((s) => s.value === (section.severity ?? 0));
                        const riskScore = (section.severity ?? 0) * (section.likelihood ?? 0);
                        return (
                          <div key={section.id} className="border border-gray-200 rounded-lg overflow-hidden">
                            <div className="bg-yellow-50 border-b border-yellow-200 px-4 py-3">
                              <div className="flex items-center gap-2">
                                <AlertTriangle className="h-4 w-4 text-yellow-600" />
                                <h4 className="font-medium text-gray-900">{section.label || `Hazard #${idx + 1}`}</h4>
                              </div>
                            </div>
                            <div className="p-4 bg-white">
                              <div className="space-y-4">
                                {/* Hazard Description */}
                                <div>
                                  <label className="text-xs font-medium text-gray-600 uppercase tracking-wider">
                                    Hazard Description
                                  </label>
                                  <p className="mt-1 text-sm text-gray-900">{section.value}</p>
                                </div>

                                {/* Risk Assessment Grid */}
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  {/* Hazard Analysis */}
                                  <div>
                                    <div className="flex items-center gap-2 mb-2">
                                      <AlertTriangle className="h-4 w-4 text-red-600" />
                                      <h4 className="font-medium text-sm">Identified Hazards</h4>
                                    </div>

                                    {section.hazards && section.hazards.length > 0 ? (
                                      <div className="space-y-2">
                                        {section.hazards.map((hazard) => (
                                          <div key={hazard.id} className="flex items-center gap-2">
                                            <div className="w-2 h-2 bg-red-500 rounded-full flex-shrink-0"></div>
                                            <span className="text-sm">
                                              {hazard.name} ({CATEGORY_MAP[hazard.type as keyof typeof CATEGORY_MAP]})
                                            </span>
                                          </div>
                                        ))}

                                        {(section.severity || section.likelihood) && (
                                          <div className="flex sm:flex-col md:flex-row text-xs text-muted-foreground gap-1 mt-2">
                                            {section.severity && <div>Severity: {section.severity}/5</div>}
                                            {section.severity && section.likelihood && <span> • </span>}
                                            {section.likelihood && <div>Likelihood: {section.likelihood}/5</div>}
                                            {riskScore > 0 && (
                                              <>
                                                <span> • </span>
                                                <div>Risk Score: {riskScore}</div>
                                              </>
                                            )}
                                          </div>
                                        )}
                                      </div>
                                    ) : (
                                      <p className="text-sm text-muted-foreground">No specific hazards identified</p>
                                    )}

                                    {(!section.hazards || section.hazards.length === 0) && (section.severity || section.likelihood) && (
                                      <div className="mt-2">
                                        <div className="text-xs font-medium text-gray-600 uppercase tracking-wider mb-1">
                                          Risk Level
                                        </div>
                                        <p className="text-sm font-medium text-gray-900">
                                          {severityMeta?.label || 'Not specified'}
                                        </p>
                                        <div className="flex sm:flex-col md:flex-row text-xs text-muted-foreground gap-1 mt-1">
                                          {section.severity && <div>Severity: {section.severity}/5</div>}
                                          {section.severity && section.likelihood && <span> • </span>}
                                          {section.likelihood && <div>Likelihood: {section.likelihood}/5</div>}
                                          {riskScore > 0 && (
                                            <>
                                              <span> • </span>
                                              <div>Risk Score: {riskScore}</div>
                                            </>
                                          )}
                                        </div>
                                      </div>
                                    )}
                                  </div>

                                  {/* Control Measures */}
                                  <div>
                                    <div className="flex items-center gap-2 mb-2">
                                      <Shield className="h-4 w-4 text-green-600" />
                                      <h4 className="font-medium text-sm">Control Measures</h4>
                                    </div>

                                    {section.controlMeasures && section.controlMeasures.length > 0 ? (
                                      <div className="space-y-2">
                                        {section.controlMeasures.map((controlMeasure) => (
                                          <div key={controlMeasure.id} className="flex items-center gap-2">
                                            <div className="w-2 h-2 bg-green-500 rounded-full flex-shrink-0"></div>
                                            <span className="text-sm">
                                              {controlMeasure.name} ({CONTROL_MEASURE_TYPE_MAP[controlMeasure.type as keyof typeof CONTROL_MEASURE_TYPE_MAP]})
                                            </span>
                                          </div>
                                        ))}
                                      </div>
                                    ) : (
                                      <p className="text-sm text-muted-foreground">No control measures defined</p>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}

                {/* Step-by-Step Procedure */}
                {(preSections.length > 0 || procedureSections.length > 0 || postSections.length > 0) && (
                  <div className="mt-8 space-y-6">
                    <h3 className="text-base font-semibold text-gray-900">Step-by-Step Procedure</h3>

                    {/* Pre-Requisites */}
                    {preSections.length > 0 && (
                      <div className="rounded-lg border border-gray-200 overflow-hidden">
                        <div className="bg-gradient-to-r from-emerald-500 to-emerald-600 px-4 py-3">
                          <h4 className="font-medium text-white flex items-center gap-2">Pre-Requisites</h4>
                        </div>
                        <div className="p-4 bg-white">
                          <ul className="space-y-2">
                            {preSections.map((section) => (
                              <li key={section.id} className="flex items-start gap-3">
                                <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                                  <div className="w-2.5 h-2.5 bg-green-600 rounded-full"></div>
                                </div>
                                <span className="text-sm text-gray-700 leading-relaxed">{section.value}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    )}

                    {/* Procedure Steps */}
                    {procedureSections.length > 0 && (
                      <div className="rounded-lg border border-gray-200 overflow-hidden">
                        <div className="bg-gradient-to-r from-blue-500 to-blue-600 px-4 py-3">
                          <h4 className="font-medium text-white">Procedure Steps</h4>
                        </div>
                        <div className="p-4 bg-white">
                          <div className="space-y-4">
                            {procedureSections.map((section, idx) => (
                              <div key={section.id} className="relative flex items-start gap-3">
                                {idx < procedureSections.length - 1 && (
                                  <div className="absolute left-4.5 top-10 w-0.5 h-full bg-gray-200"></div>
                                )}

                                <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-full flex items-center justify-center text-base font-bold shadow-lg">
                                  {idx + 1}
                                </div>
                                <div className="flex-1 bg-gray-50 rounded-lg p-3">
                                  <p className="text-sm text-gray-700 leading-relaxed whitespace-pre-wrap">
                                    {section.value}
                                  </p>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Post-Procedure Actions */}
                    {postSections.length > 0 && (
                      <div className="rounded-lg border border-gray-200 overflow-hidden">
                        <div className="bg-gradient-to-r from-purple-500 to-purple-600 px-4 py-3">
                          <h4 className="font-medium text-white">Post-Procedure Actions</h4>
                        </div>
                        <div className="p-4 bg-white">
                          <ul className="space-y-2">
                            {postSections.map((section) => (
                              <li key={section.id} className="flex items-start gap-3">
                                <div className="flex-shrink-0 w-6 h-6 bg-purple-100 text-purple-700 rounded-full flex items-center justify-center mt-0.5">
                                  <div className="w-2.5 h-2.5 bg-purple-600 rounded-full"></div>
                                </div>
                                <span className="text-sm text-gray-700 leading-relaxed">{section.value}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* Emergency Plans */}
                {emergencySections.length > 0 && (
                  <div className="mt-6 space-y-6">
                    <h3 className="text-base font-semibold text-gray-900 mb-4">Emergency Plans</h3>
                    <div className="space-y-3">
                      {emergencySections.map((section) => (
                        <div key={section.id} className="bg-red-50 rounded-lg p-4 border border-red-200">
                          {section.label && <h4 className="font-medium text-red-800 mb-2">{section.label}</h4>}
                          <p className="text-red-900 text-sm leading-relaxed whitespace-pre-line">{section.value}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>


            {/* Additional Notes */}
            {sop.notes && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Additional Notes & Considerations</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-gray-700 whitespace-pre-wrap leading-relaxed">{sop.notes}</div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Right column - metadata sidebar */}
          <div className="space-y-4">
            {/* Risk Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4" />
                  Risk Summary
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <div className="text-sm text-gray-500 mb-1">Highest Risk Score</div>
                    <div className="font-semibold text-2xl">{sop.highestSeverity || 0}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500 mb-1">Total Hazards</div>
                    <div className="font-semibold text-gray-900">{stepSections.length} hazards</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500 mb-1">Procedure Steps</div>
                    <div className="font-semibold text-gray-900">{procedureSections.length} steps</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Location and Assets */}
            {(sop.location || (sop.assets && sop.assets.length > 0)) && (
              <Card>
                <CardHeader>
                  <CardTitle>Location and Assets</CardTitle>
                </CardHeader>
                <CardContent>
                  {sop.location && (
                    <div className="flex flex-col gap-2 mb-4">
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-gray-500" />
                        <span>Location: {sop.location.name}</span>
                      </div>
                    </div>
                  )}
                  {sop.assets && sop.assets.length > 0 && (
                    <div className="flex flex-col gap-2">
                      <div className="flex flex-wrap items-center gap-2">
                        {sop.assets.map((asset) => (
                          <Badge variant="outline" key={asset.id}>
                            {asset.name}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Timeline */}
            <Timeline entityId={sop.id} entityType="sop" />
          </div>
        </div>
      </>

      <ArchiveConfirmationDialog
        archived={!!sop?.archivedAt}
        showDialog={showArchiveConfirm}
        setShowDialog={setShowArchiveConfirm}
        entityId={sopInstanceId}
        entityType="sop"
      />
      {statusAction && (
        <SopConfirmationDialog
          showDialog={showStatusConfirm}
          setShowDialog={setShowStatusConfirm}
          sopId={sop.id}
          action={statusAction}
        />
      )}
    </div>
  );
}
