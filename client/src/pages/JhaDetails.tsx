import { ArchiveConfirmationDialog } from '@/components/composite/archive-confirmation-dialog';
import { ArchivedBadge } from '@/components/composite/archived-badge';
import { Back } from '@/components/composite/back';
import { JhaStatusBadge } from '@/components/composite/jha-status-badge';
import { SlugBadge } from '@/components/composite/slug-badge';
import { Timeline } from '@/components/composite/timeline';
import { JhaConfirmationDialog } from '@/components/jha/details/jha-confirmation-dialog';
import { JhaDetailsError } from '@/components/jha/details/jha-details-error';
import { JhaDetailsLoading } from '@/components/jha/details/jha-details-loading';
import { JhaVersionHistory } from '@/components/jha/details/jha-version-history';
import { JhaRiskLevelBadge, JhaRiskScoreBadge } from '@/components/jha/jha-risk-badge';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useAppContext } from '@/contexts/app-context';
import { usePermissions } from '@/hooks/use-permissions';
import { generateJhaReportPdf } from '@/lib/generate-jha-report-pdf';
import { trpc } from '@/providers/trpc';
import { formatDate } from '@shared/date-utils';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { approvalStatusEnum } from '@shared/schema';
import { CONTROL_MEASURE_TYPE_MAP } from '@shared/types/jha.types';
import { CATEGORY_MAP } from '@shared/types/schema.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import {
  AlertTriangle,
  Archive,
  ArrowLeft,
  Calendar,
  Check,
  ChevronsDownUp,
  ChevronsUpDown,
  Clock,
  Download,
  Edit,
  History,
  MapPin,
  MoreVertical,
  Send,
  Settings,
  Shield,
  User,
  X,
} from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { toast } from 'sonner';
import { useLocation, useSearchParams } from 'wouter';

export default function JhaDetails({ params }: { params: { id: string } }) {
  const { user } = useAppContext();
  const jhaInstanceId = params.id;
  const [searchParams] = useSearchParams();
  const versionId = searchParams.get('version');
  const [_, navigate] = useLocation();
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const [accordionValue, setAccordionValue] = useState<string[]>([]);
  const { hasPermission } = usePermissions();

  const {
    data: jha,
    isLoading,
    error,
  } = trpc.jha.getByInstanceId.useQuery({
    id: jhaInstanceId,
    versionId,
  });

  const [showStatusConfirm, setShowStatusConfirm] = useState(false);
  const [statusAction, setStatusAction] = useState<(typeof approvalStatusEnum.enumValues)[number] | null>(null);

  const isApprover = useMemo(() => user?.id === jha?.approverId, [user, jha?.approverId]);
  const isOwner = useMemo(() => user?.id === jha?.ownerId, [user, jha?.ownerId]);

  const isViewingOlderVersion = !!versionId;

  useEffect(() => {
    if (error?.data?.code === 'FORBIDDEN') {
      navigate(ROUTES.NOT_FOUND);
    }
  }, [error]);

  // Helper function to track edit initiated
  const handleEditClick = () => {
    navigate(ROUTES.BUILD_JHA_EDIT_PATH(jhaInstanceId));
  };

  // Handle collapse all
  const handleCollapseAll = useCallback(() => {
    setAccordionValue([]);
  }, []);

  // Handle expand all
  const handleExpandAll = useCallback(() => {
    if (jha?.steps) {
      const allStepIds = jha.steps.map((step) => `step-${step.id}`);
      setAccordionValue(allStepIds);
    }
  }, [jha?.steps]);

  const handleSubmitForReview = useCallback(() => {
    if (!jha) {
      toast.error('Something went wrong', {
        description: 'Please try again',
      });
      return;
    }
    setStatusAction(approvalStatusEnum.enumValues[1]);
    setShowStatusConfirm(true);
  }, [jha?.id]);

  const handleApprove = useCallback(() => {
    if (!jha) {
      toast.error('Something went wrong', {
        description: 'Please try again',
      });
      return;
    }
    setStatusAction(approvalStatusEnum.enumValues[2]);
    setShowStatusConfirm(true);
  }, [jha?.id]);

  const handleReject = useCallback(() => {
    if (!jha) {
      toast.error('Something went wrong', {
        description: 'Please try again',
      });
      return;
    }
    setStatusAction(approvalStatusEnum.enumValues[0]);
    setShowStatusConfirm(true);
  }, [jha?.id]);

  const handleInitiateRevision = useCallback(() => {
    if (!jha) {
      toast.error('Something went wrong', {
        description: 'Please try again',
      });
      return;
    }
    navigate(ROUTES.BUILD_JHA_EDIT_PATH(jhaInstanceId));
  }, [jha?.id]);

  const handleDownloadPdf = useCallback(() => {
    if (!jha) return;
    generateJhaReportPdf(jha);
  }, [jha?.id]);

  if (isLoading) {
    return <JhaDetailsLoading />;
  }

  if (error || !jha) {
    return <JhaDetailsError />;
  }

  return (
    <div className="container mx-auto py-4 px-4">
      {/* Historical Version Alert */}
      {isViewingOlderVersion && (
        <Alert className="mb-4 bg-amber-50 border-amber-200">
          <History className="h-4 w-4" />
          <AlertDescription>
            Viewing Historical Version {jha.version} - Read Only
            <br />
            This is a historical version from {formatDate(jha.createdAt)} by {jha.owner?.fullName}. No editing is
            available.
            <Button
              className="justifyEnd"
              variant="outline"
              size="sm"
              onClick={() => navigate(ROUTES.BUILD_JHA_DETAILS_PATH(jhaInstanceId))}
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back to Latest Version</span>
            </Button>
          </AlertDescription>
        </Alert>
      )}

      <>
        {/* JHA Header */}
        <div className="flex flex-col md:flex-row justify-between items-start">
          {/* Desktop View */}
          <div className="hidden md:block w-full">
            <div className="flex flex-wrap items-center gap-2 mb-2">
              <Back />
              <SlugBadge slug={jha.slug} />
              <JhaStatusBadge status={jha.status} />
              <Badge variant="outline">Version {jha.version}</Badge>
              {jha.archivedAt && <ArchivedBadge />}
            </div>
            <h1 className="text-2xl md:text-3xl font-bold mb-2">{jha.title}</h1>
          </div>

          {/* Mobile View - with menu in title area */}
          <div className="md:hidden w-full">
            <div className="flex flex-wrap items-center gap-2 mb-2">
              <Back />
              <SlugBadge slug={jha.slug} />
              <JhaStatusBadge status={jha.status} />
              <Badge className="capitalize bg-slate-50 text-slate-700 border border-slate-200" variant="outline">
                v{jha.version}
              </Badge>
              {jha.archivedAt && <ArchivedBadge />}
            </div>

            {/* Mobile title row with menu */}
            <div className="flex items-center justify-between">
              <h1 className="text-2xl font-bold mb-2 pr-3 flex-1">{jha.title}</h1>

              <div className="flex items-center gap-2">
                {/* Mobile Version History Button */}

                {/* Mobile menu in the title row */}
                {hasPermission(MODULES.EHS_JHA, ALLOWED_ACTIONS.EDIT) && !isViewingOlderVersion && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-9 w-9 p-0 flex items-center justify-center"
                        aria-label="Open actions menu"
                      >
                        <MoreVertical className="h-5 w-5" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-56">
                      <DropdownMenuLabel>JHA Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />

                      {isOwner && jha.status === approvalStatusEnum.enumValues[0] && (
                        <DropdownMenuItem className="cursor-pointer" onClick={handleSubmitForReview}>
                          <Send className="mr-2 h-4 w-4" />
                          <span>Submit for Review</span>
                        </DropdownMenuItem>
                      )}

                      {isApprover && jha.status === approvalStatusEnum.enumValues[1] && (
                        <>
                          <DropdownMenuItem className="cursor-pointer" onClick={handleApprove}>
                            <Check className="mr-2 h-4 w-4" />
                            <span>Approve</span>
                          </DropdownMenuItem>

                          <DropdownMenuItem className="cursor-pointer" onClick={handleReject}>
                            <X className="mr-2 h-4 w-4" />
                            <span>Reject</span>
                          </DropdownMenuItem>
                        </>
                      )}

                      {jha.status === approvalStatusEnum.enumValues[0] && (
                        <DropdownMenuItem className="cursor-pointer" onClick={handleEditClick}>
                          <Edit className="mr-2 h-4 w-4" />
                          <span>Edit JHA</span>
                        </DropdownMenuItem>
                      )}

                      <JhaVersionHistory jhaInstanceId={jhaInstanceId} jhaTitle={jha.title} />

                      {jha.status === approvalStatusEnum.enumValues[2] && (
                        <DropdownMenuItem className="cursor-pointer" onClick={handleDownloadPdf}>
                          <Download className="mr-2 h-4 w-4" />
                          <span>Generate PDF Report</span>
                        </DropdownMenuItem>
                      )}

                      <DropdownMenuSeparator />

                      <DropdownMenuItem className="cursor-pointer" onClick={() => setShowArchiveConfirm(true)}>
                        <Archive className="mr-2 h-4 w-4" />
                        <span>{jha.archivedAt ? 'Unarchive JHA' : 'Archive JHA'}</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </div>
            </div>
          </div>

          {/* Desktop buttons - only shown on desktop */}
          {!isViewingOlderVersion && (
            <div className="hidden md:flex gap-2 self-start">
              {hasPermission(MODULES.EHS_JHA, ALLOWED_ACTIONS.EDIT) &&
                isApprover &&
                jha.status === approvalStatusEnum.enumValues[1] && (
                  <>
                    <Button
                      size="sm"
                      onClick={handleApprove}
                      variant="outline"
                      className="bg-emerald-100 hover:bg-emerald-200"
                    >
                      <Check className="h-4 w-4" />
                      <span>Approve</span>
                    </Button>
                    <Button size="sm" onClick={handleReject} variant="outline" className="bg-red-100 hover:bg-red-200">
                      <X className="h-4 w-4" />
                      <span>Reject</span>
                    </Button>
                  </>
                )}

              {(hasPermission(MODULES.EHS_JHA, ALLOWED_ACTIONS.EDIT, jha.createdBy) || isOwner) &&
                jha.status === approvalStatusEnum.enumValues[0] && (
                  <Button size="sm" onClick={handleSubmitForReview}>
                    <Send className="h-4 w-4" />
                    <span>Submit for Review</span>
                  </Button>
                )}

              {hasPermission(MODULES.EHS_JHA, ALLOWED_ACTIONS.EDIT, jha.createdBy) &&
                jha.status === approvalStatusEnum.enumValues[0] && (
                  <Button size="sm" onClick={handleEditClick}>
                    <Edit className="h-4 w-4" />
                    <span>Edit</span>
                  </Button>
                )}

              {(hasPermission(MODULES.EHS_JHA, ALLOWED_ACTIONS.EDIT, jha.createdBy) || isOwner) &&
                jha.status === approvalStatusEnum.enumValues[2] && (
                  <Button size="sm" onClick={handleInitiateRevision}>
                    <History className="h-4 w-4" />
                    <span>Initiate Revision</span>
                  </Button>
                )}

              {/* Version History Button - always visible */}
              <JhaVersionHistory jhaInstanceId={jhaInstanceId} jhaTitle={jha.title} />

              {/* Desktop-only Options Menu */}

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="flex items-center gap-1">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Options</DropdownMenuLabel>
                  {jha.status === approvalStatusEnum.enumValues[2] && (
                    <DropdownMenuItem onClick={handleDownloadPdf}>
                      <Download className="mr-2 h-4 w-4" />
                      <span>Generate PDF Report</span>
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuSeparator />

                  {hasPermission(MODULES.EHS_JHA, ALLOWED_ACTIONS.EDIT) && (
                    <DropdownMenuItem onClick={() => setShowArchiveConfirm(true)}>
                      <Archive className="mr-2 h-4 w-4" />
                      <span>{jha.archivedAt ? 'Unarchive JHA' : 'Archive JHA'}</span>
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}
        </div>

        {/* Context bar with metadata */}
        <div className="flex flex-wrap items-center text-sm text-muted-foreground mb-4 gap-y-2">
          <div className="flex items-center">
            <User className="h-4 w-4 mr-1.5" />
            <span>Owner: {jha.owner?.fullName}</span>
          </div>

          <div className="hidden sm:block text-muted-foreground mx-2">•</div>

          <div className="flex items-center">
            <User className="h-4 w-4 mr-1.5" />
            <span>Approver: {jha.approver?.fullName}</span>
          </div>

          <div className="hidden sm:block text-muted-foreground mx-2">•</div>

          <div className="flex items-center">
            <Clock className="h-4 w-4 mr-1.5" />
            <span>Last Reviewed: {formatDate(jha.updatedAt, true)}</span>
          </div>

          {jha.reviewDate && (
            <>
              <div className="hidden sm:block text-muted-foreground mx-2">•</div>
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-1.5" />
                <span>Review Date: {formatDate(jha.reviewDate, true)}</span>
              </div>
            </>
          )}
        </div>

        {/* Main content grid */}
        <div
          className={`grid grid-cols-1 md:grid-cols-3 gap-4 ${
            jha.archivedAt ? 'p-3 bg-amber-50/30 border border-amber-200 rounded-lg' : ''
          }`}
        >
          {/* Left column - main content */}
          <div className="md:col-span-2 space-y-4">
            {/* Task Steps & Risk Assessment */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Task Steps & Risk Assessment
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    <Button variant="ghost" size="sm" className="text-muted-foreground" onClick={handleExpandAll}>
                      <ChevronsUpDown className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" className="text-muted-foreground" onClick={handleCollapseAll}>
                      <ChevronsDownUp className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground">
                  Sequential breakdown of job tasks with associated hazards, risk levels, and control measures
                </p>
              </CardHeader>
              <CardContent>
                {jha.steps && jha.steps.length > 0 ? (
                  <Accordion
                    type="multiple"
                    className="w-full"
                    value={accordionValue}
                    onValueChange={setAccordionValue}
                  >
                    {jha.steps.map((step, _index) => {
                      const riskScore = step.severity * step.likelihood;

                      return (
                        <AccordionItem key={step.id} value={`step-${step.id}`}>
                          <AccordionTrigger className="hover:no-underline">
                            <div className="flex items-center justify-between w-full">
                              <div className="flex items-center gap-3">
                                <div className="flex items-center justify-center w-8 h-8 bg-blue-500 text-white rounded-full text-sm font-medium">
                                  {step.serial}
                                </div>
                                <div className="text-left">
                                  <h3 className="font-medium text-lg">{step.title}</h3>
                                  <p className="text-sm text-muted-foreground">
                                    Step {step.serial} of {jha.steps.length}
                                  </p>
                                </div>
                              </div>

                              <div className="flex items-center gap-2">
                                <JhaRiskScoreBadge score={riskScore} />
                                <JhaRiskLevelBadge score={riskScore} />
                              </div>
                            </div>
                          </AccordionTrigger>
                          <AccordionContent>
                            <div>
                              {step.description && (
                                <div className="mb-4 p-3 bg-gray-50 rounded-md">
                                  <h4 className="font-medium text-sm mb-2">Step Description</h4>
                                  <p className="text-sm text-gray-700">{step.description}</p>
                                </div>
                              )}

                              {/* Risk assessment details */}
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {/* Hazard Analysis */}
                                <div>
                                  <div className="flex items-center gap-2 mb-2">
                                    <AlertTriangle className="h-4 w-4 text-red-600" />
                                    <h4 className="font-medium text-sm">Hazards</h4>
                                  </div>

                                  {step.hazards && step.hazards.length > 0 ? (
                                    <div className="space-y-2">
                                      {step.hazards.map((hazard) => (
                                        <div key={hazard.id} className="flex items-center gap-2">
                                          <div className="w-2 h-2 bg-red-500 rounded-full flex-shrink-0"></div>
                                          <span className="text-sm">
                                            {hazard.name} ({CATEGORY_MAP[hazard.type]})
                                          </span>
                                        </div>
                                      ))}

                                      <div className="flex sm:flex-col md:flex-row text-xs text-muted-foreground gap-1">
                                        <div>Severity: {step.severity}/5</div> ⋅
                                        <div>Likelihood: {step.likelihood}/5</div>
                                      </div>
                                    </div>
                                  ) : (
                                    <p className="text-sm text-muted-foreground">No hazards identified</p>
                                  )}
                                </div>

                                {/* Control Measures */}
                                <div>
                                  <div className="flex items-center gap-2 mb-2">
                                    <Shield className="h-4 w-4 text-green-600" />
                                    <h4 className="font-medium text-sm">Control Measures</h4>
                                  </div>

                                  {step.controlMeasures && step.controlMeasures.length > 0 ? (
                                    <div className="space-y-2">
                                      {step.controlMeasures.map((controlMeasure) => (
                                        <div key={controlMeasure.id} className="flex items-center gap-2">
                                          <div className="w-2 h-2 bg-green-500 rounded-full flex-shrink-0"></div>
                                          <span className="text-sm">
                                            {controlMeasure.name} ({CONTROL_MEASURE_TYPE_MAP[controlMeasure.type]})
                                          </span>
                                        </div>
                                      ))}
                                    </div>
                                  ) : (
                                    <p className="text-sm text-muted-foreground">No control measures defined</p>
                                  )}
                                </div>
                              </div>
                            </div>
                          </AccordionContent>
                        </AccordionItem>
                      );
                    })}
                  </Accordion>
                ) : (
                  <p className="text-muted-foreground">No task steps defined for this JHA.</p>
                )}
              </CardContent>
            </Card>

            {/* Additional Notes & Considerations */}
            {jha.notes && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Additional Notes & Considerations</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Special instructions, emergency procedures, and safety requirements
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="text-gray-700 whitespace-pre-wrap leading-relaxed">{jha.notes}</div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Right column - metadata & timeline */}
          <div className="space-y-4">
            {/* Risk Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4" />
                  Risk Summary
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <div className="text-sm text-gray-500 mb-1">Highest Risk Score</div>
                    <div className="flex items-center gap-2">
                      <div className="font-semibold text-2xl">{jha.highestSeverity || 0}</div>
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500 mb-1">Total Task Steps</div>
                    <div className="font-semibold text-gray-900">{jha.steps?.length || 0} steps</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Location and Assets */}
            {(jha.locationId || (jha.assetIds && jha.assetIds.length > 0)) && (
              <Card>
                <CardHeader>
                  <CardTitle>Location and Assets</CardTitle>
                </CardHeader>
                <CardContent>
                  {jha.location?.name && (
                    <div className="flex flex-col gap-2 mb-4">
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-gray-500" />
                        <span>Location: {jha.location.name}</span>
                      </div>
                    </div>
                  )}
                  {Array.isArray(jha.assetIds) && jha.assetIds.length > 0 && (
                    <div className="flex flex-col gap-2">
                      <div className="flex flex-wrap items-center gap-2">
                        {jha.assets?.map((asset) => (
                          <Badge variant="outline" key={asset.id}>
                            {asset.name}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Timeline - Temporarily commented out until jha entity type is supported */}
            <Timeline entityId={jha.id} entityType="jha" />
          </div>
        </div>
      </>

      <ArchiveConfirmationDialog
        archived={!!jha?.archivedAt}
        showDialog={showArchiveConfirm}
        setShowDialog={setShowArchiveConfirm}
        entityId={jhaInstanceId}
        entityType="jha"
      />
      {statusAction && (
        <JhaConfirmationDialog
          showDialog={showStatusConfirm}
          setShowDialog={setShowStatusConfirm}
          jhaId={jha.id}
          action={statusAction}
        />
      )}
    </div>
  );
}
