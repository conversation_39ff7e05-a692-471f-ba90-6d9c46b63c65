import { UpsertControlMeasureModal } from '@/components/control-measures/upsert-control-measure-modal';
import { SeedControlMeasuresModal } from '@/components/control-measures/seed-control-measures-modal';
import { ControlMeasuresEmpty } from '@/components/control-measures/control-measures-empty';
import { ControlMeasuresError } from '@/components/control-measures/control-measures-error';
import { Filters } from '@/components/control-measures/control-measures-filters';
import { ControlMeasuresLoading } from '@/components/control-measures/control-measures-loading';
import { MobileFilters } from '@/components/control-measures/control-measures-mobile-filters';
import { ControlMeasuresMobileView } from '@/components/control-measures/control-measures-mobile-view';
import { ControlMeasuresTable } from '@/components/control-measures/control-measures-table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { useIsMobile } from '@/hooks/use-mobile';
import { useInfiniteControlMeasures } from '@/hooks/use-paginated-data';
import { usePermissions } from '@/hooks/use-permissions';
import { useControlMeasuresUrlFilters } from '@/hooks/use-url-filters';
import { exportControlMeasuresCSV } from '@/lib/export-control-measures';
import { trpc } from '@/providers/trpc';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { FileDown, Search, Sprout, X } from 'lucide-react';
import { AnimatePresence, motion } from 'motion/react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { useLocation } from 'wouter';
import { useLocalStorage } from '@uidotdev/usehooks';

export default function ControlMeasuresLog() {
  const [_, navigate] = useLocation();
  const isMobile = useIsMobile();
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [isSeedModalOpen, setIsSeedModalOpen] = useState<boolean>(false);

  const [location] = useLocation();
  const { hasPermission } = usePermissions();

  // Track if user has seen the seed button highlight
  const [hasSeenSeedHighlight, setHasSeenSeedHighlight] = useLocalStorage(
    'ehs/control-measures-seed-highlight-seen',
    false,
  );
  const [showSeedHighlight, setShowSeedHighlight] = useState(false);

  const { filters, immediateFilters, updateFilter, resetFilters, activeFilterCount } = useControlMeasuresUrlFilters();

  const {
    data: controlMeasures,
    isLoading,
    error,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
  } = useInfiniteControlMeasures({ filters });

  const { mutateAsync: exportControlMeasures, isPending: isExporting } = trpc.controlMeasures.export.useMutation();

  useEffect(() => {
    if (error?.data?.code === 'FORBIDDEN') {
      navigate(ROUTES.NOT_FOUND);
    }
  }, [error]);

  // Show seed button highlight on first visit
  useEffect(() => {
    if (!hasSeenSeedHighlight && controlMeasures && !isLoading) {
      // Small delay to ensure page is fully loaded
      const timer = setTimeout(() => {
        setShowSeedHighlight(true);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [hasSeenSeedHighlight, controlMeasures, isLoading]);

  // Check for create query parameter and open modal
  useEffect(() => {
    if (location === ROUTES.CONTROL_MEASURES_NEW) {
      setIsModalOpen(true);
    } else {
      // Close modal if navigating away from the new route
      setIsModalOpen(false);
    }
  }, [location]);

  const handleExport = async () => {
    try {
      toast.info('Exporting control measures...');
      const controlMeasures = await exportControlMeasures(filters);

      exportControlMeasuresCSV(controlMeasures);
      toast.success('Control measures exported successfully');
    } catch (error) {
      console.error(error);
      toast.error('Error exporting control measures');
    }
  };

  const handleSeedButtonClick = () => {
    setIsSeedModalOpen(true);
    // Mark as seen and stop the highlight animation
    if (showSeedHighlight) {
      setShowSeedHighlight(false);
      setHasSeenSeedHighlight(true);
    }
  };

  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <div className="flex flex-col md:flex-row justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Control Measures</h1>
        </div>

        <div className="flex flex-col-reverse md:flex-row items-center gap-2 w-full md:w-auto">
          <div className="relative flex-1 md:w-64 w-full">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search control measures..."
              className="pl-8 pr-4"
              value={immediateFilters.search ?? ''}
              onChange={(e) => updateFilter('search', e.target.value)}
            />
            {immediateFilters.search && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0 "
                onClick={() => updateFilter('search', '')}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          <div className="flex items-center gap-2 w-full md:w-auto">
            {hasPermission(MODULES.CONTROL_MEASURES, ALLOWED_ACTIONS.CREATE) && (
              <Button
                onClick={() => {
                  setIsModalOpen(true);
                }}
                className="flex-1"
              >
                + Create Control Measure
              </Button>
            )}
            {hasPermission(MODULES.CONTROL_MEASURES, ALLOWED_ACTIONS.CREATE) && (
              <Tooltip delayDuration={500}>
                <TooltipTrigger asChild>
                  <motion.div
                    className="relative"
                    animate={
                      showSeedHighlight
                        ? {
                            scale: [1, 1.02, 1],
                          }
                        : {}
                    }
                    transition={{
                      duration: 2,
                      repeat: showSeedHighlight ? Infinity : 0,
                      ease: 'easeInOut',
                    }}
                  >
                    <AnimatePresence>
                      {showSeedHighlight && (
                        <>
                          {/* Subtle outer glow */}
                          <motion.div
                            className="absolute -inset-1 rounded-md bg-blue-400/20"
                            initial={{ opacity: 0 }}
                            animate={{
                              opacity: [0.2, 0.4, 0.2],
                            }}
                            exit={{ opacity: 0 }}
                            transition={{
                              duration: 2,
                              repeat: Infinity,
                              ease: 'easeInOut',
                            }}
                          />
                        </>
                      )}
                    </AnimatePresence>

                    <Button
                      variant="outline"
                      size="icon"
                      onClick={handleSeedButtonClick}
                      className={`relative z-10 transition-all duration-300 ${
                        showSeedHighlight ? 'bg-blue-50 border-blue-300 hover:bg-blue-100' : ''
                      }`}
                    >
                      <motion.div
                        animate={
                          showSeedHighlight
                            ? {
                                rotate: [0, 15, -15, 10, -5, 0],
                              }
                            : {}
                        }
                        transition={{
                          duration: 1.5,
                          repeat: showSeedHighlight ? Infinity : 0,
                          ease: 'linear',
                          times: [0, 0.2, 0.4, 0.6, 0.8, 1],
                        }}
                      >
                        <Sprout
                          className={`h-4 w-4 transition-colors duration-300 ${
                            showSeedHighlight ? 'text-blue-600' : ''
                          }`}
                        />
                      </motion.div>
                    </Button>
                  </motion.div>
                </TooltipTrigger>
                <TooltipContent align="end">Seed default control measures</TooltipContent>
              </Tooltip>
            )}
            {hasPermission(MODULES.CONTROL_MEASURES, ALLOWED_ACTIONS.EXPORT) && (
              <Tooltip delayDuration={500}>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" disabled={isExporting} onClick={handleExport}>
                    <FileDown className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent align="end">
                  Export control measures (maximum 500 records). <br />
                  Only the first 500 records matching your <br /> current filters will be included in the export.
                </TooltipContent>
              </Tooltip>
            )}
            {/* Mobile filters */}
            <MobileFilters
              activeFilterCount={activeFilterCount}
              filters={immediateFilters}
              updateFilter={updateFilter}
              resetFilters={resetFilters}
            />
          </div>
        </div>
      </div>

      {/* Desktop Filters bar */}
      <Filters
        filters={immediateFilters}
        updateFilter={updateFilter}
        activeFilterCount={activeFilterCount}
        resetFilters={resetFilters}
      />

      {/* Create Control Measure Modal */}
      {hasPermission(MODULES.CONTROL_MEASURES, ALLOWED_ACTIONS.CREATE) && (
        <UpsertControlMeasureModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} />
      )}

      {/* Seed Control Measures Modal */}
      {hasPermission(MODULES.CONTROL_MEASURES, ALLOWED_ACTIONS.CREATE) && (
        <SeedControlMeasuresModal isOpen={isSeedModalOpen} onClose={() => setIsSeedModalOpen(false)} />
      )}

      {/* Error state */}
      {error && <ControlMeasuresError />}

      {/* Loading state */}
      {isLoading && <ControlMeasuresLoading />}

      {/* Empty state - when no control measures are found and not loading */}
      {controlMeasures && controlMeasures.length === 0 && !isLoading && !error && (
        <ControlMeasuresEmpty
          hasActiveFilters={activeFilterCount > 0}
          onResetFilters={resetFilters}
          onCreateControlMeasure={() => {
            setIsModalOpen(true);
          }}
          onSeedControlMeasure={handleSeedButtonClick}
        />
      )}

      {/* Desktop Table View */}
      {!isMobile && controlMeasures && controlMeasures.length > 0 && (
        <ControlMeasuresTable controlMeasures={controlMeasures} />
      )}

      {/* Mobile Card View */}
      {isMobile && controlMeasures && controlMeasures.length > 0 && (
        <ControlMeasuresMobileView controlMeasures={controlMeasures} />
      )}

      {/* Load More Button for Infinite Scrolling */}
      {controlMeasures && controlMeasures.length > 0 && hasNextPage && (
        <div className="flex justify-center mt-6">
          <Button onClick={() => fetchNextPage()} disabled={isFetchingNextPage} variant="outline" className="px-6">
            {isFetchingNextPage ? 'Loading more...' : `Load More (${controlMeasures.length} loaded)`}
          </Button>
        </div>
      )}
    </div>
  );
}
