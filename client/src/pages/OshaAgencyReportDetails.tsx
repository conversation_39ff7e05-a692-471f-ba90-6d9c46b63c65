import { AgencyReportTypeBadge } from '@/components/agency-reports/agency-report-type-badge';
import { AgencyReportDetailsError } from '@/components/agency-reports/details/agency-report-details-error';
import { AgencyReportDetailsLoading } from '@/components/agency-reports/details/agency-report-details-loading';
import { ArchiveConfirmationDialog } from '@/components/composite/archive-confirmation-dialog';
import { ArchivedBadge } from '@/components/composite/archived-badge';
import { Back } from '@/components/composite/back';
import { OshaAuditTrail } from '@/components/composite/osha-audit-trail';
import { SlugBadge } from '@/components/composite/slug-badge';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Label } from '@/components/ui/label';
import { usePermissions } from '@/hooks/use-permissions';
import { generateOshaAgencyReportPdf } from '@/lib/generate-osha-agency-report-pdf';
import { trpc } from '@/providers/trpc';
import { formatDate, formatTime } from '@shared/date-utils';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { OSHA_AGENCY_REPORT_TYPE_MAP } from '@shared/types/osha.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import {
  AlertTriangle,
  Archive,
  Calendar,
  CheckCircle,
  Clock,
  Download,
  Edit,
  MapPin,
  MoreVertical,
  Phone,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { useLocation } from 'wouter';

export default function OshaAgencyReportDetails({ params }: { params: { id: string } }) {
  const reportId = params.id;
  const [_, navigate] = useLocation();
  const [loading, setLoading] = useState(false);
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const { hasPermission } = usePermissions();

  const {
    data: report,
    isLoading,
    error,
  } = trpc.oshaAgencyReport.getById.useQuery({
    id: reportId,
  });

  const { mutate: createOshaAuditTrail } = trpc.oshaAuditTrail.create.useMutation();

  useEffect(() => {
    if (error?.data?.code === 'FORBIDDEN') {
      navigate(ROUTES.NOT_FOUND);
    }
  }, [error, navigate]);

  const handleDownloadReport = () => {
    if (!report) return;

    setLoading(true);

    try {
      generateOshaAgencyReportPdf(report);

      createOshaAuditTrail({
        entityId: report.id,
        entityType: 'osha_agency_report',
        action: 'downloaded',
      });

      setLoading(false);

      toast.success('PDF Downloaded', {
        description: `Agency report ${report.slug || 'AR-' + report.id.slice(-4)} has been downloaded.`,
      });
    } catch (error) {
      console.error('Error generating Agency Report PDF:', error);
      setLoading(false);

      toast.error('Export failed', {
        description: 'There was an error creating the PDF download.',
      });
    }
  };

  const handleEditClick = () => {
    navigate(ROUTES.BUILD_OSHA_AGENCY_REPORT_EDIT_PATH(reportId));
  };

  const handleArchive = () => {
    setShowArchiveConfirm(true);
  };

  if (isLoading) {
    return <AgencyReportDetailsLoading />;
  }

  if (error || !report) {
    return <AgencyReportDetailsError />;
  }

  return (
    <div className="container mx-auto py-4 px-4">
      <>
        {/* Report Header */}
        <div className="flex flex-col md:flex-row justify-between items-start">
          {/* Desktop View */}
          <div className="hidden md:block w-full">
            <div className="flex flex-wrap items-center gap-2 mb-2">
              <Back />
              <SlugBadge slug={report.slug} />
              <Badge className="bg-green-50 text-green-700 border-green-200">
                <CheckCircle className="h-3 w-3 mr-1" />
                Submitted
              </Badge>
              <AgencyReportTypeBadge type={report.typeOfIncident} />
              {report.archivedAt && <ArchivedBadge />}
            </div>
            <h1 className="text-2xl md:text-3xl font-bold mb-2">Serious Incident Report</h1>
          </div>

          {/* Mobile View - with menu in title area */}
          <div className="md:hidden w-full">
            <div className="flex flex-wrap items-center gap-2 mb-2">
              <Back />
              <SlugBadge slug={report.slug} />
              <Badge className="bg-green-50 text-green-700 border-green-200">
                <CheckCircle className="h-3 w-3 mr-1" />
                Submitted
              </Badge>
              <AgencyReportTypeBadge type={report.typeOfIncident} />
              {report.archivedAt && <ArchivedBadge />}
            </div>

            {/* Mobile title row with menu */}
            <div className="flex items-center justify-between">
              <h1 className="text-2xl font-bold mb-2 pr-3 flex-1">Serious Incident Report</h1>

              {/* Mobile menu in the title row */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-9 w-9 p-0 flex items-center justify-center"
                    aria-label="Open actions menu"
                  >
                    <MoreVertical className="h-5 w-5" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel>Report Actions</DropdownMenuLabel>
                  <DropdownMenuSeparator />

                  {hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.EDIT) && !report.archivedAt && (
                    <DropdownMenuItem className="cursor-pointer" onClick={handleEditClick}>
                      <Edit className="mr-2 h-4 w-4" />
                      <span>Edit Report</span>
                    </DropdownMenuItem>
                  )}

                  <DropdownMenuItem className="cursor-pointer" onClick={handleDownloadReport} disabled={loading}>
                    <Download className="mr-2 h-4 w-4" />
                    <span>{loading ? 'Downloading...' : 'Download PDF'}</span>
                  </DropdownMenuItem>

                  <DropdownMenuSeparator />

                  {hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.EDIT) && (
                    <DropdownMenuItem className="cursor-pointer" onClick={handleArchive}>
                      <Archive className="mr-2 h-4 w-4" />
                      <span>{report.archivedAt ? 'Unarchive Report' : 'Archive Report'}</span>
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Desktop buttons - only shown on desktop */}
          <div className="hidden md:flex gap-2 self-start">
            {hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.EDIT) && !report.archivedAt && (
              <Button size="sm" onClick={handleEditClick}>
                <Edit className="h-4 w-4" />
                <span>Edit</span>
              </Button>
            )}

            <Button variant="outline" size="sm" onClick={handleDownloadReport} disabled={loading}>
              <Download className="h-4 w-4" />
              <span>{loading ? 'Downloading...' : 'Download PDF'}</span>
            </Button>

            {/* Desktop-only Options Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="flex items-center gap-1">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Options</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.EDIT) && (
                  <DropdownMenuItem onClick={handleArchive}>
                    <Archive className="mr-2 h-4 w-4" />
                    <span>{report.archivedAt ? 'Unarchive Report' : 'Archive Report'}</span>
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Context bar with metadata */}
        <div className="flex flex-wrap items-center text-sm text-muted-foreground mb-4 gap-y-2">
          <div className="flex items-center">
            <Calendar className="h-4 w-4 mr-1.5" />
            <span>{formatDate(report.dateOfIncident, true)}</span>
          </div>

          <div className="hidden sm:block text-muted-foreground mx-2">•</div>

          <div className="flex items-center">
            <Clock className="h-4 w-4 mr-1.5" />
            <span>{formatTime(report.dateOfIncident)}</span>
          </div>

          <div className="hidden sm:block text-muted-foreground mx-2">•</div>

          <div className="flex items-center">
            <MapPin className="h-4 w-4 mr-1.5" />
            <span>{report?.oshaLocation?.name}</span>
          </div>
        </div>

        {/* Archived Status Banner */}
        {report.archivedAt && (
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-4 flex items-center justify-between">
            <div className="flex items-center">
              <Archive className="h-5 w-5 text-amber-600 mr-3" />
              <div>
                <p className="font-medium text-amber-800">This report is archived</p>
                <p className="text-sm text-amber-700">
                  Archived reports are read-only and don't appear in active lists.
                </p>
              </div>
            </div>
            {hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.EDIT) && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleArchive}
                className="bg-white border-amber-300 text-amber-700 hover:bg-amber-50"
              >
                <Archive className="h-4 w-4 mr-2" />
                Unarchive Report
              </Button>
            )}
          </div>
        )}

        {/* Main content grid */}
        <div
          className={`grid grid-cols-1 md:grid-cols-3 gap-4 ${
            report.archivedAt ? 'p-3 bg-amber-50/30 border border-amber-200 rounded-lg' : ''
          }`}
        >
          {/* Left column - main content */}
          <div className="md:col-span-2 space-y-4">
            {/* Severity Alert Card with red background */}
            <Card className="border-red-200 pt-0 overflow-hidden">
              <CardHeader className="bg-red-50 border-b border-red-200 pt-4 ">
                <div className="flex items-center space-x-3">
                  <AlertTriangle className="h-6 w-6 text-red-600 flex-shrink-0" />
                  <div>
                    <CardTitle className="text-lg text-red-800">Critical Incident Alert</CardTitle>
                    <p className="text-sm text-red-600 mt-1">
                      {OSHA_AGENCY_REPORT_TYPE_MAP[report.typeOfIncident]} - Regulatory notification required
                    </p>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-4">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  <div className="text-center p-3 bg-red-50 rounded-md border border-red-200">
                    <div className="text-xl font-bold text-red-600">
                      {report.typeOfIncident === 'fatality' ? report.affectedCount : 0}
                    </div>
                    <div className="text-xs font-medium text-red-800">Fatalities</div>
                  </div>
                  <div className="text-center p-3 bg-red-50 rounded-md border border-red-200">
                    <div className="text-xl font-bold text-red-600">
                      {report.typeOfIncident === 'hospitalization' ? report.affectedCount : 0}
                    </div>
                    <div className="text-xs font-medium text-red-800">Hospitalizations</div>
                  </div>
                  <div className="text-center p-3 bg-red-50 rounded-md border border-red-200">
                    <div className="text-xl font-bold text-red-600">
                      {report.typeOfIncident === 'amputation' ? report.affectedCount : 0}
                    </div>
                    <div className="text-xs font-medium text-red-800">Amputations</div>
                  </div>
                  <div className="text-center p-3 bg-red-50 rounded-md border border-red-200">
                    <div className="text-xl font-bold text-red-600">
                      {report.typeOfIncident === 'eye_loss' ? report.affectedCount : 0}
                    </div>
                    <div className="text-xs font-medium text-red-800">Eye Loss</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Description */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Incident Description</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-gray-700 whitespace-pre-wrap leading-relaxed">
                  {report.description || 'No description provided.'}
                </div>
              </CardContent>
            </Card>

            {/* Employees Involved */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Employees Involved</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-gray-700 leading-relaxed">
                  {report?.employeesInvolved?.trim() || 'No employees specified'}
                </div>
              </CardContent>
            </Card>

            {/* Emergency Contact */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Emergency Contact</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Contact Person</Label>
                    <p className="font-medium mt-1">{report.companyContactPerson || 'Not specified'}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Phone Number</Label>
                    <div className="flex items-center mt-1">
                      <Phone className="h-4 w-4 text-gray-400 mr-2 flex-shrink-0" />
                      <p className="font-medium">{report.contactPersonPhone || 'Not specified'}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right column - metadata & timeline */}
          <div className="space-y-4">
            {/* Incident Details */}
            <Card>
              <CardHeader>
                <CardTitle>Incident Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <Label className="text-sm font-medium text-gray-500">Date of Incident</Label>
                  <p className="font-medium">{formatDate(report.dateOfIncident, true)}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Time of Incident</Label>
                  <p className="font-medium">{formatTime(report.dateOfIncident)}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Incident Type</Label>
                  <p className="font-medium">{OSHA_AGENCY_REPORT_TYPE_MAP[report.typeOfIncident]}</p>
                </div>
              </CardContent>
            </Card>

            {/* Location */}
            <Card>
              <CardHeader>
                <CardTitle>Location</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-gray-500" />
                  <span className="font-medium">{report?.oshaLocation?.name}</span>
                </div>
              </CardContent>
            </Card>

            {/* Report Timeline */}
            <OshaAuditTrail entityId={report.id} entityType="osha_agency_report" />
            {/* <Card>
              <CardHeader>
                <CardTitle>Report Timeline</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <Label className="text-sm font-medium text-gray-500">Report Prepared</Label>
                  <p className="font-medium">{formatDate(report.datePrepared || report.createdAt)}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Submitted to System</Label>
                  <p className="font-medium">{formatDate(report.updatedAt || report.createdAt)}</p>
                </div>
                <div className="pt-3 border-t">
                  <p className="text-sm text-muted-foreground">
                    <strong>Next Steps:</strong> Complete actual agency notification using OSHA's 24-hour reporting
                    requirements.
                  </p>
                </div>
              </CardContent>
            </Card> */}
          </div>
        </div>
      </>

      {/* Archive Confirmation Dialog */}
      <ArchiveConfirmationDialog
        archived={!!report?.archivedAt}
        showDialog={showArchiveConfirm}
        setShowDialog={setShowArchiveConfirm}
        entityId={report.id}
        entityType="oshaAgencyReport"
      />
    </div>
  );
}
