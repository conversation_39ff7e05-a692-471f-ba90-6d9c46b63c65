import { AgencyReportBanner } from '@/components/agency-reports/agency-report-banner';
import { getAffectedCountLabel } from '@/components/agency-reports/agency-report.logic';
import { EditAgencyReportError } from '@/components/agency-reports/edit/edit-agency-report-error';
import { EditAgencyReportLoading } from '@/components/agency-reports/edit/edit-agency-report-loading';
import { AsyncOshaLocationSelect } from '@/components/composite/async-osha-location-select';
import { Back } from '@/components/composite/back';
import { Button } from '@/components/ui/button';
import { DateTimePicker } from '@/components/ui/date-time-picker';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { formatPhoneNumber } from '@/lib/utils';
import { trpc } from '@/providers/trpc';
import { zodResolver } from '@hookform/resolvers/zod';
import { ROUTES } from '@shared/ROUTE_PATHS';
import {
  EditOshaAgencyReportForm,
  EditOshaAgencyReportFormSchema,
  OSHA_AGENCY_REPORT_TYPE_MAP,
} from '@shared/types/osha.types';
import { addMonths, endOfYear, subYears } from 'date-fns';
import { FileText } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { useLocation } from 'wouter';

const FormSchema = EditOshaAgencyReportFormSchema;

type FormType = EditOshaAgencyReportForm;

export default function EditOshaAgencyReport({ params }: { params: { id: string } }) {
  const [_, navigate] = useLocation();
  const utils = trpc.useUtils();
  const reportId = params.id;

  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch the existing agency report
  const {
    data: agencyReport,
    isSuccess,
    error,
    isLoading,
  } = trpc.oshaAgencyReport.getByIdForEdit.useQuery({ id: reportId });

  // Update mutation
  const { mutateAsync: updateAgencyReport } = trpc.oshaAgencyReport.update.useMutation();

  const form = useForm<FormType>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      id: reportId,
      dateOfIncident: new Date(),
      oshaLocationId: undefined,
      typeOfIncident: 'fatality',
      description: '',
      employeesInvolved: '',
      companyContactPerson: '',
      contactPersonPhone: '',
      affectedCount: undefined,
      datePrepared: new Date(),
    },
    mode: 'onSubmit',
  });

  // Handle navigation on error
  useEffect(() => {
    if (error?.data?.code === 'FORBIDDEN') {
      navigate(ROUTES.NOT_FOUND);
    }
  }, [error, navigate]);

  // Pre-fill form with existing data when agency report is loaded
  useEffect(() => {
    if (agencyReport && isSuccess) {
      form.reset({
        id: agencyReport.id,
        dateOfIncident: agencyReport.dateOfIncident,
        oshaLocationId: agencyReport.oshaLocationId,
        typeOfIncident: agencyReport.typeOfIncident,
        description: agencyReport.description ?? undefined,
        employeesInvolved: agencyReport.employeesInvolved ?? undefined,
        companyContactPerson: agencyReport.companyContactPerson,
        contactPersonPhone: agencyReport.contactPersonPhone,
        affectedCount: agencyReport.affectedCount,
        datePrepared: agencyReport.datePrepared,
      });
    }
  }, [agencyReport, isSuccess, form]);

  // Handle form submission
  async function onSubmit(values: FormType) {
    setIsSubmitting(true);

    const toUpdate = {};

    // Only include dirty (changed) fields in the update
    for (const field in form.formState.dirtyFields) {
      if (form.formState.dirtyFields[field as keyof typeof form.formState.dirtyFields]) {
        (toUpdate as Record<string, unknown>)[field] = values[field as keyof FormType];
      }
    }

    try {
      await updateAgencyReport({ ...toUpdate, id: values.id });

      utils.oshaAgencyReport.list.invalidate();
      utils.oshaAgencyReport.getById.invalidate({ id: reportId });

      toast('Agency Report Updated', {
        description: 'The serious incident report has been updated successfully.',
      });

      navigate(ROUTES.OSHA_AGENCY_REPORTS);
    } catch (error) {
      console.error('Error updating agency report:', error);
      toast('Error Updating Report', {
        description: 'There was a problem updating the agency report. Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  if (isLoading) {
    return <EditAgencyReportLoading />;
  }

  if (error) {
    return <EditAgencyReportError />;
  }

  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 md:px-8 lg:px-10 py-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <Back />
        <h1 className="text-2xl font-bold text-red-700">Edit #{agencyReport?.slug}</h1>
      </div>

      <AgencyReportBanner />

      {/* Form Card */}
      <div className="bg-white border rounded-lg p-6">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Form Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:items-start">
              {/* Date and Time of Incident */}
              <FormField
                control={form.control}
                name="dateOfIncident"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Date & Time of Incident <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <DateTimePicker
                        selected={field.value}
                        onSelect={field.onChange}
                        placeholder="Select incident date & time"
                        disabled={{ after: endOfYear(new Date()) }}
                        startMonth={subYears(new Date(), 5)}
                        endMonth={addMonths(new Date(), 11)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Location */}
              <FormField
                control={form.control}
                name="oshaLocationId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Location of Incident <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <AsyncOshaLocationSelect
                        value={field.value}
                        onChange={field.onChange}
                        placeholder="Select location where incident occurred"
                        mustIncludeObjectIds={agencyReport?.oshaLocationId ? [agencyReport.oshaLocationId] : undefined}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Type of Serious Incident */}
            <FormField
              control={form.control}
              name="typeOfIncident"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Type of Serious Incident <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <RadioGroup value={field.value} onValueChange={field.onChange} className="grid grid-cols-2 gap-3">
                      {Object.entries(OSHA_AGENCY_REPORT_TYPE_MAP).map(([value, label]) => (
                        <div key={value} className="flex items-center space-x-2">
                          <RadioGroupItem value={value} id={`type-${value}`} />
                          <Label htmlFor={`type-${value}`}>{label}</Label>
                        </div>
                      ))}
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Affected Count */}
            <FormField
              control={form.control}
              name="affectedCount"
              render={({ field }) => (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:items-start">
                  <FormItem>
                    <FormLabel>
                      {getAffectedCountLabel(form.watch('typeOfIncident'))} <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="number"
                        placeholder={getAffectedCountLabel(form.watch('typeOfIncident'))}
                        value={field.value ?? ''}
                        onChange={(e) => field.onChange(+e.target.value)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </div>
              )}
            />

            {/* Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Brief Description of Incident <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="What happened and how it happened..."
                      value={field.value || ''}
                      onChange={field.onChange}
                      onBlur={field.onBlur}
                      name={field.name}
                      ref={field.ref}
                      rows={4}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Additional Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:items-start">
              <FormField
                control={form.control}
                name="employeesInvolved"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Employee(s) Involved (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter employee names separated by commas (e.g., John Doe, Jane Smith)"
                        {...field}
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormDescription>Enter the names of employees involved, separated by commas</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="companyContactPerson"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Company Contact Person for OSHA <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="Contact person name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="contactPersonPhone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Contact Person's Phone <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="(*************"
                        {...field}
                        onChange={(e) => {
                          const formatted = formatPhoneNumber(e.target.value);
                          field.onChange(formatted);
                        }}
                      />
                    </FormControl>
                    <FormDescription>Enter phone number with area code</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="datePrepared"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date/Time Prepared</FormLabel>
                    <FormControl>
                      <DateTimePicker placeholder="Select date/time prepared" disabled selected={field.value} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Submit Actions */}
            <div className="flex flex-col sm:flex-row gap-4 pt-6 border-t sm:justify-end">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate(ROUTES.OSHA_AGENCY_REPORTS)}
                className="sm:w-auto"
              >
                Cancel
              </Button>
              <Button type="submit" variant="destructive" disabled={isSubmitting || !form.formState.isDirty}>
                {isSubmitting ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                    Updating...
                  </>
                ) : (
                  <>
                    <FileText className="h-4 w-4 mr-2" />
                    Save & Mark as Reported
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
