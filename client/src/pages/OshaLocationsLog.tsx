import { CreateOshaLocationModal } from '@/components/osha-locations/create-osha-location-modal';
import { OshaLocationsEmpty } from '@/components/osha-locations/osha-locations-empty';
import { OshaLocationsError } from '@/components/osha-locations/osha-locations-error';
import { OshaLocationsFiltersComponent } from '@/components/osha-locations/osha-locations-filters';
import { OshaLocationsLoading } from '@/components/osha-locations/osha-locations-loading';
import { OshaLocationsMobileFilters } from '@/components/osha-locations/osha-locations-mobile-filters';
import { OshaLocationsMobileView } from '@/components/osha-locations/osha-locations-mobile-view';
import { OshaLocationsTable } from '@/components/osha-locations/osha-locations-table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { useIsMobile } from '@/hooks/use-mobile';
import { useInfiniteOshaLocations } from '@/hooks/use-paginated-data';
import { usePermissions } from '@/hooks/use-permissions';
import { useOshaLocationsUrlFilters } from '@/hooks/use-url-filters';
import { exportOshaLocationsCSV } from '@/lib/export-osha-locations';
import { trpc } from '@/providers/trpc';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { FileDown, Search, X } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { useLocation } from 'wouter';

export default function OshaLocationsLog() {
  const [_, navigate] = useLocation();
  const isMobile = useIsMobile();
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

  const [location] = useLocation();
  const { hasPermission } = usePermissions();

  const { filters, immediateFilters, updateFilter, resetFilters, activeFilterCount } = useOshaLocationsUrlFilters();

  const {
    data: oshaLocations,
    isLoading,
    error,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
  } = useInfiniteOshaLocations({ filters });

  const { mutateAsync: exportOshaLocations, isPending: isExporting } = trpc.oshaLocation.export.useMutation();

  useEffect(() => {
    if (error?.data?.code === 'FORBIDDEN') {
      navigate(ROUTES.NOT_FOUND);
    }
  }, [error]);

  // Check for create query parameter and open modal
  useEffect(() => {
    if (location === ROUTES.OSHA_LOCATIONS_NEW) {
      setIsModalOpen(true);
    } else {
      // Close modal if navigating away from the new route
      setIsModalOpen(false);
    }
  }, [location]);

  const handleExport = async () => {
    try {
      toast.info('Exporting OSHA locations...');
      const oshaLocations = await exportOshaLocations(filters);

      exportOshaLocationsCSV(oshaLocations);
      toast.success('Exporting OSHA locations completed...');
    } catch (error) {
      console.error(error);
      toast.error('Error exporting OSHA locations');
    }
  };

  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <div className="flex flex-col gap-4 justify-between items-start lg:flex-row ">
        <div>
          <h1 className="text-2xl font-bold md:mb-0">OSHA Locations</h1>
        </div>
        <div className="w-full flex flex-col-reverse items-start pb-4 gap-4 md:pb-0 md:flex-row md:gap-2 md:w-auto">
          <div className="relative flex-1 md:w-64 w-full">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search OSHA locations..."
              className="pl-8 pr-4"
              value={immediateFilters.search ?? ''}
              onChange={(e) => updateFilter('search', e.target.value)}
            />
            {immediateFilters.search && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0 "
                onClick={() => updateFilter('search', '')}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          <div className="flex items-start gap-4 w-full md:flex-row md:gap-2 md:w-auto">
            {hasPermission(MODULES.OSHA_LOCATIONS, ALLOWED_ACTIONS.CREATE) && (
              <Button
                onClick={() => {
                  setIsModalOpen(true);
                }}
                className="flex-1"
              >
                + Create OSHA Location
              </Button>
            )}
            {hasPermission(MODULES.OSHA_LOCATIONS, ALLOWED_ACTIONS.EXPORT) && (
              <Tooltip delayDuration={500}>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" disabled={isExporting} onClick={handleExport}>
                    <FileDown className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent align="end">
                  Export OSHA locations (maximum 500 records). <br />
                  Only the first 500 records matching your <br /> current filters will be included in the export.
                </TooltipContent>
              </Tooltip>
            )}
            <OshaLocationsMobileFilters
              activeFilterCount={activeFilterCount}
              filters={immediateFilters}
              updateFilter={updateFilter}
              resetFilters={resetFilters}
            />
          </div>
        </div>
      </div>

      {/* Desktop Filters bar */}
      <OshaLocationsFiltersComponent
        filters={immediateFilters}
        updateFilter={updateFilter}
        activeFilterCount={activeFilterCount}
        resetFilters={resetFilters}
      />
      {/* Create OSHA Location Modal */}
      {hasPermission(MODULES.OSHA_LOCATIONS, ALLOWED_ACTIONS.CREATE) && (
        <CreateOshaLocationModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} />
      )}

      {/* Error state */}
      {error && <OshaLocationsError />}

      {/* Loading state */}
      {isLoading && <OshaLocationsLoading />}

      {/* Empty state - when no OSHA locations are found and not loading */}
      {oshaLocations && oshaLocations.length === 0 && !isLoading && !error && (
        <OshaLocationsEmpty
          hasActiveFilters={activeFilterCount > 0}
          onResetFilters={resetFilters}
          onCreateOshaLocation={() => {
            setIsModalOpen(true);
          }}
        />
      )}

      {/* Desktop Table View */}
      {!isMobile && oshaLocations && oshaLocations.length > 0 && <OshaLocationsTable oshaLocations={oshaLocations} />}

      {/* Mobile Card View */}
      {isMobile && oshaLocations && oshaLocations.length > 0 && (
        <OshaLocationsMobileView oshaLocations={oshaLocations} />
      )}

      {/* Load More Button for Infinite Scrolling */}
      {oshaLocations && oshaLocations.length > 0 && hasNextPage && (
        <div className="flex justify-center mt-6">
          <Button onClick={() => fetchNextPage()} disabled={isFetchingNextPage} variant="outline" className="px-6">
            {isFetchingNextPage ? 'Loading more...' : `Load More (${oshaLocations.length} loaded)`}
          </Button>
        </div>
      )}
    </div>
  );
}
