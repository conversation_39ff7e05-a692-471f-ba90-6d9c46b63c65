import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { oshaAgencyReportTypeEnum } from '@shared/schema';
import { OSHA_AGENCY_REPORT_TYPE_MAP, OshaAgencyReportsFilters } from '@shared/types/osha.types';
import { Archive, ChevronDown, Filter, X } from 'lucide-react';

export function AgencyReportsFilters({
  toggleFilter,
  filters,
  updateFilter,
  activeFilterCount,
  resetFilters,
}: {
  toggleFilter: (type: 'typeOfIncident', value: (typeof oshaAgencyReportTypeEnum.enumValues)[number]) => void;
  filters: OshaAgencyReportsFilters;
  updateFilter: (
    key: keyof OshaAgencyReportsFilters,
    value: OshaAgencyReportsFilters[keyof OshaAgencyReportsFilters],
  ) => void;
  activeFilterCount: number;
  resetFilters: () => void;
}) {
  return (
    <div className="hidden py-2 md:block">
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm" className={activeFilterCount > 0 ? 'bg-muted' : ''}>
          <Filter className="h-4 w-4" />
          Filters {activeFilterCount > 0 && `(${activeFilterCount})`}
        </Button>

        {/* Type Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              Type
              <Badge className="ml-1 px-1 py-0 h-5" variant="secondary">
                {filters.typeOfIncident?.length || 'All'}
              </Badge>
              <ChevronDown className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            {oshaAgencyReportTypeEnum.enumValues.map((type) => (
              <DropdownMenuCheckboxItem
                key={type}
                className="flex items-center gap-2"
                onSelect={(e) => {
                  e.preventDefault();
                  toggleFilter('typeOfIncident', type);
                }}
                checked={filters.typeOfIncident?.includes(type)}
              >
                {OSHA_AGENCY_REPORT_TYPE_MAP[type]}
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Archive Filter */}
        <Button
          variant={filters.includeArchived ? 'default' : 'outline'}
          size="sm"
          onClick={() => {
            updateFilter('includeArchived', !filters.includeArchived);
          }}
          className={filters.includeArchived ? 'bg-amber-600 hover:bg-amber-700' : ''}
        >
          <Archive className="h-4 w-4" />
          Include Archived
        </Button>

        {/* Reset Filters */}
        {activeFilterCount > 0 && (
          <Button variant="ghost" size="sm" onClick={resetFilters}>
            <X className="h-4 w-4" />
            Clear Filters
          </Button>
        )}
      </div>
    </div>
  );
}
