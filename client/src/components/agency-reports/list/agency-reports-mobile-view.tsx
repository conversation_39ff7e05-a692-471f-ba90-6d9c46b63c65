import { ArchivedBadge } from '@/components/composite/archived-badge';
import { StatusIndicator } from '@/components/composite/status-indicator';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { RouterOutputs } from '@shared/types/router.types';
import { format } from 'date-fns';
import { Eye, MapPin } from 'lucide-react';
import { useLocation } from 'wouter';
import { AgencyReportTypeBadge } from '../agency-report-type-badge';

export function AgencyReportsMobileView({
  agencyReports,
}: {
  agencyReports: RouterOutputs['oshaAgencyReport']['list']['result'];
}) {
  const [_, navigate] = useLocation();

  return (
    <div className="space-y-3">
      {agencyReports.map((report) => (
        <Card
          key={report.id}
          className={cn(
            'group relative overflow-hidden border-0 shadow-sm hover:shadow-md transition-all duration-200 bg-white hover:bg-gray-50/50',
            report.archivedAt ? 'bg-gradient-to-r from-amber-50/80 to-amber-50/60' : '',
          )}
          onClick={() => navigate(ROUTES.OSHA_AGENCY_REPORT_DETAILS?.replace(':id', report.id) || '#')}
        >
          <CardContent>
            <div className="flex">
              {/* Status indicator line - left side */}
              <StatusIndicator archived={!!report.archivedAt} />

              {/* Content Section */}
              <div className="flex-1">
                {/* Header Section */}
                <div className="px-4">
                  <div className="flex items-start justify-between gap-3">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="text-sm font-medium text-muted-foreground">
                          {report.slug || `INC-${report.id.slice(-4)}`}
                        </span>
                        {report.archivedAt && <ArchivedBadge />}
                      </div>

                      <h3 className="font-semibold text-gray-900 text-base leading-tight line-clamp-2 mb-2">
                        {report.description || 'No description available'}
                      </h3>
                    </div>
                  </div>
                </div>

                {/* Content Section */}
                <div className="px-4 pb-3">
                  <div className="space-y-2.5">
                    {/* Type and Date */}
                    <div className="flex items-center justify-between">
                      <AgencyReportTypeBadge type={report.typeOfIncident} />
                      <div className="flex items-center text-xs text-gray-500">
                        <span>{format(new Date(report.dateOfIncident), 'MMM d, yyyy')}</span>
                      </div>
                    </div>

                    {/* Location */}
                    <div className="flex items-center text-sm text-gray-600">
                      <MapPin className="h-3.5 w-3.5 mr-2 text-gray-400" />
                      <span className="truncate">{report.oshaLocation?.name || 'No location'}</span>
                    </div>

                    {/* Description */}
                    {report.description && (
                      <div className="text-sm text-gray-600">
                        <span className="truncate line-clamp-2">{report.description}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Actions Section */}
                <div className="px-4">
                  <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                    <div className="flex items-center gap-1.5">
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-8 px-3 text-xs font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
                        onClick={(e) => {
                          e.stopPropagation();
                          navigate(ROUTES.BUILD_OSHA_AGENCY_REPORT_DETAILS_PATH(report.id));
                        }}
                      >
                        <Eye className="h-3.5 w-3.5 mr-1.5" />
                        View
                      </Button>
                    </div>

                    <div className="text-xs text-gray-500">
                      Prepared: {format(new Date(report.createdAt), 'dd/MM/yyyy')}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
