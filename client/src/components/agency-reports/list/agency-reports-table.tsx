import { ArchivedBadge } from '@/components/composite/archived-badge';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { cn } from '@/lib/utils';
import { formatDate } from '@shared/date-utils';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { RouterOutputs } from '@shared/types/router.types';
import { Eye, Info, MapPin } from 'lucide-react';
import { useLocation } from 'wouter';
import { AgencyReportTypeBadge } from '../agency-report-type-badge';

export function AgencyReportsTable({
  agencyReports,
}: {
  agencyReports: RouterOutputs['oshaAgencyReport']['list']['result'];
}) {
  const [_, navigate] = useLocation();

  return (
    <div className="overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="max-w-80">Report ID</TableHead>
            <TableHead className="w-36">Date & Time</TableHead>
            <TableHead className="w-24">Type</TableHead>
            <TableHead>OSHA Location</TableHead>
            <TableHead>Description</TableHead>
            <TableHead className="w-28">Prepared</TableHead>
            <TableHead className="w-32 text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {agencyReports.length === 0 ? (
            <TableRow>
              <TableCell colSpan={7} className="h-24 text-center">
                <div className="flex flex-col items-center justify-center">
                  <Info className="h-8 w-8 text-muted-foreground mb-2" />
                  <p className="text-muted-foreground">No agency reports found</p>
                </div>
              </TableCell>
            </TableRow>
          ) : (
            agencyReports.map((report) => (
              <TableRow
                key={report.id}
                className={cn(
                  'cursor-pointer hover:bg-muted/50',
                  report.archivedAt ? 'bg-amber-50/50 hover:bg-amber-50/80' : '',
                )}
                onClick={() => navigate(ROUTES.OSHA_AGENCY_REPORT_DETAILS?.replace(':id', report.id) || '#')}
              >
                <TableCell className="max-w-80">
                  <div className="flex items-center gap-1">
                    <span className="font-medium line-clamp-1 truncate">{report.slug}</span>
                    {report.archivedAt && <ArchivedBadge />}
                  </div>
                </TableCell>
                <TableCell className="w-36">{formatDate(report.createdAt)}</TableCell>
                <TableCell className="w-24">
                  <AgencyReportTypeBadge type={report.typeOfIncident} />
                </TableCell>
                <TableCell>
                  <div className="flex items-center">
                    <MapPin className="h-3 w-3 mr-1" />
                    <span className="truncate">{report.oshaLocation?.name || 'No location'}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-sm text-muted-foreground line-clamp-1 truncate max-w-xs">
                    {report.description || 'No description available'}
                  </div>
                </TableCell>
                <TableCell className="w-28 text-sm text-muted-foreground">
                  {formatDate(report.datePrepared, true)}
                </TableCell>
                <TableCell className="w-32 text-right">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                    onClick={() => navigate(ROUTES.BUILD_OSHA_AGENCY_REPORT_DETAILS_PATH(report.id))}
                  >
                    <Eye className="h-4 w-4 text-blue-600" />
                  </Button>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}
