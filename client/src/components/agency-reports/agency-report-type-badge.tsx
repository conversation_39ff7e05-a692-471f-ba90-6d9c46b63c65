import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { OSHA_AGENCY_REPORT_TYPE_MAP } from '@shared/types/osha.types';
import { oshaAgencyReportTypeEnum } from '@shared/schema';

export const AgencyReportTypeBadge = ({ type }: { type: (typeof oshaAgencyReportTypeEnum.enumValues)[number] }) => {
  const TYPE_BADGE_COLORS = {
    [oshaAgencyReportTypeEnum.enumValues[0]]: 'bg-red-50 text-red-700 border-red-200',
    [oshaAgencyReportTypeEnum.enumValues[1]]: 'bg-orange-50 text-orange-700 border-orange-200',
    [oshaAgencyReportTypeEnum.enumValues[2]]: 'bg-yellow-50 text-yellow-700 border-yellow-200',
    [oshaAgencyReportTypeEnum.enumValues[3]]: 'bg-purple-50 text-purple-700 border-purple-200',
  } as const;

  return (
    <Badge variant="outline" className={cn(TYPE_BADGE_COLORS[type])}>
      {OSHA_AGENCY_REPORT_TYPE_MAP[type]}
    </Badge>
  );
};
