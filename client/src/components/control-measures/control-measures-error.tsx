import { But<PERSON> } from '@/components/ui/button';
import { AlertTriangle } from 'lucide-react';

export const ControlMeasuresError = () => {
  return (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <AlertTriangle className="h-12 w-12 text-red-500 mb-4" />
      <h3 className="text-lg font-semibold mb-2">Error loading control measures</h3>
      <p className="text-muted-foreground mb-6 max-w-md">
        We encountered an error while loading the control measures. Please try refreshing the page.
      </p>
      <Button onClick={() => window.location.reload()}>Refresh Page</Button>
    </div>
  );
};
