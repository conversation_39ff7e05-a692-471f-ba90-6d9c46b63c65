import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { trpc } from '@/providers/trpc';
import { usePermissions } from '@/hooks/use-permissions';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { Redirect } from 'wouter';
import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Info } from 'lucide-react';
import { CONTROL_MEASURE_TYPE_MAP } from '@shared/types/jha.types';

export const SeedControlMeasuresModal = ({
  isOpen,
  onClose,
  onSuccess,
}: {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}) => {
  const utils = trpc.useUtils();
  const { hasPermission } = usePermissions();

  const [selectedControlMeasures, setSelectedControlMeasures] = useState<string[]>([]);

  const { data: defaultControlMeasures, isLoading: isLoadingDefaults } = trpc.controlMeasures.listDefault.useQuery(
    undefined,
    {
      enabled: isOpen,
    },
  );

  const { mutateAsync: bulkCreateControlMeasures, isPending: isCreating } = trpc.controlMeasures.bulkCreate.useMutation(
    {
      onSuccess: () => {
        utils.controlMeasures.list.invalidate();
        toast('Control measures seeded successfully', {
          description: `${selectedControlMeasures.length} control measures have been created`,
        });
      },
      onError: (error) => {
        toast('Error seeding control measures', {
          description: error.message || 'Please try again',
        });
      },
    },
  );

  // Pre-select all control measures when modal opens
  useEffect(() => {
    if (defaultControlMeasures && isOpen) {
      setSelectedControlMeasures(defaultControlMeasures.map((controlMeasure) => controlMeasure.control_measure_id));
    }
  }, [defaultControlMeasures, isOpen]);

  const handleSelectAll = (checked: boolean) => {
    if (checked && defaultControlMeasures) {
      setSelectedControlMeasures(defaultControlMeasures.map((controlMeasure) => controlMeasure.control_measure_id));
    } else {
      setSelectedControlMeasures([]);
    }
  };

  const handleSelectControlMeasure = (controlMeasureId: string, checked: boolean) => {
    if (checked) {
      setSelectedControlMeasures((prev) => [...prev, controlMeasureId]);
    } else {
      setSelectedControlMeasures((prev) => prev.filter((id) => id !== controlMeasureId));
    }
  };

  const handleSeedControlMeasures = async () => {
    if (!defaultControlMeasures || selectedControlMeasures.length === 0) {
      toast('No control measures selected', {
        description: 'Please select at least one control measure to seed',
      });
      return;
    }

    try {
      const controlMeasuresToCreate = defaultControlMeasures
        .filter((controlMeasure) => selectedControlMeasures.includes(controlMeasure.control_measure_id))
        .map((controlMeasure) => ({
          name: controlMeasure.name,
          type: controlMeasure.type,
        }));

      await bulkCreateControlMeasures(controlMeasuresToCreate);
      setSelectedControlMeasures([]);
      onClose();
      onSuccess?.();
    } catch (error) {
      console.error('Error seeding control measures:', error);
    }
  };

  const handleClose = () => {
    setSelectedControlMeasures([]);
    onClose();
  };

  if (!hasPermission(MODULES.CONTROL_MEASURES, ALLOWED_ACTIONS.CREATE)) {
    return <Redirect to={ROUTES.CONTROL_MEASURES_LIST} />;
  }

  const isAllSelected = defaultControlMeasures && selectedControlMeasures.length === defaultControlMeasures.length;
  const isIndeterminate =
    selectedControlMeasures.length > 0 && selectedControlMeasures.length < (defaultControlMeasures?.length || 0);

  return (
    <AlertDialog open={isOpen} onOpenChange={handleClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Seed Default Control Measures</AlertDialogTitle>
          <AlertDialogDescription>
            Select the default control measures you want to add to your organization. All control measures are
            pre-selected by default.
          </AlertDialogDescription>
        </AlertDialogHeader>

        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            Seeding control measures with the same name as existing control measures will not create duplicates. Only
            new control measures will be added to your organization.
          </AlertDescription>
        </Alert>

        <div className="flex-1 overflow-hidden border rounded-xl">
          {isLoadingDefaults ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-sm text-gray-500">Loading default control measures...</div>
            </div>
          ) : (
            <ScrollArea className="h-[400px]">
              {/* Header */}
              <div className="sticky top-0 bg-background border-b grid grid-cols-[auto_1fr_1fr] gap-4 p-4 items-center font-medium text-sm">
                <div className="flex justify-center">
                  <Checkbox
                    checked={isAllSelected}
                    onCheckedChange={handleSelectAll}
                    ref={(ref) => {
                      if (ref) {
                        // TypeScript doesn't know about the indeterminate property on checkbox elements
                        (ref as HTMLInputElement).indeterminate = isIndeterminate;
                      }
                    }}
                  />
                </div>
                <div>Name</div>
                <div className="text-center">Type</div>
              </div>

              {/* Rows */}
              <div className="divide-y">
                {defaultControlMeasures?.map((controlMeasure) => (
                  <div
                    key={controlMeasure.control_measure_id}
                    className="grid grid-cols-[auto_1fr_1fr] gap-4 p-4 items-center hover:bg-gray-50"
                  >
                    <div className="flex justify-center">
                      <Checkbox
                        checked={selectedControlMeasures.includes(controlMeasure.control_measure_id)}
                        onCheckedChange={(checked) =>
                          handleSelectControlMeasure(controlMeasure.control_measure_id, checked as boolean)
                        }
                      />
                    </div>
                    <div className="text-sm">{controlMeasure.name}</div>
                    <div className="flex justify-center">
                      <Badge variant="outline" className="capitalize text-xs">
                        {CONTROL_MEASURE_TYPE_MAP[controlMeasure.type]}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
              <ScrollBar />
            </ScrollArea>
          )}
        </div>

        <AlertDialogFooter className="flex items-center justify-between">
          <div className="text-sm text-gray-500">
            {selectedControlMeasures.length} of {defaultControlMeasures?.length || 0} control measures selected
          </div>
          <div className="flex gap-2">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button onClick={handleSeedControlMeasures} disabled={isCreating || selectedControlMeasures.length === 0}>
              {isCreating ? 'Creating...' : `Create ${selectedControlMeasures.length} Control Measures`}
            </Button>
          </div>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
