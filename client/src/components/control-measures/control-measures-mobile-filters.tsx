import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Label } from '@/components/ui/label';
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetT<PERSON>le,
  SheetTrigger,
} from '@/components/ui/sheet';
import { CONTROL_MEASURE_TYPE_MAP } from '@shared/types/jha.types';
import { ControlMeasuresFilters, ControlMeasuresCategorySchema } from '@shared/types/settings.types';
import { Archive, ChevronDown, Filter } from 'lucide-react';

const controlMeasureTypeOptions = ControlMeasuresCategorySchema.options.map((type) => ({
  value: type,
  label: CONTROL_MEASURE_TYPE_MAP[type],
}));

export const MobileFilters = ({
  activeFilterCount,
  filters,
  updateFilter,
  resetFilters,
}: {
  activeFilterCount: number;
  filters: ControlMeasuresFilters;
  updateFilter: (
    key: keyof ControlMeasuresFilters,
    value: string | boolean | string[] | { from?: Date; to?: Date } | undefined,
  ) => void;
  resetFilters: () => void;
}) => {
  const handleTypeChange = (type: string, checked: boolean) => {
    const currentTypes = filters.type || [];
    if (checked) {
      updateFilter('type', [...currentTypes, type]);
    } else {
      updateFilter(
        'type',
        currentTypes.filter((t) => t !== type),
      );
    }
  };

  // Handle include archived toggle
  const handleArchiveToggle = () => {
    const newValue = !filters.includeArchived;
    updateFilter('includeArchived', newValue);
  };

  return (
    <div className="md:hidden">
      <Sheet>
        <SheetTrigger asChild>
          <Button variant="outline" size={activeFilterCount > 0 ? 'default' : 'icon'}>
            <Filter className="h-4 w-4" />
            {activeFilterCount > 0 && (
              <span className="text-xs bg-primary text-primary-foreground rounded-full w-4 h-4 flex items-center justify-center">
                {activeFilterCount}
              </span>
            )}
          </Button>
        </SheetTrigger>
        <SheetContent side="right">
          <SheetHeader>
            <SheetTitle>Filter Control Measures</SheetTitle>
            <SheetDescription>Apply filters to narrow down your control measures list.</SheetDescription>
          </SheetHeader>

          <div className="p-4 space-y-4">
            {/* Type Filter */}
            <Collapsible defaultOpen className="bg-muted rounded-md p-3">
              <CollapsibleTrigger className="flex items-center justify-between w-full">
                <h3 className="text-sm font-medium">Type</h3>
                <ChevronDown className="h-4 w-4 transition-transform ui-open:rotate-180" />
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-2 space-y-2">
                {controlMeasureTypeOptions.map((option) => (
                  <div key={option.value} className="flex items-center space-x-2">
                    <Checkbox
                      id={`type-${option.value}-mobile`}
                      checked={filters.type?.includes(option.value) || false}
                      onCheckedChange={(checked) => handleTypeChange(option.value, checked as boolean)}
                    />
                    <label htmlFor={`type-${option.value}-mobile`} className="text-sm cursor-pointer">
                      {option.label}
                    </label>
                  </div>
                ))}
              </CollapsibleContent>
            </Collapsible>

            {/* Include Archived Filter */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Archive Status</Label>
              <Button
                variant={filters.includeArchived ? 'default' : 'outline'}
                size="sm"
                onClick={handleArchiveToggle}
                className={`justify-start w-full ${filters.includeArchived ? 'bg-amber-600 hover:bg-amber-700' : ''}`}
              >
                <Archive className="h-4 w-4 mr-2" />
                Include Archived
              </Button>
            </div>
          </div>

          <SheetFooter className="pt-4">
            <SheetClose asChild>
              <Button variant="outline" className="w-full" onClick={resetFilters}>
                Clear Filters
              </Button>
            </SheetClose>
            <SheetClose asChild>
              <Button className="w-full">Apply Filters ({activeFilterCount})</Button>
            </SheetClose>
          </SheetFooter>
        </SheetContent>
      </Sheet>
    </div>
  );
};
