import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { CONTROL_MEASURE_TYPE_MAP } from '@shared/types/jha.types';

import { ControlMeasuresCategorySchema, ControlMeasuresFilters } from '@shared/types/settings.types';
import { Archive, ChevronDown, Filter, X } from 'lucide-react';

const controlMeasureTypeOptions = ControlMeasuresCategorySchema.options.map((type) => ({
  value: type,
  label: CONTROL_MEASURE_TYPE_MAP[type],
}));

export const Filters = ({
  filters,
  updateFilter,
  activeFilterCount,
  resetFilters,
}: {
  filters: ControlMeasuresFilters;
  updateFilter: (
    key: keyof ControlMeasuresFilters,
    value: string | boolean | string[] | { from?: Date; to?: Date } | undefined,
  ) => void;
  activeFilterCount: number;
  resetFilters: () => void;
}) => {
  const handleTypeChange = (type: string, checked: boolean) => {
    const currentTypes = filters.type || [];
    if (checked) {
      updateFilter('type', [...currentTypes, type]);
    } else {
      updateFilter(
        'type',
        currentTypes.filter((t) => t !== type),
      );
    }
  };

  return (
    <div className="hidden  bg-gray-50 md:sticky md:top-0 md:z-10 md:block py-6">
      <div className="flex items-center gap-2 overflow-x-auto">
        <Button variant="outline" size="sm" className={activeFilterCount > 0 ? 'bg-muted' : ''}>
          <Filter className="h-4 w-4" />
          Filters {activeFilterCount > 0 && `(${activeFilterCount})`}
        </Button>

        {/* Type Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              Type
              <Badge className="ml-1 h-5 px-1 py-0" variant="secondary">
                {filters.type?.length || 'All'}
              </Badge>
              <ChevronDown className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-64" align="start">
            <div className="max-h-48 overflow-y-auto">
              {controlMeasureTypeOptions.map((option) => (
                <DropdownMenuCheckboxItem
                  key={option.value}
                  checked={filters.type?.includes(option.value) || false}
                  onCheckedChange={(checked) => handleTypeChange(option.value, checked as boolean)}
                >
                  {option.label}
                </DropdownMenuCheckboxItem>
              ))}
            </div>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Include Archived */}
        <Button
          variant={filters.includeArchived ? 'default' : 'outline'}
          size="sm"
          onClick={() => updateFilter('includeArchived', !filters.includeArchived)}
          className={filters.includeArchived ? 'bg-amber-600 hover:bg-amber-700' : ''}
        >
          <Archive className="h-4 w-4" />
          Include Archived
        </Button>

        {/* Reset Filters */}
        {activeFilterCount > 0 && (
          <Button variant="ghost" size="sm" onClick={resetFilters} className="h-9">
            <X className="h-3.5 w-3.5" />
            Clear Filters
          </Button>
        )}
      </div>
    </div>
  );
};
