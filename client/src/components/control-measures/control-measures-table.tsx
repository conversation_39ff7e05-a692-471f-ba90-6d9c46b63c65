import { ArchiveConfirmationDialog } from '@/components/composite/archive-confirmation-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { CONTROL_MEASURE_TYPE_MAP } from '@shared/types/jha.types';
import { RouterOutputs } from '@shared/types/router.types';
import { formatDate } from '@shared/date-utils';
import { Archive, Trash2 } from 'lucide-react';
import { useState } from 'react';
import { UpsertControlMeasureModal } from '@/components/control-measures/upsert-control-measure-modal';

type ControlMeasuresTableProps = {
  controlMeasures: RouterOutputs['controlMeasures']['list']['result'];
};

export const ControlMeasuresTable = ({ controlMeasures }: ControlMeasuresTableProps) => {
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const [selectedControlMeasure, setSelectedControlMeasure] = useState<
    RouterOutputs['controlMeasures']['list']['result'][number] | null
  >(null);

  const [showCreateControlMeasureModal, setShowCreateControlMeasureModal] = useState(false);

  return (
    <div className="overflow-hidden hidden md:block">
      {selectedControlMeasure && (
        <ArchiveConfirmationDialog
          archived={!!selectedControlMeasure.archivedAt}
          showDialog={showArchiveConfirm}
          setShowDialog={setShowArchiveConfirm}
          entityId={selectedControlMeasure.id}
          entityType="controlMeasure"
        />
      )}

      {selectedControlMeasure && (
        <UpsertControlMeasureModal
          isOpen={showCreateControlMeasureModal}
          onClose={() => setShowCreateControlMeasureModal(false)}
          onSuccess={() => {
            setShowCreateControlMeasureModal(false);
          }}
          mode="edit"
          controlMeasure={selectedControlMeasure}
        />
      )}

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="max-w-80">Name</TableHead>
            <TableHead className="w-24">Type</TableHead>
            <TableHead className="w-24">Status</TableHead>
            <TableHead className="w-44">Created By</TableHead>
            <TableHead className="w-36">Created Date</TableHead>
            <TableHead className="w-24 text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {controlMeasures.map((controlMeasure) => (
            <TableRow
              onClick={(e) => {
                e.stopPropagation();
                setSelectedControlMeasure(controlMeasure);
                setShowCreateControlMeasureModal(true);
              }}
              className={`cursor-pointer ${controlMeasure.archivedAt ? 'bg-amber-50/50 hover:bg-amber-50/80' : ''}`}
              key={controlMeasure.id}
            >
              <TableCell className="font-medium max-w-80 line-clamp-1 truncate">{controlMeasure.name}</TableCell>
              <TableCell className="w-24">
                <Badge variant="outline" className="capitalize">
                  {CONTROL_MEASURE_TYPE_MAP[controlMeasure.type]}
                </Badge>
              </TableCell>
              <TableCell className="w-24">
                <Badge
                  className={`${
                    !controlMeasure.archivedAt
                      ? 'bg-green-50 text-green-700 border-green-200'
                      : 'bg-gray-50 text-gray-700 border-gray-200'
                  } font-medium flex items-center border px-2 py-1`}
                  variant="outline"
                >
                  {!controlMeasure.archivedAt ? 'Active' : 'Inactive'}
                </Badge>
              </TableCell>
              <TableCell className="w-44">
                <div className="flex flex-col">
                  <span className="font-medium truncate">
                    {typeof controlMeasure.createdBy === 'object' && controlMeasure.createdBy?.fullName
                      ? controlMeasure.createdBy.fullName
                      : '--'}
                  </span>
                  <span className="text-sm text-gray-500">
                    {typeof controlMeasure.createdBy === 'object' && controlMeasure.createdBy?.username
                      ? controlMeasure.createdBy.username
                      : '--'}
                  </span>
                </div>
              </TableCell>
              <TableCell className="w-36">{formatDate(controlMeasure.createdAt, true)}</TableCell>
              <TableCell className="w-24 text-right">
                <div className="flex items-center justify-end gap-1">
                  {/* Archive Button */}
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 w-8 p-0 text-red-600 hover:text-red-700 border-gray-300 hover:border-red-300 rounded-sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedControlMeasure(controlMeasure);
                          setShowArchiveConfirm(true);
                        }}
                      >
                        {controlMeasure.archivedAt ? <Archive className="h-4 w-4" /> : <Trash2 className="h-4 w-4" />}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{controlMeasure.archivedAt ? 'Unarchive' : 'Archive'}</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
