import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { usePermissions } from '@/hooks/use-permissions';
import { trpc } from '@/providers/trpc';
import { zodResolver } from '@hookform/resolvers/zod';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { controlMeasureCategoryEnum } from '@shared/schema';
import { RouterOutputs } from '@shared/types/router.types';
import { CONTROL_MEASURE_TYPE_MAP } from '@shared/types/jha.types';
import { ControlMeasuresCreateSchema, ControlMeasuresUpdateSchema } from '@shared/types/settings.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { Redirect } from 'wouter';
import { z } from 'zod';

type CreateControlMeasureFormData = z.infer<typeof ControlMeasuresCreateSchema>;

type UpdateControlMeasureFormData = z.infer<typeof ControlMeasuresUpdateSchema>;

export const UpsertControlMeasureModal = ({
  isOpen,
  onClose,
  onSuccess,
  controlMeasure,
  mode = 'create',
}: {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  controlMeasure?: RouterOutputs['controlMeasures']['list']['result'][number] | null;
  mode?: 'create' | 'edit';
}) => {
  const utils = trpc.useUtils();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { hasPermission } = usePermissions();

  const createControlMeasureMutation = trpc.controlMeasures.create.useMutation({
    onSuccess: () => {
      utils.controlMeasures.list.invalidate();
      toast('Control measure created', {
        description: 'The control measure has been successfully created',
      });
    },
    onError: (error) => {
      toast('Error creating control measure', {
        description: error.message || 'Please try again',
      });
    },
  });

  const updateControlMeasureMutation = trpc.controlMeasures.update.useMutation({
    onSuccess: () => {
      utils.controlMeasures.list.invalidate();
      toast('Control measure updated', {
        description: 'The control measure has been successfully updated',
      });
    },
    onError: (error) => {
      toast('Error updating control measure', {
        description: error.message || 'Please try again',
      });
    },
  });

  const form = useForm<CreateControlMeasureFormData | UpdateControlMeasureFormData>({
    resolver: zodResolver(mode === 'create' ? ControlMeasuresCreateSchema : ControlMeasuresUpdateSchema),
    defaultValues: {
      name: '',
      type: undefined,
    },
  });

  useEffect(() => {
    if (controlMeasure) {
      form.reset({
        id: controlMeasure.id,
        name: controlMeasure.name,
        type: controlMeasure.type,
      });
    }
  }, [controlMeasure, form]);

  const onSubmit = async (data: CreateControlMeasureFormData) => {
    setIsSubmitting(true);
    try {
      if (mode === 'edit' && controlMeasure?.id) {
        await updateControlMeasure({ ...data, id: controlMeasure.id });
      } else {
        await createControlMeasure(data);
      }
      form.reset();
      onClose();
      onSuccess?.();
    } catch (error) {
      console.error('Error creating control measure:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const createControlMeasure = async (data: CreateControlMeasureFormData) => {
    await createControlMeasureMutation.mutateAsync({
      name: data.name.trim(),
      type: data.type,
    });
  };

  const updateControlMeasure = async (data: UpdateControlMeasureFormData) => {
    await updateControlMeasureMutation.mutateAsync({
      id: controlMeasure?.id || '',
      name: data.name.trim(),
      type: data.type,
    });
  };

  const handleClose = () => {
    form.reset();
    onClose();
  };

  if (!hasPermission(MODULES.CONTROL_MEASURES, ALLOWED_ACTIONS.CREATE)) {
    return <Redirect to={ROUTES.CONTROL_MEASURES_LIST} />;
  }

  return (
    <AlertDialog open={isOpen} onOpenChange={handleClose}>
      <AlertDialogContent className="sm:max-w-md">
        <AlertDialogHeader>
          <AlertDialogTitle>{mode === 'edit' ? 'Edit Control Measure' : 'Create Control Measure'}</AlertDialogTitle>
          <AlertDialogDescription>
            {mode === 'edit'
              ? 'Update the control measure details.'
              : 'Create a new control measure that can be used across your organization.'}
          </AlertDialogDescription>
        </AlertDialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Control Measure Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter control measure name" {...field} value={field.value || ''} />
                  </FormControl>
                  <FormDescription>The name of the control measure</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Control Measure Type</FormLabel>
                  <FormControl>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value);
                      }}
                      value={field.value}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select control measure type" />
                      </SelectTrigger>
                      <SelectContent>
                        {controlMeasureCategoryEnum.enumValues.map((enumType) => (
                          <SelectItem key={enumType} value={enumType}>
                            {CONTROL_MEASURE_TYPE_MAP[enumType]}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormDescription>
                    Examples: Engineering, Administrative, PPE, Elimination, Substitution
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <AlertDialogFooter>
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancel
              </Button>

              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Saving...' : mode === 'edit' ? 'Update Control Measure' : 'Create Control Measure'}
              </Button>
            </AlertDialogFooter>
          </form>
        </Form>
      </AlertDialogContent>
    </AlertDialog>
  );
};
