import { Button } from '@/components/ui/button';
import { usePermissions } from '@/hooks/use-permissions';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { useLocalStorage } from '@uidotdev/usehooks';
import { Gauge, Sprout } from 'lucide-react';
import { AnimatePresence, motion } from 'motion/react';
import { useEffect, useState } from 'react';

export const ControlMeasuresEmpty = ({
  hasActiveFilters,
  onResetFilters,
  onCreateControlMeasure,
  onSeedControlMeasure,
}: {
  hasActiveFilters: boolean;
  onResetFilters: () => void;
  onCreateControlMeasure: () => void;
  onSeedControlMeasure: () => void;
}) => {
  const { hasPermission } = usePermissions();
  
  // Use the same localStorage flag as the seed button
  const [hasSeenSeedHighlight] = useLocalStorage('ehs/control-measures-seed-highlight-seen', false);
  const [showAnimation, setShowAnimation] = useState(false);

  const isFirstVisit = !hasSeenSeedHighlight;
  const shouldShowAnimation = isFirstVisit && !hasActiveFilters && showAnimation;

  // Show animation on first visit for empty state only
  useEffect(() => {
    if (isFirstVisit && !hasActiveFilters) {
      const timer = setTimeout(() => setShowAnimation(true), 1500);
      return () => clearTimeout(timer);
    } else {
      setShowAnimation(false);
    }
  }, [isFirstVisit, hasActiveFilters]);

  const handleButtonClick = isFirstVisit ? onSeedControlMeasure : onCreateControlMeasure;
  const buttonText = isFirstVisit ? 'Seed Control Measures' : 'Create Control Measure';

  if (hasActiveFilters) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <Gauge className="h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No control measures found</h3>
        <p className="text-gray-500 mb-4">Try adjusting your search or filter criteria.</p>
        <Button variant="outline" onClick={onResetFilters}>
          Clear filters
        </Button>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <Gauge className="h-12 w-12 text-gray-400 mb-4" />
      <h3 className="text-lg font-medium text-gray-900 mb-2">No control measures yet</h3>
      <p className="text-gray-500 mb-4">Get started by creating your first control measure.</p>
      {hasPermission(MODULES.CONTROL_MEASURES, ALLOWED_ACTIONS.CREATE) && (
        <motion.div
          className="relative"
          animate={shouldShowAnimation ? { scale: [1, 1.02, 1] } : {}}
          transition={{ duration: 3, repeat: shouldShowAnimation ? Infinity : 0, ease: [0.4, 0.0, 0.2, 1] }}
        >
          <AnimatePresence>
            {shouldShowAnimation && (
              <motion.div
                className="absolute -inset-1 rounded-md"
                style={{ background: `var(--primary)`, opacity: 0.1 }}
                initial={{ opacity: 0 }}
                animate={{ opacity: [0.05, 0.15, 0.05] }}
                exit={{ opacity: 0 }}
                transition={{ duration: 3, repeat: Infinity, ease: [0.4, 0.0, 0.2, 1] }}
              />
            )}
          </AnimatePresence>
          
          <Button 
            onClick={handleButtonClick}
            className="relative z-10 transition-all duration-300"
            style={shouldShowAnimation ? {
              backgroundColor: `var(--primary)`,
              color: `var(--primary-foreground)`
            } : {}}
          >
            <motion.span
              className="flex items-center gap-2"
              animate={shouldShowAnimation ? { scale: [1, 1.01, 1] } : {}}
              transition={{ duration: 2.5, repeat: shouldShowAnimation ? Infinity : 0, ease: [0.4, 0.0, 0.2, 1] }}
            >
              <motion.div
                animate={shouldShowAnimation ? { rotate: [0, 8, -8, 0] } : {}}
                transition={{ duration: 2, repeat: shouldShowAnimation ? Infinity : 0, ease: [0.4, 0.0, 0.2, 1] }}
              >
                <Sprout className="h-4 w-4" />
              </motion.div>
              {buttonText}
            </motion.span>
          </Button>
        </motion.div>
      )}
    </div>
  );
};
