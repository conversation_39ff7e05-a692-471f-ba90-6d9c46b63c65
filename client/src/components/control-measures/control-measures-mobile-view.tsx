import { ArchiveConfirmationDialog } from '@/components/composite/archive-confirmation-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { CONTROL_MEASURE_TYPE_MAP } from '@shared/types/jha.types';
import { RouterOutputs } from '@shared/types/router.types';
import { formatDate } from '@shared/date-utils';
import { Archive, Trash2 } from 'lucide-react';
import { useState } from 'react';
import { UpsertControlMeasureModal } from '@/components/control-measures/upsert-control-measure-modal';
import { cn } from '@/lib/utils';

type ControlMeasuresMobileViewProps = {
  controlMeasures: RouterOutputs['controlMeasures']['list']['result'];
};

export const ControlMeasuresMobileView = ({ controlMeasures }: ControlMeasuresMobileViewProps) => {
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const [selectedControlMeasure, setSelectedControlMeasure] = useState<
    RouterOutputs['controlMeasures']['list']['result'][number] | null
  >(null);
  const [showCreateControlMeasureModal, setShowCreateControlMeasureModal] = useState(false);

  return (
    <div className="space-y-4 md:hidden">
      {selectedControlMeasure && (
        <ArchiveConfirmationDialog
          archived={!!selectedControlMeasure.archivedAt}
          showDialog={showArchiveConfirm}
          setShowDialog={setShowArchiveConfirm}
          entityId={selectedControlMeasure.id}
          entityType="controlMeasure"
        />
      )}

      {selectedControlMeasure && (
        <UpsertControlMeasureModal
          isOpen={showCreateControlMeasureModal}
          onClose={() => setShowCreateControlMeasureModal(false)}
          onSuccess={() => {
            setShowCreateControlMeasureModal(false);
          }}
          mode="edit"
          controlMeasure={selectedControlMeasure}
        />
      )}

      {controlMeasures.map((controlMeasure) => (
        <Card
          key={controlMeasure.id}
          className={cn('cursor-pointer', `${controlMeasure.archivedAt ? 'bg-amber-50/50 border-amber-200' : ''}`)}
          onClick={(e) => {
            e.stopPropagation();
            setSelectedControlMeasure(controlMeasure);
            setShowCreateControlMeasureModal(true);
          }}
        >
          <CardContent className="p-4">
            <div className="flex justify-between items-start mb-3">
              <h3 className="font-medium text-sm leading-tight pr-2">{controlMeasure.name}</h3>
              <Badge
                className={`${
                  !controlMeasure.archivedAt
                    ? 'bg-green-50 text-green-700 border-green-200'
                    : 'bg-gray-50 text-gray-700 border-gray-200'
                } font-medium flex items-center border px-2 py-1 text-xs`}
                variant="outline"
              >
                {!controlMeasure.archivedAt ? 'Active' : 'Inactive'}
              </Badge>
            </div>

            <div className="flex flex-wrap items-center gap-2 mb-3">
              <Badge variant="outline" className="text-xs capitalize">
                {CONTROL_MEASURE_TYPE_MAP[controlMeasure.type]}
              </Badge>
            </div>

            <div className="flex justify-between items-center">
              <div className="text-xs text-gray-500">
                <div className="font-medium">
                  {typeof controlMeasure.createdBy === 'object' && controlMeasure.createdBy?.fullName
                    ? controlMeasure.createdBy.fullName
                    : '--'}
                </div>
                <div>{formatDate(controlMeasure.createdAt, true)}</div>
              </div>

              <div className="flex items-center gap-1">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-8 w-8 p-0 text-red-600 hover:text-red-700 border-gray-300 hover:border-red-300 rounded-sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedControlMeasure(controlMeasure);
                        setShowArchiveConfirm(true);
                      }}
                    >
                      {controlMeasure.archivedAt ? <Archive className="h-4 w-4" /> : <Trash2 className="h-4 w-4" />}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{controlMeasure.archivedAt ? 'Unarchive' : 'Archive'}</p>
                  </TooltipContent>
                </Tooltip>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
