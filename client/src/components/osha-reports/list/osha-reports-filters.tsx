import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { oshaTypeEnum } from '@shared/schema';
import { OSHA_TYPE_MAP, OshaReportsFilters } from '@shared/types/osha.types';
import { Archive, ChevronDown, Filter, X } from 'lucide-react';

export const Filters = ({
  toggleFilter,
  filters,
  updateFilter,
  activeFilterCount,
  resetFilters,
}: {
  toggleFilter: (type: 'caseType', value: (typeof oshaTypeEnum.enumValues)[number]) => void;
  filters: OshaReportsFilters;
  updateFilter: (key: keyof OshaReportsFilters, value: OshaReportsFilters[keyof OshaReportsFilters]) => void;
  activeFilterCount: number;
  resetFilters: () => void;
}) => {
  return (
    <div className="hidden  bg-gray-50 md:top-0 md:z-10 md:flex flex-wrap items-center gap-2">
      <Button variant="outline" size="sm" className={activeFilterCount > 0 ? 'bg-muted' : ''}>
        <Filter className="h-4 w-4" />
        Filters {activeFilterCount > 0 && `(${activeFilterCount})`}
      </Button>
      {/* Case Type Filter */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm" className="gap-1">
            Case Type
            <Badge className="ml-1 px-2 py-0" variant="secondary">
              {filters.caseType?.length || 'All'}
            </Badge>
            <ChevronDown className="h-4 w-4 opacity-50" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-[180px]">
          {oshaTypeEnum.enumValues.map((type) => (
            <DropdownMenuCheckboxItem
              key={type}
              className="flex items-center gap-2"
              onSelect={(e) => {
                e.preventDefault();
                toggleFilter('caseType', type);
              }}
              checked={filters.caseType?.includes(type)}
            >
              <span>{OSHA_TYPE_MAP[type]}</span>
            </DropdownMenuCheckboxItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Archive Filter */}
      <Button
        variant={filters.includeArchived ? 'default' : 'outline'}
        size="sm"
        onClick={() => updateFilter('includeArchived', !filters.includeArchived)}
        className={filters.includeArchived ? 'bg-amber-600 hover:bg-amber-700' : ''}
      >
        <Archive className="h-4 w-4" />
        Include Archived
      </Button>

      {/* Reset Filters */}
      {activeFilterCount > 0 && (
        <Button variant="ghost" size="sm" onClick={resetFilters} className="h-9">
          <X className="h-3.5 w-3.5" />
          Clear Filters
        </Button>
      )}
    </div>
  );
};
