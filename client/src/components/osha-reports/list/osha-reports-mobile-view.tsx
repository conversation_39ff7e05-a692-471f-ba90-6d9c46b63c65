import { ArchivedBadge } from '@/components/composite/archived-badge';
import { CaseTypeBadge } from '@/components/composite/case-type-badge';
import { StatusIndicator } from '@/components/composite/status-indicator';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { formatDate } from '@shared/date-utils';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { RouterOutputs } from '@shared/types/router.types';
import { Calendar, Eye, MapPin } from 'lucide-react';
import { useLocation } from 'wouter';

export const OshaReportsMobileView = ({
  oshaReports,
}: {
  oshaReports: RouterOutputs['oshaReport']['list']['result'];
}) => {
  const [_, navigate] = useLocation();

  return (
    <div className="space-y-3">
      {oshaReports.map((log) => (
        <Card
          key={log.id}
          className={cn(
            'group relative overflow-hidden border-0 shadow-sm hover:shadow-md transition-all duration-200',
            log.archivedAt ? 'bg-gradient-to-r from-amber-50/80 to-amber-50/60' : 'bg-white',
            'hover:bg-gray-50/50',
          )}
          onClick={() => navigate(ROUTES.BUILD_OSHA_REPORT_DETAILS_PATH(log.id))}
        >
          <CardContent>
            <div className="flex">
              <StatusIndicator archived={!!log.archivedAt} />

              {/* Content Section */}
              <div className="flex-1">
                {/* Header Section */}
                <div className="px-4">
                  <div className="flex items-start justify-between gap-3">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="text-sm font-medium text-muted-foreground">{log.slug}</span>
                        {log.archivedAt && <ArchivedBadge />}
                        {log.privacyCase && (
                          <span className="text-xs font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded-md">
                            Privacy Case
                          </span>
                        )}
                      </div>

                      <h3 className="font-semibold text-gray-900 text-base leading-tight line-clamp-2 mb-2">
                        {log.privacyCase ? 'Privacy Case' : log.employeeName}
                      </h3>
                    </div>
                  </div>
                </div>

                {/* Content Section */}
                <div className="px-4 pb-3">
                  <div className="space-y-2.5">
                    {/* Type and Date */}
                    <div className="flex flex-col gap-2">
                      <CaseTypeBadge caseType={log.type} />
                      <div className="flex items-center text-xs text-gray-500">
                        <Calendar className="h-3 w-3 mr-1.5" />
                        {formatDate(log.createdAt)}
                      </div>
                    </div>

                    {/* Location */}
                    {log.employeeWorkLocation && (
                      <div className="flex items-center text-sm text-gray-600">
                        <MapPin className="h-3.5 w-3.5 mr-2 text-gray-400" />
                        <span className="truncate">{log.employeeWorkLocation}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Actions Section */}
                <div className="px-4">
                  <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                    <div className="flex items-center gap-1.5">
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-8 px-3 text-xs font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
                        onClick={(e) => {
                          e.stopPropagation();
                          navigate(ROUTES.BUILD_OSHA_REPORT_DETAILS_PATH(log.id));
                        }}
                      >
                        <Eye className="h-3.5 w-3.5 mr-1.5" />
                        View
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
