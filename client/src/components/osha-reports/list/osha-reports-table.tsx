import { ArchivedBadge } from '@/components/composite/archived-badge';
import { CaseTypeBadge } from '@/components/composite/case-type-badge';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { cn } from '@/lib/utils';
import { formatDate } from '@shared/date-utils';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { RouterOutputs } from '@shared/types/router.types';
import { Eye } from 'lucide-react';
import { useLocation } from 'wouter';

export const OshaReportsTable = ({ oshaReports }: { oshaReports: RouterOutputs['oshaReport']['list']['result'] }) => {
  const [_, navigate] = useLocation();

  return (
    <div className="overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="max-w-80">OSHA Log ID</TableHead>
            <TableHead className="w-24">Employee</TableHead>
            <TableHead className="w-24">Job Title</TableHead>
            <TableHead className="w-28">Case Type</TableHead>
            <TableHead className="w-28">Fatality</TableHead>
            <TableHead className="w-36">Date</TableHead>
            <TableHead className="w-28">OSHA Location</TableHead>
            <TableHead className="w-28">Days Away</TableHead>
            <TableHead className="w-28">Days Restricted</TableHead>
            <TableHead className="w-32 text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {oshaReports.map((report) => (
            <TableRow
              key={report.id}
              className={cn(
                'cursor-pointer hover:bg-muted/50',
                report.archivedAt ? 'bg-amber-50/50 hover:bg-amber-50/80' : '',
              )}
              onClick={() => navigate(ROUTES.BUILD_OSHA_REPORT_DETAILS_PATH(report.id))}
            >
              <TableCell className="max-w-80">
                <div className="flex flex-col gap-1">
                  <span className="font-medium truncate">{report.slug}</span>
                  {report.archivedAt && <ArchivedBadge />}
                  <div className="text-sm text-muted-foreground line-clamp-1 truncate">{report.eventTitle}</div>
                </div>
              </TableCell>
              <TableCell className="w-24">
                <div className="text-sm text-muted-foreground truncate">
                  {report.privacyCase ? <span className="text-blue-600">Privacy Case</span> : report.employeeName}
                </div>
              </TableCell>
              <TableCell className="w-24">
                <div className="text-sm text-muted-foreground truncate">{report.employeeJobTitle}</div>
              </TableCell>
              <TableCell className="w-28">
                <CaseTypeBadge caseType={report.type} />
              </TableCell>
              <TableCell className="w-28">
                {report.wasDeceased ? (
                  <Badge className="bg-red-100 text-red-800 border-red-200" variant="outline">
                    Yes
                  </Badge>
                ) : (
                  <Badge className="bg-green-100 text-green-800 border-green-200" variant="outline">
                    No
                  </Badge>
                )}
              </TableCell>
              <TableCell className="w-36 text-sm text-gray-600">{formatDate(report.createdAt, true)}</TableCell>
              <TableCell className="w-28 text-sm text-gray-600">{report.oshaLocation?.name || 'Not specified'}</TableCell>
              <TableCell className="w-28 text-sm text-gray-900 font-medium">{report.daysAwayFromWork || 0}</TableCell>
              <TableCell className="w-28 text-sm text-gray-900 font-medium">{report.daysRestrictedFromWork}</TableCell>

              <TableCell className="w-32 text-right">
                <Button
                  variant="link"
                  size="icon"
                  onClick={(e) => {
                    e.stopPropagation();
                    navigate(ROUTES.BUILD_OSHA_REPORT_DETAILS_PATH(report.id));
                  }}
                >
                  <Eye className="h-4 w-4 text-blue-600" />
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
