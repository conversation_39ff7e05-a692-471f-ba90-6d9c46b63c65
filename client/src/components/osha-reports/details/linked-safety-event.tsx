import { StatusBadge } from '@/components/composite/status-badge';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { trpc } from '@/providers/trpc';
import { formatDate } from '@shared/date-utils';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { REPORT_TYPE_MAP } from '@shared/types/schema.types';
import { LinkIcon, Loader2 } from 'lucide-react';

export const LinkedSafetyEvent = ({ eventId }: { eventId?: string }) => {
  const { data, isLoading, isError } = trpc.event.getById.useQuery(
    {
      id: eventId!,
    },
    {
      enabled: !!eventId,
    },
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex justify-between items-center">
          <div className="flex items-center">
            <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
              1
            </span>
            <LinkIcon className="h-5 w-5 text-blue-600 mr-2" />
            Linked Safety Event Reference
          </div>
          <a
            href={ROUTES.BUILD_EVENT_DETAILS_PATH(eventId!)}
            target="_blank"
            className="text-primary hover:underline font-normal cursor-pointer text-sm"
          >
            Go to safety event
          </a>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading && (
          <div className="flex items-center justify-center p-6">
            <Loader2 className="animate-spin h-4 w-4 mr-2" />
            <p>Loading safety event details...</p>
          </div>
        )}

        {isError && (
          <div className="flex items-center justify-center p-6">
            <Loader2 className="animate-spin h-4 w-4 mr-2" />
            <p>Loading safety event details...</p>
          </div>
        )}

        {!isLoading && !isError && data && (
          <div className="border rounded-md p-4 bg-blue-50">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-3">
              <div>
                <h4 className="text-sm font-medium text-gray-500 mb-1">Safety Event ID</h4>
                <p className="font-medium">{data.slug}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500 mb-1">Title</h4>
                <p className="font-medium">{data.title}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500 mb-1">Date & Time</h4>
                <p>{formatDate(data.reportedAt)}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500 mb-1">Location</h4>
                <p>{data.location?.name || 'Not specified'}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500 mb-1">Status</h4>
                <StatusBadge status={data.status} />
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500 mb-1">Type</h4>
                <Badge variant="outline" className="border-blue-200">
                  {REPORT_TYPE_MAP[data.type]}
                </Badge>
              </div>
              <div className="col-span-2">
                <h4 className="text-sm font-medium text-gray-500 mb-1">Description</h4>
                <div className="bg-white p-2 rounded-md">
                  <p>{data.description || 'No description available'}</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
