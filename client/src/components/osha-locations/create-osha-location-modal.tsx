import { Button } from '@/components/ui/button';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { trpc } from '@/providers/trpc';
import { CreateOshaLocationSchema } from '@shared/types/settings.types';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import { usePermissions } from '@/hooks/use-permissions';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { Redirect } from 'wouter';

type CreateOshaLocationFormData = z.infer<typeof CreateOshaLocationSchema>;

export const CreateOshaLocationModal = ({
  isOpen,
  onClose,
  onSuccess,
}: {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}) => {
  const utils = trpc.useUtils();

  const { hasPermission } = usePermissions();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<CreateOshaLocationFormData>({
    resolver: zodResolver(CreateOshaLocationSchema),
    defaultValues: {
      name: '',
    },
  });

  const { mutateAsync: createOshaLocation } = trpc.oshaLocation.create.useMutation({
    onSuccess: () => {
      utils.oshaLocation.list.invalidate();
      toast('OSHA location created', {
        description: 'The OSHA location has been successfully created',
      });
    },
    onError: (error) => {
      toast('Error creating OSHA location', {
        description: error.message || 'Please try again',
      });
    },
  });

  const onSubmit = async (data: CreateOshaLocationFormData) => {
    try {
      // Trim the name to ensure no leading/trailing whitespace
      const trimmedData = {
        ...data,
        name: data.name.trim(),
      };

      await createOshaLocation(trimmedData);
      reset();
      onClose();
      onSuccess?.();
    } catch (error) {
      console.error('Error creating OSHA location:', error);
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  if (!hasPermission(MODULES.OSHA_LOCATIONS, ALLOWED_ACTIONS.CREATE)) {
    return <Redirect to={ROUTES.OSHA_LOCATIONS_LIST} />;
  }

  return (
    <AlertDialog open={isOpen} onOpenChange={handleClose}>
      <AlertDialogContent className="sm:max-w-md">
        <AlertDialogHeader>
          <AlertDialogTitle>Create OSHA Location</AlertDialogTitle>
          <AlertDialogDescription>
            Create a new OSHA location that can be used across your organization.
          </AlertDialogDescription>
        </AlertDialogHeader>

        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            handleSubmit(onSubmit)(e);
          }}
          className="space-y-4"
        >
          <div className="space-y-2">
            <Label htmlFor="name">Name *</Label>
            <Input
              id="name"
              placeholder="Enter OSHA location name"
              {...register('name')}
              className={errors.name ? 'border-red-500' : ''}
            />
            {errors.name && <p className="text-sm text-red-500">{errors.name.message}</p>}
          </div>

          <AlertDialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Creating...' : 'Create OSHA Location'}
            </Button>
          </AlertDialogFooter>
        </form>
      </AlertDialogContent>
    </AlertDialog>
  );
};
