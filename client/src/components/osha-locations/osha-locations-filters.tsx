import { AsyncUsersFilter } from '@/components/composite/async-users-filter';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DateRangePicker } from '@/components/ui/date-range-picker';
import { OshaLocationsFilters } from '@shared/types/settings.types';
import { Archive, Filter, X } from 'lucide-react';

export const OshaLocationsFiltersComponent = ({
  filters,
  updateFilter,
  activeFilterCount,
  resetFilters,
}: {
  filters: OshaLocationsFilters;
  updateFilter: (key: keyof OshaLocationsFilters, value: OshaLocationsFilters[keyof OshaLocationsFilters]) => void;
  activeFilterCount: number;
  resetFilters: () => void;
}) => {
  return (
    <div className="hidden  bg-gray-50 py-6 md:sticky md:top-0 md:z-10 md:block ">
      <div className="flex items-center gap-2 flex-wrap">
        <Button variant="outline" size="sm" className={activeFilterCount > 0 ? 'bg-muted' : ''}>
          <Filter className="h-4 w-4" />
          Filters
          {activeFilterCount > 0 && (
            <Badge className="ml-1 h-5 px-1 py-0" variant="secondary">
              {activeFilterCount}
            </Badge>
          )}
        </Button>

        {/* Created By Filter */}
        <AsyncUsersFilter
          selected={filters.createdBy}
          onSelect={(createdBy) => {
            updateFilter('createdBy', createdBy);
          }}
          label="Created By"
          placeholder="Search created by"
        />

        {/* Created Date Range Filter */}
        <DateRangePicker
          range={{
            from: filters.createdDateRange?.from,
            to: filters.createdDateRange?.to,
          }}
          setRange={(dateRange) => {
            updateFilter('createdDateRange', dateRange);
          }}
          placeholder="Created date range"
        />

        {/* Include Archived Filter */}
        <Button
          variant={filters.includeArchived ? 'default' : 'outline'}
          size="sm"
          onClick={() => {
            const newValue = !filters.includeArchived;
            updateFilter('includeArchived', newValue);
          }}
          className={filters.includeArchived ? 'bg-amber-600 hover:bg-amber-700' : ''}
        >
          <Archive className="h-4 w-4" />
          Include Archived
        </Button>

        {/* Reset Filters */}
        {activeFilterCount > 0 && (
          <Button variant="ghost" size="sm" onClick={resetFilters} className="h-9">
            <X className="h-3.5 w-3.5" />
            Clear Filters
          </Button>
        )}
      </div>
    </div>
  );
};
