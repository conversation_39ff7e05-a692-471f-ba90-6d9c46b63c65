import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Draw } from '@/components/ui/draw';
import { Label } from '@/components/ui/label';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { formatDate } from '@shared/date-utils';
import { RouterOutputs } from '@shared/types/router.types';
import { FileText } from 'lucide-react';

export const CompanyExecutiveCertificationDetails = ({
  establishmentInfo,
  onEdit,
}: {
  establishmentInfo?: RouterOutputs['oshaSummary']['getEstablishmentInformation'];
  onEdit: () => void;
}) => {
  return (
    <Card className="pb-5">
      <CardHeader>
        <CardTitle className="flex items-center justify-between w-full">
          <div className="flex items-center">
            <div className="flex items-center justify-center w-12 h-12 bg-blue-50 rounded-xl mr-4">
              <FileText className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <div>Company Executive Certification</div>
              <div className="text-sm font-normal text-gray-600 mt-1">Digital signature and certification</div>
            </div>
          </div>
          {!establishmentInfo?.archivedAt && (
            <div>
              <Tooltip delayDuration={1000}>
                <TooltipTrigger asChild>
                  <div>
                    <Button variant="outline" disabled={!establishmentInfo?.id} size="sm" onClick={onEdit}>
                      Edit
                    </Button>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  Save the establishment information before saving the executive certification.
                </TooltipContent>
              </Tooltip>
            </div>
          )}
        </CardTitle>
      </CardHeader>

      <CardContent className="pt-6 space-y-6">
        {/* Company Name */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-gray-700">Name of Company Executive</Label>
          <p className="text-gray-900 font-medium">{establishmentInfo?.executiveName ?? 'Not specified'}</p>
        </div>

        {/* Facility ID */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-gray-700">Title</Label>
          <p className="text-gray-900 font-medium">{establishmentInfo?.executiveTitle ?? 'Not specified'}</p>
        </div>

        {/* Date Certified */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-gray-700">Date Certified</Label>
          <p className="text-gray-900 font-medium">
            {establishmentInfo?.dateCertified ? formatDate(establishmentInfo?.dateCertified, true) : 'Not specified'}
          </p>
        </div>

        {/* Digital Signature */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-gray-700">Digital Signature</Label>
          <Draw viewMode data={establishmentInfo?.digitalSignature ?? ''} />
        </div>
      </CardContent>
    </Card>
  );
};
