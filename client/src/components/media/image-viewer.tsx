import { TransientFileSchema } from '@shared/types/files.types';
import { z } from 'zod';
import { useEffect, useRef, useState } from 'react';

interface ImageViewerSpecificProps {
  file: z.infer<typeof TransientFileSchema>;
}

interface ImageViewerProps
  extends Omit<React.ImgHTMLAttributes<HTMLImageElement>, 'src' | 'onError'>,
    ImageViewerSpecificProps {}

export const ImageViewer = (props: ImageViewerProps) => {
  const { file, alt, className, ...restProps } = props;
  const imageAlt = alt || file.name;
  const imgRef = useRef<HTMLImageElement>(null);
  const [currentSize, setCurrentSize] = useState<'small' | 'original'>('small');

  useEffect(() => {
    const img = imgRef.current;
    if (!img || !img.parentElement) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width } = entry.contentRect;

        // Choose image variant based on parent container width
        if (width <= 300) {
          setCurrentSize('small');
        } else {
          setCurrentSize('original');
        }
      }
    });

    resizeObserver.observe(img.parentElement);

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  return (
    <img
      {...restProps}
      alt={imageAlt}
      className={className}
      decoding="async"
      loading="lazy"
      ref={imgRef}
      src={getVariantUrl(file.url, currentSize)}
      onError={() => {
        // fallback to original if there is no variant ready
        imgRef.current!.src = file.url;
      }}
    />
  );
};

const getVariantUrl = (originalUrl: string, variant: 'small' | 'original') => {
  if (variant === 'original' || !variant) {
    return originalUrl;
  }

  // Blob URLs and data URLs can't have variants - return original URL
  if (originalUrl.startsWith('blob:') || originalUrl.startsWith('data:')) {
    return originalUrl;
  }

  // Split URL at the last dot to insert suffix before file extension
  const lastDotIndex = originalUrl.lastIndexOf('.');
  if (lastDotIndex !== -1) {
    const baseUrl = originalUrl.substring(0, lastDotIndex);
    const extension = originalUrl.substring(lastDotIndex);

    return `${baseUrl}_${variant}${extension}`;
  }

  return originalUrl;
};
