import { UploadArea } from '@/components/media/upload/upload-area';
import { useImageUpload } from '@/hooks/use-image-upload';
import { trpc } from '@/providers/trpc';
import { getValidationErrorMessage, MAX_FILE_SIZE_MB, validateFiles } from '@shared/files-utils';
import { entityTypeEnum } from '@shared/schema';
import { isHEIC, isImage, TransientFileSchema } from '@shared/types/files.types';
import axios from 'axios';
import { createElement, useCallback, useEffect, useRef, useState } from 'react';
import { toast } from 'sonner';
import { z } from 'zod';
import { MediaPreviewGrid } from './media-preview-grid';
import { MediaViewerModal } from './media-viewer-modal';

type TransientFile = z.infer<typeof TransientFileSchema>;

export type UploadState = {
  isUploading: boolean;
  pendingUploads: number;
  completedUploads: number;
  totalFiles: number;
};

type FileUploadStatus = 'pending' | 'uploading' | 'completed' | 'error' | 'canceled';

type MediaUploadProps = {
  className?: string;
  disabled?: boolean;
  entityId?: string;
  entityType: (typeof entityTypeEnum.enumValues)[number];
  files: TransientFile[];
  isPublic?: boolean;
  maxFiles?: number;
  maxSize?: number; // in MB
  onFileRemove?: (file: TransientFile) => Promise<void>;
  onUploadStateChange?: (state: UploadState) => void;
  setFiles: (tFiles: TransientFile[]) => void;
  upkeepCompanyId?: string;
};

export const MediaUpload = ({
  className,
  disabled = false,
  entityId,
  entityType,
  files,
  isPublic = false,
  maxFiles = 5,
  maxSize = MAX_FILE_SIZE_MB,
  onFileRemove,
  onUploadStateChange,
  setFiles,
  upkeepCompanyId,
}: MediaUploadProps) => {
  const [isRemovingFile, setIsRemovingFile] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [fileStatuses, setFileStatuses] = useState<Map<string, FileUploadStatus>>(new Map());

  const [uploadState, setUploadState] = useState<UploadState>({
    isUploading: false,
    pendingUploads: 0,
    completedUploads: 0,
    totalFiles: 0,
  });

  const [isViewerOpen, setIsViewerOpen] = useState(false);
  const [viewerIndex, setViewerIndex] = useState(0);

  const { uploadImage } = useImageUpload({ isPublic, upkeepCompanyId });
  const { mutateAsync: getPresignedUrlPrivate } = trpc.file.getPresignedUrl.useMutation();
  const { mutateAsync: updateFilePrivate } = trpc.file.update.useMutation();
  const { mutateAsync: getPresignedUrlPublic } = trpc.file.getPresignedUrlPublic.useMutation();
  const { mutateAsync: updateFilePublic } = trpc.file.updatePublic.useMutation();

  const inflightControllersRef = useRef(new Map<string, AbortController>());
  const currentFilesRef = useRef<TransientFile[]>(files);

  useEffect(() => {
    currentFilesRef.current = files;
  }, [files]);

  useEffect(() => {
    onUploadStateChange?.(uploadState);
  }, [onUploadStateChange, uploadState]);

  const updateUploadStateFromStatuses = useCallback(() => {
    const statuses = Array.from(fileStatuses.values());
    const totalFiles = statuses.length;
    const completedUploads = statuses.filter(
      (status) => status === 'completed' || status === 'error' || status === 'canceled',
    ).length;
    const pendingUploads = totalFiles - completedUploads;

    setUploadState({
      isUploading: pendingUploads > 0,
      pendingUploads,
      completedUploads,
      totalFiles,
    });
  }, [fileStatuses]);

  useEffect(() => {
    updateUploadStateFromStatuses();
  }, [updateUploadStateFromStatuses]);

  const updateFileStatus = useCallback((fileId: string, status: FileUploadStatus) => {
    setFileStatuses((prev) => {
      const newMap = new Map(prev);
      newMap.set(fileId, status);
      return newMap;
    });
  }, []);

  const updateFilesWithRef = useCallback(
    (updateFn: (currentFiles: TransientFile[]) => TransientFile[]) => {
      const updatedFiles = updateFn(currentFilesRef.current);
      currentFilesRef.current = updatedFiles;
      setFiles(updatedFiles);
    },
    [setFiles],
  );

  const getPlaceholderUrl = useCallback((fileName: string): string => {
    const svg = `
      <svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
        <rect width="200" height="150" fill="#f3f4f6" rx="8"/>
        <g transform="translate(100,75)">
          <circle cx="0" cy="-20" r="10" fill="#6b7280">
            <animate attributeName="opacity" values="1;0.3;1" dur="1.5s" repeatCount="indefinite"/>
          </circle>
          <text x="0" y="10" text-anchor="middle" font-family="sans-serif" font-size="9" fill="#374151">
            Processing
          </text>
          <text x="0" y="25" text-anchor="middle" font-family="sans-serif" font-size="8" fill="#6b7280">
            ${fileName.length > 20 ? fileName.substring(0, 17) + '...' : fileName}
          </text>
        </g>
      </svg>
    `;
    return `data:image/svg+xml;base64,${btoa(svg)}`;
  }, []);

  const handleFilesSelected = async (fileList: FileList) => {
    // Prevent race conditions: don't allow new operations while processing
    if (isProcessing) {
      return;
    }

    setIsProcessing(true);

    try {
      const validationResult = validateFiles(fileList, {
        maxSize,
        maxFiles,
        existingFileCount: files.length,
      });

      if (!validationResult.isValid) {
        const errorMessage = getValidationErrorMessage(validationResult);
        if (errorMessage) {
          toast.error('File validation failed', {
            description: () => {
              return (
                <ul>
                  {errorMessage.map((item, idx) =>
                    createElement('li', {
                      children: item,
                      key: idx,
                    }),
                  )}
                </ul>
              );
            },
          });
        }
        return;
      }

      const newFiles: TransientFile[] = [];
      const filesArray = Array.from(fileList);

      for (const file of filesArray) {
        let fileName = file.name;
        while (
          [...files, ...newFiles].findIndex((f) => f.name === fileName) !== -1 ||
          inflightControllersRef.current.has(fileName)
        ) {
          const fileNameWithExt = fileName.split('.');
          const extension = fileNameWithExt.pop();
          fileName = `${fileNameWithExt.join('')} (Copy).${extension}`;
        }

        const previewUrl = isHEIC(file.type) ? getPlaceholderUrl(fileName) : URL.createObjectURL(file);
        newFiles.push({ name: fileName, url: previewUrl, type: file.type, size: file.size, file });
        updateFileStatus(fileName, 'pending');
      }

      setFiles([...files, ...newFiles]);

      const uploadPromises = newFiles.map(async (fileItem) => {
        updateFileStatus(fileItem.name, 'uploading');

        try {
          if (isImage(fileItem.type)) {
            const result = await uploadImage(fileItem, { entityType, entityId });

            if (result?.file) {
              updateFilesWithRef((currentFiles) => {
                const updatedFiles = [...currentFiles];
                const idx = updatedFiles.findIndex((f) => f.name === fileItem.name);

                if (idx !== -1) {
                  const previousUrl = updatedFiles[idx].url;
                  const shouldUseServerUrl = isHEIC(fileItem.type);
                  updatedFiles[idx] = {
                    ...updatedFiles[idx],
                    id: result.file.id,
                    name: result.file.fileName,
                    url: shouldUseServerUrl ? result.file.presignedUrl : previousUrl,
                    type: result.file.mimeType,
                    size: result.file.fileSize,
                  };
                  if (shouldUseServerUrl && previousUrl?.startsWith('blob:')) {
                    URL.revokeObjectURL(previousUrl);
                  }
                }

                return updatedFiles;
              });
            }
            updateFileStatus(fileItem.name, 'completed');
          } else {
            const presigned = isPublic
              ? await getPresignedUrlPublic({
                  fileName: fileItem.name,
                  fileSize: fileItem.size,
                  mimeType: fileItem.type,
                  entityType,
                  entityId,
                  upkeepCompanyId: upkeepCompanyId!,
                })
              : await getPresignedUrlPrivate({
                  fileName: fileItem.name,
                  fileSize: fileItem.size,
                  mimeType: fileItem.type,
                  entityType,
                  entityId,
                });

            const controller = new AbortController();
            inflightControllersRef.current.set(fileItem.name, controller);

            await axios.put(presigned.presignedUrl, fileItem.file, {
              headers: { 'Content-Type': presigned.file.mimeType },
              signal: controller.signal,
            });

            if (!controller.signal.aborted) {
              const updateResult = isPublic
                ? await updateFilePublic({
                    id: presigned.file.id,
                    s3Key: presigned.file.s3Key,
                    status: 'completed',
                    upkeepCompanyId: upkeepCompanyId!,
                  })
                : await updateFilePrivate({
                    id: presigned.file.id,
                    s3Key: presigned.file.s3Key,
                    status: 'completed',
                  });

              updateFilesWithRef((currentFiles) => {
                const updatedFiles = [...currentFiles];
                const idx = updatedFiles.findIndex((f) => f.name === fileItem.name);
                if (idx !== -1) {
                  updatedFiles[idx] = {
                    ...updatedFiles[idx],
                    id: updateResult?.id || presigned.file.id,
                    name: presigned.file.fileName,
                    type: presigned.file.mimeType,
                    size: presigned.file.fileSize,
                  };
                }
                return updatedFiles;
              });

              updateFileStatus(fileItem.name, 'completed');
            }
          }
        } catch (error) {
          if (axios.isCancel(error)) {
            updateFileStatus(fileItem.name, 'canceled');
          } else {
            updateFileStatus(fileItem.name, 'error');
            toast.error(`Failed to upload ${fileItem.name}`, {
              description: 'Please try uploading the file again.',
            });
          }
        } finally {
          inflightControllersRef.current.delete(fileItem.name);
        }
      });

      await Promise.allSettled(uploadPromises);
    } catch {
      toast.error('Failed to process files', {
        description: 'There was a problem processing some files. Please try again.',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleFileRemove = useCallback(
    async (fileName: string) => {
      // Prevent race conditions: don't allow new operations while processing
      if (isProcessing || isRemovingFile) {
        return;
      }

      setIsRemovingFile(true);

      try {
        const fileToRemove = currentFilesRef.current.find((item) => item.name === fileName);

        if (!fileToRemove) {
          return;
        }

        const controller = inflightControllersRef.current.get(fileToRemove.name);
        if (controller) {
          controller.abort();
          inflightControllersRef.current.delete(fileToRemove.name);
        }

        if (fileToRemove.url && fileToRemove.url.startsWith('blob:')) {
          URL.revokeObjectURL(fileToRemove.url);
        }

        setFileStatuses((prev) => {
          const newMap = new Map(prev);
          newMap.delete(fileName);
          return newMap;
        });

        updateFilesWithRef((currentFiles) => currentFiles.filter((item) => item.name !== fileName));

        if (fileToRemove.id && onFileRemove) {
          await onFileRemove(fileToRemove);
        }
      } catch {
        toast.error('Failed to remove file', {
          description: `There was a problem removing "${fileName}". Please try again.`,
        });
      } finally {
        setIsRemovingFile(false);
      }
    },
    [onFileRemove, updateFilesWithRef, isProcessing, isRemovingFile],
  );

  const handleThumbnailClick = useCallback((index: number) => {
    setViewerIndex(index);
    setIsViewerOpen(true);
  }, []);

  return (
    <>
      <UploadArea
        className={className}
        currentFileCount={files.length}
        disabled={disabled || isProcessing || isRemovingFile}
        maxFiles={maxFiles}
        maxSize={maxSize}
        onFilesSelected={handleFilesSelected}
      />

      <MediaPreviewGrid
        files={files}
        isRemoving={isRemovingFile}
        onFileRemove={handleFileRemove}
        onThumbnailClick={handleThumbnailClick}
      />

      <MediaViewerModal
        files={files}
        initialIndex={viewerIndex}
        isOpen={isViewerOpen}
        onClose={() => setIsViewerOpen(false)}
      />
    </>
  );
};
