import { cn } from '@/lib/utils';
import {
  DocumentPreviewCapability,
  getDocumentPreviewCapability,
  getMediaLabel,
  formatFileSize,
} from '@shared/types/files.types';
import { Download, File, FileText, AlertCircle, Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Button } from '../ui/button';
import { Alert, AlertDescription } from '../ui/alert';

type DocumentFile = {
  url: string;
  name: string;
  type: string;
  size?: number;
};

type DocumentPreviewProps = {
  file: DocumentFile;
  className?: string;
  onError?: (error: Error) => void;
  onLoad?: () => void;
};

export function DocumentPreview({ file, className, onError, onLoad }: DocumentPreviewProps) {
  const [previewError, setPreviewError] = useState<Error | null>(null);
  const previewCapability = getDocumentPreviewCapability(file.type);

  const handleError = (error: Error) => {
    setPreviewError(error);
    onError?.(error);
  };

  const handleLoad = () => {
    setPreviewError(null);
    onLoad?.();
  };

  const renderPreview = () => {
    if (previewError) {
      return <ErrorFallbackPreview file={file} error={previewError} />;
    }

    switch (previewCapability) {
      case DocumentPreviewCapability.OBJECT_EMBED:
        return <ObjectEmbedPreview file={file} onError={handleError} onLoad={handleLoad} />;

      case DocumentPreviewCapability.TEXT_CONTENT:
        return <TextContentPreview file={file} onError={handleError} onLoad={handleLoad} />;

      case DocumentPreviewCapability.DOWNLOAD_ONLY:
        return <DownloadOnlyPreview file={file} />;

      default:
        return <UnsupportedPreview file={file} />;
    }
  };

  return <div className={cn('w-full h-full flex flex-col', className)}>{renderPreview()}</div>;
}

/**
 * Preview component for PDFs using HTML object/embed tags.
 * Implements graceful degradation with fallback mechanisms.
 */
type PreviewComponentProps = {
  file: DocumentFile;
  onError?: (error: Error) => void;
  onLoad?: () => void;
};

function ObjectEmbedPreview({ file, onError, onLoad }: PreviewComponentProps) {
  const [fallbackToDownload, setFallbackToDownload] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const handleObjectError = () => {
    setFallbackToDownload(true);
    setIsLoading(false);
    onError?.(
      new Error(
        `Could not preview ${file.name}. The document may be corrupted or your browser doesn't support PDF preview.`,
      ),
    );
  };

  const handleObjectLoad = () => {
    setIsLoading(false);
    onLoad?.();
  };

  if (fallbackToDownload) {
    return <DownloadOnlyPreview file={file} />;
  }

  return (
    <div className="relative w-full h-full">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-background/80 z-10">
          <div className="flex flex-col items-center gap-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <p className="text-sm text-muted-foreground">Loading document...</p>
          </div>
        </div>
      )}

      <object
        data={file.url}
        type={file.type}
        className="w-full h-full"
        onError={handleObjectError}
        onLoad={handleObjectLoad}
        aria-label={`Preview of ${file.name}`}
      >
        <embed
          src={file.url}
          type={file.type}
          className="w-full h-full"
          onError={handleObjectError}
          aria-label={`Preview of ${file.name}`}
        />
        {/* Final fallback if both object and embed fail */}
        <DownloadOnlyPreview file={file} />
      </object>
    </div>
  );
}

/**
 * Preview component for text-based files (plain text, CSV).
 * Loads and displays file content with proper formatting.
 */
function TextContentPreview({ file, onError, onLoad }: PreviewComponentProps) {
  const [content, setContent] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadTextContent = async () => {
      try {
        const response = await fetch(file.url);

        if (!response.ok) {
          throw new Error(`Failed to load ${file.name}: ${response.status} ${response.statusText}`);
        }

        // Check if the response is actually text
        const contentType = response.headers.get('content-type');
        if (contentType && !contentType.includes('text') && !contentType.includes('csv')) {
          throw new Error(`${file.name} does not appear to be a text file`);
        }

        const text = await response.text();

        // Basic validation for text content
        if (text.length > 1024 * 1024) {
          // 1MB limit for text preview
          throw new Error(`${file.name} is too large to preview (${Math.round(text.length / 1024)}KB)`);
        }

        // Sanitize text content for security
        const sanitizedText = sanitizeTextContent(text);
        setContent(sanitizedText);
        onLoad?.();
      } catch (error) {
        onError?.(error as Error);
      } finally {
        setIsLoading(false);
      }
    };

    loadTextContent();
  }, [file.url, file.name, onError, onLoad]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="flex flex-col items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <p className="text-sm text-muted-foreground">Loading text content...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full overflow-auto bg-white border border-border rounded-md">
      <div className="p-4">
        <pre className="text-sm font-mono whitespace-pre-wrap break-words leading-relaxed">
          {content || '(Empty file)'}
        </pre>
      </div>
    </div>
  );
}

/**
 * Fallback component for documents that cannot be previewed.
 */
function DownloadOnlyPreview({ file }: { file: DocumentFile }) {
  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = file.url;
    link.download = file.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const formatFileSizeWithParens = (bytes?: number) => {
    if (!bytes) return '';
    return ` (${formatFileSize(bytes)})`;
  };

  return (
    <div className="flex flex-col items-center justify-center h-full p-8 text-center">
      <div className="flex flex-col items-center gap-4 max-w-md">
        <div className="p-4 rounded-full bg-muted">
          <File className="h-12 w-12 text-muted-foreground" />
        </div>

        <div className="space-y-2">
          <h3 className="font-medium text-lg">Preview Not Available</h3>
          <p className="text-sm text-muted-foreground">
            {getMediaLabel(file.type)} files cannot be previewed in the browser. Click the download button to view this
            file.
          </p>
        </div>

        <div className="text-xs text-muted-foreground">
          <div className="font-mono bg-muted px-2 py-1 rounded">
            {file.name}
            {formatFileSizeWithParens(file.size)}
          </div>
        </div>

        <Button onClick={handleDownload} className="flex items-center gap-2">
          <Download className="h-4 w-4" />
          Download {getMediaLabel(file.type)}
        </Button>
      </div>
    </div>
  );
}

function UnsupportedPreview({ file }: { file: DocumentFile }) {
  return (
    <div className="flex flex-col items-center justify-center h-full p-8 text-center">
      <div className="flex flex-col items-center gap-4 max-w-md">
        <div className="p-4 rounded-full bg-destructive/10">
          <AlertCircle className="h-12 w-12 text-destructive" />
        </div>

        <div className="space-y-2">
          <h3 className="font-medium text-lg">Unsupported File Type</h3>
          <p className="text-sm text-muted-foreground">
            This file type is not supported for preview or download through the viewer.
          </p>
        </div>

        <div className="text-xs text-muted-foreground">
          <div className="font-mono bg-muted px-2 py-1 rounded">{file.name}</div>
        </div>
      </div>
    </div>
  );
}

function ErrorFallbackPreview({ file, error }: { file: DocumentFile; error: Error }) {
  return (
    <div className="flex flex-col items-center justify-center h-full p-8">
      <Alert className="max-w-md">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          <div className="space-y-2">
            <p className="font-medium">Preview Error</p>
            <p className="text-sm">{error.message}</p>
            <DownloadOnlyPreview file={file} />
          </div>
        </AlertDescription>
      </Alert>
    </div>
  );
}

type DocumentThumbnailProps = {
  file: DocumentFile;
  isSelected?: boolean;
};

export const DocumentThumbnail = ({ file, isSelected = false }: DocumentThumbnailProps) => {
  const capability = getDocumentPreviewCapability(file.type);
  const isPreviewable =
    capability !== DocumentPreviewCapability.DOWNLOAD_ONLY && capability !== DocumentPreviewCapability.UNSUPPORTED;

  return (
    <div
      className={cn('flex items-center justify-center w-full h-full bg-muted relative', isSelected && 'bg-primary/10')}
    >
      <FileText
        className={cn('h-6 w-6', isSelected ? 'text-primary' : 'text-muted-foreground', !isPreviewable && 'opacity-60')}
      />

      {/* Small indicator for previewable documents */}
      {isPreviewable && <div className="absolute top-1 right-1 w-2 h-2 bg-green-500 rounded-full" />}
    </div>
  );
};

/**
 * Sanitizes text content for safe display.
 * Removes potentially dangerous content while preserving formatting.
 */
const sanitizeTextContent = (content: string) => {
  // Basic sanitization - remove null bytes and control characters except newlines/tabs
  return content
    .replace(/\0/g, '') // Remove null bytes
    .replace(/[\x01-\x08\x0B-\x0C\x0E-\x1F\x7F]/g, ''); // Remove other control chars
};
