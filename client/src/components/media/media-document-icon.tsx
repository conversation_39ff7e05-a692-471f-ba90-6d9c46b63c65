import { DOCUMENT_TYPE_BY_MIME_TYPE } from '@shared/types/files.types';
import { TransientFileSchema } from '@shared/types/files.types';
import { FileText, FileSpreadsheet, Sheet, FileIcon } from 'lucide-react';
import z from 'zod';
type TransientFile = z.infer<typeof TransientFileSchema>;

/**
 * MediaDocumentIcon component displays the appropriate icon based on file type.
 *
 * Renders different icons for various file types (PDF, spreadsheet, word document,
 * presentation, text) with appropriate colors. Falls back to a generic file icon
 * when the file type is not recognized.
 *
 * @param file - A TransientFile object containing file metadata including MIME type
 */
export const MediaDocumentIcon = ({ file }: { file: TransientFile }) => {
  const mimeType = file.type;
  const fileType = DOCUMENT_TYPE_BY_MIME_TYPE[mimeType as keyof typeof DOCUMENT_TYPE_BY_MIME_TYPE];

  const ICONS_BY_FILE_TYPE = {
    pdf: <FileText className="h-7 w-7 sm:h-8 sm:w-8 text-red-500" />,
    spreadsheet: <FileSpreadsheet className="h-7 w-7 sm:h-8 sm:w-8 text-green-600" />,
    word: <FileText className="h-7 w-7 sm:h-8 sm:w-8 text-blue-600" />,
    presentation: <Sheet className="h-7 w-7 sm:h-8 sm:w-8 text-orange-500" />,
    text: <FileText className="h-7 w-7 sm:h-8 sm:w-8 text-gray-600" />,
  } as const;

  const IconComponent = ICONS_BY_FILE_TYPE[fileType as keyof typeof ICONS_BY_FILE_TYPE];
  return IconComponent ?? <FileIcon className="h-7 w-7 sm:h-8 sm:w-8 text-gray-500" />;
};
