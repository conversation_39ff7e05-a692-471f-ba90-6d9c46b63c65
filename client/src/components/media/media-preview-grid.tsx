import { getMedia<PERSON>abel, isImage, isVideo, formatFileSize } from '@shared/types/files.types';
import { TransientFileSchema } from '@shared/types/files.types';
import { X, Loader2, Play } from 'lucide-react';
import type React from 'react';
import { z } from 'zod';
import { MediaDocumentIcon } from './media-document-icon';
import { ImageViewer } from '@/components/media/image-viewer';
import { Button } from '../ui/button';

type TransientFile = z.infer<typeof TransientFileSchema>;

type MediaPreviewGridProps = {
  className?: string;
  files: TransientFile[];
  isRemoving: boolean;
  onFileRemove: (fileName: string) => void;
  onThumbnailClick: (index: number) => void;
};

export const MediaPreviewGrid = ({
  className = '',
  files,
  isRemoving,
  onFileRemove,
  onThumbnailClick,
}: MediaPreviewGridProps) => {
  if (files.length === 0) {
    return null;
  }

  const renderFilePreview = (file: TransientFile, index: number) => {
    if (isImage(file.type)) {
      return (
        <ImageViewer
          file={file}
          alt={`Preview of ${file.name}`}
          className="h-full w-full object-cover cursor-pointer hover:opacity-90 transition-opacity"
          onClick={() => onThumbnailClick(index)}
        />
      );
    }

    if (isVideo(file.type)) {
      return (
        <div
          className="relative h-full w-full cursor-pointer hover:opacity-90 transition-opacity"
          onClick={() => onThumbnailClick(index)}
        >
          <video src={file.url} className="h-full w-full object-cover" preload="metadata" muted>
            Your browser does not support the video tag.
          </video>
          <div className="absolute inset-0 flex items-center justify-center bg-black/20">
            <div className="flex items-center justify-center w-10 h-10 bg-white/90 rounded-full shadow-lg">
              <Play className="h-5 w-5 text-gray-700 ml-0.5" fill="currentColor" />
            </div>
          </div>
        </div>
      );
    }

    // Document preview
    return (
      <div
        className="flex flex-col items-center justify-center h-full w-full bg-muted cursor-pointer hover:opacity-90 transition-opacity"
        onClick={() => onThumbnailClick(index)}
      >
        <MediaDocumentIcon file={file} />
        <span className="text-xs text-muted-foreground truncate max-w-[90%] mt-1 text-center px-1">{file.name}</span>
      </div>
    );
  };

  return (
    <div className={`mt-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 ${className}`}>
      {files.map((file, index) => (
        <div
          key={file.name}
          className="relative rounded-md border bg-white sm:bg-muted/20 p-2 group shadow-xs"
          aria-label={`Preview of uploaded file: ${file.name}`}
        >
          <div className="aspect-video relative flex items-center justify-center overflow-hidden rounded">
            {renderFilePreview(file, index)}
          </div>

          {/* Remove button */}
          <Button
            type="button"
            variant="destructive"
            size="icon"
            onClick={() => onFileRemove(file.name)}
            className="absolute cursor-pointer -top-1.5 -right-1.5 h-5 w-5 rounded-full bg-destructive text-destructive-foreground flex items-center justify-center hover:bg-destructive/80 transition-colors"
            aria-label={`Remove file ${file.name}`}
            disabled={isRemoving}
          >
            {isRemoving && file.id ? <Loader2 className="h-3 w-3 animate-spin" /> : <X className="h-3 w-3" />}
          </Button>

          {/* File caption */}
          <div className="mt-2 text-xs text-center text-muted-foreground">
            <div className="truncate max-w-full" title={file.name}>
              {getMediaLabel(file.type)} {index + 1} of {files.length}
              <br />
              {file.name}
            </div>
            {/* Show file size */}
            <div className="text-[10px] opacity-75 mt-0.5">{formatFileSize(file.size)}</div>
          </div>
        </div>
      ))}
    </div>
  );
};
