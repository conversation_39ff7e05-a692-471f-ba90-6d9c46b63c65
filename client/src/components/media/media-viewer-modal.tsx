import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { cn } from '@/lib/utils';
import { isImage, isVideo, isDocument, TransientFileSchema } from '@shared/types/files.types';
import { DocumentPreview, DocumentThumbnail } from './document-preview';
import { ImageViewer } from './image-viewer';
import { handleDownload } from '@/lib/download-file';
import { ChevronLeft, ChevronRight, Download, File } from 'lucide-react';
import { useEffect, useState } from 'react';
import { z } from 'zod';
import { toast } from 'sonner';
import React from 'react';

type MediaViewerModalProps = {
  files: z.infer<typeof TransientFileSchema>[];
  initialIndex: number;
  isOpen: boolean;
  onClose: () => void;
};

export const MediaViewerModal = ({ files, initialIndex, isOpen, onClose }: MediaViewerModalProps) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);

  // Reset current index when modal opens with new initial index
  useEffect(() => {
    setCurrentIndex(initialIndex);
  }, [initialIndex, isOpen]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'Escape':
          onClose();
          break;
        case 'ArrowLeft':
          goToPrevious();
          break;
        case 'ArrowRight':
          goToNext();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, currentIndex]);

  const currentFile = files[currentIndex];

  const currentFileIsVideo = isVideo(currentFile?.type);
  const currentFileIsImage = isImage(currentFile?.type);
  const currentFileIsDocument = isDocument(currentFile?.type);

  const goToPrevious = () => {
    setCurrentIndex((prev) => (prev > 0 ? prev - 1 : files.length - 1));
  };

  const goToNext = () => {
    setCurrentIndex((prev) => (prev < files.length - 1 ? prev + 1 : 0));
  };

  const handleFileDownload = async () => {
    if (currentFile) {
      await handleDownload(currentFile.url, currentFile.name, (msg, options) =>
        toast(msg, options as { description?: string }),
      );
    }
  };

  if (!currentFile) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      {/* aria-describedby - https://www.radix-ui.com/primitives/docs/components/dialog#description */}
      <DialogContent className="sm:max-w-5xl max-w-[95vw] max-h-[90vh] p-0 border-0" aria-describedby={undefined}>
        <DialogTitle className="hidden" />
        <div className="h-full flex flex-col bg-background rounded-lg overflow-hidden">
          {/* Header */}
          <div className="flex-shrink-0 p-4 border-b">
            <div className="flex items-center justify-between">
              <Button variant="ghost" size="icon" onClick={handleFileDownload}>
                <Download className="h-3.5 w-3.5" />
              </Button>
              <div className="flex justify-center flex-1">
                <h3 className="font-medium truncate text-sm">{currentFile.name}</h3>
              </div>
            </div>
          </div>

          {/* Media Content */}
          <div
            className="flex items-center justify-center p-4 relative overflow-hidden"
            style={{
              height:
                files.length > 1
                  ? 'calc(90vh - 140px)' // Reserve space for header + thumbnails
                  : 'calc(90vh - 80px)', // Reserve space for header only
            }}
          >
            {currentFileIsImage && (
              <ImageViewer file={currentFile} alt={currentFile.name} className="max-w-full max-h-full object-contain" />
            )}

            {currentFileIsVideo && (
              <video src={currentFile.url} controls className="max-w-full max-h-full" autoPlay={false}>
                Your browser does not support the video tag.
              </video>
            )}

            {currentFileIsDocument && (
              <DocumentPreview
                file={currentFile}
                className="max-w-full max-h-full"
                onError={(error) => {
                  console.error('Document preview error:', error);
                }}
              />
            )}

            {/* Navigation Arrows */}
            {files.length > 1 && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={goToPrevious}
                  className="absolute left-6 top-1/2 -translate-y-1/2 h-12 w-12 rounded-full bg-black/40 text-white border border-white/20 hover:border-white/40 shadow-lg transition-all duration-200 hover:scale-105"
                >
                  <ChevronLeft className="h-6 w-6" />
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={goToNext}
                  className="absolute right-6 top-1/2 -translate-y-1/2 h-12 w-12 rounded-full bg-black/40 text-white border border-white/20 hover:border-white/40 shadow-lg transition-all duration-200 hover:scale-105"
                >
                  <ChevronRight className="h-6 w-6" />
                </Button>
              </>
            )}
          </div>

          {/* Bottom Thumbnail Strip (for multiple files) */}
          {files.length > 1 && (
            <div className="flex-shrink-0 p-3 border-t bg-background/95">
              <div className="flex justify-center gap-2 overflow-x-auto">
                {files.map((file, index) => (
                  <React.Fragment key={file.name}>
                    <button
                      onClick={() => setCurrentIndex(index)}
                      className={cn(
                        'flex-shrink-0 w-14 h-14 rounded border-2 overflow-hidden transition-colors',
                        index === currentIndex ? 'border-primary' : 'border-border hover:border-primary/60',
                      )}
                    >
                      {isImage(file.type) ? (
                        <ImageViewer
                          file={file}
                          alt={`Thumbnail ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      ) : isVideo(file.type) ? (
                        <video src={file.url} className="w-full h-full object-cover" muted />
                      ) : isDocument(file.type) ? (
                        <DocumentThumbnail file={file} isSelected={index === currentIndex} />
                      ) : (
                        <div className="flex items-center justify-center w-full h-full bg-muted">
                          <File className="h-6 w-6 text-muted-foreground" />
                        </div>
                      )}
                    </button>
                  </React.Fragment>
                ))}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
