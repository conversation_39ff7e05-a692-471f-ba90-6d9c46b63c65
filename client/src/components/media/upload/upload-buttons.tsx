import { Button } from '@/components/ui/button';
import { Camera, FileImage, Upload } from 'lucide-react';
import type React from 'react';

type UploadActionsProps = {
  onCameraClick: () => void;
  onGalleryClick: () => void;
  disabled?: boolean;
  showCamera?: boolean;
  className?: string;
};

/**
 * Component for upload action buttons (camera, gallery, browse).
 */
export const UploadButtons = ({
  onCameraClick,
  onGalleryClick,
  disabled = false,
  showCamera = true,
  className = '',
}: UploadActionsProps) => {
  return (
    <div className={className}>
      {/* Mobile buttons (camera, gallery, and documents) */}
      <div className="flex flex-col gap-2 justify-center mt-3 sm:hidden">
        {showCamera && (
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={onCameraClick}
            className="text-xs h-10 flex items-center justify-center gap-2"
            disabled={disabled}
          >
            <Camera className="h-4 w-4" />
            Take Photo or Video
          </Button>
        )}

        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={onGalleryClick}
          className="text-xs h-10 flex items-center justify-center gap-2"
          disabled={disabled}
        >
          <FileImage className="h-4 w-4" />
          Choose Files
        </Button>
      </div>

      {/* Desktop button (browse files) */}
      <div className="hidden sm:flex justify-start mt-3">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={onGalleryClick}
          className="text-xs h-9 px-3"
          disabled={disabled}
          aria-label="Browse files to upload"
        >
          <Upload className="h-4 w-4 mr-2" />
          Browse Files
        </Button>
      </div>
    </div>
  );
};
