import { SUPPORTED_MIME_TYPES } from '@shared/types/files.types';
import type React from 'react';
import { useRef, useCallback } from 'react';

type UploadInputControlsProps = {
  onFilesSelected: (files: FileList) => void;
  disabled?: boolean;
  allowMultiple?: boolean;
  acceptCamera?: boolean;
};

export const useInputControls = ({
  onFilesSelected,
  disabled = false,
  allowMultiple = true,
  acceptCamera = true,
}: UploadInputControlsProps) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const cameraInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (e.target.files) {
        onFilesSelected(e.target.files);
        // Reset input so same file can be selected again
        e.target.value = '';
      }
    },
    [onFilesSelected],
  );

  const triggerFileSelect = useCallback(() => {
    if (!disabled) {
      inputRef.current?.click();
    }
  }, [disabled]);

  const triggerCameraSelect = useCallback(() => {
    if (!disabled && acceptCamera) {
      cameraInputRef.current?.click();
    }
  }, [disabled, acceptCamera]);

  const InputElements = useCallback(
    () => (
      <>
        <input
          ref={inputRef}
          type="file"
          onChange={handleFileChange}
          multiple={allowMultiple}
          accept={Object.keys(SUPPORTED_MIME_TYPES).join(',')}
          className="hidden"
          aria-hidden="true"
          disabled={disabled}
        />

        {acceptCamera && (
          <input
            ref={cameraInputRef}
            type="file"
            onChange={handleFileChange}
            accept="image/*,video/*"
            capture="environment"
            className="hidden"
            aria-hidden="true"
            disabled={disabled}
          />
        )}
      </>
    ),
    [handleFileChange, allowMultiple, acceptCamera, disabled],
  );

  return {
    triggerFileSelect,
    triggerCameraSelect,
    InputElements,
  };
};
