import { ACCEPTED_FORMATS } from '@shared/types/files.types';
import { Info, Upload } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

type UploadInstructionsProps = {
  maxSize: number; // in MB
  maxFiles: number;
  title?: string;
  description?: string;
  className?: string;
};

// JPG, JPEG, PNG, WEBP, HEIC, MP4, MOV, PDF, DOCX, DOC, XLSX, XLS, PPTX, PPT, CSV, TXT, RTF.
const acceptedFormatsSingleLine = ACCEPTED_FORMATS.map((item) => item.toUpperCase())
  .join(', ')
  .replaceAll('.', '');

/**
 * Component for displaying upload instructions and format information.
 */
export const UploadInstructions = ({
  maxSize,
  maxFiles,
  title = 'Add a photo, video, or document (optional)',
  description = 'Upload supporting media or documentation to help clarify what happened.',
  className = '',
}: UploadInstructionsProps) => {
  return (
    <div className={`flex flex-col items-center sm:items-start mb-4 ${className}`}>
      {/* Title with icon and tooltip with accept rules */}
      <div className="flex items-center gap-2 mb-2 w-full justify-center sm:justify-start">
        <div className="shrink-0 flex items-center justify-center h-6 w-6 rounded-full bg-primary/10">
          <Upload className="h-3 w-3 text-primary" />
        </div>
        <div className="text-[14px] font-medium flex items-center gap-2 w-full">
          {title}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <Info className="h-3 w-3" />
              </TooltipTrigger>
              <TooltipContent>
                <p>
                  <span className="font-bold">Accepted files:</span> {acceptedFormatsSingleLine}.
                  <br />
                  <span className="font-bold">Max:</span> {maxSize} MB per file (up to {maxFiles}).
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      <div className="w-full">
        <div className="text-[12px] text-muted-foreground text-center sm:text-left">{description}</div>
      </div>
    </div>
  );
};
