import { cn } from '@/lib/utils';
import type React from 'react';
import { useCallback, useState } from 'react';
import { useInputControls } from './hooks/use-input-controls';
import { UploadButtons } from './upload-buttons';
import { UploadInstructions } from './upload-instructions';

type UploadAreaProps = {
  className?: string;
  currentFileCount: number;
  description?: string;
  disabled: boolean;
  maxFiles: number;
  maxSize: number; // in MB
  onFilesSelected: (files: FileList) => void;
  showCamera?: boolean;
  title?: string;
};

/**
 * Complete upload area that composes focused components for file selection.
 */
export const UploadArea = ({
  className,
  currentFileCount,
  description,
  disabled,
  maxFiles,
  maxSize,
  onFilesSelected,
  showCamera = true,
  title,
}: UploadAreaProps) => {
  const remainingCount = maxFiles - currentFileCount;
  const canUploadMore = remainingCount > 0 && !disabled;

  const [dragActive, setDragActive] = useState(false);
  const { triggerFileSelect, triggerCameraSelect, InputElements } = useInputControls({
    acceptCamera: showCamera,
    allowMultiple: true,
    disabled: !canUploadMore,
    onFilesSelected,
  });

  const handleDrag = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();

      if (!canUploadMore) return;

      if (e.type === 'dragenter' || e.type === 'dragover') {
        setDragActive(true);
      } else if (e.type === 'dragleave') {
        setDragActive(false);
      }
    },
    [canUploadMore],
  );

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setDragActive(false);

      if (!canUploadMore) return;

      if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
        onFilesSelected(e.dataTransfer.files);
      }
    },
    [onFilesSelected, canUploadMore],
  );

  return (
    <>
      <InputElements />

      <div
        className={cn(
          'relative transition-colors',
          dragActive && !canUploadMore ? 'bg-primary/5 border-primary/30' : '',
          'rounded-[8px] border border-dashed border-border',
          'p-4 sm:p-4',
          'bg-[#F9FAFB] sm:bg-background',
          'min-h-[44px]',
        )}
        onDragEnter={handleDrag}
        onDragOver={handleDrag}
        onDragLeave={handleDrag}
        onDrop={handleDrop}
      >
        <UploadInstructions maxSize={maxSize} maxFiles={maxFiles} title={title} description={description} />

        <UploadButtons
          onCameraClick={triggerCameraSelect}
          onGalleryClick={triggerFileSelect}
          disabled={!canUploadMore}
          showCamera={showCamera}
        />

        <div className={`mt-3 flex justify-center sm:justify-start ${className}`}>
          <div className="text-xs px-2 py-1 rounded-full bg-primary/10 text-primary">
            {remainingCount} {remainingCount <= 1 ? 'file' : 'files'} remaining
          </div>
        </div>
      </div>
    </>
  );
};
