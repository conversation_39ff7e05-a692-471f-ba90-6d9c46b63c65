import { ArchiveConfirmationDialog } from '@/components/composite/archive-confirmation-dialog';
import { UpsertHazardModal } from '@/components/hazards/upsert-hazard-modal';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { formatDate } from '@shared/date-utils';
import { RouterOutputs } from '@shared/types/router.types';
import { CATEGORY_MAP } from '@shared/types/schema.types';
import { Archive, Trash2 } from 'lucide-react';
import { useState } from 'react';

export const HazardsMobileView = ({ hazards }: { hazards: RouterOutputs['hazards']['list']['result'] }) => {
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const [selectedHazard, setSelectedHazard] = useState<RouterOutputs['hazards']['list']['result'][number] | null>(null);
  const [showUpsertHazardModal, setShowUpsertHazardModal] = useState(false);

  return (
    <div className="md:hidden space-y-4">
      {selectedHazard && (
        <ArchiveConfirmationDialog
          archived={!!selectedHazard.archivedAt}
          showDialog={showArchiveConfirm}
          setShowDialog={setShowArchiveConfirm}
          entityId={selectedHazard.id}
          entityType="hazard"
        />
      )}

      {selectedHazard && (
        <UpsertHazardModal
          isOpen={showUpsertHazardModal}
          onClose={() => setShowUpsertHazardModal(false)}
          onSuccess={() => {
            setShowUpsertHazardModal(false);
          }}
          mode="edit"
          hazard={selectedHazard}
        />
      )}

      {hazards.map((hazard) => (
        <Card
          key={hazard.id}
          className={cn('cursor-pointer', `${hazard.archivedAt ? 'bg-amber-50/50 border-amber-200' : ''}`)}
          onClick={(e) => {
            e.stopPropagation();
            setSelectedHazard(hazard);
            setShowUpsertHazardModal(true);
          }}
        >
          <CardContent className="p-4">
            <div className="flex justify-between items-start mb-3">
              <h3 className="font-medium text-sm leading-tight pr-2">{hazard.name}</h3>
              <Badge
                className={`${
                  !hazard.archivedAt
                    ? 'bg-green-50 text-green-700 border-green-200'
                    : 'bg-gray-50 text-gray-700 border-gray-200'
                } font-medium flex items-center border px-2 py-1 text-xs`}
                variant="outline"
              >
                {!hazard.archivedAt ? 'Active' : 'Inactive'}
              </Badge>
            </div>

            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-500">Type:</span>
                <Badge variant="outline" className="capitalize text-xs">
                  {CATEGORY_MAP[hazard.type]}
                </Badge>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-500">Created by:</span>
                <div className="text-right">
                  <div className="font-medium">
                    {typeof hazard.createdBy === 'object' && hazard.createdBy?.fullName
                      ? hazard.createdBy.fullName
                      : '--'}
                  </div>
                  <div className="text-xs text-gray-500">
                    {typeof hazard.createdBy === 'object' && hazard.createdBy?.username
                      ? hazard.createdBy.username
                      : '--'}
                  </div>
                </div>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-500">Created:</span>
                <span>{formatDate(hazard.createdAt, true)}</span>
              </div>
            </div>

            <div className="flex justify-end mt-4">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 w-8 p-0 text-red-600 hover:text-red-700 border-gray-300 hover:border-red-300 rounded-sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedHazard(hazard);
                      setShowArchiveConfirm(true);
                    }}
                  >
                    {hazard.archivedAt ? <Archive className="h-4 w-4" /> : <Trash2 className="h-4 w-4" />}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{hazard.archivedAt ? 'Unarchive' : 'Archive'}</p>
                </TooltipContent>
              </Tooltip>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
