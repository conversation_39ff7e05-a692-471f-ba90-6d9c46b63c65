import { ArchiveConfirmationDialog } from '@/components/composite/archive-confirmation-dialog';
import { UpsertHazardModal } from '@/components/hazards/upsert-hazard-modal';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { formatDate } from '@shared/date-utils';
import { RouterOutputs } from '@shared/types/router.types';
import { CATEGORY_MAP } from '@shared/types/schema.types';
import { Archive, Trash2 } from 'lucide-react';
import { useState } from 'react';

export const HazardsTable = ({ hazards }: { hazards: RouterOutputs['hazards']['list']['result'] }) => {
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const [selectedHazard, setSelectedHazard] = useState<RouterOutputs['hazards']['list']['result'][number] | null>(null);
  const [showCreateHazardModal, setShowCreateHazardModal] = useState(false);

  return (
    <div className="overflow-hidden hidden md:block">
      {selectedHazard && (
        <ArchiveConfirmationDialog
          archived={!!selectedHazard.archivedAt}
          showDialog={showArchiveConfirm}
          setShowDialog={setShowArchiveConfirm}
          entityId={selectedHazard.id}
          entityType="hazard"
        />
      )}

      {selectedHazard && (
        <UpsertHazardModal
          isOpen={showCreateHazardModal}
          onClose={() => setShowCreateHazardModal(false)}
          onSuccess={() => {
            setShowCreateHazardModal(false);
          }}
          mode="edit"
          hazard={selectedHazard}
        />
      )}

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="max-w-80">Name</TableHead>
            <TableHead className="w-24">Type</TableHead>
            <TableHead className="w-24">Status</TableHead>
            <TableHead className="w-44">Created By</TableHead>
            <TableHead className="w-36">Created Date</TableHead>
            <TableHead className="w-24 text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {hazards.map((hazard) => (
            <TableRow
              onClick={(e) => {
                e.stopPropagation();
                setSelectedHazard(hazard);
                setShowCreateHazardModal(true);
              }}
              className={`cursor-pointer ${hazard.archivedAt ? 'bg-amber-50/50 hover:bg-amber-50/80' : ''}`}
              key={hazard.id}
            >
              <TableCell className="font-medium max-w-80 line-clamp-1 truncate">{hazard.name}</TableCell>
              <TableCell className="w-24">
                <Badge variant="outline" className="capitalize">
                  {CATEGORY_MAP[hazard.type]}
                </Badge>
              </TableCell>
              <TableCell className="w-24">
                <Badge
                  className={`${
                    !hazard.archivedAt
                      ? 'bg-green-50 text-green-700 border-green-200'
                      : 'bg-gray-50 text-gray-700 border-gray-200'
                  } font-medium flex items-center border px-2 py-1`}
                  variant="outline"
                >
                  {!hazard.archivedAt ? 'Active' : 'Inactive'}
                </Badge>
              </TableCell>
              <TableCell className="w-44">
                <div className="flex flex-col">
                  <span className="font-medium truncate">
                    {typeof hazard.createdBy === 'object' && hazard.createdBy?.fullName
                      ? hazard.createdBy.fullName
                      : '--'}
                  </span>
                  <span className="text-sm text-gray-500">
                    {typeof hazard.createdBy === 'object' && hazard.createdBy?.username
                      ? hazard.createdBy.username
                      : '--'}
                  </span>
                </div>
              </TableCell>
              <TableCell className="w-36">{formatDate(hazard.createdAt, true)}</TableCell>
              <TableCell className="w-24 text-right">
                <div className="flex items-center justify-end gap-1">
                  {/* Archive Button */}
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 w-8 p-0 text-red-600 hover:text-red-700 border-gray-300 hover:border-red-300 rounded-sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedHazard(hazard);
                          setShowArchiveConfirm(true);
                        }}
                      >
                        {hazard.archivedAt ? <Archive className="h-4 w-4" /> : <Trash2 className="h-4 w-4" />}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{hazard.archivedAt ? 'Unarchive' : 'Archive'}</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
