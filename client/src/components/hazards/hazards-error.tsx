import { Button } from '@/components/ui/button';
import { AlertTriangle } from 'lucide-react';

export const HazardsError = () => {
  const handleRefresh = () => {
    window.location.reload();
  };

  return (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <AlertTriangle className="h-12 w-12 text-red-500 mb-4" />
      <h3 className="text-lg font-medium text-gray-900 mb-2">Something went wrong</h3>
      <p className="text-gray-500 mb-4">We couldn't load the hazards. Please try again.</p>
      <Button onClick={handleRefresh} variant="outline">
        Try again
      </Button>
    </div>
  );
};
