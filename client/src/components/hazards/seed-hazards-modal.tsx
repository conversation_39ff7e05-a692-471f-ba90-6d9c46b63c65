import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { trpc } from '@/providers/trpc';
import { usePermissions } from '@/hooks/use-permissions';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { Redirect } from 'wouter';
import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Info } from 'lucide-react';
import { CATEGORY_MAP } from '@shared/types/schema.types';

export const SeedHazardsModal = ({
  isOpen,
  onClose,
  onSuccess,
}: {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}) => {
  const utils = trpc.useUtils();
  const { hasPermission } = usePermissions();

  const [selectedHazards, setSelectedHazards] = useState<string[]>([]);

  const { data: defaultHazards, isLoading: isLoadingDefaults } = trpc.hazards.listDefault.useQuery(undefined, {
    enabled: isOpen,
  });

  const { mutateAsync: bulkCreateHazards, isPending: isCreating } = trpc.hazards.bulkCreate.useMutation({
    onSuccess: () => {
      utils.hazards.list.invalidate();
      toast('Hazards seeded successfully', {
        description: `${selectedHazards.length} hazards have been created`,
      });
    },
    onError: (error) => {
      toast('Error seeding hazards', {
        description: error.message || 'Please try again',
      });
    },
  });

  // Pre-select all hazards when modal opens
  useEffect(() => {
    if (defaultHazards && isOpen) {
      setSelectedHazards(defaultHazards.map((hazard) => hazard.hazard_id));
    }
  }, [defaultHazards, isOpen]);

  const handleSelectAll = (checked: boolean) => {
    if (checked && defaultHazards) {
      setSelectedHazards(defaultHazards.map((hazard) => hazard.hazard_id));
    } else {
      setSelectedHazards([]);
    }
  };

  const handleSelectHazard = (hazardId: string, checked: boolean) => {
    if (checked) {
      setSelectedHazards((prev) => [...prev, hazardId]);
    } else {
      setSelectedHazards((prev) => prev.filter((id) => id !== hazardId));
    }
  };

  const handleSeedHazards = async () => {
    if (!defaultHazards || selectedHazards.length === 0) {
      toast('No hazards selected', {
        description: 'Please select at least one hazard to seed',
      });
      return;
    }

    try {
      const hazardsToCreate = defaultHazards
        .filter((hazard) => selectedHazards.includes(hazard.hazard_id))
        .map((hazard) => ({
          name: hazard.name,
          type: hazard.type,
        }));

      await bulkCreateHazards(hazardsToCreate);
      setSelectedHazards([]);
      onClose();
      onSuccess?.();
    } catch (error) {
      console.error('Error seeding hazards:', error);
    }
  };

  const handleClose = () => {
    setSelectedHazards([]);
    onClose();
  };

  if (!hasPermission(MODULES.HAZARDS, ALLOWED_ACTIONS.CREATE)) {
    return <Redirect to={ROUTES.HAZARDS_LIST} />;
  }

  const isAllSelected = defaultHazards && selectedHazards.length === defaultHazards.length;
  const isIndeterminate = selectedHazards.length > 0 && selectedHazards.length < (defaultHazards?.length || 0);

  return (
    <AlertDialog open={isOpen} onOpenChange={handleClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Seed Default Hazards</AlertDialogTitle>
          <AlertDialogDescription>
            Select the default hazards you want to add to your organization. All hazards are pre-selected by default.
          </AlertDialogDescription>
        </AlertDialogHeader>

        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            Seeding hazards with the same name as existing hazards will not create duplicates. Only new hazards will be
            added to your organization.
          </AlertDescription>
        </Alert>

        <div className="flex-1 overflow-hidden border rounded-xl">
          {isLoadingDefaults ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-sm text-gray-500">Loading default hazards...</div>
            </div>
          ) : (
            <ScrollArea className="h-[400px]">
              {/* Header */}
              <div className="sticky top-0 bg-background border-b grid grid-cols-[auto_1fr_1fr] gap-4 p-4 items-center font-medium text-sm">
                <div className="flex justify-center">
                  <Checkbox
                    checked={isAllSelected}
                    onCheckedChange={handleSelectAll}
                    ref={(ref) => {
                      if (ref) {
                        // TypeScript doesn't know about the indeterminate property on checkbox elements
                        (ref as HTMLInputElement).indeterminate = isIndeterminate;
                      }
                    }}
                  />
                </div>
                <div>Name</div>
                <div className="text-center">Type</div>
              </div>

              {/* Rows */}
              <div className="divide-y">
                {defaultHazards?.map((hazard) => (
                  <div
                    key={hazard.hazard_id}
                    className="grid grid-cols-[auto_1fr_1fr] gap-4 p-4 items-center hover:bg-gray-50"
                  >
                    <div className="flex justify-center">
                      <Checkbox
                        checked={selectedHazards.includes(hazard.hazard_id)}
                        onCheckedChange={(checked) => handleSelectHazard(hazard.hazard_id, checked as boolean)}
                      />
                    </div>
                    <div className="text-sm">{hazard.name}</div>
                    <div className="flex justify-center">
                      <Badge variant="outline" className="capitalize text-xs">
                        {CATEGORY_MAP[hazard.type]}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
              <ScrollBar />
            </ScrollArea>
          )}
        </div>

        <AlertDialogFooter className="flex items-center justify-between">
          <div className="text-sm text-gray-500">
            {selectedHazards.length} of {defaultHazards?.length || 0} hazards selected
          </div>
          <div className="flex gap-2">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button onClick={handleSeedHazards} disabled={isCreating || selectedHazards.length === 0}>
              {isCreating ? 'Creating...' : `Create ${selectedHazards.length} Hazards`}
            </Button>
          </div>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
