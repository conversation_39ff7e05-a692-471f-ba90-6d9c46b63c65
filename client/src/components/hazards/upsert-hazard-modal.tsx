import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { usePermissions } from '@/hooks/use-permissions';
import { trpc } from '@/providers/trpc';
import { zodResolver } from '@hookform/resolvers/zod';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { hazardCategoryEnum } from '@shared/schema';
import { RouterOutputs } from '@shared/types/router.types';
import { CATEGORY_MAP } from '@shared/types/schema.types';
import { HazardsCreateSchema, HazardsUpdateSchema } from '@shared/types/settings.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { Redirect } from 'wouter';
import { z } from 'zod';

type CreateHazardFormData = z.infer<typeof HazardsCreateSchema>;

type UpdateHazardFormData = z.infer<typeof HazardsUpdateSchema>;

export const UpsertHazardModal = ({
  isOpen,
  onClose,
  onSuccess,
  hazard,
  mode = 'create',
}: {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  hazard?: RouterOutputs['hazards']['list']['result'][number] | null;
  mode?: 'create' | 'edit';
}) => {
  const utils = trpc.useUtils();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { hasPermission } = usePermissions();

  const createHazardMutation = trpc.hazards.create.useMutation({
    onSuccess: () => {
      utils.hazards.list.invalidate();
      toast('Hazard created', {
        description: 'The hazard has been successfully created',
      });
    },
    onError: (error) => {
      toast('Error creating hazard', {
        description: error.message || 'Please try again',
      });
    },
  });

  const updateHazardMutation = trpc.hazards.update.useMutation({
    onSuccess: () => {
      utils.hazards.list.invalidate();
      toast('Hazard updated', {
        description: 'The hazard has been successfully updated',
      });
    },
    onError: (error) => {
      toast('Error updating hazard', {
        description: error.message || 'Please try again',
      });
    },
  });

  const form = useForm<CreateHazardFormData | UpdateHazardFormData>({
    resolver: zodResolver(mode === 'create' ? HazardsCreateSchema : HazardsUpdateSchema),
    defaultValues: {
      name: '',
      type: undefined,
    },
  });

  useEffect(() => {
    if (hazard) {
      form.reset({
        id: hazard.id,
        name: hazard.name,
        type: hazard.type,
      });
    }
  }, [hazard]);

  const onSubmit = async (data: CreateHazardFormData) => {
    setIsSubmitting(true);
    try {
      if (mode === 'edit' && hazard?.id) {
        await updateHazard({ ...data, id: hazard.id });
      } else {
        await createHazard(data);
      }
      form.reset();
      onClose();
      onSuccess?.();
    } catch (error) {
      console.error('Error creating hazard:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const createHazard = async (data: CreateHazardFormData) => {
    await createHazardMutation.mutateAsync({
      name: data.name.trim(),
      type: data.type,
    });
  };

  const updateHazard = async (data: UpdateHazardFormData) => {
    await updateHazardMutation.mutateAsync({
      id: hazard?.id || '',
      name: data.name.trim(),
      type: data.type,
    });
  };

  const handleClose = () => {
    form.reset();
    onClose();
  };

  if (!hasPermission(MODULES.HAZARDS, ALLOWED_ACTIONS.CREATE)) {
    return <Redirect to={ROUTES.HAZARDS_LIST} />;
  }

  return (
    <AlertDialog open={isOpen} onOpenChange={handleClose}>
      <AlertDialogContent className="sm:max-w-md">
        <AlertDialogHeader>
          <AlertDialogTitle>{mode === 'create' ? 'Create' : 'Edit'} Hazard</AlertDialogTitle>
          <AlertDialogDescription>
            {mode === 'create'
              ? 'Create a new hazard that can be used across your organization.'
              : 'Edit the hazard name and category.'}
          </AlertDialogDescription>
        </AlertDialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Hazard Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter hazard name" {...field} value={field.value || ''} />
                  </FormControl>
                  <FormDescription>The name of the hazard</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Hazard Category</FormLabel>
                  <FormControl>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value);
                      }}
                      value={field.value}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select hazard category" />
                      </SelectTrigger>
                      <SelectContent>
                        {hazardCategoryEnum.enumValues.map((category) => (
                          <SelectItem key={category} value={category}>
                            {CATEGORY_MAP[category]}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormDescription>Examples: Chemical, Electrical, Ergonomic, Fall, Fire, Mechanical</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <AlertDialogFooter>
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancel
              </Button>

              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Saving...' : mode === 'edit' ? 'Update Hazard' : 'Create Hazard'}
              </Button>
            </AlertDialogFooter>
          </form>
        </Form>
      </AlertDialogContent>
    </AlertDialog>
  );
};
