import { AlertNotifications } from '@/components/composite/alert-notifications';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useAppContext } from '@/contexts/app-context';
import { useConfig } from '@/hooks/use-config';
import { useIsMobile } from '@/hooks/use-mobile';
import { usePermissions } from '@/hooks/use-permissions';
import { cn } from '@/lib/utils';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import {
  AlertTriangle,
  Building2,
  Calendar,
  ChevronDown,
  ChevronUp,
  <PERSON>lipboard<PERSON>heck,
  ClipboardList,
  FileBarChart,
  Gauge,
  HardHat,
  type LucideIcon,
  MapPin,
  QrCode,
  Settings,
  Shield,
  ShieldCheck,
  TriangleAlert,
  X
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { Link, useLocation } from 'wouter';

type SidebarProps = {
  onCloseMobile?: () => void;
  onOpenConsentManager?: () => void;
};

export type NavItem = {
  title: string;
  path: string;
  icon: LucideIcon;
  active?: boolean;
  badge?: number;
};

export type NavSection = {
  key: string;
  title?: string;
  items: NavItem[];
};

export const Sidebar = ({ onCloseMobile, onOpenConsentManager }: SidebarProps) => {
  const [location] = useLocation();
  const isMobile = useIsMobile();
  const { user } = useAppContext();
  const { VITE_MIXPANEL_TOKEN } = useConfig();
  const { hasPermission } = usePermissions();

  // Check if current location is within settings pages
  const isOnSettingsPage =
    location.toString().startsWith(ROUTES.OSHA_LOCATIONS_LIST) ||
    location.toString().startsWith(ROUTES.HAZARDS_LIST) ||
    location.toString().startsWith(ROUTES.CONTROL_MEASURES_LIST);

  const [isSettingsExpanded, setIsSettingsExpanded] = useState(isOnSettingsPage);

  // Auto-expand/collapse based on current route
  useEffect(() => {
    setIsSettingsExpanded(isOnSettingsPage);
  }, [isOnSettingsPage]);

  const navSections: NavSection[] = [
    {
      key: 'Safety Events',
      title: 'Safety',
      items: [
        ...(hasPermission(MODULES.EHS_ACCESS_POINT, ALLOWED_ACTIONS.VIEW)
          ? [{ title: 'Access Points', path: ROUTES.ACCESS_POINTS_LIST, icon: QrCode }]
          : []),
        ...(hasPermission(MODULES.EHS_EVENT, ALLOWED_ACTIONS.VIEW)
          ? [{ title: 'Safety Events', path: ROUTES.EVENT_LIST, icon: AlertTriangle }]
          : []),
        ...(hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.VIEW)
          ? [{ title: 'CAPAs', path: ROUTES.CAPA_LIST, icon: ClipboardCheck }]
          : []),
      ],
    },
    ...(hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.VIEW)
      ? [
          {
            key: 'OSHA',
            title: 'OSHA',
            items: [
              { title: 'OSHA Log (Form 300)', path: ROUTES.OSHA_REPORTS, icon: FileBarChart },
              { title: 'Summary (Form 300A)', path: ROUTES.OSHA_SUMMARY, icon: Calendar },
              { title: 'Agency Reports', path: ROUTES.OSHA_AGENCY_REPORTS, icon: Building2 },
            ],
          },
        ]
      : []),
    ...(hasPermission(MODULES.EHS_JHA, ALLOWED_ACTIONS.VIEW)
      ? [{ key: 'JHA', title: 'JHA', items: [{ title: 'Job Hazard Analyses', path: ROUTES.JHA_LIST, icon: HardHat }] }]
      : []),
    ...(hasPermission(MODULES.SOP, ALLOWED_ACTIONS.VIEW) && user?.featureFlags?.ehsSop
      ? [
          {
            key: 'SOP',
            title: 'SOP',
            items: [{ title: 'Standard Operating Procedures', path: ROUTES.SOP_LIST, icon: ClipboardList }],
          },
        ]
      : []),
  ];

  return (
    <aside
      className={`${isMobile ? 'w-[280px]' : 'w-[240px]'} shrink-0 border-r border-gray-200 bg-white h-full flex flex-col shadow-lg`}
    >
      <div className="px-2 py-3.5 border-b border-gray-200 flex items-center justify-between">
        <div className="flex items-center">
          <ShieldCheck color="#ff273b" className="w-5 h-5" />
          <span className="ml-2 font-semibold text-lg">UpKeep EHS</span>
        </div>
        <div className="flex items-center gap-2">
          {/* Notifications Bell */}
          <AlertNotifications />
          {isMobile ? (
            <Button variant="ghost" size="icon" className="w-8 h-8 rounded-full" onClick={onCloseMobile}>
              <X className="w-5 h-5 text-gray-600" />
            </Button>
          ) : (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Avatar className="w-6 h-6 cursor-pointer hover:opacity-80 transition-opacity">
                  <AvatarFallback className="bg-indigo-100 text-indigo-800 font-medium text-xs">
                    {user?.firstName.charAt(0)}
                  </AvatarFallback>
                </Avatar>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start" className="min-w-56">
                <div className="flex items-center gap-2 p-1">
                  <Avatar>
                    <AvatarFallback className="bg-indigo-100 text-indigo-800 font-medium text-xs">
                      {user?.firstName.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium text-base">
                      {user?.firstName} {user?.lastName}
                    </p>
                    <p className="text-sm whitespace-nowrap truncate text-muted-foreground">{user?.email}</p>
                  </div>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>

      <div className="overflow-y-auto flex-1">
        {navSections.map((section) => (
          <div key={section.key} className="mt-2 px-3">
            {section.title && (
              <div className="text-xs font-medium uppercase tracking-wider px-3 py-1">{section.title}</div>
            )}

            {section.items.map((item) => {
              // Check if current location starts with the item's path to handle nested routes
              // Special case for dashboard: only mark active if it's exactly /dashboard
              const isActive = location.toString().startsWith(item.path) && item.path !== ROUTES.BASE;

              return (
                <Link
                  key={item.path}
                  href={item.path}
                  className={cn(
                    'sidebar-item px-3 py-2 rounded flex items-center text-gray-600 hover:bg-gray-100 my-0.5',
                    isActive && 'bg-blue-50 text-blue-600 font-medium border-l-2 border-blue-600',
                  )}
                  onClick={() => isMobile && onCloseMobile && onCloseMobile()}
                >
                  <item.icon className={cn('w-4 h-4', isActive && 'text-blue-600')} />
                  <span className="ml-3 text-sm">{item.title}</span>
                  {item.badge && (
                    <Badge className="ml-auto bg-blue-100 text-blue-800 hover:bg-blue-100 text-xs">{item.badge}</Badge>
                  )}
                </Link>
              );
            })}
          </div>
        ))}
      </div>

      <div className="p-3 border-t border-gray-200 mt-auto">
        {(hasPermission(MODULES.OSHA_LOCATIONS, ALLOWED_ACTIONS.VIEW) ||
          hasPermission(MODULES.HAZARDS, ALLOWED_ACTIONS.VIEW) ||
          hasPermission(MODULES.CONTROL_MEASURES, ALLOWED_ACTIONS.VIEW)) && (
          <Collapsible defaultOpen>
            <CollapsibleTrigger asChild>
              <button
                onClick={() => setIsSettingsExpanded(!isSettingsExpanded)}
                className={cn(
                  'sidebar-item px-3 py-2 rounded flex items-center justify-between text-gray-600 hover:bg-gray-100 my-0.5 cursor-pointer w-full',
                )}
              >
                <div className="flex items-center">
                  <Settings className="w-5 h-5" />
                  <span className="ml-3 text-sm font-bold">Settings</span>
                </div>
                {isSettingsExpanded ? <ChevronDown className="w-4 h-4" /> : <ChevronUp className="w-4 h-4" />}
              </button>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <div className="ml-4 mt-1 space-y-1">
                {hasPermission(MODULES.OSHA_LOCATIONS, ALLOWED_ACTIONS.VIEW) && (
                  <Link
                    href={ROUTES.OSHA_LOCATIONS_LIST}
                    className={cn(
                      'sidebar-item px-3 py-2 rounded flex items-center text-gray-600 hover:bg-gray-100 my-0.5',
                      location.toString().startsWith(ROUTES.OSHA_LOCATIONS_LIST) &&
                        'bg-blue-50 text-blue-600 font-medium border-l-2 border-blue-600',
                    )}
                    onClick={() => isMobile && onCloseMobile && onCloseMobile()}
                  >
                    <MapPin
                      className={cn(
                        'w-4 h-4',
                        location.toString().startsWith(ROUTES.OSHA_LOCATIONS_LIST) && 'text-blue-600',
                      )}
                    />
                    <span className="ml-3 text-sm">OSHA Locations</span>
                  </Link>
                )}
                {hasPermission(MODULES.HAZARDS, ALLOWED_ACTIONS.VIEW) && (
                  <Link
                    href={ROUTES.HAZARDS_LIST}
                    className={cn(
                      'sidebar-item px-3 py-2 rounded flex items-center text-gray-600 hover:bg-gray-100 my-0.5',
                      location.toString().startsWith(ROUTES.HAZARDS_LIST) &&
                        'bg-blue-50 text-blue-600 font-medium border-l-2 border-blue-600',
                    )}
                    onClick={() => isMobile && onCloseMobile && onCloseMobile()}
                  >
                    <TriangleAlert
                      className={cn('w-4 h-4', location.toString().startsWith(ROUTES.HAZARDS_LIST) && 'text-blue-600')}
                    />
                    <span className="ml-3 text-sm">Hazards</span>
                  </Link>
                )}
                {hasPermission(MODULES.CONTROL_MEASURES, ALLOWED_ACTIONS.VIEW) && (
                  <Link
                    href={ROUTES.CONTROL_MEASURES_LIST}
                    className={cn(
                      'sidebar-item px-3 py-2 rounded flex items-center text-gray-600 hover:bg-gray-100 my-0.5',
                      location.toString().startsWith(ROUTES.CONTROL_MEASURES_LIST) &&
                        'bg-blue-50 text-blue-600 font-medium border-l-2 border-blue-600',
                    )}
                    onClick={() => isMobile && onCloseMobile && onCloseMobile()}
                  >
                    <Gauge
                      className={cn(
                        'w-4 h-4',
                        location.toString().startsWith(ROUTES.CONTROL_MEASURES_LIST) && 'text-blue-600',
                      )}
                    />
                    <span className="ml-3 text-sm">Control Measures</span>
                  </Link>
                )}
              </div>
            </CollapsibleContent>
          </Collapsible>
        )}

        <div className="flex flex-col space-y-1">
          {/* Collapsible Settings Section */}

          {VITE_MIXPANEL_TOKEN && (
            <div
              onClick={() => onOpenConsentManager?.()}
              className={cn(
                'sidebar-item px-3 py-2 rounded flex items-center text-gray-600 hover:bg-gray-100 my-0.5 cursor-pointer',
              )}
            >
              <Shield className="w-5 h-5" />
              <span className="ml-3 text-sm font-bold">Privacy Settings</span>
            </div>
          )}

          <a
            href="/web/work-orders"
            className={cn('sidebar-item px-3 py-2 rounded flex items-center text-gray-600 hover:bg-gray-100 my-0.5')}
          >
            <Settings className="text-red-600 w-5 h-5" />
            <span className="ml-3 text-sm font-bold">UpKeep CMMS</span>
          </a>
        </div>
      </div>
    </aside>
  );
};
