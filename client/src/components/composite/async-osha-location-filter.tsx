import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { useInfiniteMinimalOshaLocations } from '@/hooks/use-paginated-data';
import { cn } from '@/lib/utils';
import { useDebounce } from '@uidotdev/usehooks';
import { ChevronDown, Loader2, Search } from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';

export const AsyncOshaLocationFilter = ({
  selected,
  onSelect,
  className,
  label,
  placeholder,
}: {
  selected?: string;
  onSelect: (locationId: string) => void;
  label?: string;
  placeholder?: string;
  className?: string;
}) => {
  const [open, setOpen] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const [search, setSearch] = useState('');

  const debouncedSearch = useDebounce(search, 300);

  const {
    data: locations,
    isLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = useInfiniteMinimalOshaLocations({
    filters: {
      search: debouncedSearch,
      mustIncludeObjectIds: selected ? [selected] : undefined,
    },
    enabled: open || !!selected,
  });

  // Handle infinite scrolling
  useEffect(() => {
    if (!open || !hasNextPage) return;

    let cleanup: (() => void) | undefined;

    // Use a timeout to ensure the PopoverContent is fully rendered
    const timeoutId = window.setTimeout(() => {
      const scrollArea = scrollAreaRef.current;
      if (!scrollArea) return;

      let scrollTimeoutId: number;

      const handleScroll = () => {
        clearTimeout(scrollTimeoutId);
        scrollTimeoutId = window.setTimeout(() => {
          const { scrollTop, scrollHeight, clientHeight } = scrollArea;
          const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

          if (distanceFromBottom < 50 && !isFetchingNextPage) {
            fetchNextPage();
          }
        }, 100);
      };

      scrollArea.addEventListener('scroll', handleScroll, { passive: true });

      cleanup = () => {
        scrollArea.removeEventListener('scroll', handleScroll);
        clearTimeout(scrollTimeoutId);
      };
    }, 100);

    return () => {
      clearTimeout(timeoutId);
      cleanup?.();
    };
  }, [open, hasNextPage, fetchNextPage, isFetchingNextPage]);

  // Focus input when dropdown opens
  useEffect(() => {
    if (open && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [open]);

  // Handle keyboard events
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
  }, []);

  // Find the selected location name for display
  const selectedLocation = locations.find((location) => location.id === selected);

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className={cn('justify-between', className)}
          aria-expanded={open}
          aria-haspopup="menu"
          aria-label={`${label || 'OSHA Location'} filter: ${selectedLocation?.name || 'All Locations'}`}
        >
          <div className="flex items-center">
            {label && <span className="text-sm font-medium mr-2">{label}</span>}
            <span className="text-sm">{selectedLocation?.name || 'All Locations'}</span>
          </div>
          <ChevronDown className="h-4 w-4 ml-2" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="p-0 min-w-16">
        {/* Search Input */}
        <div className="flex items-center pl-4">
          <Search size={15} />
          <Input
            ref={inputRef}
            className="w-full border-none px-2 py-1 bg-transparent shadow-none outline-hidden placeholder:text-muted-foreground focus-visible:outline-hidden focus-visible:ring-0 focus-visible:ring-offset-0"
            value={search}
            placeholder={placeholder || 'Search locations...'}
            onChange={handleSearchChange}
          />
        </div>
        <Separator />

        {/* Scrollable Location List */}
        <div ref={scrollAreaRef} className="max-h-64 overflow-auto p-1">
          {/* All Locations Option */}
          <DropdownMenuCheckboxItem
            className="flex items-center space-x-2"
            onSelect={(e) => {
              e.preventDefault();
              onSelect(''); // Pass empty string to clear selection
              setOpen(false);
            }}
            checked={!selected || selected === ''}
          >
            <label className="cursor-pointer">All Locations</label>
          </DropdownMenuCheckboxItem>

          {isLoading && (
            <div className="flex items-center justify-center p-2">
              <Loader2 className="animate-spin size-4" />
              <span className="ml-2 text-sm text-muted-foreground">Loading...</span>
            </div>
          )}

          {locations.length === 0 && !isLoading && (
            <div className="p-2 text-sm text-muted-foreground text-center">No locations found</div>
          )}

          {locations.map((location) => (
            <DropdownMenuCheckboxItem
              key={location.id}
              className="flex items-center space-x-2"
              onSelect={(e) => {
                e.preventDefault();
                onSelect(location.id);
                setOpen(false); // Close dropdown after selection
              }}
              checked={selected === location.id}
            >
              <label className="capitalize cursor-pointer">{location.name}</label>
            </DropdownMenuCheckboxItem>
          ))}

          {isFetchingNextPage && (
            <div className="flex items-center justify-center p-2">
              <Loader2 className="animate-spin size-4" />
              <span className="ml-2 text-sm text-muted-foreground">Loading more...</span>
            </div>
          )}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
