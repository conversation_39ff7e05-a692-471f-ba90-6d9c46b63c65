import { Button } from '@/components/ui/button';
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { formatPhoneNumber } from '@/lib/utils';
import { Plus, Trash2 } from 'lucide-react';
import { useFieldArray, useFormContext } from 'react-hook-form';

type WitnessItem = {
  fullName: string;
  phoneNumber?: string;
  email?: string;
  notes?: string;
};

type WitnessSectionProps = {
  name?: string;
  title?: string;
  description?: string;
};

export function WitnessSection({
  name = 'witnesses',
  title = 'Witnesses',
  description = 'Add witnesses who saw or were involved in the incident',
}: WitnessSectionProps) {
  const form = useFormContext();

  const { fields, append, remove } = useFieldArray({ control: form.control, name });

  const addWitness = () => {
    const newWitness: WitnessItem = {
      fullName: '',
      phoneNumber: '',
      email: '',
      notes: '',
    };
    append(newWitness);
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
        <p className="text-sm text-gray-600 mt-1">{description}</p>
      </div>

      <div className="space-y-6">
        {fields.map((field, index) => (
          <div key={field.id} className="border border-gray-200 rounded-lg p-6 bg-gray-50">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Witness {index + 1}</h3>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => remove(index)}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>

            <div className="space-y-8">
              {/* Row 1: Full Name and Phone Number */}
              <div className="flex flex-col md:flex-row md:items-start gap-2">
                <FormField
                  control={form.control}
                  name={`${name}.${index}.fullName`}
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormLabel>
                        Full Name <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter witness full name"
                          {...field}
                          value={field.value || ''}
                          className="focus:ring-0 focus:ring-offset-0 focus:border-input focus:shadow-none"
                        />
                      </FormControl>
                      <FormDescription>The full name of the person who witnessed the incident</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name={`${name}.${index}.phoneNumber`}
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormLabel>Phone Number</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter phone number"
                          {...field}
                          value={field.value || ''}
                          className="focus:ring-0 focus:ring-offset-0 focus:border-input focus:shadow-none"
                          onChange={(e) => {
                            const formatted = formatPhoneNumber(e.target.value);
                            field.onChange(formatted);
                          }}
                        />
                      </FormControl>
                      <FormDescription>Contact phone number for follow-up</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Row 2: Email Address and Notes */}
              <div className="flex flex-col md:flex-row md:items-start gap-2">
                <FormField
                  control={form.control}
                  name={`${name}.${index}.email`}
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormLabel>Email Address</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter email address"
                          type="email"
                          {...field}
                          value={field.value || ''}
                          className="focus:ring-0 focus:ring-offset-0 focus:border-input focus:shadow-none"
                        />
                      </FormControl>
                      <FormDescription>Email address for follow-up communications</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name={`${name}.${index}.notes`}
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormLabel>Notes</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Additional notes about the witness or their involvement"
                          className="min-h-[80px] focus:ring-0 focus:ring-offset-0 focus:border-input focus:shadow-none"
                          {...field}
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormDescription>
                        Any additional information about the witness or their observations
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </div>
        ))}

        <Button type="button" variant="outline" onClick={addWitness} className="w-full">
          <Plus className="h-4 w-4 mr-2" />
          Add Witness
        </Button>
      </div>
    </div>
  );
}
