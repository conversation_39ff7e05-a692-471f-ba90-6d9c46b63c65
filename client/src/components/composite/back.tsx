import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

export const Back = ({ onClick }: { onClick?: () => void }) => {
  return (
    <Button
      variant="outline"
      size="sm"
      onClick={onClick || (() => window.history.back())}
      className="gap-1 hover:bg-neutral-300 text-neutral-900"
    >
      <ArrowLeft className="h-4 w-4" />
      <span>Back</span>
    </Button>
  );
};
