import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { trpc } from '@/providers/trpc';
import { formatDate } from '@shared/date-utils';
import { oshaAuditTrailActionEnum, oshaEntityTypeEnum } from '@shared/schema';
import { Archive, Calendar, CheckCircle, Download, Edit3, FilePlus, History, PenTool, User } from 'lucide-react';
import { useState } from 'react';

const ActionIconMap = {
  [oshaAuditTrailActionEnum.enumValues[0]]: FilePlus, // created
  [oshaAuditTrailActionEnum.enumValues[1]]: Edit3, // updated
  [oshaAuditTrailActionEnum.enumValues[2]]: CheckCircle, // submitted
  [oshaAuditTrailActionEnum.enumValues[3]]: Download, // downloaded
  [oshaAuditTrailActionEnum.enumValues[4]]: PenTool, // signed
  [oshaAuditTrailActionEnum.enumValues[5]]: Archive, // archived
  [oshaAuditTrailActionEnum.enumValues[6]]: Archive, // restored
} as const;

const ActionColorMap = {
  [oshaAuditTrailActionEnum.enumValues[0]]: 'bg-green-100 text-green-700', // created - Pale Green
  [oshaAuditTrailActionEnum.enumValues[1]]: 'bg-blue-100 text-blue-700', // updated - Pale Blue
  [oshaAuditTrailActionEnum.enumValues[2]]: 'bg-orange-100 text-orange-700', // submitted - Pale Orange
  [oshaAuditTrailActionEnum.enumValues[3]]: 'bg-purple-100 text-purple-700', // downloaded - Pale Purple
  [oshaAuditTrailActionEnum.enumValues[4]]: 'bg-indigo-100 text-indigo-700', // signed - Pale Indigo
  [oshaAuditTrailActionEnum.enumValues[5]]: 'bg-gray-100 text-gray-700', // archived - Pale Gray
  [oshaAuditTrailActionEnum.enumValues[6]]: 'bg-emerald-100 text-emerald-700', // restored - Pale Emerald
};

const AuditTrailTimeline = {
  [oshaAuditTrailActionEnum.enumValues[0]]: 'Created', // created
  [oshaAuditTrailActionEnum.enumValues[1]]: 'Updated', // updated
  [oshaAuditTrailActionEnum.enumValues[2]]: 'Submitted', // submitted
  [oshaAuditTrailActionEnum.enumValues[3]]: 'Downloaded', // downloaded
  [oshaAuditTrailActionEnum.enumValues[4]]: 'Signed', // signed
  [oshaAuditTrailActionEnum.enumValues[5]]: 'Archived', // archived
  [oshaAuditTrailActionEnum.enumValues[6]]: 'Restored', // restored
} as const;

export const OshaAuditTrail = ({
  entityId,
  entityType,
}: {
  entityId: string;
  entityType: (typeof oshaEntityTypeEnum.enumValues)[number];
}) => {
  const { data: auditTrail } = trpc.oshaAuditTrail.get.useQuery({
    id: entityId,
    entityType,
  });

  const [auditTrailExpanded, setAuditTrailExpanded] = useState(false);

  return (
    <>
      <Card>
        <Collapsible open={auditTrailExpanded} onOpenChange={setAuditTrailExpanded} className="border-none shadow-none">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center">
                <History className="h-5 w-5 text-gray-600 mr-2" />
                Audit Trail
              </CardTitle>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                  <span className="sr-only">Toggle</span>
                  {auditTrailExpanded ? <span className="text-lg">−</span> : <span className="text-lg">+</span>}
                </Button>
              </CollapsibleTrigger>
            </div>
          </CardHeader>
          <CollapsibleContent>
            <CardContent className="pt-0">
              <div className="space-y-3">
                {auditTrail?.map((entry, index) => {
                  const ActionIcon = ActionIconMap[entry.action as keyof typeof ActionIconMap];
                  const actionColor = ActionColorMap[entry.action as keyof typeof ActionColorMap];

                  return (
                    <div key={index} className="mb-4">
                      <div className="flex items-center">
                        <div className="flex flex-col items-center mr-2 mt-1">
                          <div className={`rounded-full w-7 h-7 flex items-center justify-center ${actionColor}`}>
                            {ActionIcon && <ActionIcon className="h-4 w-4" />}
                          </div>
                        </div>
                        <div className="space-y-1">
                          <p className="font-medium text-sm">
                            {AuditTrailTimeline[entry.action as keyof typeof AuditTrailTimeline]}
                          </p>
                          <div className="flex items-center text-xs text-muted-foreground gap-1">
                            <Calendar className="h-4 w-4" />
                            {formatDate(entry.createdAt)}
                          </div>
                          {entry.createdBy?.fullName && (
                            <div className="flex items-center gap-1 text-xs text-muted-foreground">
                              <User className="h-4 w-4" />
                              {entry.createdBy.fullName}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>
    </>
  );
};
