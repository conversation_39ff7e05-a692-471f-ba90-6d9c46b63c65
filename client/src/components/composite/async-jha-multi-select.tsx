import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverPortal, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { useAppContext } from '@/contexts/app-context';
import { useInfiniteMinimalJhas } from '@/hooks/use-paginated-data';
import { cn } from '@/lib/utils';
import { JHA_STATUS_MAP } from '@shared/types/jha.types';
import { RouterOutputs } from '@shared/types/router.types';
import { useDebounce } from '@uidotdev/usehooks';
import { Check, ChevronsUpDown, Loader2, Search, X } from 'lucide-react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

type JhaOption = RouterOutputs['jha']['minimalList']['result'][number];

export const AsyncJhaMultiSelect = ({
  onChange,
  className,
  value: selected = null,
  placeholder = 'Select JHAs...',
  disabled = false,
  upkeepCompanyId,
  ...props
}: {
  value?: JhaOption['instanceId'][] | null;
  onChange: (selected: JhaOption['instanceId'][]) => void;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
  upkeepCompanyId?: string | null;
}) => {
  const { user } = useAppContext();
  const [open, setOpen] = useState(false);
  const [selectedJhas, setSelectedJhas] = useState<Record<string, JhaOption>>({});
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  const [search, setSearch] = useState('');
  const debouncedSearch = useDebounce(search, 300);

  const {
    data: jhas,
    isLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = useInfiniteMinimalJhas({
    filters: {
      search: debouncedSearch,
      includeLocation: true,
    },
    enabled: !!upkeepCompanyId || !!user?.upkeepCompanyId,
  });

  const options = useMemo(() => {
    return (
      jhas?.reduce(
        (acc, jha) => {
          acc[jha.instanceId!] = jha;
          return acc;
        },
        {} as Record<string, JhaOption>,
      ) || {}
    );
  }, [jhas]);

  // Computed value for selected JHA objects
  const selectedOptions = useMemo(() => {
    if (!Array.isArray(selected)) return [];
    return selected
      .filter((instanceId): instanceId is string => instanceId !== null)
      .map((instanceId) => selectedJhas[instanceId] || options[instanceId])
      .filter(Boolean);
  }, [selected, selectedJhas, options]);

  const handleUnselect = useCallback(
    (instanceId: JhaOption['instanceId']) => {
      if (disabled || !instanceId) return;

      const updatedSelectedIds = (selected || []).filter((id) => id !== instanceId);
      onChange(updatedSelectedIds.length > 0 ? updatedSelectedIds : []);

      // Remove from selected JHAs when unselected
      setSelectedJhas((prev) => {
        const updated = { ...prev };
        delete updated[instanceId];
        return updated;
      });
    },
    [disabled, selected, onChange],
  );

  // Store JHA data as it gets loaded to preserve across searches
  useEffect(() => {
    if (jhas && jhas.length > 0) {
      setSelectedJhas((prev) => {
        const updated = { ...prev };
        jhas.forEach((jha) => {
          if (jha.instanceId) {
            updated[jha.instanceId] = jha;
          }
        });
        return updated;
      });
    }
  }, [jhas]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (disabled) return;

      if (e.key === 'Escape') {
        setOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [disabled]);

  // Handle infinite scrolling
  useEffect(() => {
    if (!open || !hasNextPage) return;

    let cleanup: (() => void) | undefined;

    // Use a timeout to ensure the PopoverContent is fully rendered
    const timeoutId = window.setTimeout(() => {
      const scrollArea = scrollAreaRef.current;
      if (!scrollArea) return;

      let scrollTimeoutId: number;

      const handleScroll = () => {
        clearTimeout(scrollTimeoutId);
        scrollTimeoutId = window.setTimeout(() => {
          const { scrollTop, scrollHeight, clientHeight } = scrollArea;
          const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

          if (distanceFromBottom < 50 && !isFetchingNextPage) {
            fetchNextPage();
          }
        }, 100);
      };

      scrollArea.addEventListener('scroll', handleScroll, { passive: true });

      cleanup = () => {
        scrollArea.removeEventListener('scroll', handleScroll);
        clearTimeout(scrollTimeoutId);
      };
    }, 100);

    return () => {
      clearTimeout(timeoutId);
      cleanup?.();
    };
  }, [open, hasNextPage, fetchNextPage, isFetchingNextPage]);

  const handleSearchChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      if (disabled) return;
      setSearch(event.target.value);
    },
    [disabled],
  );

  const onSelect = useCallback(
    (instanceId: JhaOption['instanceId']) => {
      if (!onChange || disabled || !instanceId) return;

      const currentSelected = selected || [];
      const isSelected = currentSelected.includes(instanceId);

      const updatedSelected = isSelected
        ? currentSelected.filter((id) => id !== instanceId)
        : [...currentSelected, instanceId];

      onChange(updatedSelected.length > 0 ? updatedSelected : []);
      setOpen(true);
    },
    [onChange, disabled, selected],
  );

  const renderSelectedValue = () => {
    if (selectedOptions.length === 0) {
      return <span className="text-muted-foreground">{placeholder}</span>;
    }

    return selectedOptions.map((jha) => (
      <Badge
        variant="outline"
        key={jha.instanceId}
        className="flex items-center gap-1 pr-1 max-w-xs group-hover:bg-background"
      >
        <div className="flex flex-col min-w-0">
          <span className="font-semibold text-xs truncate">
            {jha.slug}: {jha.title}
          </span>
          <span className="text-[10px] text-muted-foreground truncate">
            {jha.location?.name || 'No location'} - {jha.status}
          </span>
        </div>
        <Button
          variant="ghost"
          size="xs"
          className="border-none ml-1"
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              handleUnselect(jha.instanceId);
            }
          }}
          onMouseDown={(e) => {
            e.preventDefault();
            e.stopPropagation();
          }}
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            handleUnselect(jha.instanceId);
          }}
        >
          <X size={12} className="text-muted-foreground" />
        </Button>
      </Badge>
    ));
  };

  return (
    <Popover open={open} onOpenChange={setOpen} modal>
      <PopoverTrigger asChild className={className}>
        <div
          {...props}
          role="combobox"
          aria-expanded={open}
          aria-disabled={disabled}
          aria-haspopup="listbox"
          aria-label={
            selectedOptions.length > 0
              ? `Selected JHAs: ${selectedOptions.map((jha) => `${jha.slug}: ${jha.title}`).join(', ')}`
              : placeholder
          }
          className={cn(
            'group flex w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background transition-[color,box-shadow] placeholder:text-muted-foreground focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:bg-input/30',
            open && !disabled && 'border-ring ring-[3px] ring-ring/50',
            'aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40',
            'h-full min-h-[2.25rem]',
            disabled && 'cursor-not-allowed opacity-50',
            'cursor-pointer',
          )}
          onClick={() => !disabled && setOpen(!open)}
        >
          <div className="flex items-center gap-2 flex-1 min-w-0 flex-wrap">{renderSelectedValue()}</div>

          <div className="flex items-center gap-2 flex-shrink-0">
            {isLoading && <Loader2 className="animate-spin size-4" />}
            <ChevronsUpDown className="size-4 shrink-0 opacity-50" />
          </div>
        </div>
      </PopoverTrigger>
      <PopoverPortal>
        <PopoverContent avoidCollisions className="w-[var(--radix-popover-trigger-width)] p-0">
          <div>
            <div className="flex items-center p-2 pl-4">
              <Search size={15} />
              <input
                className="w-full border-none px-2 py-1 outline-hidden placeholder:text-muted-foreground placeholder:text-sm text-sm focus-visible:outline-hidden disabled:cursor-not-allowed disabled:opacity-50"
                value={search}
                placeholder="Search JHAs by ID or title..."
                onChange={handleSearchChange}
                disabled={disabled}
              />
            </div>
            <Separator />
            <div ref={scrollAreaRef} className="p-2 overflow-auto" style={{ height: 200 }}>
              {Object.keys(options).length === 0 && !isLoading && (
                <div className="p-2 text-sm text-muted-foreground text-center">No JHAs found</div>
              )}

              {Object.values(options).map((jha) => {
                const isSelected = selected?.includes(jha.instanceId!) ?? false;
                return (
                  <div
                    key={jha.instanceId}
                    onClick={() => !disabled && onSelect(jha.instanceId)}
                    className={cn(
                      'flex cursor-pointer items-center rounded-md p-2 text-sm hover:bg-secondary/80 gap-3',
                      disabled && 'cursor-not-allowed opacity-50 hover:bg-transparent',
                    )}
                  >
                    <Check className={cn('size-4 flex-shrink-0', isSelected ? 'opacity-100' : 'opacity-0')} />
                    <div className="flex flex-col flex-1">
                      <span className="font-semibold">
                        {jha.slug}: {jha.title}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {jha.location?.name || 'No location'} - {JHA_STATUS_MAP[jha.status]}
                      </span>
                    </div>
                  </div>
                );
              })}

              {isFetchingNextPage && (
                <div className="flex items-center justify-center p-2">
                  <Loader2 className="animate-spin size-4" />
                  <span className="ml-2 text-sm text-muted-foreground">Loading more...</span>
                </div>
              )}
            </div>
          </div>
        </PopoverContent>
      </PopoverPortal>
    </Popover>
  );
};
