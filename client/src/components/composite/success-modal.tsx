import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-mobile';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { RouterOutputs } from '@shared/types/router.types';
import confetti from 'canvas-confetti';
import { FileText, Plus, ShieldCheck } from 'lucide-react';
import type { LucideIcon } from 'lucide-react';
import { AnimatePresence, motion } from 'motion/react';
import { useEffect } from 'react';
import { useLocation } from 'wouter';

type CreatedEvent = RouterOutputs['event']['create'];
type CreatedPublicEvent = RouterOutputs['event']['createPublic'];
type CreatedCapa = RouterOutputs['capa']['create'];
type CreatedOshaReport = RouterOutputs['oshaReport']['create'];

interface SuccessModalProps {
  isOpen: boolean;
  onClose?: () => void;
  data?: CreatedEvent | CreatedPublicEvent | CreatedCapa | CreatedOshaReport;
  entity: 'event' | 'publicEvent' | 'capa' | 'oshaReport';
  onSecondaryActionClick?: () => void;
}

type ActionConfig = {
  label: string;
  onClick?: () => void;
  icon?: LucideIcon;
};

type SuccessModalConfig = {
  title: string;
  description?: string;
  icon: {
    comp: LucideIcon;
    color: string;
    bgColor: string;
  };
  emoji?: string;
  primaryAction?: ActionConfig;
  secondaryAction?: ActionConfig;
};

export function SuccessModal({ isOpen, onClose, entity, data, onSecondaryActionClick }: SuccessModalProps) {
  const isMobile = useIsMobile();
  const [, navigate] = useLocation();

  const CONFIG_MAP: Record<SuccessModalProps['entity'], SuccessModalConfig> = {
    event: {
      title: 'Report Submitted!',
      description: 'Thank you for speaking up. Your action helps keep everyone safer.',
      icon: {
        comp: ShieldCheck,
        color: 'text-indigo-600',
        bgColor: 'bg-indigo-50',
      },
      emoji: '🦺',
      primaryAction: {
        label: 'View Event',
        onClick: () => {
          if (data?.id) {
            navigate(ROUTES.BUILD_EVENT_DETAILS_PATH(data.id));
          }
        },
        icon: FileText,
      },
      secondaryAction: {
        label: 'Submit Another Event',
        onClick: onSecondaryActionClick,
        icon: Plus,
      },
    },
    publicEvent: {
      title: 'Report Submitted!',
      description:
        'A confirmation email has been sent to the address you provided. Our safety team will review your report and take appropriate action.',
      icon: {
        comp: ShieldCheck,
        color: 'text-green-600',
        bgColor: 'bg-green-100',
      },
      emoji: '',

      secondaryAction: {
        label: 'Submit Another Event',
        onClick: onSecondaryActionClick,
        icon: Plus,
      },
    },
    capa: {
      title: 'CAPA Created!',
      description: "Thank you for taking action. You've helped move us one step closer to a safer, stronger workplace.",
      icon: {
        comp: ShieldCheck,
        color: 'text-green-600',
        bgColor: 'bg-green-100',
      },
      emoji: '🎉',

      primaryAction: {
        label: 'View CAPA',
        onClick: () => {
          if (data?.id) {
            navigate(ROUTES.BUILD_CAPA_DETAILS_PATH(data.id));
          }
        },
        icon: FileText,
      },
      secondaryAction: {
        label: 'Create Another CAPA',
        onClick: onSecondaryActionClick,
        icon: Plus,
      },
    },
    oshaReport: {
      title: 'OSHA Report Created!',
      description: 'Your OSHA report has been successfully submitted and recorded.',
      icon: {
        comp: ShieldCheck,
        color: 'text-orange-600',
        bgColor: 'bg-orange-100',
      },
      emoji: '📋',

      primaryAction: {
        label: 'View OSHA Report',
        onClick: () => {
          if (data?.id) {
            navigate(ROUTES.BUILD_OSHA_REPORT_DETAILS_PATH(data.id));
          }
        },
        icon: FileText,
      },
      secondaryAction: {
        label: 'Create Another OSHA Report',
        onClick: onSecondaryActionClick,
        icon: Plus,
      },
    },
  };

  const config = CONFIG_MAP[entity];
  const Icon = config.icon.comp;

  // Trigger confetti animation
  useEffect(() => {
    if (isOpen) {
      // Trigger confetti after a short delay to ensure modal is visible
      const timer = setTimeout(() => {
        triggerConfetti();

        // Trigger haptic feedback on mobile if supported
        if (isMobile && navigator.vibrate) {
          navigator.vibrate([100, 50, 100]);
        }
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [isOpen, isMobile]);

  // Confetti animation function
  const triggerConfetti = () => {
    const duration = 3000;
    const animationEnd = Date.now() + duration;
    const defaults = {
      startVelocity: 30,
      spread: 360,
      ticks: 60,
      zIndex: 9999,
    };

    function randomInRange(min: number, max: number) {
      return Math.random() * (max - min) + min;
    }

    const interval = setInterval(() => {
      const timeLeft = animationEnd - Date.now();

      if (timeLeft <= 0) {
        return clearInterval(interval);
      }

      const particleCount = 50 * (timeLeft / duration);

      // Since they are random anyway, we can use the same random call for both colors
      confetti({
        ...defaults,
        particleCount,
        origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 },
        colors: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#F97316'],
      });

      confetti({
        ...defaults,
        particleCount,
        origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 },
        colors: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#F97316'],
      });
    }, 250);
  };

  // Different styling and layout for mobile vs desktop
  const renderMobileContent = () => (
    <motion.div
      className="fixed inset-x-0 bottom-0 z-50 bg-white rounded-t-xl shadow-2xl"
      initial={{ y: '100%' }}
      animate={{ y: 0 }}
      exit={{ y: '100%' }}
      transition={{ type: 'spring', damping: 30, stiffness: 300 }}
    >
      <div className="p-6 space-y-6">
        <div className="flex flex-col items-center text-center">
          {Icon && (
            <div className={`mb-4 flex items-center justify-center h-20 w-20 rounded-full ${config.icon.bgColor}`}>
              <Icon className={`h-10 w-10 ${config.icon.color}`} />
            </div>
          )}
          <h2 className="text-2xl font-bold text-center flex items-center gap-2">
            {config.emoji && <span>{config.emoji}</span>}
            {config.title}
          </h2>
          {config.description && <p className="text-muted-foreground mt-2 mb-4">{config.description}</p>}
          {data?.slug && (
            <div className="mb-4">
              <p className="text-lg font-mono font-bold text-gray-900">{data.slug}</p>
            </div>
          )}
        </div>

        <div className="space-y-3">
          {config?.secondaryAction && (
            <Button variant="outline" className="w-full py-6 text-base" onClick={config.secondaryAction.onClick}>
              {config.secondaryAction.icon && <config.secondaryAction.icon className="mr-2 h-5 w-5" />}
              {config.secondaryAction.label}
            </Button>
          )}

          {config?.primaryAction && (
            <Button className="w-full py-6 text-base" onClick={config.primaryAction.onClick}>
              {config.primaryAction.icon && <config.primaryAction.icon className="mr-2 h-5 w-5" />}
              {config.primaryAction.label}
            </Button>
          )}
        </div>
      </div>
    </motion.div>
  );

  const renderDesktopContent = () => (
    <motion.div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={onClose}
    >
      <motion.div
        className="relative bg-white rounded-xl shadow-xl w-[550px] p-8"
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        transition={{ type: 'spring', damping: 25, stiffness: 300 }}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex flex-col items-center text-center mb-8">
          {Icon && (
            <div className={`mb-6 flex items-center justify-center h-24 w-24 rounded-full ${config.icon.bgColor}`}>
              <Icon className={`h-12 w-12 ${config.icon.color}`} />
            </div>
          )}
          <h2 className="text-3xl font-bold text-center flex items-center gap-2">
            {config.emoji && <span>{config.emoji}</span>}
            {config.title}
          </h2>
          {config.description && <p className="text-muted-foreground mt-3 text-lg max-w-md">{config.description}</p>}
          {data?.slug && (
            <div className="mb-6">
              <p className="text-2xl font-mono font-bold text-gray-900">{data.slug}</p>
            </div>
          )}
        </div>

        <div className="flex justify-center space-x-4">
          {config?.secondaryAction && (
            <Button variant="outline" onClick={config.secondaryAction.onClick}>
              {config.secondaryAction.icon && <config.secondaryAction.icon className="mr-2 h-4 w-4" />}
              {config.secondaryAction.label}
            </Button>
          )}

          {config?.primaryAction && (
            <Button onClick={config.primaryAction.onClick}>
              {config.primaryAction.icon && <config.primaryAction.icon className="mr-2 h-4 w-4" />}
              {config.primaryAction.label}
            </Button>
          )}
        </div>
      </motion.div>
    </motion.div>
  );

  return <AnimatePresence>{isOpen && (isMobile ? renderMobileContent() : renderDesktopContent())}</AnimatePresence>;
}
