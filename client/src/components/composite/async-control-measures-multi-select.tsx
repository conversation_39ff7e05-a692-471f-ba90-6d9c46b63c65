import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverPortal, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { useInfiniteControlMeasures } from '@/hooks/use-paginated-data';
import { cn } from '@/lib/utils';
import { CONTROL_MEASURE_TYPE_MAP } from '@shared/types/jha.types';
import { RouterOutputs } from '@shared/types/router.types';
import { useDebounce } from '@uidotdev/usehooks';
import { Check, ChevronsUpDown, Loader2, Plus, Search, X } from 'lucide-react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { controlMeasureCategoryEnum } from '@shared/schema';

type ControlMeasureOption = RouterOutputs['controlMeasures']['list']['result'][number];

type NewControlMeasureOption = Pick<ControlMeasureOption, 'name' | 'type'>;

export const AsyncControlMeasuresMultiSelect = ({
  onChange,
  onNewValueChange,
  className,
  value: selected = null,
  newValue = null,
  placeholder = 'Select control measures...',
  disabled = false,
  ...props
}: {
  value?: ControlMeasureOption['id'][] | null;
  newValue?: NewControlMeasureOption[] | null;
  onChange: (selected: ControlMeasureOption['id'][] | undefined) => void;
  onNewValueChange?: (newValues: NewControlMeasureOption[] | undefined) => void;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
}) => {
  const [open, setOpen] = useState(false);
  const [selectedControlMeasures, setSelectedControlMeasures] = useState<Record<string, ControlMeasureOption>>({});
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  const [search, setSearch] = useState('');
  const [customInput, setCustomInput] = useState('');
  const debouncedSearch = useDebounce(search, 300);

  const {
    data: controlMeasures,
    isLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = useInfiniteControlMeasures({
    filters: {
      search: debouncedSearch,
    },
  });

  const options = useMemo(() => {
    return (
      controlMeasures?.reduce(
        (acc, controlMeasure) => {
          acc[controlMeasure.id] = controlMeasure;
          return acc;
        },
        {} as Record<string, ControlMeasureOption>,
      ) || {}
    );
  }, [controlMeasures]);

  // Computed value for selected control measure objects
  const selectedOptions = useMemo(() => {
    if (!Array.isArray(selected)) return [];
    return selected.map((id) => selectedControlMeasures[id] || options[id]).filter(Boolean);
  }, [selected, selectedControlMeasures, options]);

  // Custom control measures that will be created
  const customControlMeasures = useMemo(() => {
    return newValue || [];
  }, [newValue]);

  const handleUnselect = useCallback(
    (controlMeasureId: ControlMeasureOption['id']) => {
      if (disabled) return;

      const updatedSelectedIds = (selected || []).filter((id) => id !== controlMeasureId);
      onChange(updatedSelectedIds.length > 0 ? updatedSelectedIds : undefined);

      // Remove from selected control measures when unselected
      setSelectedControlMeasures((prev) => {
        const updated = { ...prev };
        delete updated[controlMeasureId];
        return updated;
      });
    },
    [disabled, selected, onChange],
  );

  const handleUnselectCustom = useCallback(
    (customControlMeasureName: string) => {
      if (disabled || !onNewValueChange) return;

      const updatedCustomControlMeasures = customControlMeasures.filter(
        (controlMeasure) => controlMeasure.name !== customControlMeasureName,
      );
      onNewValueChange(updatedCustomControlMeasures.length > 0 ? updatedCustomControlMeasures : undefined);
    },
    [disabled, onNewValueChange, customControlMeasures],
  );

  const handleAddCustomControlMeasure = useCallback(
    (inputValue?: string) => {
      const valueToAdd = inputValue || search.trim();
      if (disabled || !onNewValueChange || !valueToAdd) return;

      // Check if it already exists in existing control measures
      const existsInControlMeasures = controlMeasures.some(
        (controlMeasure) => controlMeasure.name.toLowerCase() === valueToAdd.toLowerCase(),
      );

      // Check if it already exists in custom control measures
      const existsInCustom = customControlMeasures.some(
        (controlMeasure) => controlMeasure.name.toLowerCase() === valueToAdd.toLowerCase(),
      );

      if (!existsInControlMeasures && !existsInCustom) {
        const newControlMeasure = { name: valueToAdd, type: controlMeasureCategoryEnum.enumValues[5] };
        const updatedCustomControlMeasures = [...customControlMeasures, newControlMeasure];
        onNewValueChange(updatedCustomControlMeasures);
        if (inputValue) {
          setCustomInput('');
        } else {
          setSearch('');
        }
      }
    },
    [disabled, onNewValueChange, search, controlMeasures, customControlMeasures],
  );

  const handleAddFromCustomInput = useCallback(() => {
    if (customInput.trim()) {
      handleAddCustomControlMeasure(customInput.trim());
    }
  }, [customInput, handleAddCustomControlMeasure]);

  // Store control measure data as it gets loaded to preserve across searches
  useEffect(() => {
    if (controlMeasures && controlMeasures.length > 0) {
      setSelectedControlMeasures((prev) => {
        const updated = { ...prev };
        controlMeasures.forEach((controlMeasure) => {
          updated[controlMeasure.id] = controlMeasure;
        });
        return updated;
      });
    }
  }, [controlMeasures]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (disabled) return;

      if (e.key === 'Escape') {
        setOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [disabled]);

  // Handle infinite scrolling
  useEffect(() => {
    if (!open || !hasNextPage) return;

    let cleanup: (() => void) | undefined;

    // Use a timeout to ensure the PopoverContent is fully rendered
    const timeoutId = window.setTimeout(() => {
      const scrollArea = scrollAreaRef.current;
      if (!scrollArea) return;

      let scrollTimeoutId: number;

      const handleScroll = () => {
        clearTimeout(scrollTimeoutId);
        scrollTimeoutId = window.setTimeout(() => {
          const { scrollTop, scrollHeight, clientHeight } = scrollArea;
          const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

          if (distanceFromBottom < 50 && !isFetchingNextPage) {
            fetchNextPage();
          }
        }, 100);
      };

      scrollArea.addEventListener('scroll', handleScroll, { passive: true });

      cleanup = () => {
        scrollArea.removeEventListener('scroll', handleScroll);
        clearTimeout(scrollTimeoutId);
      };
    }, 100);

    return () => {
      clearTimeout(timeoutId);
      cleanup?.();
    };
  }, [open, hasNextPage, fetchNextPage, isFetchingNextPage]);

  const handleSearchChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      if (disabled) return;
      setSearch(event.target.value);
    },
    [disabled],
  );

  const handleSearchKeyDown = useCallback(
    (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (disabled) return;

      if (event.key === 'Enter' && search.trim() && onNewValueChange) {
        event.preventDefault();
        handleAddCustomControlMeasure();
      }
    },
    [disabled, search, onNewValueChange, handleAddCustomControlMeasure],
  );

  const handleCustomInputKeyDown = useCallback(
    (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (disabled) return;

      if (event.key === 'Enter' && customInput.trim() && onNewValueChange) {
        event.preventDefault();
        handleAddFromCustomInput();
      }
    },
    [disabled, customInput, onNewValueChange, handleAddFromCustomInput],
  );

  const onSelect = useCallback(
    (controlMeasureId: ControlMeasureOption['id']) => {
      if (!onChange || disabled) return;

      const currentSelected = selected || [];
      const isSelected = currentSelected.includes(controlMeasureId);

      const updatedSelected = isSelected
        ? currentSelected.filter((id) => id !== controlMeasureId)
        : [...currentSelected, controlMeasureId];

      onChange(updatedSelected.length > 0 ? updatedSelected : undefined);
      setOpen(true);
    },
    [onChange, disabled, selected],
  );

  const renderSelectedValue = () => {
    const totalCount = selectedOptions.length + customControlMeasures.length;

    if (totalCount === 0) {
      return <span className="text-muted-foreground">{placeholder}</span>;
    }

    return (
      <span className="text-sm">
        {totalCount} control measure{totalCount === 1 ? '' : 's'} selected
      </span>
    );
  };

  const renderSelectedItems = () => {
    if (selectedOptions.length === 0 && customControlMeasures.length === 0) {
      return null;
    }

    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-2">
        <div className="text-xs font-medium mb-2 text-green-800">Selected Control Measure(s):</div>
        <div className="flex flex-wrap gap-1">
          {selectedOptions.map((controlMeasure) => {
            const displayType = CONTROL_MEASURE_TYPE_MAP[controlMeasure.type];
            return (
              <div
                key={controlMeasure.id}
                className="inline-flex items-center gap-1 px-2 py-1 rounded text-xs bg-white/50 text-green-800"
              >
                <span>
                  {controlMeasure.name} ({displayType})
                </span>
                <Button
                  variant="ghost"
                  size="xs"
                  className="border-none p-0 h-auto w-auto ml-1"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleUnselect(controlMeasure.id);
                    }
                  }}
                  onMouseDown={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleUnselect(controlMeasure.id);
                  }}
                >
                  <X size={12} className="text-green-600" />
                </Button>
              </div>
            );
          })}
          {customControlMeasures.map((controlMeasure) => {
            const customType =
              CONTROL_MEASURE_TYPE_MAP[controlMeasure?.type as keyof typeof CONTROL_MEASURE_TYPE_MAP] || 'Custom';

            return (
              <div
                key={`custom-${controlMeasure.name}`}
                className="inline-flex items-center gap-1 px-2 py-1 rounded text-xs bg-white/50 text-green-800"
              >
                <span>
                  {controlMeasure.name} ({customType})
                </span>
                <Button
                  variant="ghost"
                  size="xs"
                  className="border-none p-0 h-auto w-auto ml-1"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleUnselectCustom(controlMeasure.name);
                    }
                  }}
                  onMouseDown={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleUnselectCustom(controlMeasure.name);
                  }}
                >
                  <X size={12} className="text-green-600" />
                </Button>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div>
      {renderSelectedItems()}
      <Popover open={open} onOpenChange={setOpen} modal>
        <PopoverTrigger asChild className={className}>
          <div
            {...props}
            role="combobox"
            aria-expanded={open}
            aria-disabled={disabled}
            aria-haspopup="listbox"
            aria-label={
              selectedOptions.length > 0 || customControlMeasures.length > 0
                ? `Selected control measures: ${[...selectedOptions.map((controlMeasure) => controlMeasure.name), ...customControlMeasures.map((controlMeasure) => controlMeasure.name)].join(', ')}`
                : placeholder
            }
            className={cn(
              'group flex w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background transition-[color,box-shadow] placeholder:text-muted-foreground focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:bg-input/30',
              open && !disabled && 'border-ring ring-[3px] ring-ring/50',
              'aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40',
              'h-9',
              disabled && 'cursor-not-allowed opacity-50',
              'cursor-pointer',
            )}
            onClick={() => !disabled && setOpen(!open)}
          >
            <div className="flex items-center gap-2 flex-1 min-w-0">{renderSelectedValue()}</div>

            <div className="flex items-center gap-2 flex-shrink-0">
              {isLoading && <Loader2 className="animate-spin size-4" />}
              <ChevronsUpDown className="size-4 shrink-0 opacity-50" />
            </div>
          </div>
        </PopoverTrigger>
        <PopoverPortal>
          <PopoverContent avoidCollisions className="w-[var(--radix-popover-trigger-width)] p-0">
            <div>
              <div className="flex items-center p-2 pl-4">
                <Search size={15} />
                <input
                  className="w-full border-none px-2 py-1 outline-hidden placeholder:text-muted-foreground placeholder:text-sm text-sm focus-visible:outline-hidden disabled:cursor-not-allowed disabled:opacity-50"
                  value={search}
                  placeholder="Search control measures..."
                  onChange={handleSearchChange}
                  onKeyDown={handleSearchKeyDown}
                  disabled={disabled}
                />
              </div>
              {onNewValueChange && (
                <>
                  <Separator />
                  <div className="flex items-center p-2 pl-4 gap-2">
                    <input
                      className="flex-1 border-none px-2 py-1 outline-hidden placeholder:text-muted-foreground focus-visible:outline-hidden disabled:cursor-not-allowed disabled:opacity-50 text-sm bg-muted/30 rounded"
                      value={customInput}
                      placeholder="Add custom control measure..."
                      onChange={(e) => setCustomInput(e.target.value)}
                      onKeyDown={handleCustomInputKeyDown}
                      disabled={disabled}
                    />
                    <Button
                      variant="ghost"
                      size="xs"
                      className="border-none p-1 h-6 w-6"
                      onClick={handleAddFromCustomInput}
                      disabled={disabled || !customInput.trim()}
                    >
                      <Plus size={14} />
                    </Button>
                  </div>
                </>
              )}
              <Separator />
              <div ref={scrollAreaRef} className="p-2 overflow-auto" style={{ height: 300 }}>
                {Object.keys(options).length === 0 && !isLoading && (
                  <div className="p-2 text-sm text-muted-foreground text-center">No control measures found</div>
                )}

                {Object.values(options).map((controlMeasure) => {
                  const isSelected = selected?.includes(controlMeasure.id) ?? false;
                  return (
                    <div
                      key={controlMeasure.id}
                      onClick={() => !disabled && onSelect(controlMeasure.id)}
                      className={cn(
                        'flex cursor-pointer items-center rounded-md p-2 text-sm hover:bg-secondary/80 gap-3',
                        disabled && 'cursor-not-allowed opacity-50 hover:bg-transparent',
                      )}
                    >
                      <Check className={cn('size-4 flex-shrink-0', isSelected ? 'opacity-100' : 'opacity-0')} />
                      <div className="flex flex-col">
                        <span>{controlMeasure.name}</span>
                        <span className="text-xs text-muted-foreground">
                          {CONTROL_MEASURE_TYPE_MAP[controlMeasure.type]}
                        </span>
                      </div>
                    </div>
                  );
                })}

                {isFetchingNextPage && (
                  <div className="flex items-center justify-center p-2">
                    <Loader2 className="animate-spin size-4" />
                    <span className="ml-2 text-sm text-muted-foreground">Loading more...</span>
                  </div>
                )}

                {isLoading && !isFetchingNextPage && (
                  <div className="flex items-center justify-center p-2">
                    <Loader2 className="animate-spin size-4" />
                    <span className="ml-2 text-sm text-muted-foreground">Loading...</span>
                  </div>
                )}

                {/* Add custom control measure option */}
                {search.trim() &&
                  onNewValueChange &&
                  !controlMeasures?.some((cm) => cm.name.toLowerCase() === search.trim().toLowerCase()) &&
                  !customControlMeasures.some((cm) => cm.name.toLowerCase() === search.trim().toLowerCase()) && (
                    <>
                      <Separator className="my-2" />
                      <div
                        onClick={() => !disabled && handleAddCustomControlMeasure()}
                        className={cn(
                          'flex cursor-pointer items-center rounded-md p-2 text-sm hover:bg-secondary/80 gap-3',
                          disabled && 'cursor-not-allowed opacity-50 hover:bg-transparent',
                        )}
                      >
                        <Plus className="size-4 flex-shrink-0" />
                        <div className="flex flex-col">
                          <span>Add custom control measure</span>
                          <span className="text-xs text-muted-foreground">"{search.trim()}"</span>
                        </div>
                      </div>
                    </>
                  )}

                {/* Custom control measures section */}
                {customControlMeasures.length > 0 && (
                  <>
                    <Separator className="my-2" />
                    <div className="px-2 py-1 text-xs font-medium text-muted-foreground">Custom Control Measures</div>
                    {customControlMeasures.map((controlMeasure) => (
                      <div
                        key={`custom-${controlMeasure.name}`}
                        className="flex items-center rounded-md p-2 text-sm gap-3"
                      >
                        <Check className="size-4 flex-shrink-0 opacity-100" />
                        <div className="flex flex-col flex-1">
                          <span>{controlMeasure.name}</span>
                          <span className="text-xs text-muted-foreground">
                            {CONTROL_MEASURE_TYPE_MAP[controlMeasure.type]}
                          </span>
                        </div>
                        <Button
                          variant="ghost"
                          size="xs"
                          className="border-none"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleUnselectCustom(controlMeasure.name);
                          }}
                        >
                          <X size={14} className="text-muted-foreground" />
                        </Button>
                      </div>
                    ))}
                  </>
                )}
              </div>
            </div>
          </PopoverContent>
        </PopoverPortal>
      </Popover>
    </div>
  );
};
