import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ScrollA<PERSON>, ScrollBar } from '@/components/ui/scroll-area';
import { useNotifications } from '@/hooks/use-notifications';
import { cn } from '@/lib/utils';
import { formatDate } from '@shared/date-utils';
import { Bell } from 'lucide-react';
import { useLocation } from 'wouter';

export const AlertNotifications = () => {
  const [, navigate] = useLocation();
  const { notifications, unreadCount, isConnected, markAsRead, markAllAsRead } = useNotifications();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className={cn('relative w-8 h-8 cursor-pointer p-2')}>
          <Bell className={cn('w-4 h-4', isConnected ? 'text-gray-700' : 'text-gray-400')} />
          {/* Notification Badge */}
          {unreadCount > 0 && (
            <span className="absolute top-0 right-0 bg-red-500 text-white text-[10px] rounded-full h-3 w-3 flex items-center justify-center min-w-[12px]">
              {unreadCount > 9 ? '9+' : unreadCount}
            </span>
          )}
          {/* Connection indicator */}
          {!isConnected && (
            <span className="absolute bottom-0 right-0 bg-yellow-500 text-white text-xs rounded-full h-2 w-2 flex items-center justify-center">
              <span className="w-1 h-1 bg-white rounded-full animate-pulse"></span>
            </span>
          )}
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-80 max-h-96 overflow-y-auto">
        <DropdownMenuLabel>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500 mt-1">
                {unreadCount > 0
                  ? `${unreadCount} unread notification${unreadCount !== 1 ? 's' : ''}`
                  : 'No new notifications'}
              </p>
            </div>
            {unreadCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => markAllAsRead()}
                className="text-xs text-primary-600 hover:text-primary-700"
              >
                Mark all read
              </Button>
            )}
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />

        {/* Notifications List */}
        <ScrollArea className="overflow-y-auto max-h-80">
          {notifications.length > 0 ? (
            notifications.map((notification) => (
              <DropdownMenuItem
                key={notification.id}
                className={cn(
                  'flex flex-col items-start cursor-pointer hover:bg-gray-50 border-b mb-1',
                  !notification.readAt ? 'bg-blue-50' : '',
                )}
                onClick={() => {
                  if (!notification.readAt) {
                    markAsRead(notification.id);
                  }
                  if (notification.url) {
                    navigate(notification.url);
                  }
                }}
              >
                <div className="flex items-start justify-between w-full">
                  <div className="flex-1 min-w-0">
                    <h4
                      className={cn(
                        'text-sm font-medium truncate',
                        !notification.readAt ? 'text-gray-900' : 'text-gray-700',
                      )}
                    >
                      {notification.title}
                    </h4>
                    {notification.description && (
                      <p className="text-xs text-gray-500 mt-1 line-clamp-2">{notification.description}</p>
                    )}
                    <p className="text-xs text-gray-400 mt-2">{formatDate(new Date(notification.createdAt))}</p>
                  </div>
                  {!notification.readAt && (
                    <div className="w-2 h-2 bg-blue-500 rounded-full mr-2 mt-2 flex-shrink-0"></div>
                  )}
                </div>
              </DropdownMenuItem>
            ))
          ) : (
            <div className="p-4 text-center text-gray-500">
              <Bell className="w-12 h-12 mx-auto mb-2 text-gray-300" />
              <p className="text-sm">No notifications yet</p>
              <p className="text-xs text-gray-400 mt-1">Notifications will appear here when there are updates</p>
            </div>
          )}
          <ScrollBar />
        </ScrollArea>

        {notifications.length > 10 && (
          <div className="p-3 border-t border-gray-100">
            <Button variant="ghost" size="sm" className="w-full text-sm">
              View all notifications
            </Button>
          </div>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
