import { CreateOshaLocationModal } from '@/components/osha-locations/create-osha-location-modal';
import { Popover, PopoverContent, PopoverPortal, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { useInfiniteMinimalOshaLocations } from '@/hooks/use-paginated-data';
import { cn } from '@/lib/utils';
import { trpc } from '@/providers/trpc';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { RouterOutputs } from '@shared/types/router.types';
import { useDebounce } from '@uidotdev/usehooks';
import { Check, ChevronsUpDown, Info, Loader2, Plus, Search, X } from 'lucide-react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Link } from 'wouter';

type OshaLocationOption = RouterOutputs['oshaLocation']['minimalList']['result'][number];

type AsyncOshaLocationSelectProps = {
  value?: OshaLocationOption['id'] | null;
  onChange: (selected: OshaLocationOption['id'] | undefined) => void;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
  mustIncludeObjectIds?: string[];
};

export const AsyncOshaLocationSelect = ({
  onChange,
  className,
  value: selected = null,
  placeholder = 'Select OSHA location...',
  disabled = false,
  mustIncludeObjectIds,
  ...props
}: AsyncOshaLocationSelectProps) => {
  const [open, setOpen] = useState(false);
  const [selectedOshaLocation, setSelectedOshaLocation] = useState<OshaLocationOption | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  const [search, setSearch] = useState('');
  const debouncedSearch = useDebounce(search, 300);

  const {
    data: oshaLocations,
    isLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = useInfiniteMinimalOshaLocations({
    filters: {
      search: debouncedSearch,
      mustIncludeObjectIds,
    },
    enabled: open,
  });

  const { data: archivedLocations } = trpc.oshaSummary.getArchived.useQuery({ year: new Date().getFullYear() });

  const options = useMemo(() => {
    return (
      oshaLocations?.reduce(
        (acc, oshaLocation) => {
          acc[oshaLocation.id] = oshaLocation;
          return acc;
        },
        {} as Record<string, RouterOutputs['oshaLocation']['minimalList']['result'][number]>,
      ) || {}
    );
  }, [oshaLocations]);

  const handleUnselect = useCallback(() => {
    if (disabled) return;
    onChange(undefined);
    setSelectedOshaLocation(null);
  }, [disabled, onChange]);

  useEffect(() => {
    if (selected && options[selected] && selectedOshaLocation?.id !== selected) {
      setSelectedOshaLocation(options[selected]);
    }

    if (!selected && selectedOshaLocation) {
      setSelectedOshaLocation(null);
    }
  }, [selected, options, selectedOshaLocation]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (disabled) return;

      if (e.key === 'Escape') {
        setOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [disabled]);

  // Handle infinite scrolling
  useEffect(() => {
    if (!open || !hasNextPage) return;

    let cleanup: (() => void) | undefined;

    // Use a timeout to ensure the PopoverContent is fully rendered
    const timeoutId = window.setTimeout(() => {
      const scrollArea = scrollAreaRef.current;
      if (!scrollArea) return;

      let scrollTimeoutId: number;

      const handleScroll = () => {
        clearTimeout(scrollTimeoutId);
        scrollTimeoutId = window.setTimeout(() => {
          const { scrollTop, scrollHeight, clientHeight } = scrollArea;
          const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

          if (distanceFromBottom < 50 && !isFetchingNextPage) {
            fetchNextPage();
          }
        }, 100);
      };

      scrollArea.addEventListener('scroll', handleScroll, { passive: true });

      cleanup = () => {
        scrollArea.removeEventListener('scroll', handleScroll);
        clearTimeout(scrollTimeoutId);
      };
    }, 100);

    return () => {
      clearTimeout(timeoutId);
      cleanup?.();
    };
  }, [open, hasNextPage, fetchNextPage, isFetchingNextPage]);

  const handleSearchChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      if (disabled) return;
      setSearch(event.target.value);
    },
    [disabled],
  );

  const onSelect = useCallback(
    (oshaLocationId: OshaLocationOption['id']) => {
      if (!onChange || disabled) return;

      onChange(oshaLocationId);
      setSelectedOshaLocation(options[oshaLocationId]);
      setOpen(false);
    },
    [onChange, disabled, options],
  );

  const handleAddNewLocation = useCallback(() => {
    setOpen(false);
    setShowCreateModal(true);
  }, []);

  const renderSelectedValue = () => {
    if (isLoading) {
      return <span className="text-muted-foreground text-base md:text-sm">Loading...</span>;
    }

    if (selectedOshaLocation) {
      return (
        <div className="flex items-center gap-2">
          <span className="text-sm">{selectedOshaLocation.name}</span>
        </div>
      );
    }

    return <span className="text-muted-foreground">{placeholder}</span>;
  };

  return (
    <>
      <Popover open={open} onOpenChange={setOpen} modal>
        <PopoverTrigger asChild className={className}>
          <div
            {...props}
            role="combobox"
            aria-expanded={open}
            aria-disabled={disabled}
            aria-haspopup="listbox"
            aria-label={selectedOshaLocation ? `Selected OSHA location: ${selectedOshaLocation.name}` : placeholder}
            className={cn(
              'group flex h-9 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background transition-[color,box-shadow] placeholder:text-muted-foreground focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:bg-input/30',
              open && !disabled && 'border-ring ring-[3px] ring-ring/50',
              'aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40',
              disabled && 'cursor-not-allowed opacity-50',
              'cursor-pointer',
            )}
            onClick={() => !disabled && setOpen(!open)}
          >
            <div className="flex items-center gap-2 flex-1 min-w-0">{renderSelectedValue()}</div>

            <div className="flex items-center gap-2 flex-shrink-0">
              {selected && (
                <X
                  size={16}
                  className="text-muted-foreground hover:text-foreground cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleUnselect();
                  }}
                />
              )}
              {isLoading && <Loader2 className="animate-spin size-4" />}
              <ChevronsUpDown className="size-4 shrink-0 opacity-50" />
            </div>
          </div>
        </PopoverTrigger>
        <PopoverPortal>
          <PopoverContent avoidCollisions className="w-[var(--radix-popover-trigger-width)] p-0">
            <div>
              <div className="flex items-center p-2 pl-4">
                <Search size={15} />
                <input
                  className="w-full border-none px-2 py-1 outline-hidden placeholder:text-muted-foreground placeholder:text-sm text-sm focus-visible:outline-hidden disabled:cursor-not-allowed disabled:opacity-50"
                  value={search}
                  placeholder="Search OSHA locations..."
                  onChange={handleSearchChange}
                  disabled={disabled}
                />
              </div>
              <Separator />
              <div ref={scrollAreaRef} className="p-2 overflow-auto" style={{ height: 150 }}>
                <ul>
                  {Object.keys(options).length === 0 && !isLoading && (
                    <li className="p-2 text-sm text-muted-foreground text-center">No OSHA locations found</li>
                  )}
                  {Object.values(options).map((oshaLocation) => {
                    const isDisabled = disabled;
                    const isArchived = archivedLocations?.has(oshaLocation.id);
                    return (
                      <li
                        onClick={() => !isDisabled && !isArchived && onSelect(oshaLocation.id)}
                        className={cn(
                          'flex cursor-pointer items-center rounded-md p-2 text-sm hover:bg-secondary/80 gap-3',
                          disabled && 'cursor-not-allowed opacity-50 hover:bg-transparent',
                          isArchived && 'cursor-not-allowed',
                        )}
                        key={oshaLocation.id}
                      >
                        <Check
                          className={cn(
                            'size-4 flex-shrink-0',
                            selected === oshaLocation.id ? 'opacity-100' : 'opacity-0',
                          )}
                        />
                        <div className="flex items-center gap-2">
                          <span className={cn(isArchived && 'line-through')}>{oshaLocation.name}</span>
                          <div className="flex items-center gap-1">
                            {isArchived && <span className="text-muted-foreground">(Archived)</span>}
                            {isArchived && (
                              <Tooltip>
                                <TooltipTrigger>
                                  <Info className="size-4 flex-shrink-0 text-muted-foreground" />
                                </TooltipTrigger>
                                <TooltipContent>
                                  This location is archived for the current year.
                                  <br />
                                  To create a report at this location, restore the year in OSHA Summary. <br />
                                  <Link className="underline" href={ROUTES.OSHA_SUMMARY}>
                                    Go to OSHA Summary
                                  </Link>
                                </TooltipContent>
                              </Tooltip>
                            )}
                          </div>
                        </div>
                      </li>
                    );
                  })}
                  {isFetchingNextPage && (
                    <li className="flex items-center justify-center p-2">
                      <Loader2 className="animate-spin size-4" />
                      <span className="ml-2 text-sm text-muted-foreground">Loading more...</span>
                    </li>
                  )}
                </ul>
              </div>
              <Separator />
              <div
                onClick={() => !disabled && handleAddNewLocation()}
                className={cn(
                  'flex cursor-pointer items-center rounded-md p-2 text-sm hover:bg-secondary/80 gap-3 text-primary',
                  disabled && 'cursor-not-allowed opacity-50 hover:bg-transparent',
                )}
              >
                <Plus className="size-4 flex-shrink-0" />
                <span>Add New Location</span>
              </div>
            </div>
          </PopoverContent>
        </PopoverPortal>
      </Popover>

      <CreateOshaLocationModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSuccess={() => setShowCreateModal(false)}
      />
    </>
  );
};
