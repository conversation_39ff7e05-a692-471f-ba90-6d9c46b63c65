import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { AlertTriangle, Bot } from 'lucide-react';

type AiDisclaimerModalProps = {
  open: boolean;
  onAccept: () => void;
  onCancel: () => void;
};

export const AiDisclaimerModal = ({ open, onAccept, onCancel }: AiDisclaimerModalProps) => {
  return (
    <Dialog open={open} onOpenChange={(isOpen) => !isOpen && onCancel()}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader className="space-y-4">
          <div className="flex items-center gap-3">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-amber-100">
              <AlertTriangle className="h-6 w-6 text-amber-600" />
            </div>
            <div>
              <DialogTitle className="text-xl font-semibold text-gray-900">
                AI Content Disclaimer
              </DialogTitle>
              <p className="text-sm text-gray-500 mt-1">User Responsibility Notice</p>
            </div>
          </div>
          <DialogDescription className="sr-only">Disclaimer for AI-generated content responsibility</DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-6">
          {/* Purpose Section */}
          <div className="rounded-lg bg-blue-50 border border-blue-200 p-4">
            <div className="flex items-start gap-3">
              <Bot className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-blue-900 mb-2">AI Tool Purpose</h3>
                <p className="text-blue-800 text-sm leading-relaxed">
                  The content generated by this AI tool is provided for{' '}
                  <strong>drafting and informational purposes only</strong>. It is not a substitute for professional safety,
                  legal, or compliance advice.
                </p>
              </div>
            </div>
          </div>

          {/* Responsibility Section */}
          <div className="rounded-lg bg-orange-50 border border-orange-200 p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-orange-600 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-orange-900 mb-2">Your Responsibility</h3>
                <p className="text-orange-800 text-sm leading-relaxed">
                  <strong>You, as the author</strong>, retain full responsibility and liability for reviewing,
                  fact-checking, and ensuring the accuracy, completeness, and compliance of all information before
                  submission or use.
                </p>
              </div>
            </div>
          </div>

          {/* Liability Section */}
          <div className="rounded-lg bg-red-50 border border-red-200 p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-red-900 mb-2">Liability Limitation</h3>
                <p className="text-red-800 text-sm leading-relaxed">
                  By proceeding, you acknowledge that{' '}
                  <strong>UpKeep is not liable for any outcomes, claims, or damages</strong> resulting from reliance on
                  AI-generated content.
                </p>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="flex flex-col-reverse gap-3 sm:flex-row sm:justify-end pt-6 border-t border-gray-200">
          <Button variant="outline" onClick={onCancel} className="sm:w-auto w-full">
            Cancel
          </Button>
          <Button onClick={onAccept} className="bg-blue-600 hover:bg-blue-700 text-white sm:w-auto w-full">
            I Accept & Understand
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
