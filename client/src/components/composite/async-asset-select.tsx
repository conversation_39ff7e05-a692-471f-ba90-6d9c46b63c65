import { Popover, PopoverContent, PopoverPortal, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { useAppContext } from '@/contexts/app-context';
import { useInfiniteAssetsPublic } from '@/hooks/use-paginated-data';
import { cn } from '@/lib/utils';
import { Location } from '@shared/types/assets.types';
import { RouterOutputs } from '@shared/types/router.types';
import { useDebounce } from '@uidotdev/usehooks';
import { Check, ChevronsUpDown, Loader2, Search, X } from 'lucide-react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

type AssetOption = RouterOutputs['asset']['searchPublic']['result'][number];

type AsyncAssetSelectProps = {
  value?: AssetOption['id'] | null;
  onChange: (selected: AssetOption['id'] | null) => void;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
  locationId?: Location['id'];
  upkeepCompanyId?: string | null;
};

export const AsyncAssetSelect = ({
  onChange,
  className,
  value: selected = null,
  placeholder = 'Select asset...',
  disabled = false,
  locationId,
  upkeepCompanyId,
  ...props
}: AsyncAssetSelectProps) => {
  const { user } = useAppContext();
  const [open, setOpen] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState<AssetOption | null>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  const [search, setSearch] = useState('');
  const debouncedSearch = useDebounce(search, 300);

  const {
    data: assets,
    isLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = useInfiniteAssetsPublic({
    search: debouncedSearch,
    upkeepCompanyId: upkeepCompanyId ?? user?.upkeepCompanyId!,
    locationId,
    enabled: !!upkeepCompanyId || !!user?.upkeepCompanyId,
  });

  const options = useMemo(() => {
    return (
      assets?.reduce(
        (acc, asset) => {
          acc[asset.id] = asset;
          return acc;
        },
        {} as Record<string, RouterOutputs['asset']['searchPublic']['result'][number]>,
      ) || {}
    );
  }, [assets]);

  const handleUnselect = useCallback(() => {
    if (disabled) return;
    onChange(null);
    setSelectedAsset(null);
  }, [disabled, onChange]);

  useEffect(() => {
    if (selected && options[selected] && selectedAsset?.id !== selected) {
      setSelectedAsset(options[selected]);
    }

    if (!selected && selectedAsset) {
      setSelectedAsset(null);
    }
  }, [selected, options, selectedAsset]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (disabled) return;

      if (e.key === 'Escape') {
        setOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [disabled]);

  // Handle infinite scrolling
  useEffect(() => {
    if (!open || !hasNextPage) return;

    let cleanup: (() => void) | undefined;

    // Use a timeout to ensure the PopoverContent is fully rendered
    const timeoutId = window.setTimeout(() => {
      const scrollArea = scrollAreaRef.current;
      if (!scrollArea) return;

      let scrollTimeoutId: number;

      const handleScroll = () => {
        clearTimeout(scrollTimeoutId);
        scrollTimeoutId = window.setTimeout(() => {
          const { scrollTop, scrollHeight, clientHeight } = scrollArea;
          const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

          if (distanceFromBottom < 50 && !isFetchingNextPage) {
            fetchNextPage();
          }
        }, 100);
      };

      scrollArea.addEventListener('scroll', handleScroll, { passive: true });

      cleanup = () => {
        scrollArea.removeEventListener('scroll', handleScroll);
        clearTimeout(scrollTimeoutId);
      };
    }, 100);

    return () => {
      clearTimeout(timeoutId);
      cleanup?.();
    };
  }, [open, hasNextPage, fetchNextPage, isFetchingNextPage]);

  const handleSearchChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      if (disabled) return;
      setSearch(event.target.value);
    },
    [disabled],
  );

  const onSelect = useCallback(
    (assetId: AssetOption['id']) => {
      if (!onChange || disabled) return;

      onChange(assetId);
      setSelectedAsset(options[assetId]);
      setOpen(false);
    },
    [onChange, disabled, options],
  );

  const renderSelectedValue = () => {
    if (selectedAsset) {
      return (
        <div className="flex items-center gap-2">
          <span className="text-sm">{selectedAsset.name}</span>
        </div>
      );
    }

    return <span className="text-muted-foreground">{placeholder}</span>;
  };

  return (
    <Popover open={open} onOpenChange={setOpen} modal>
      <PopoverTrigger asChild className={className}>
        <div
          {...props}
          role="combobox"
          aria-expanded={open}
          aria-disabled={disabled}
          aria-haspopup="listbox"
          aria-label={selectedAsset ? `Selected asset: ${selectedAsset.name}` : placeholder}
          className={cn(
            'group flex h-9 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background transition-[color,box-shadow] placeholder:text-muted-foreground focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:bg-input/30',
            open && !disabled && 'border-ring ring-[3px] ring-ring/50',
            'aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40',
            disabled && 'cursor-not-allowed opacity-50',
            'cursor-pointer',
          )}
          onClick={() => !disabled && setOpen(!open)}
        >
          <div className="flex items-center gap-2 flex-1 min-w-0">{renderSelectedValue()}</div>

          <div className="flex items-center gap-2 flex-shrink-0">
            {selectedAsset && (
              <X
                size={16}
                className="text-muted-foreground"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleUnselect();
                }}
              />
            )}
            {isLoading && <Loader2 className="animate-spin size-4" />}
            <ChevronsUpDown className="size-4 shrink-0 opacity-50" />
          </div>
        </div>
      </PopoverTrigger>
      <PopoverPortal>
        <PopoverContent avoidCollisions className="w-[var(--radix-popover-trigger-width)] p-0">
          <div>
            <div className="flex items-center p-2 pl-4">
              <Search size={15} />
              <input
                className="w-full border-none px-2 py-1 outline-hidden placeholder:text-muted-foreground placeholder:text-sm text-sm focus-visible:outline-hidden disabled:cursor-not-allowed disabled:opacity-50"
                value={search}
                placeholder="Search assets..."
                onChange={handleSearchChange}
                disabled={disabled}
              />
            </div>
            <Separator />
            <div ref={scrollAreaRef} className="p-2 overflow-auto" style={{ height: 200 }}>
              <ul>
                {Object.keys(options).length === 0 && !isLoading && (
                  <li className="p-2 text-sm text-muted-foreground text-center">No assets found</li>
                )}
                {Object.values(options).map((asset) => (
                  <li
                    onClick={() => !disabled && onSelect(asset.id)}
                    className={cn(
                      'flex cursor-pointer items-center rounded-md p-2 text-sm hover:bg-secondary/80 gap-3',
                      disabled && 'cursor-not-allowed opacity-50 hover:bg-transparent',
                    )}
                    key={asset.id}
                  >
                    <Check
                      className={cn(
                        'size-4 flex-shrink-0',
                        selectedAsset?.id === asset.id ? 'opacity-100' : 'opacity-0',
                      )}
                    />
                    <span>{asset.name}</span>
                  </li>
                ))}
                {isFetchingNextPage && (
                  <li className="flex items-center justify-center p-2">
                    <Loader2 className="animate-spin size-4" />
                    <span className="ml-2 text-sm text-muted-foreground">Loading more...</span>
                  </li>
                )}
              </ul>
            </div>
          </div>
        </PopoverContent>
      </PopoverPortal>
    </Popover>
  );
};
