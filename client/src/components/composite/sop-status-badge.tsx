import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { approvalStatusEnum } from '@shared/schema';
import { SOP_STATUS_MAP } from '@shared/types/sop.types';
import { Archive, CheckCircleIcon, ClipboardCheck, FileText } from 'lucide-react';

export const SopStatusBadge = ({ status }: { status: (typeof approvalStatusEnum.enumValues)[number] }) => {
  const statusIconMap = {
    draft: <FileText className="h-3 w-3 mr-1" />,
    review: <ClipboardCheck className="h-3 w-3 mr-1" />,
    approved: <CheckCircleIcon className="h-3 w-3 mr-1" />,
    archived: <Archive className="h-3 w-3 mr-1" />,
  } as const;

  const statusColorMap = {
    draft: 'bg-gray-50 text-gray-700 border-gray-200',
    review: 'bg-blue-50 text-blue-700 border-blue-200',
    approved: 'bg-green-50 text-green-700 border-green-200',
    archived: 'bg-amber-50 text-amber-700 border-amber-200',
  } as const;

  const icon = statusIconMap[status];

  return (
    <Badge className={cn(statusColorMap[status])} variant="outline">
      {icon}
      {SOP_STATUS_MAP[status]}
    </Badge>
  );
};

