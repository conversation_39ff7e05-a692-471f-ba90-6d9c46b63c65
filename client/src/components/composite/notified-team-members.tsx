import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { UserPublic } from '@shared/types/users.types';

export const NotifiedTeamMembers = ({ users }: { users: UserPublic[] }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Notified Team Members</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-wrap items-center gap-2">
          {users.map((user) => (
            <Badge variant="outline" key={user.id}>
              {user.fullName}
            </Badge>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
