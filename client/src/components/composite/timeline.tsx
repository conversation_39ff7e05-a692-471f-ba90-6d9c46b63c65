import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { trpc } from '@/providers/trpc';
import { auditTrailActionEnum, entityTypeEnum } from '@shared/schema';
import { RouterOutputs } from '@shared/types/router.types';
import { format } from 'date-fns';
import {
  Archive,
  ArchiveRestore,
  Calendar,
  CheckCircle2,
  Clock,
  Edit3,
  FilePlus,
  MessageCircle,
  RefreshCw,
  Trash2,
  Upload,
  User,
  XCircle,
  type LucideIcon,
} from 'lucide-react';
import { useMemo } from 'react';

const ActionIconMap = {
  [auditTrailActionEnum.enumValues[0]]: FilePlus,
  [auditTrailActionEnum.enumValues[1]]: Edit3,
  [auditTrailActionEnum.enumValues[2]]: Trash2,
  [auditTrailActionEnum.enumValues[3]]: MessageCircle,
  [auditTrailActionEnum.enumValues[4]]: Upload,
  [auditTrailActionEnum.enumValues[5]]: CheckCircle2,
  [auditTrailActionEnum.enumValues[6]]: Clock,
  [auditTrailActionEnum.enumValues[7]]: RefreshCw,
  [auditTrailActionEnum.enumValues[8]]: Archive,
  [auditTrailActionEnum.enumValues[9]]: ArchiveRestore,
  [auditTrailActionEnum.enumValues[10]]: CheckCircle2,
  [auditTrailActionEnum.enumValues[11]]: XCircle,
  [auditTrailActionEnum.enumValues[12]]: Clock,
};

const ActionColorMap = {
  [auditTrailActionEnum.enumValues[0]]: 'bg-green-100 text-green-700', // Created - Pale Green
  [auditTrailActionEnum.enumValues[1]]: 'bg-blue-100 text-blue-700', // Updated - Pale Blue
  [auditTrailActionEnum.enumValues[2]]: 'bg-red-100 text-red-700', // Deleted - Pale Red
  [auditTrailActionEnum.enumValues[3]]: 'bg-purple-100 text-purple-700', // Commented - Pale Purple
  [auditTrailActionEnum.enumValues[4]]: 'bg-indigo-100 text-indigo-700', // Uploaded - Pale Indigo
  [auditTrailActionEnum.enumValues[5]]: 'bg-emerald-100 text-emerald-700', // Completed/Closed - Pale Emerald
  [auditTrailActionEnum.enumValues[6]]: 'bg-yellow-100 text-yellow-700', // In Review - Pale Yellow
  [auditTrailActionEnum.enumValues[7]]: 'bg-orange-100 text-orange-700', // Reopened - Pale Orange
  [auditTrailActionEnum.enumValues[8]]: 'bg-gray-100 text-gray-700', // Archived - Pale Gray
  [auditTrailActionEnum.enumValues[9]]: 'bg-cyan-100 text-cyan-700', // Unarchived - Pale Cyan
  [auditTrailActionEnum.enumValues[10]]: 'bg-green-100 text-green-700', // Approved - Pale Green
  [auditTrailActionEnum.enumValues[11]]: 'bg-red-100 text-red-700', // Rejected - Pale Yellow
  [auditTrailActionEnum.enumValues[12]]: 'bg-gray-100 text-gray-700', // Drafted - Pale Gray
};

const StatusTimeline = {
  [auditTrailActionEnum.enumValues[0]]: 'Opened',
  [auditTrailActionEnum.enumValues[6]]: 'In Review',
  [auditTrailActionEnum.enumValues[5]]: 'Closed',
  [auditTrailActionEnum.enumValues[7]]: 'Reopened',
  [auditTrailActionEnum.enumValues[8]]: 'Archived',
  [auditTrailActionEnum.enumValues[9]]: 'Unarchived',
  [auditTrailActionEnum.enumValues[10]]: 'Approved',
  [auditTrailActionEnum.enumValues[11]]: 'Rejected',
  [auditTrailActionEnum.enumValues[12]]: 'Drafted',
} as const;

const AuditTrailTimeline = {
  [auditTrailActionEnum.enumValues[0]]: 'Created',
  [auditTrailActionEnum.enumValues[1]]: 'Updated',
  [auditTrailActionEnum.enumValues[2]]: 'Deleted',
  [auditTrailActionEnum.enumValues[3]]: 'Commented',
  [auditTrailActionEnum.enumValues[10]]: 'Approved',
  [auditTrailActionEnum.enumValues[11]]: 'Rejected',
  [auditTrailActionEnum.enumValues[12]]: 'Drafted',
} as const;

const TimelineItem = ({
  event,
  ActionIcon,
}: {
  event: RouterOutputs['auditTrail']['get'][number];
  ActionIcon: LucideIcon;
}) => {
  const colorClasses = ActionColorMap[event.action] || 'bg-gray-100 text-gray-700';

  return (
    <div className="mb-4">
      <div className="flex items-center">
        <div className="flex flex-col items-center mr-2">
          <div className={`rounded-full w-7 h-7 flex items-center justify-center ${colorClasses}`}>
            {ActionIcon && <ActionIcon className="h-4 w-4" />}
          </div>
        </div>
        <div className="space-y-1">
          <p className="font-medium text-sm">
            {StatusTimeline[event.action as keyof typeof StatusTimeline] ||
              AuditTrailTimeline[event.action as keyof typeof AuditTrailTimeline]}
          </p>
          <div className="flex items-center text-xs text-muted-foreground gap-1">
            <Calendar className="h-4 w-4" />
            {format(event.timestamp, 'MMM d, yyyy h:mm a')}
          </div>
          {event.user?.fullName && (
            <div className="flex items-center mt-1 text-xs text-muted-foreground">
              <User className="h-4 w-4 mr-1" />
              {event.user?.fullName}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export const Timeline = ({
  entityId,
  entityType,
}: {
  entityId: string;
  entityType: (typeof entityTypeEnum.enumValues)[number];
}) => {
  const { data: trail } = trpc.auditTrail.get.useQuery({
    entityId: entityId,
    entityType: entityType,
  });

  const statusTimeline = useMemo(
    () => trail?.filter((event) => StatusTimeline[event.action as keyof typeof StatusTimeline]),
    [trail],
  );

  const auditTrailTimeline = useMemo(
    () => trail?.filter((event) => AuditTrailTimeline[event.action as keyof typeof AuditTrailTimeline]),
    [trail],
  );

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Status Timeline</CardTitle>
        </CardHeader>
        <CardContent>
          <div>
            {statusTimeline?.map((event, i) => {
              const ActionIcon = ActionIconMap[event.action];
              return <TimelineItem key={i} event={event} ActionIcon={ActionIcon} />;
            })}
          </div>
        </CardContent>
      </Card>

      {/* Audit Trail (Collapsible) */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Audit Trail</CardTitle>
        </CardHeader>
        <CardContent>
          <Accordion type="single" collapsible>
            <AccordionItem value="audit-trail">
              <AccordionTrigger className="pt-0 pb-2">View Internal Log</AccordionTrigger>
              <AccordionContent>
                {auditTrailTimeline?.map((event, i) => {
                  const ActionIcon = ActionIconMap[event.action];
                  return <TimelineItem key={i} event={event} ActionIcon={ActionIcon} />;
                })}
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </CardContent>
      </Card>
    </>
  );
};
