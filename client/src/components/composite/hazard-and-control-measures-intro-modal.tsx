import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { AlertTriangle, Sprout, ExternalLink } from 'lucide-react';
import { useLocation } from 'wouter';

export const HazardAndControlMeasuresIntroModal = ({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) => {
  const [_, navigate] = useLocation();

  const handleGoToHazards = () => {
    navigate(ROUTES.HAZARDS_LIST);
    onClose();
  };

  const handleGoToControlMeasures = () => {
    navigate(ROUTES.CONTROL_MEASURES_LIST);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center gap-2 sm:gap-3 mb-2">
            <div className="p-1.5 sm:p-2 bg-amber-100 rounded-lg flex-shrink-0">
              <AlertTriangle className="h-5 w-5 sm:h-6 sm:w-6 text-amber-600" />
            </div>
            <DialogTitle className="text-lg sm:text-xl">Improve Your JHA Setup</DialogTitle>
          </div>
          <DialogDescription className="text-sm sm:text-base leading-relaxed">
            To get the most accurate Job Hazard Analyses (JHAs), we recommend starting by adding your common hazards and control measures to your library.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 sm:space-y-6">
          {/* Why section */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 sm:p-4">
            <h3 className="font-semibold text-blue-900 mb-2 sm:mb-3 flex items-center gap-2 text-sm sm:text-base">
              <Sprout className="h-4 w-4 flex-shrink-0" />
              Why start here?
            </h3>
            <div className="text-blue-800 space-y-1 sm:space-y-2 text-xs sm:text-sm">
              <p>• Makes sure the system uses your hazards and controls first</p>
              <p>• Prevents duplicates and keeps everything consistent across the team</p>
              <p>• Uses clear, industry-specific language your workers already know</p>
              <p>• Builds a reusable library you can rely on for future JHAs</p>
            </div>
          </div>

          {/* Action items */}
          <div className="space-y-3 sm:space-y-4">
            <h3 className="font-semibold text-gray-900 text-sm sm:text-base">Quick Setup</h3>

            <div className="grid gap-3 sm:grid-cols-2">
              <div className="border border-gray-200 rounded-lg p-3 sm:p-4 hover:border-gray-300 transition-colors">
                <h4 className="font-medium text-gray-900 mb-2 text-sm sm:text-base">Add Hazards</h4>
                <p className="text-xs sm:text-sm text-gray-600 mb-3 leading-relaxed">
                  Enter the common hazards your team faces (e.g., slips, electrical shock, machine guarding).
                </p>
                <Button variant="outline" size="sm" onClick={handleGoToHazards} className="w-full text-xs sm:text-sm">
                  <ExternalLink className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                  Go to Hazards
                </Button>
              </div>

              <div className="border border-gray-200 rounded-lg p-3 sm:p-4 hover:border-gray-300 transition-colors">
                <h4 className="font-medium text-gray-900 mb-2 text-sm sm:text-base">Add Control Measures</h4>
                <p className="text-xs sm:text-sm text-gray-600 mb-3 leading-relaxed">
                  Enter the standard safety steps you use to control those hazards (e.g., PPE, lockout/tagout, signage).
                </p>
                <Button variant="outline" size="sm" onClick={handleGoToControlMeasures} className="w-full text-xs sm:text-sm">
                  <ExternalLink className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                  Go to Control Measures
                </Button>
              </div>
            </div>
          </div>

          {/* Note */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 sm:p-4">
            <p className="text-xs sm:text-sm text-gray-700 leading-relaxed">
              <strong>Note:</strong> You can skip this and create JHAs right away, but adding your hazards and controls first will make them clearer, faster, and more reliable every time.
            </p>
          </div>
        </div>

        <DialogFooter className="gap-2 sm:gap-3 flex-col sm:flex-row">
          <Button variant="outline" onClick={onClose} className="text-xs sm:text-sm w-full sm:w-auto">
            Skip for Now
          </Button>
          <Button onClick={handleGoToHazards} className="text-xs sm:text-sm w-full sm:w-auto">
            <Sprout className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
            Start with Hazards
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
