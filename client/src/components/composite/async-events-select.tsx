import { Badge } from '@/components/ui/badge';
import { Popover, PopoverContent, PopoverPortal, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { useInfiniteMinimalEvents } from '@/hooks/use-paginated-data';
import { cn } from '@/lib/utils';
import { RouterOutputs } from '@shared/types/router.types';
import { STATUS_MAP } from '@shared/types/schema.types';
import { useDebounce } from '@uidotdev/usehooks';
import { Check, ChevronsUpDown, Loader2, Search, X } from 'lucide-react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

type EventOption = RouterOutputs['event']['minimalList']['result'][number];

type AsyncEventSelectProps = {
  value?: EventOption['id'] | null;
  onChange: (selected: EventOption['id'] | null) => void;
  className?: string;
  placeholder?: string;
  mustIncludeObjectIds?: string[];
  oshaReportable?: boolean;
};

export const AsyncEventsSelect = ({
  onChange,
  className,
  value: selected = null,
  placeholder = 'Select event...',
  mustIncludeObjectIds = [],
  oshaReportable,
  ...props
}: AsyncEventSelectProps) => {
  const [open, setOpen] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<EventOption | null>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const [search, setSearch] = useState('');

  const debouncedSearch = useDebounce(search, 300);

  const {
    data: events,
    isLoading,
    isFetchingNextPage,
    fetchNextPage,
    hasNextPage,
  } = useInfiniteMinimalEvents({
    filters: {
      search: debouncedSearch,
      includeArchived: false,
      status: [],
      type: [],
      severity: [],
      oshaReportable,
      locationIds: [],
      mustIncludeObjectIds,
    },
    enabled: true,
  });

  const options = useMemo(() => {
    return events.reduce(
      (acc, event) => {
        acc[event.id] = event;
        return acc;
      },
      {} as Record<EventOption['id'], EventOption>,
    );
  }, [events]);

  useEffect(() => {
    if (selected && options[selected] && selectedEvent?.id !== selected) {
      setSelectedEvent(options[selected]);
    }

    if (!selected && selectedEvent) {
      setSelectedEvent(null);
    }
  }, [selected, options]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  // Handle infinite scrolling
  useEffect(() => {
    if (!open || !hasNextPage) return;

    let cleanup: (() => void) | undefined;

    // Use a timeout to ensure the PopoverContent is fully rendered
    const timeoutId = window.setTimeout(() => {
      const scrollArea = scrollAreaRef.current;
      if (!scrollArea) return;

      let scrollTimeoutId: number;

      const handleScroll = () => {
        clearTimeout(scrollTimeoutId);
        scrollTimeoutId = window.setTimeout(() => {
          const { scrollTop, scrollHeight, clientHeight } = scrollArea;
          const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

          if (distanceFromBottom < 50 && !isFetchingNextPage) {
            fetchNextPage();
          }
        }, 100);
      };

      scrollArea.addEventListener('scroll', handleScroll, { passive: true });

      cleanup = () => {
        scrollArea.removeEventListener('scroll', handleScroll);
        clearTimeout(scrollTimeoutId);
      };
    }, 100);

    return () => {
      clearTimeout(timeoutId);
      cleanup?.();
    };
  }, [open, hasNextPage, fetchNextPage, isFetchingNextPage]);

  const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(event.target.value);
  }, []);

  const onSelect = useCallback(
    (eventId: EventOption['id'] | null) => {
      setSelectedEvent(eventId ? options[eventId] : null);
      setOpen(false);
      onChange(eventId);
    },
    [options, onChange],
  );

  const formatDate = useCallback((date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric',
    }).format(date);
  }, []);

  const renderSelectedValue = () => {
    if (selectedEvent) {
      const event = selectedEvent;

      return (
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">
              {event.slug} — {event.title}
            </span>
            <Badge variant="secondary" className="text-xs">
              {STATUS_MAP[event.status]}
            </Badge>
            <span className="text-xs text-muted-foreground">({formatDate(event.reportedAt)})</span>
          </div>
          <div
            className="flex items-center gap-2 cursor-pointer"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onSelect(null);
            }}
          >
            <X className="size-4 text-muted-foreground mr-2" />
          </div>
        </div>
      );
    }

    return <span className="text-muted-foreground">{placeholder}</span>;
  };

  return (
    <Popover open={open} onOpenChange={setOpen} modal>
      <PopoverTrigger asChild className={className}>
        <div
          {...props}
          role="combobox"
          aria-expanded={open}
          aria-haspopup="listbox"
          aria-label={selectedEvent ? `Selected event: ${selectedEvent.slug} — ${selectedEvent.title}` : placeholder}
          className={cn(
            'group flex h-9 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background transition-[color,box-shadow] placeholder:text-muted-foreground focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:bg-input/30',
            open && 'border-ring ring-[3px] ring-ring/50',
            'aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40',
            'h-9 cursor-pointer',
          )}
          onClick={() => setOpen(!open)}
        >
          <div className="flex items-center gap-2 flex-1 min-w-0">{renderSelectedValue()}</div>

          <div className="flex items-center gap-2 flex-shrink-0">
            {isLoading && <Loader2 className="animate-spin size-4" />}
            <ChevronsUpDown className="size-4 shrink-0 opacity-50" />
          </div>
        </div>
      </PopoverTrigger>
      <PopoverPortal>
        <PopoverContent avoidCollisions className="w-[var(--radix-popover-trigger-width)] p-0">
          <div>
            <div className="flex items-center p-2 pl-4">
              <Search size={15} />
              <input
                className="w-full border-none px-2 py-1 outline-hidden placeholder:text-muted-foreground placeholder:text-sm text-sm focus-visible:outline-hidden disabled:cursor-not-allowed disabled:opacity-50"
                value={search}
                placeholder="Search events..."
                onChange={handleSearchChange}
              />
            </div>
            <Separator />
            <div ref={scrollAreaRef} className="p-2 overflow-auto" style={{ height: 200 }}>
              <ul>
                {Object.keys(options).length === 0 && !isLoading && (
                  <li className="p-2 text-sm text-muted-foreground text-center">No events found</li>
                )}
                {Object.values(options).map((event) => (
                  <li
                    onClick={() => onSelect(event.id)}
                    className="flex cursor-pointer items-center rounded-md p-2 text-sm hover:bg-secondary/80 gap-3"
                    key={event.id}
                  >
                    <Check
                      className={cn(
                        'size-4 flex-shrink-0',
                        selectedEvent?.id === event.id ? 'opacity-100' : 'opacity-0',
                      )}
                    />
                    <div className="flex items-center gap-2 flex-1 min-w-0">
                      <span className="font-medium">
                        {event.slug} — {event.title}
                      </span>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="text-xs">
                          {STATUS_MAP[event.status]}
                        </Badge>
                        <span className="text-xs text-muted-foreground">({formatDate(event.reportedAt)})</span>
                      </div>
                    </div>
                  </li>
                ))}
                {isFetchingNextPage && (
                  <li className="flex items-center justify-center p-2">
                    <Loader2 className="animate-spin size-4" />
                    <span className="ml-2 text-sm text-muted-foreground">Loading more...</span>
                  </li>
                )}
              </ul>
            </div>
          </div>
        </PopoverContent>
      </PopoverPortal>
    </Popover>
  );
};
