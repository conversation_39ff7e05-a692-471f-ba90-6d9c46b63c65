import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Camera, Sparkles, Upload, Video, Zap } from 'lucide-react';
import { AnimatePresence, motion } from 'motion/react';
import { useCallback, useRef, useState } from 'react';
import { toast } from 'sonner';

type VideoInputProps = {
  onUpload: (videoBase64: string, filename: string) => Promise<void>;
  isParsing?: boolean;
};

// TODO: Define supported video file types when video analysis is implemented
const SUPPORTED_FILE_TYPES = ['video/mp4', 'video/webm', 'video/avi', 'video/mov'];

const MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB (placeholder - adjust based on requirements)

export const VideoInput = ({ onUpload, isParsing }: VideoInputProps) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): string | null => {
    if (!SUPPORTED_FILE_TYPES.includes(file.type)) {
      return 'Unsupported file type. Please upload video files only.';
    }

    if (file.size > MAX_FILE_SIZE) {
      return 'File size too large. Maximum file size is 100MB.';
    }

    return null;
  };

  const handleFileSelect = useCallback(
    async (file: File) => {
      const validationError = validateFile(file);
      if (validationError) {
        toast.error('Invalid File', {
          description: validationError,
        });
        return;
      }

      setUploadedFile(file);

      try {
        // Convert file to base64
        const buffer = await file.arrayBuffer();
        const fileBase64 = btoa(new Uint8Array(buffer).reduce((data, byte) => data + String.fromCharCode(byte), ''));

        // Send to video analysis API (placeholder implementation)
        await onUpload(fileBase64, file.name);
      } catch (error) {
        console.error('Error processing video file:', error);
        toast.error('Video Processing Error', {
          description: 'Failed to process the video file. Please try again.',
        });
        setUploadedFile(null);
      }
    },
    [onUpload],
  );

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragOver(false);

      const files = Array.from(e.dataTransfer.files);
      if (files.length > 0) {
        handleFileSelect(files[0]);
      }
    },
    [handleFileSelect],
  );

  const handleFileInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = e.target.files;
      if (files && files.length > 0) {
        handleFileSelect(files[0]);
      }
    },
    [handleFileSelect],
  );

  const handleBrowseClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (fileType: string): string => {
    if (fileType.includes('mp4')) return '🎬';
    if (fileType.includes('webm')) return '📹';
    if (fileType.includes('avi')) return '🎞️';
    if (fileType.includes('mov')) return '🎥';
    return '📹';
  };

  return (
    <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.3 }}>
      {/* Video Upload Area */}
      <div
        className={cn(
          'border-2 border-dashed rounded-lg p-6 text-center transition-all duration-200 cursor-pointer relative',
          isDragOver
            ? 'border-purple-400 bg-purple-50/50'
            : 'border-purple-200 hover:border-purple-300 hover:bg-purple-50/30',
          isParsing && 'pointer-events-none opacity-60',
        )}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={!isParsing ? handleBrowseClick : undefined}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept="video/*"
          onChange={handleFileInputChange}
          className="hidden"
          disabled={isParsing}
        />

        <AnimatePresence mode="wait">
          {isParsing ? (
            <motion.div
              key="parsing"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="space-y-3"
            >
              <div className="flex justify-center">
                <div className="relative">
                  <div className="absolute inset-0 animate-ping opacity-20 bg-purple-400 rounded-full" />
                  <div
                    className="absolute inset-0 animate-ping opacity-40 bg-purple-500 rounded-full"
                    style={{ animationDelay: '0.5s' }}
                  />
                  <div className="absolute inset-0 animate-pulse opacity-60 bg-purple-300 rounded-full" />
                  <Sparkles
                    className="h-10 w-10 text-purple-600 relative z-10 animate-spin"
                    style={{ animationDuration: '3s' }}
                  />
                </div>
              </div>
              <div className="space-y-1">
                <h3 className="text-base font-semibold text-gray-900">AI Analyzing Video</h3>
                <p className="text-sm text-purple-700">
                  Our AI is processing your video and extracting procedures. This may take a few minutes...
                </p>
                <div className="flex items-center justify-center gap-1 text-xs text-purple-600">
                  <Zap className="h-3 w-3 animate-pulse" />
                  <span className="animate-pulse">Analyzing video content</span>
                </div>
              </div>
            </motion.div>
          ) : uploadedFile ? (
            <motion.div
              key="uploaded"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="space-y-3"
            >
              <div className="flex justify-center">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                  <Video className="w-6 h-6 text-purple-600" />
                </div>
              </div>
              <div className="space-y-2">
                <h3 className="text-base font-medium text-gray-900">Video Ready for Processing</h3>
                <div className="bg-purple-50 border border-purple-200 rounded-md p-2 text-left">
                  <div className="flex items-center space-x-2">
                    <span className="text-base">{getFileIcon(uploadedFile.type)}</span>
                    <div>
                      <p className="text-sm font-medium text-gray-900">{uploadedFile.name}</p>
                      <p className="text-xs text-gray-500">{formatFileSize(uploadedFile.size)}</p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ) : (
            <motion.div
              key="upload"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="space-y-3"
            >
              <div className="flex justify-center space-x-4">
                {/* Upload existing video option */}
                <div
                  className={cn(
                    'w-16 h-16 rounded-full flex items-center justify-center transition-colors',
                    isDragOver ? 'bg-purple-200' : 'bg-purple-100',
                  )}
                >
                  <Upload
                    className={cn('w-6 h-6 transition-colors', isDragOver ? 'text-purple-700' : 'text-purple-600')}
                  />
                </div>

                {/* TODO: Add video recording option when implemented */}
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center opacity-50">
                  <Camera className="w-6 h-6 text-gray-400" />
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-base font-medium text-gray-900">Upload video or record demonstration</h3>
                <p className="text-sm text-gray-600">
                  Our AI will analyze the video and extract procedures. Processing may take several minutes.
                </p>
                <div className="bg-amber-50 border border-amber-200 rounded-md p-2 text-xs text-amber-800">
                  ⚠️ <strong>Coming Soon:</strong> Video analysis is currently in development
                </div>
              </div>

              <Button type="button" variant="outline" className="border-purple-300 text-purple-700 hover:bg-purple-50">
                <Upload className="w-4 h-4 mr-2" />
                Upload Video{' '}
                <span className="text-xs text-gray-400 ml-2">
                  <strong>MP4, WebM, AVI</strong> • Max 100MB
                </span>
              </Button>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  );
};
