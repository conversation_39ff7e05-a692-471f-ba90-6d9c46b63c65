import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useAnalytics } from '@/hooks/use-analytics';
import { trpc } from '@/providers/trpc';
import { ENTITY_TYPE_MAP } from '@shared/types/schema.types';
import { toast } from 'sonner';

export const ArchiveConfirmationDialog = ({
  archived,
  showDialog,
  setShowDialog,
  entityId,
  entityType,
}: {
  archived: boolean;
  showDialog: boolean;
  setShowDialog: (show: boolean) => void;
  entityId: string;
  entityType:
    | 'event'
    | 'capa'
    | 'accessPoint'
    | 'oshaReport'
    | 'oshaSummary'
    | 'oshaLocation'
    | 'oshaAgencyReport'
    | 'jha'
    | 'sop'
    | 'hazard'
    | 'controlMeasure';
}) => {
  const utils = trpc.useUtils();

  const entityTypeToEnumMap: Record<typeof entityType, keyof typeof ENTITY_TYPE_MAP> = {
    event: 'event',
    capa: 'capa',
    accessPoint: 'access_point',
    oshaReport: 'osha_report',
    oshaSummary: 'osha_company_information',
    oshaLocation: 'global_location',
    oshaAgencyReport: 'osha_agency_report',
    jha: 'jha',
    sop: 'sop',
    hazard: 'hazard',
    controlMeasure: 'control_measure',
  };

  const configMap = {
    event: {
      label: ENTITY_TYPE_MAP[entityTypeToEnumMap.event],
      payload: {
        archivedAt: archived ? null : new Date(),
      },
      message: {
        archive:
          'This will remove the event from the active list while preserving its current status. Archived events can be viewed by selecting the "Include Archived" filter in the event log.',
        restore:
          'This will restore the event to the active list with its previous status. Unarchived events will appear in the default event log view.',
      },
    },
    capa: {
      label: ENTITY_TYPE_MAP[entityTypeToEnumMap.capa],
      payload: {
        archivedAt: archived ? null : new Date(),
      },
      message: {
        archive:
          'This will remove the CAPA from the active list while preserving its current status. Archived CAPAs can be viewed by selecting the "Include Archived" filter in the CAPA log.',
        restore:
          'This will restore the CAPA to the active list with its previous status. Unarchived CAPAs will appear in the default CAPA log view.',
      },
    },
    accessPoint: {
      label: ENTITY_TYPE_MAP[entityTypeToEnumMap.accessPoint],
      payload: {
        archivedAt: archived ? null : new Date(),
      },
      message: {
        archive:
          'This will remove the access point from the active list while preserving its current status. Archived access points can be viewed by selecting the "Include Archived" filter in the access point log.',
        restore:
          'This will restore the access point to the active list with its previous status. Unarchived access points will appear in the default access point log view.',
      },
    },
    oshaReport: {
      label: ENTITY_TYPE_MAP[entityTypeToEnumMap.oshaReport],
      payload: {
        archivedAt: archived ? null : new Date(),
      },
      message: {
        archive:
          'This will remove the OSHA report from the active list while preserving its current status. Archived OSHA reports can be viewed by selecting the "Include Archived" filter in the OSHA report log.',
        restore:
          'This will restore the OSHA report to the active list with its previous status. Unarchived OSHA reports will appear in the default OSHA report log view.',
      },
    },
    oshaAgencyReport: {
      label: ENTITY_TYPE_MAP[entityTypeToEnumMap.oshaAgencyReport],
      payload: {
        archivedAt: archived ? null : new Date(),
      },
      message: {
        archive:
          'This will remove the agency report from the active list while preserving its current status. Archived agency reports can be viewed by selecting the "Include Archived" filter in the agency report log.',
        restore:
          'This will restore the agency report to the active list with its previous status. Unarchived agency reports will appear in the default agency report log view.',
      },
    },
    oshaSummary: {
      label: 'OSHA Summary',
      payload: {
        archivedAt: archived ? null : new Date(),
      },
      message: {
        archive:
          'This will lock all logs for the selected year, making them read-only. You can still view and export data. You can restore the year if needed.',
        restore:
          'This will unlock all logs for the selected year, making them editable again. You can still view and export data.',
      },
    },
    oshaLocation: {
      label: ENTITY_TYPE_MAP[entityTypeToEnumMap.oshaLocation],
      payload: {
        archivedAt: archived ? null : new Date(),
      },
      message: {
        archive: 'This will archive the OSHA location. It can be restored later if needed.',
        restore: 'This will restore the OSHA location to the active list.',
      },
    },
    jha: {
      label: ENTITY_TYPE_MAP[entityTypeToEnumMap.jha],
      payload: {},
      message: {
        archive: 'This will archive the JHA. It can be restored later if needed.',
        restore: 'This will restore the JHA to the active list.',
      },
    },
    sop: {
      label: ENTITY_TYPE_MAP[entityTypeToEnumMap.sop],
      payload: {},
      message: {
        archive:
          'Are you sure you want to archive this SOP? It will no longer be active but will be retained for historical records. You can restore it later.',
        restore: 'This will restore the SOP to the active list.',
      },
    },
    hazard: {
      label: ENTITY_TYPE_MAP[entityTypeToEnumMap.hazard],
      payload: {},
      message: {
        archive: 'This will archive the hazard. It can be restored later if needed.',
        restore: 'This will restore the hazard to the active list.',
      },
    },
    controlMeasure: {
      label: ENTITY_TYPE_MAP[entityTypeToEnumMap.controlMeasure],
      payload: {},
      message: {
        archive: 'This will archive the control measure. It can be restored later if needed.',
        restore: 'This will restore the control measure to the active list.',
      },
    },
  };

  const analytics = useAnalytics();
  const trackArchive = (action: boolean) => {
    let eventKey;
    switch (entityType) {
      case 'capa':
        eventKey = ANALYTICS_EVENTS.CAPA.ARCHIVED;
        break;
      case 'event':
        eventKey = ANALYTICS_EVENTS.EVENT.ARCHIVED;
        break;
      case 'jha':
        eventKey = ANALYTICS_EVENTS.JHA.ARCHIVED;
        break;
      case 'sop':
        eventKey = ANALYTICS_EVENTS.SOP.ARCHIVED;
        break;
      default:
        eventKey = ANALYTICS_EVENTS.ACCESS_POINT.ARCHIVED;
        break;
    }

    analytics.track(eventKey, {
      [`${entityType === 'accessPoint' ? 'access_point' : entityType}_id`]: entityId,
      action: action ? 'unarchived' : 'archived',
    });
  };

  const mutationMap = {
    event: trpc.event.toggleArchive.useMutation({
      onSuccess: () => {
        utils.event.list.invalidate();
        utils.event.getById.invalidate({ id: entityId });
        utils.auditTrail.get.invalidate({ entityId, entityType: 'event' });

        trackArchive(archived);
        toast.success(
          archived ? `${configMap[entityType].label} Unarchived` : `${configMap[entityType].label} Archived`,
          {
            description: archived
              ? `${configMap[entityType].label} has been restored to its previous status.`
              : `${configMap[entityType].label} has been archived.`,
          },
        );
      },
      onError: () => {
        toast.error('Action Failed', {
          description: `Could not ${archived ? 'unarchive' : 'archive'} the ${configMap[entityType].label}. Please try again.`,
        });
      },
    }),
    capa: trpc.capa.toggleArchive.useMutation({
      onSuccess: () => {
        utils.capa.list.invalidate();
        utils.capa.getById.invalidate({ id: entityId });
        utils.auditTrail.get.invalidate({ entityId, entityType: 'capa' });

        trackArchive(archived);
        toast.success(
          archived ? `${configMap[entityType].label} Unarchived` : `${configMap[entityType].label} Archived`,
          {
            description: archived
              ? `${configMap[entityType].label} has been restored to its previous status.`
              : `${configMap[entityType].label} has been archived.`,
          },
        );
      },
      onError: () => {
        toast.error('Action Failed', {
          description: `Could not ${archived ? 'unarchive' : 'archive'} the ${configMap[entityType].label}. Please try again.`,
        });
      },
    }),
    accessPoint: trpc.accessPoint.toggleArchive.useMutation({
      onSuccess: () => {
        utils.accessPoint.list.invalidate();
        utils.accessPoint.getById.invalidate({ id: entityId });
        trackArchive(archived);
        toast.success(
          archived ? `${configMap[entityType].label} Unarchived` : `${configMap[entityType].label} Archived`,
          {
            description: archived
              ? `${configMap[entityType].label} has been restored to its previous status.`
              : `${configMap[entityType].label} has been archived.`,
          },
        );
      },
      onError: () => {
        toast.error('Action Failed', {
          description: `Could not ${archived ? 'unarchive' : 'archive'} the ${configMap[entityType].label}. Please try again.`,
        });
      },
    }),
    oshaReport: trpc.oshaReport.toggleArchive.useMutation({
      onSuccess: () => {
        utils.oshaReport.list.invalidate();
        utils.oshaReport.getById.invalidate({ id: entityId });
        utils.oshaAuditTrail.get.invalidate({ id: entityId, entityType: 'osha_report' });
        trackArchive(archived);
        toast.success(
          archived ? `${configMap[entityType].label} Unarchived` : `${configMap[entityType].label} Archived`,
          {
            description: archived
              ? `${configMap[entityType].label} has been restored to its previous status.`
              : `${configMap[entityType].label} has been archived.`,
          },
        );
      },
      onError: () => {
        toast.error('Action Failed', {
          description: `Could not ${archived ? 'unarchive' : 'archive'} the ${configMap[entityType].label}. Please try again.`,
        });
      },
    }),
    oshaAgencyReport: trpc.oshaAgencyReport.toggleArchive.useMutation({
      onSuccess: () => {
        utils.oshaAgencyReport.list.invalidate();
        utils.oshaAgencyReport.getById.invalidate({ id: entityId });
        utils.oshaAuditTrail.get.invalidate({ id: entityId, entityType: 'osha_agency_report' });
        toast.success(
          archived ? `${configMap[entityType].label} Unarchived` : `${configMap[entityType].label} Archived`,
          {
            description: archived
              ? `${configMap[entityType].label} has been restored to its previous status.`
              : `${configMap[entityType].label} has been archived.`,
          },
        );
      },
      onError: () => {
        toast.error('Action Failed', {
          description: `Could not ${archived ? 'unarchive' : 'archive'} the ${configMap[entityType].label}. Please try again.`,
        });
      },
    }),
    oshaSummary: trpc.oshaSummary.toggleArchiveOshaSummary.useMutation({
      onSuccess: () => {
        utils.oshaSummary.getEstablishmentInformation.invalidate();
        toast.success(
          archived ? `${configMap[entityType].label} Unarchived` : `${configMap[entityType].label} Archived`,
          {
            description: archived
              ? `${configMap[entityType].label} has been restored to its previous status.`
              : `${configMap[entityType].label} has been archived.`,
          },
        );
      },
    }),
    oshaLocation: trpc.oshaLocation.toggleArchive.useMutation({
      onSuccess: () => {
        utils.oshaLocation.list.invalidate();
        toast.success(
          archived ? `${configMap[entityType].label} Unarchived` : `${configMap[entityType].label} Archived`,
          {
            description: archived
              ? `${configMap[entityType].label} has been restored to its previous status.`
              : `${configMap[entityType].label} has been archived.`,
          },
        );
      },
      onError: () => {
        toast.error('Action Failed', {
          description: `Could not ${archived ? 'unarchive' : 'archive'} the ${configMap[entityType].label}. Please try again.`,
        });
      },
    }),
    jha: trpc.jha.toggleArchive.useMutation({
      onSuccess: () => {
        utils.jha.list.invalidate();
        utils.jha.getByInstanceId.invalidate({ id: entityId });
        utils.auditTrail.get.invalidate({ entityType: 'jha' });
        toast.success(
          archived ? `${configMap[entityType].label} Unarchived` : `${configMap[entityType].label} Archived`,
          {
            description: archived
              ? `${configMap[entityType].label} has been restored to its previous status.`
              : `${configMap[entityType].label} has been archived.`,
          },
        );
      },
      onError: () => {
        toast.error('Action Failed', {
          description: `Could not ${archived ? 'unarchive' : 'archive'} the ${configMap[entityType].label}. Please try again.`,
        });
      },
    }),
    sop: trpc.sop.toggleArchive.useMutation({
      onSuccess: () => {
        utils.sop.list.invalidate();
        utils.sop.getByInstanceId.invalidate({ id: entityId });
        utils.auditTrail.get.invalidate({ entityId, entityType: 'sop' });
        toast.success(
          archived ? `${configMap[entityType].label} Unarchived` : `${configMap[entityType].label} Archived`,
          {
            description: archived
              ? `${configMap[entityType].label} has been restored to its previous status.`
              : `${configMap[entityType].label} has been archived.`,
          },
        );
      },
      onError: () => {
        toast.error('Action Failed', {
          description: `Could not ${archived ? 'unarchive' : 'archive'} the ${configMap[entityType].label}. Please try again.`,
        });
      },
    }),
    hazard: trpc.hazards.toggleArchive.useMutation({
      onSuccess: () => {
        utils.hazards.list.invalidate();
        toast.success(
          archived ? `${configMap[entityType].label} Unarchived` : `${configMap[entityType].label} Archived`,
          {
            description: archived
              ? `${configMap[entityType].label} has been restored to its previous status.`
              : `${configMap[entityType].label} has been archived.`,
          },
        );
      },
      onError: () => {
        toast.error('Action Failed', {
          description: `Could not ${archived ? 'unarchive' : 'archive'} the ${configMap[entityType].label}. Please try again.`,
        });
      },
    }),
    controlMeasure: trpc.controlMeasures.toggleArchive.useMutation({
      onSuccess: () => {
        utils.controlMeasures.list.invalidate();
        toast.success(
          archived ? `${configMap[entityType].label} Unarchived` : `${configMap[entityType].label} Archived`,
          {
            description: archived
              ? `${configMap[entityType].label} has been restored to its previous status.`
              : `${configMap[entityType].label} has been archived.`,
          },
        );
      },
      onError: () => {
        toast.error('Action Failed', {
          description: `Could not ${archived ? 'unarchive' : 'archive'} the ${configMap[entityType].label}. Please try again.`,
        });
      },
    }),
  };

  const onArchive = async () => {
    const mutation = mutationMap[entityType];

    await mutation.mutateAsync({
      id: entityId,
      ...configMap[entityType].payload,
    });

    setShowDialog(false);
  };

  return (
    <AlertDialog open={showDialog} onOpenChange={setShowDialog}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{archived ? 'Unarchive' : 'Archive'}</AlertDialogTitle>
          <AlertDialogDescription>
            {archived ? configMap[entityType].message.restore : configMap[entityType].message.archive}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={onArchive} className={archived ? '' : 'bg-red-600 hover:bg-red-700'}>
            {archived ? 'Unarchive' : 'Archive'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
