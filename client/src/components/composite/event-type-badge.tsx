import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { reportTypeEnum } from '@shared/schema';
import { REPORT_TYPE_MAP } from '@shared/types/schema.types';

export const EventTypeBadge = ({ type }: { type: (typeof reportTypeEnum.enumValues)[number] }) => {
  const colorMap = {
    [reportTypeEnum.enumValues[0]]: 'bg-blue-50 text-blue-700 border-blue-200',
    [reportTypeEnum.enumValues[1]]: 'bg-amber-50 text-amber-700 border-amber-200',
    [reportTypeEnum.enumValues[2]]: 'bg-emerald-50 text-emerald-700 border-emerald-200',
    [reportTypeEnum.enumValues[3]]: 'bg-purple-50 text-purple-700 border-purple-200',
  };

  const typeLabel = REPORT_TYPE_MAP[type];

  const color = colorMap[type];

  return (
    <Badge className={cn(color)} variant="outline">
      {typeLabel}
    </Badge>
  );
};
