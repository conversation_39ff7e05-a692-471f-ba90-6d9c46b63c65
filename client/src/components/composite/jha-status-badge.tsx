import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { approvalStatusEnum } from '@shared/schema';
import { JHA_STATUS_MAP } from '@shared/types/jha.types';
import { Archive, CheckCircleIcon, ClipboardCheck, FileText } from 'lucide-react';

export const JhaStatusBadge = ({ status }: { status: (typeof approvalStatusEnum.enumValues)[number] }) => {
  const statusIconMap = {
    draft: <FileText className="h-3 w-3 mr-1" />,
    review: <ClipboardCheck className="h-3 w-3 mr-1" />,
    approved: <CheckCircleIcon className="h-3 w-3 mr-1" />,
    archived: <Archive className="h-3 w-3 mr-1" />,
  };

  const statusColorMap = {
    draft: 'bg-gray-50 text-gray-700 border-gray-200',
    review: 'bg-blue-50 text-blue-700 border-blue-200',
    approved: 'bg-green-50 text-green-700 border-green-200',
    archived: 'bg-amber-50 text-amber-700 border-amber-200',
  };

  const icon = statusIconMap[status];

  return (
    <Badge className={cn(statusColorMap[status])} variant="outline">
      {icon}
      {JHA_STATUS_MAP[status]}
    </Badge>
  );
};
