import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useAppContext } from '@/contexts/app-context';
import { useInfiniteUsersPublic } from '@/hooks/use-paginated-data';
import { usePermissions } from '@/hooks/use-permissions';
import { trpc } from '@/providers/trpc';
import { formatDate } from '@shared/date-utils';
import { statusEnum } from '@shared/schema';
import { RouterOutputs } from '@shared/types/router.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { MessageSquare, MoreHorizontal, Send, Trash2 } from 'lucide-react';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { toast } from 'sonner';

// Type definitions
type Comment = RouterOutputs['event']['listComments'][0];

type TeamMemberMention = {
  id: string;
  fullName: string;
};

type DropdownPosition = {
  top: number;
  left: number;
};

// Utility function to get cursor position in contentEditable
const getCursorPosition = (element: HTMLDivElement) => {
  const selection = window.getSelection();
  if (!selection || selection.rangeCount === 0) return { top: 34, left: 20 };

  const range = selection.getRangeAt(0);
  const rect = range.getBoundingClientRect();
  const containerRect = element.getBoundingClientRect();

  return {
    top: rect.bottom - containerRect.top + 2, // Position below cursor
    left: rect.left - containerRect.left,
  };
};

export const CommentsSection = ({
  entityId,
  entityType,
  entitySlug,
  entityTitle,
  status,
  entityOwnerId,
}: {
  entityId: string;
  entityType: 'event' | 'capa';
  entitySlug: string;
  entityTitle: string;
  entityOwnerId?: string;
  status: (typeof statusEnum.enumValues)[number];
}) => {
  const { hasPermission } = usePermissions();
  const utils = trpc.useUtils();

  const MODULE_MAP = {
    event: {
      module: MODULES.EHS_EVENT,
      router: trpc.event,
    },
    capa: {
      module: MODULES.EHS_CAPA,
      router: trpc.capa,
    },
  };

  const entityConfig = MODULE_MAP[entityType];
  const canCreateComment = hasPermission(entityConfig.module, ALLOWED_ACTIONS.EDIT, entityOwnerId);

  // tRPC mutations and queries using dynamic entity configuration
  const createCommentMutation = entityConfig.router.createComment.useMutation({
    onSuccess: () => {
      utils.auditTrail.get.invalidate({
        entityId,
        entityType,
      });
      utils[entityType].listComments.invalidate({
        entityId,
        options: { limit: 50 },
      });
    },
  });

  const listCommentsQuery = entityConfig.router.listComments.useQuery({
    entityId,
    options: { limit: 50, offset: 0 },
  });

  const deleteCommentMutation = entityConfig.router.deleteComment.useMutation({
    onSuccess: () => {
      utils[entityType].listComments.invalidate({
        entityId,
        options: { limit: 50 },
      });
    },
  });

  // State management
  const [comment, setComment] = useState('');
  const [isSubmittingComment, setIsSubmittingComment] = useState(false);
  const [showMentionDropdown, setShowMentionDropdown] = useState(false);
  const [mentionFilter, setMentionFilter] = useState('');
  const [dropdownPosition, setDropdownPosition] = useState<DropdownPosition>({ top: 34, left: 20 });
  const [selectedMentionIndex, setSelectedMentionIndex] = useState(0);
  const [commentToDelete, setCommentToDelete] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isDeletingComment, setIsDeletingComment] = useState(false);
  // Store the range where the @ mention should be inserted
  const [mentionRange, setMentionRange] = useState<Range | null>(null);

  // Refs
  const commentInputRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Context
  const { user } = useAppContext();

  // Use data from our queries
  const fetchedComments = listCommentsQuery.data || [];
  const isLoadingComments = listCommentsQuery.isLoading;

  const { data: users = [] } = useInfiniteUsersPublic({
    upkeepCompanyId: user?.upkeepCompanyId!,
  });

  // Transform users to team members format
  const teamMembers: TeamMemberMention[] = useMemo(() => {
    return users.map((user) => ({
      id: user.id,
      fullName: user.fullName ?? 'Unknown User',
    }));
  }, [users]);

  // Helper function to convert HTML content to plain text with proper line breaks
  const htmlToPlainText = useCallback((element: HTMLDivElement) => {
    let result = '';
    const childNodes = element.childNodes;

    for (let i = 0; i < childNodes.length; i++) {
      const node = childNodes[i];
      if (node.nodeType === Node.TEXT_NODE) {
        result += node.textContent || '';
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        const element = node as HTMLElement;
        const tagName = element.tagName.toLowerCase();

        if (tagName === 'div') {
          // If it's not the first div, add a line break before
          if (i > 0) {
            result += '\n';
          }

          // Process the div's contents
          const divText = htmlToPlainText(element as HTMLDivElement);
          result += divText;

          // Check if this div contains only a <br> (empty line)
          if (element.innerHTML === '<br>' || element.innerHTML === '<br/>') {
            // This represents an empty line
          }
        } else if (tagName === 'br') {
          result += '\n';
        } else if (tagName === 'span' && element.getAttribute('data-user-id')) {
          // This is a mention span, get the userId
          const userId = element.getAttribute('data-user-id');
          result += `@${userId}`;
        } else {
          // For other elements, just get text content
          result += element.textContent || '';
        }
      }
    }

    return result;
  }, []);

  const formatDisplayedMentions = useCallback(
    (content: string) => {
      if (!content || typeof content !== 'string') {
        return null;
      }

      // Track which positions in the original content are actual mentions (userID format)
      const actualMentions: Array<{
        start: number;
        end: number;
        member: TeamMemberMention;
        originalUserId: string;
      }> = [];

      // First, find all actual @userId mentions in the original content
      teamMembers.forEach((member) => {
        const userIdMentionRegex = new RegExp(`@${member.id}\\b`, 'gi');
        let match;
        while ((match = userIdMentionRegex.exec(content)) !== null) {
          actualMentions.push({
            start: match.index,
            end: match.index + match[0].length,
            member: member,
            originalUserId: member.id,
          });
        }
      });

      // Sort mentions by start position
      actualMentions.sort((a, b) => a.start - b.start);

      // Remove overlapping mentions (keep the first one)
      const uniqueMentions = actualMentions.filter((mention, index) => {
        if (index === 0) return true;
        const prevMention = actualMentions[index - 1];
        return mention.start >= prevMention.end;
      });

      if (uniqueMentions.length === 0) {
        // No actual mentions, just handle line breaks
        const lines = content.split('\n');
        return (
          <>
            {lines.map((line, index) => (
              <React.Fragment key={index}>
                {line}
                {index < lines.length - 1 && <br />}
              </React.Fragment>
            ))}
          </>
        );
      }

      // Build the JSX parts, only highlighting actual mentions
      const parts: React.ReactNode[] = [];
      let lastIndex = 0;

      uniqueMentions.forEach((mention, mentionIndex) => {
        // Add text before the mention
        if (mention.start > lastIndex) {
          const textBefore = content.substring(lastIndex, mention.start);
          const lines = textBefore.split('\n');
          parts.push(
            <React.Fragment key={`text-before-${mentionIndex}`}>
              {lines.map((line, lineIndex) => (
                <React.Fragment key={`text-${lastIndex}-${mention.start}-${lineIndex}`}>
                  {line}
                  {lineIndex < lines.length - 1 && <br />}
                </React.Fragment>
              ))}
            </React.Fragment>,
          );
        }

        // Add the highlighted mention (replace @userId with @fullName and highlight)
        parts.push(
          <span key={`mention-${mentionIndex}-${mention.member.id}`} className="text-blue-600 font-medium">
            @{mention.member.fullName}
          </span>,
        );

        lastIndex = mention.end;
      });

      // Add remaining text
      if (lastIndex < content.length) {
        const remainingText = content.substring(lastIndex);
        const lines = remainingText.split('\n');
        parts.push(
          <React.Fragment key="text-after">
            {lines.map((line, lineIndex) => (
              <React.Fragment key={`text-${lastIndex}-${lineIndex}`}>
                {line}
                {lineIndex < lines.length - 1 && <br />}
              </React.Fragment>
            ))}
          </React.Fragment>,
        );
      }

      return <>{parts}</>;
    },
    [teamMembers],
  );

  // Event handlers
  const handleCommentChange = useCallback(
    (e: React.FormEvent<HTMLDivElement>) => {
      const plainText = htmlToPlainText(e.currentTarget);
      setComment(plainText);

      // Get cursor position for mention detection
      const selection = window.getSelection();
      if (!selection || selection.rangeCount === 0) return;

      const range = selection.getRangeAt(0);

      // Create a temporary range to get text before cursor
      const tempRange = document.createRange();
      tempRange.setStart(e.currentTarget, 0);
      tempRange.setEnd(range.startContainer, range.startOffset);

      // Create a temporary div to extract the content before cursor
      const tempDiv = document.createElement('div');
      tempDiv.appendChild(tempRange.cloneContents());
      const textBeforeCursor = htmlToPlainText(tempDiv);

      const lastAtIndex = textBeforeCursor.lastIndexOf('@');

      if (lastAtIndex !== -1) {
        const textAfterAt = textBeforeCursor.substring(lastAtIndex + 1);
        const hasSpaceAfterAt = textAfterAt.includes(' ');

        if (!hasSpaceAfterAt) {
          // Filter team members based on what user is typing
          const filteredMembers = teamMembers.filter((member) =>
            member.fullName.toLowerCase().includes(textAfterAt.toLowerCase()),
          );

          if (filteredMembers.length > 0) {
            setMentionFilter(textAfterAt);
            setShowMentionDropdown(true);
            setSelectedMentionIndex(0);

            // Store the current range for mention insertion
            const currentRange = range.cloneRange();
            setMentionRange(currentRange);

            // Calculate dynamic position based on cursor location
            if (commentInputRef.current) {
              const position = getCursorPosition(commentInputRef.current);
              setDropdownPosition(position);
            }
            return;
          }
        }
      }

      setShowMentionDropdown(false);
      setSelectedMentionIndex(0);
      setMentionRange(null);
    },
    [teamMembers, htmlToPlainText],
  );

  // Helper function to extract content with userIDs for submission
  const extractCommentForSubmission = useCallback(() => {
    if (!commentInputRef.current) return '';
    return htmlToPlainText(commentInputRef.current);
  }, [htmlToPlainText]);

  const handleSubmitComment = useCallback(async () => {
    if (!comment.trim()) return;

    setIsSubmittingComment(true);

    // Get the content with userIDs for server submission
    const contentForSubmission = extractCommentForSubmission();

    await createCommentMutation.mutateAsync(
      {
        entityId,
        entitySlug,
        entityTitle,
        status,
        content: contentForSubmission,
        entityType,
      },
      {
        onSuccess: () => {
          toast.success('Comment posted', {
            description: 'Your comment has been successfully posted.',
          });

          setComment('');
          if (commentInputRef.current) {
            commentInputRef.current.innerHTML = '';
            commentInputRef.current.blur();
          }
        },
        onError: (error) => {
          console.error('Failed to post comment:', error);
          toast.error('Failed to post comment', {
            description: error.message || 'Failed to post comment. Please try again.',
          });
        },
        onSettled: () => {
          setIsSubmittingComment(false);
        },
      },
    );
  }, [
    comment,
    entityId,
    entitySlug,
    entityTitle,
    status,
    entityType,
    createCommentMutation,
    extractCommentForSubmission,
  ]);

  const handleSelectMention = useCallback(
    (userId: string) => {
      if (!commentInputRef.current || !mentionRange) return;

      const selectedUser = teamMembers.find((member) => member.id === userId);
      if (!selectedUser) return;

      try {
        // Find the @ symbol by going back from the stored range
        const tempRange = document.createRange();
        tempRange.setStart(commentInputRef.current, 0);
        tempRange.setEnd(mentionRange.startContainer, mentionRange.startOffset);

        const tempDiv = document.createElement('div');
        tempDiv.appendChild(tempRange.cloneContents());
        const textBeforeCursor = htmlToPlainText(tempDiv);

        const lastAtIndex = textBeforeCursor.lastIndexOf('@');
        if (lastAtIndex === -1) return;

        // Create the mention span
        const mentionSpan = document.createElement('span');
        mentionSpan.className = 'text-blue-600 font-medium';
        mentionSpan.textContent = `@${selectedUser.fullName}`;
        mentionSpan.setAttribute('data-user-id', userId);

        // Find the range that contains the @ and the partial text
        const replaceRange = document.createRange();

        // We need to find the actual @ position in the DOM
        let atNode = mentionRange.startContainer;
        let atOffset = mentionRange.startOffset;

        // Look backwards for the @ symbol
        while (atNode && atOffset >= 0) {
          if (atNode.nodeType === Node.TEXT_NODE) {
            const text = atNode.textContent || '';
            const beforeText = text.substring(0, atOffset);
            const atIdx = beforeText.lastIndexOf('@');

            if (atIdx !== -1) {
              // Found the @ symbol
              replaceRange.setStart(atNode, atIdx);
              replaceRange.setEnd(mentionRange.startContainer, mentionRange.startOffset);
              break;
            }

            // Move to previous sibling
            const prevSibling = atNode.previousSibling;
            if (prevSibling) {
              atNode = prevSibling;
              atOffset = prevSibling.textContent?.length || 0;
            } else {
              break;
            }
          } else {
            break;
          }
        }

        // Replace the content
        replaceRange.deleteContents();
        replaceRange.insertNode(mentionSpan);

        // Add a space after the mention
        const spaceNode = document.createTextNode(' ');
        mentionSpan.parentNode?.insertBefore(spaceNode, mentionSpan.nextSibling);

        // Update comment state
        setComment(htmlToPlainText(commentInputRef.current));

        setShowMentionDropdown(false);
        setSelectedMentionIndex(0);
        setMentionRange(null);

        // Position cursor after the space
        setTimeout(() => {
          const newRange = document.createRange();
          const selection = window.getSelection();
          if (selection && spaceNode) {
            newRange.setStart(spaceNode, 1);
            newRange.collapse(true);
            selection.removeAllRanges();
            selection.addRange(newRange);
            commentInputRef.current?.focus();
          }
        }, 0);
      } catch (error) {
        console.error('Error inserting mention:', error);
        setShowMentionDropdown(false);
        setSelectedMentionIndex(0);
        setMentionRange(null);
      }
    },
    [teamMembers, htmlToPlainText, mentionRange],
  );

  const handleKeyDown = useCallback(
    async (e: React.KeyboardEvent<HTMLDivElement>) => {
      // Handle Cmd+Enter (Mac) or Ctrl+Enter (Windows/Linux) to submit comment
      if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        if (comment.trim() && !isSubmittingComment) {
          await handleSubmitComment();
        }
        return;
      }

      if (showMentionDropdown) {
        // Get filtered members for navigation
        const filteredMembers = teamMembers.filter((member) =>
          member.fullName.toLowerCase().includes(mentionFilter.toLowerCase()),
        );

        switch (e.key) {
          case 'Escape':
            setShowMentionDropdown(false);
            setSelectedMentionIndex(0);
            setMentionRange(null);
            e.preventDefault();
            break;
          case 'ArrowDown':
            e.preventDefault();
            setSelectedMentionIndex((prev) => {
              const newIndex = prev < filteredMembers.length - 1 ? prev + 1 : 0;
              return newIndex;
            });
            break;
          case 'ArrowUp':
            e.preventDefault();
            setSelectedMentionIndex((prev) => {
              const newIndex = prev > 0 ? prev - 1 : filteredMembers.length - 1;
              return newIndex;
            });
            break;
          case 'ArrowLeft':
          case 'ArrowRight':
            // Allow normal cursor movement, but close dropdown if moving away from mention
            setTimeout(() => {
              const selection = window.getSelection();
              if (selection && selection.rangeCount > 0) {
                const range = selection.getRangeAt(0);
                const tempRange = document.createRange();
                tempRange.setStart(e.currentTarget, 0);
                tempRange.setEnd(range.startContainer, range.startOffset);

                const tempDiv = document.createElement('div');
                tempDiv.appendChild(tempRange.cloneContents());
                const textBeforeCursor = htmlToPlainText(tempDiv);

                const lastAtIndex = textBeforeCursor.lastIndexOf('@');
                if (lastAtIndex === -1 || textBeforeCursor.substring(lastAtIndex + 1).includes(' ')) {
                  setShowMentionDropdown(false);
                  setSelectedMentionIndex(0);
                  setMentionRange(null);
                }
              }
            }, 0);
            break;
          case 'Enter':
          case 'Tab':
            if (
              filteredMembers.length > 0 &&
              selectedMentionIndex >= 0 &&
              selectedMentionIndex < filteredMembers.length
            ) {
              e.preventDefault();
              const selectedMember = filteredMembers[selectedMentionIndex];
              handleSelectMention(selectedMember.id);
            }
            break;
        }
      }
    },
    [
      comment,
      handleSelectMention,
      handleSubmitComment,
      htmlToPlainText,
      isSubmittingComment,
      mentionFilter,
      selectedMentionIndex,
      showMentionDropdown,
      teamMembers,
    ],
  );

  const handleDeleteComment = useCallback((commentId: string) => {
    setCommentToDelete(commentId);
    setShowDeleteConfirm(true);
  }, []);

  const confirmDeleteComment = useCallback(async () => {
    if (!commentToDelete) return;

    setIsDeletingComment(true);
    await deleteCommentMutation.mutateAsync(
      { id: commentToDelete },
      {
        onSuccess: () => {
          toast.success('Comment deleted', {
            description: 'The comment has been successfully deleted.',
          });
        },
        onError: (error) => {
          console.error('Failed to delete comment:', error);
          toast.error('Failed to delete comment', {
            description: error.message || 'Failed to delete comment. Please try again.',
          });
        },
        onSettled: () => setIsDeletingComment(false),
      },
    );
  }, [commentToDelete, deleteCommentMutation]);

  const cancelDeleteComment = useCallback(() => {
    setShowDeleteConfirm(false);
    setCommentToDelete(null);
  }, []);

  // Close mention dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        event.target &&
        showMentionDropdown &&
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as HTMLElement)
      ) {
        setShowMentionDropdown(false);
        setSelectedMentionIndex(0);
        setMentionRange(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showMentionDropdown]);

  // Auto-scroll selected mention item into view
  useEffect(() => {
    if (showMentionDropdown && dropdownRef.current) {
      const selectedItem = dropdownRef.current.querySelector(`[data-mention-index="${selectedMentionIndex}"]`);
      if (selectedItem) {
        selectedItem.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
        });
      }
    }
  }, [selectedMentionIndex, showMentionDropdown]);

  return (
    <Card className="shadow-xs bg-white">
      <CardHeader>
        <div className="flex items-center">
          <MessageSquare className="h-5 w-5 mr-2" />
          <CardTitle className="text-base">Comments</CardTitle>
        </div>
      </CardHeader>
      <CardContent className="px-4 pb-4">
        {fetchedComments.length > 0 ? (
          <div className="space-y-4">
            {fetchedComments.map((comment: Comment) => (
              <div key={comment.id} className="bg-gray-50 rounded-md p-4">
                <div className="flex items-start space-x-3">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="flex h-full w-full items-center justify-center rounded-full bg-muted text-xs">
                      {users.find((u) => u.id === comment.userId)?.firstName?.charAt(0) || '?'}
                      {users.find((u) => u.id === comment.userId)?.lastName?.charAt(0) || ''}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-sm">
                          {users.find((u) => u.id === comment.userId)?.fullName || ''}
                        </span>
                        <span className="text-xs text-gray-500">{formatDate(comment.createdAt)}</span>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0 hover:bg-accent hover:text-accent-foreground"
                          >
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-36">
                          <DropdownMenuItem
                            className="text-destructive focus:text-destructive flex items-center cursor-pointer"
                            onClick={() => handleDeleteComment(comment.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                            <span>Delete</span>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                    <div className="text-sm text-gray-700 break-all whitespace-pre-wrap">
                      {formatDisplayedMentions(comment.content)}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            {/* Loading state */}
            {isLoadingComments ? (
              <div className="text-center py-8 text-gray-500">Loading comments...</div>
            ) : (
              <>
                <MessageSquare className="h-8 w-8 mb-2 mx-auto opacity-50" />
                <p>No comments yet</p>
                {canCreateComment && <p className="text-xs mt-1">Be the first to add a comment</p>}
              </>
            )}
          </div>
        )}

        {/* Comment Form */}
        {canCreateComment && (
          <div className="mt-4 py-4 border-t sticky bottom-0 bg-white">
            <div className="flex gap-2">
              <Avatar className="h-7 w-7 sm:h-8 sm:w-8 shrink-0 self-start mt-2">
                <AvatarFallback className="text-xs">
                  {user?.firstName?.charAt(0) || '?'}
                  {user?.lastName?.charAt(0) || ''}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 relative">
                <div
                  ref={commentInputRef}
                  className="w-full border rounded-md p-3 min-h-20 transition-all whitespace-pre-wrap break-words text-sm focus:min-h-28 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent "
                  contentEditable
                  suppressContentEditableWarning={true}
                  onInput={handleCommentChange}
                  onKeyDown={handleKeyDown}
                />

                {!comment && (
                  <div className="absolute top-3 left-3 text-gray-500 pointer-events-none text-sm">
                    Add a comment or tag a teammate using @...
                  </div>
                )}

                {/* @mention dropdown */}
                {showMentionDropdown && (
                  <div
                    className="absolute z-50 w-72 max-h-60 overflow-auto bg-white border border-gray-200 rounded-md shadow-md"
                    style={{
                      top: `${dropdownPosition.top}px`,
                      left: `${dropdownPosition.left}px`,
                    }}
                    ref={dropdownRef}
                  >
                    {(() => {
                      const filteredMembers = teamMembers.filter((member) =>
                        member.fullName.toLowerCase().includes(mentionFilter.toLowerCase()),
                      );

                      return filteredMembers.length > 0 ? (
                        <div className="py-1">
                          {filteredMembers.map((member: TeamMemberMention, index: number) => (
                            <div
                              key={member.id}
                              data-mention-index={index}
                              className={`flex items-center p-3 hover:bg-gray-50 cursor-pointer transition-colors duration-150 ${
                                selectedMentionIndex === index ? 'bg-blue-50 border-l-2 border-blue-500' : ''
                              }`}
                              onClick={(e) => {
                                handleSelectMention(member.id);
                                e.preventDefault();
                                e.stopPropagation();
                              }}
                              onMouseEnter={() => setSelectedMentionIndex(index)}
                            >
                              <Avatar className="h-6 w-6 mr-3">
                                <AvatarFallback className="text-xs">
                                  {member.fullName.charAt(0).toUpperCase()}
                                </AvatarFallback>
                              </Avatar>
                              <div className="font-medium text-sm text-gray-900">{member?.fullName || ''}</div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="p-4 text-gray-500 text-center text-sm">
                          No matching team members
                          {mentionFilter && ` for "${mentionFilter}"`}
                        </div>
                      );
                    })()}
                  </div>
                )}

                <div className="flex justify-between items-center mt-2">
                  <div className="text-xs text-gray-500 space-y-1">
                    <div>Use @username to mention</div>
                    <div className="opacity-75">
                      Press{' '}
                      <kbd className="px-1 py-0.5 text-xs bg-gray-100 border rounded">
                        {navigator.userAgent.toLowerCase().includes('mac') ? 'Cmd' : 'Ctrl'}
                      </kbd>
                      {' + '}
                      <kbd className="px-1 py-0.5 text-xs bg-gray-100 border rounded">Enter</kbd> to send
                    </div>
                  </div>
                  <Button
                    onClick={handleSubmitComment}
                    disabled={!comment.trim() || isSubmittingComment}
                    aria-label="Post comment"
                  >
                    <span className="hidden sm:inline mr-1">{isSubmittingComment ? 'Posting...' : 'Post'}</span>
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>

      {/* Delete Comment Confirmation Dialog */}
      <AlertDialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Comment</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this comment? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={cancelDeleteComment} disabled={isDeletingComment}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteComment}
              disabled={isDeletingComment}
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeletingComment ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
};
