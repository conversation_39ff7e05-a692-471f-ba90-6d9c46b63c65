import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';
import { trpc } from '@/providers/trpc';
import { Keyboard, Mic, Sparkles, Square, Zap } from 'lucide-react';
import { AnimatePresence, motion } from 'motion/react';
import { useRef, useState, useEffect } from 'react';
import { toast } from 'sonner';

export type VoiceInputRef = {
  resetTranscript: () => void;
};

type VoiceInputProps = {
  onAnalysisComplete: (text: string, mode: 'voice' | 'text') => void;
  isPublic?: boolean;
  isLoading?: boolean;
  ref?: React.RefObject<VoiceInputRef | null>;
};

// Maximum recording duration in seconds
const MAX_RECORDING_DURATION = 60;

// Supported MIME types in order of preference
const SUPPORTED_MIME_TYPES = [
  'audio/webm;codecs=opus',
  'audio/webm',
  'audio/mp4',
  'audio/mp4;codecs=mp4a.40.2',
  'audio/mpeg',
  'audio/wav',
  'audio/ogg;codecs=opus',
  'audio/ogg',
];

// Browser detection utility
const getBrowserInfo = () => {
  const userAgent = navigator.userAgent;
  const isIOS = /iPad|iPhone|iPod/.test(userAgent);
  const isSafari = /^((?!chrome|android).)*safari/i.test(userAgent);
  const isIOSSafari = isIOS && isSafari;

  return {
    isIOS,
    isSafari,
    isIOSSafari,
    isChrome: /Chrome/.test(userAgent),
    isFirefox: /Firefox/.test(userAgent),
  };
};

// Get the best supported MIME type for this browser
const getSupportedMimeType = (): string => {
  if (!window.MediaRecorder) {
    return 'audio/wav'; // Fallback
  }

  const browserInfo = getBrowserInfo();
  
  // Safari iOS has specific MIME type preferences
  if (browserInfo.isIOSSafari) {
    // iOS Safari 14.3+ prefers these formats in order
    const iosSafariTypes = [
      'audio/mp4',
      'audio/mp4;codecs=mp4a.40.2',
      'audio/wav',
      'audio/webm',
    ];
    
    for (const mimeType of iosSafariTypes) {
      if (MediaRecorder.isTypeSupported(mimeType)) {
        console.log(`iOS Safari using MIME type: ${mimeType}`);
        return mimeType;
      }
    }
  }

  // Standard browser support check
  for (const mimeType of SUPPORTED_MIME_TYPES) {
    if (MediaRecorder.isTypeSupported(mimeType)) {
      return mimeType;
    }
  }

  // Final fallback
  return browserInfo.isIOSSafari ? 'audio/mp4' : 'audio/webm';
};

// Common Components
type AnalyzeButtonProps = {
  onAnalyze: () => void;
  isDisabled: boolean;
};

const AnalyzeButton = ({ onAnalyze, isDisabled }: AnalyzeButtonProps) => (
  <motion.div
    initial={{ opacity: 0, y: -10 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -10 }}
    className="flex justify-center"
  >
    <Button onClick={onAnalyze} type="button" disabled={isDisabled}>
      <Sparkles className="h-4 w-4" />
      Analyze with AI
    </Button>
  </motion.div>
);

type StatusBadgesProps = {
  isIOSSafari: boolean;
};

const StatusBadges = ({ isIOSSafari }: StatusBadgesProps) => (
  <div className="flex justify-center items-center">
    <div className="flex gap-1">
      <Badge variant="secondary" className="bg-indigo-100 text-indigo-800 border-indigo-200">
        <Zap className="w-3 h-3" />
        AI Assist
      </Badge>
      {isIOSSafari && (
        <Badge variant="secondary" className="bg-green-100 text-green-800 border-green-200">
          iOS Compatible
        </Badge>
      )}
    </div>
  </div>
);

type InputModeToggleProps = {
  inputMode: 'voice' | 'text';
  onModeChange: (mode: 'voice' | 'text') => void;
  isSupported: boolean;
};

const InputModeToggle = ({ inputMode, onModeChange, isSupported }: InputModeToggleProps) => {
  if (!isSupported) return null;

  return (
    <div className="flex justify-center">
      <div className="bg-white border border-indigo-200 rounded-lg p-1 flex gap-1">
        <Button
          type="button"
          size="sm"
          variant={inputMode === 'voice' ? 'default' : 'ghost'}
          onClick={() => onModeChange('voice')}
          className={cn(
            'text-xs font-medium px-3 py-1 rounded-md',
            inputMode === 'voice'
              ? 'bg-indigo-100 text-indigo-700 hover:bg-indigo-200'
              : 'text-gray-600 hover:text-indigo-700 hover:bg-indigo-50',
          )}
        >
          <Mic className="w-3 h-3 mr-1" />
          Voice
        </Button>
        <Button
          type="button"
          size="sm"
          variant={inputMode === 'text' ? 'default' : 'ghost'}
          onClick={() => onModeChange('text')}
          className={cn(
            'text-xs font-medium px-3 py-1 rounded-md',
            inputMode === 'text'
              ? 'bg-indigo-100 text-indigo-700 hover:bg-indigo-200'
              : 'text-gray-600 hover:text-indigo-700 hover:bg-indigo-50',
          )}
        >
          <Keyboard className="w-3 h-3 mr-1" />
          Type
        </Button>
      </div>
    </div>
  );
};

type InstructionTextProps = {
  inputMode: 'voice' | 'text';
  isMobile: boolean;
  isIOSSafari: boolean;
  maxDuration: number;
};

const InstructionText = ({ inputMode, isMobile, isIOSSafari, maxDuration }: InstructionTextProps) => (
  <p className="text-sm text-gray-600 text-center font-medium">
    {inputMode === 'voice' ? (
      <>
        {`${isMobile ? 'Tap' : 'Click'} to record your description (max ${maxDuration}s)`}
        {isIOSSafari && (
          <span className="block mt-0.5 text-xs text-blue-600 font-normal">
            💡 First tap may request microphone permission
          </span>
        )}
      </>
    ) : (
      'Type your description in the text area below'
    )}
  </p>
);

type LoadingOverlayProps = {
  isMobile: boolean;
};

const LoadingOverlay = ({ isMobile }: LoadingOverlayProps) => (
  <div className="absolute inset-0 bg-white/90 flex items-center justify-center rounded-md backdrop-blur-sm">
    <div className="flex flex-col items-center gap-1 px-2">
      <div className="relative">
        {/* Multiple pulsing rings for enhanced effect */}
        <div className="absolute inset-0 animate-ping opacity-20 bg-indigo-400 rounded-full" />
        <div
          className="absolute inset-0 animate-ping opacity-40 bg-indigo-500 rounded-full"
          style={{ animationDelay: '0.5s' }}
        />
        <div className="absolute inset-0 animate-pulse opacity-60 bg-indigo-300 rounded-full" />
        <Sparkles
          className={`${isMobile ? 'h-6 w-6' : 'h-8 w-8'} text-indigo-600 relative z-10 animate-spin`}
          style={{ animationDuration: '3s' }}
        />
      </div>
      <div className="flex flex-col items-center">
        <p className="text-xs sm:text-sm font-semibold text-gray-800 text-center">
          {isMobile ? 'Transcribing audio...' : 'Transcribing your audio...'}
        </p>
        <div className="text-xs text-indigo-600 flex items-center gap-1">
          <Zap className="h-3 w-3 animate-pulse" />
          <span className="animate-pulse">Converting speech to text</span>
        </div>
      </div>
    </div>
  </div>
);

type TranscriptionAreaProps = {
  placeholder: string;
  value: string;
  onChange: (value: string) => void;
  disabled: boolean;
  isMobile: boolean;
  className?: string;
  isTranscribing?: boolean;
};

const TranscriptionArea = ({
  placeholder,
  value,
  onChange,
  disabled,
  isMobile,
  className,
  isTranscribing = false,
}: TranscriptionAreaProps) => (
  <div className="relative">
    <Textarea
      placeholder={placeholder}
      className={cn(
        `${isMobile ? 'min-h-[80px]' : 'min-h-[100px]'} font-medium bg-white border-indigo-200 text-gray-800 rounded-md text-sm sm:text-base focus:border-indigo-300 focus:ring-indigo-200`,
        className,
      )}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      disabled={disabled}
    />
    {isTranscribing && <LoadingOverlay isMobile={isMobile} />}
  </div>
);

type RecordingTimerProps = {
  recordingTime: number;
  maxDuration: number;
};

const RecordingTimer = ({ recordingTime, maxDuration }: RecordingTimerProps) => {
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="flex items-center gap-1 text-red-600 font-medium"
    >
      <span className={`${recordingTime >= maxDuration - 5 ? 'animate-pulse' : ''}`}>{formatTime(recordingTime)}</span>
      <span className="text-xs">/ {formatTime(maxDuration)}</span>
    </motion.div>
  );
};

type RecordingButtonProps = {
  isRecording: boolean;
  isTranscribing: boolean;
  onToggleRecording: () => void;
  isMobile: boolean;
};

const RecordingButton = ({ isRecording, isTranscribing, onToggleRecording, isMobile }: RecordingButtonProps) => {
  const recordButtonVariants = {
    recording: {
      scale: [1, 1.05, 1],
      transition: {
        repeat: Number.POSITIVE_INFINITY,
        repeatType: 'reverse' as const,
        duration: 1.5,
      },
    },
    idle: {
      scale: 1,
    },
  };

  const pulseVariants = {
    animate: {
      scale: [1, 1.2, 1],
      opacity: [0.7, 1, 0.7],
      transition: {
        repeat: Number.POSITIVE_INFINITY,
        repeatType: 'reverse' as const,
        duration: 1.5,
      },
    },
  };

  return (
    <motion.div
      className="relative cursor-pointer"
      variants={recordButtonVariants}
      animate={isRecording ? 'recording' : 'idle'}
      onClick={onToggleRecording}
    >
      {isRecording && (
        <motion.div
          className="absolute inset-0 rounded-full bg-rose-100/40 -m-1"
          variants={pulseVariants}
          animate="animate"
        />
      )}

      <Button
        type="button"
        size="lg"
        className={cn(
          'rounded-full flex items-center justify-center p-0',
          isMobile ? 'h-16 w-16' : 'h-20 w-20',
          isRecording ? 'bg-red-500 hover:bg-red-600' : 'bg-primary hover:bg-primary-700',
        )}
        disabled={isTranscribing}
      >
        <AnimatePresence mode="wait">
          {isRecording ? (
            <motion.div
              key="square"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0 }}
              transition={{ duration: 0.2 }}
            >
              <Square className={`${isMobile ? 'h-6 w-6' : 'h-8 w-8'} text-white`} />
            </motion.div>
          ) : (
            <motion.div
              key="mic"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0 }}
              transition={{ duration: 0.2 }}
            >
              <Mic className={`${isMobile ? 'h-6 w-6' : 'h-8 w-8'} text-white`} />
            </motion.div>
          )}
        </AnimatePresence>
      </Button>
    </motion.div>
  );
};

type AnalysisLoadingStateProps = {
  isLoading: boolean;
};

const AnalysisLoadingState = ({ isLoading }: AnalysisLoadingStateProps) => (
  <AnimatePresence>
    {isLoading && (
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        className="p-3 bg-gradient-to-r from-indigo-50 to-blue-50 border border-indigo-200 rounded-lg"
      >
        <div className="flex items-center gap-3">
          <Sparkles className="h-5 w-5 animate-spin text-indigo-600" />
          <div className="flex-1">
            <p className="text-sm font-medium text-indigo-900">Analyzing your input...</p>
            <p className="text-xs text-indigo-700">AI is processing your description and filling out the form</p>
          </div>
          <Zap className="h-5 w-5 text-indigo-500 animate-pulse" />
        </div>
      </motion.div>
    )}
  </AnimatePresence>
);

export function VoiceInput({ onAnalysisComplete, isPublic = false, ref, isLoading = false }: VoiceInputProps) {
  const [inputMode, setInputMode] = useState<'voice' | 'text'>('voice');
  const [isRecording, setIsRecording] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [hasTranscript, setHasTranscript] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [isSupported, setIsSupported] = useState(true);
  const [browserInfo] = useState(getBrowserInfo);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const timerRef = useRef<number | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const isMobile = useIsMobile();

  // Expose reset function to parent component via ref
  useEffect(() => {
    if (ref) {
      ref.current = {
        resetTranscript: () => {
          setTranscript('');
          setHasTranscript(false);
          // Reset to default input mode (voice) for consistency
          setInputMode(isSupported ? 'voice' : 'text');
        },
      };
    }
  }, [ref, isSupported]);

  // Auto-switch to text mode if voice is not supported
  useEffect(() => {
    if (!isSupported) {
      setInputMode('text');
    }
  }, [isSupported]);

  // Clear transcript when switching modes to avoid confusion
  useEffect(() => {
    setTranscript('');
    setHasTranscript(false);
  }, [inputMode]);

  // Check browser support on mount
  useEffect(() => {
    const checkSupport = () => {
      // Check if basic APIs are available
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        setIsSupported(false);
        return;
      }

      // Check MediaRecorder support
      if (!window.MediaRecorder) {
        setIsSupported(false);
        return;
      }

      // For iOS, we need additional checks
      if (browserInfo.isIOSSafari) {
        // iOS Safari supports MediaRecorder starting from iOS 14.3
        const iOSVersion = navigator.userAgent.match(/OS (\d+)_(\d+)/);
        if (iOSVersion) {
          const major = parseInt(iOSVersion[1]);
          const minor = parseInt(iOSVersion[2]);
          if (major < 14 || (major === 14 && minor < 3)) {
            setIsSupported(false);
            return;
          }
        }
      }

      setIsSupported(true);
    };

    checkSupport();
  }, [browserInfo]);

  // Cleanup timer and stream on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        window.clearInterval(timerRef.current);
      }
      if (streamRef.current) {
        streamRef.current.getTracks().forEach((track) => track.stop());
      }
    };
  }, []);

  const transcribeMutationCall = isPublic ? trpc.ai.transcribePublic : trpc.ai.transcribe;

  const { mutateAsync: transcribe, isPending: isTranscribing } = transcribeMutationCall.useMutation({
    onSuccess: (data) => {
      // Handle the transcript data
      if (data?.text) {
        setTranscript(data.text);
        setHasTranscript(true);
      }

      toast.success('Transcription Complete', {
        description: 'Your recording has been processed.',
      });

      onAnalysisComplete(data.text, 'voice');
    },
    onError: (error) => {
      console.error('Transcription API error:', error);
      
      let errorTitle = 'Transcription Error';
      let errorDescription = 'Failed to transcribe audio. Please try again.';
      
      if (browserInfo.isIOSSafari) {
        errorTitle = 'iOS Safari Transcription Error';
        errorDescription = 'Audio transcription failed on iOS Safari. This may be due to audio format compatibility. Please try using the text input instead.';
      }
      
      // Check for specific error types
      if (error?.message?.includes('BAD_REQUEST')) {
        errorDescription = browserInfo.isIOSSafari
          ? 'Invalid audio format for iOS Safari. Please try recording again or use text input.'
          : 'Invalid audio format. Please try recording again.';
      } else if (error?.message?.includes('INTERNAL_SERVER_ERROR')) {
        errorDescription = browserInfo.isIOSSafari
          ? 'Server error processing iOS Safari audio. Please try using the text input.'
          : 'Server error processing audio. Please try again.';
      }

      toast.error(errorTitle, {
        description: errorDescription,
      });
    },
  });

  const startRecording = async () => {
    try {
      // Reset previous data
      setTranscript('');
      setHasTranscript(false);
      audioChunksRef.current = [];
      setRecordingTime(0);

      // Check support again
      if (!isSupported) {
        throw new Error(
          browserInfo.isIOSSafari
            ? 'Voice recording requires iOS 14.3 or later. Please update your iOS or use the text input below.'
            : 'Your browser does not support audio recording. Please try Chrome, Firefox, Safari, or Edge.',
        );
      }

      // Request microphone access with specific constraints for iOS
      const constraints: MediaStreamConstraints = {
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          // iOS-specific constraints
          ...(browserInfo.isIOSSafari && {
            sampleRate: 44100,
            channelCount: 1,
          }),
        },
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      streamRef.current = stream;

      // Get the best supported MIME type
      const mimeType = getSupportedMimeType();
      console.log(`Using MIME type: ${mimeType} for ${browserInfo.isIOSSafari ? 'iOS Safari' : 'this browser'}`);

      // Set up the media recorder with appropriate options
      let mediaRecorderOptions: MediaRecorderOptions = { mimeType };
      
      // iOS Safari specific optimizations
      if (browserInfo.isIOSSafari) {
        mediaRecorderOptions = {
          mimeType,
          audioBitsPerSecond: 64000, // Lower bitrate for iOS compatibility and smaller file sizes
        };
        
        // Fallback: if the specific options don't work, try without codec specification
        if (!MediaRecorder.isTypeSupported(mimeType)) {
          const fallbackType = 'audio/mp4';
          if (MediaRecorder.isTypeSupported(fallbackType)) {
            mediaRecorderOptions.mimeType = fallbackType;
            console.log(`iOS Safari falling back to: ${fallbackType}`);
          }
        }
      }

      let mediaRecorder: MediaRecorder;
      try {
        mediaRecorder = new MediaRecorder(stream, mediaRecorderOptions);
      } catch (error) {
        console.warn('MediaRecorder failed with specified options, trying fallback:', error);
        // Fallback for iOS Safari - minimal configuration
        if (browserInfo.isIOSSafari) {
          mediaRecorder = new MediaRecorder(stream, { mimeType: 'audio/mp4' });
        } else {
          mediaRecorder = new MediaRecorder(stream); // Let browser choose
        }
      }
      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onerror = (event) => {
        console.error('MediaRecorder error:', event);
        toast.error('Recording Error', {
          description: 'An error occurred during recording. Please try again.',
        });
        stopRecording();
      };

      // Start recording with timeslice for iOS compatibility
      if (browserInfo.isIOSSafari) {
        // iOS Safari works better with timeslices
        mediaRecorder.start(1000);
      } else {
        mediaRecorder.start();
      }

      setIsRecording(true);

      // Start timer for recording duration
      timerRef.current = window.setInterval(() => {
        setRecordingTime((prev) => {
          const newTime = prev + 1;
          if (newTime >= MAX_RECORDING_DURATION) {
            // Auto stop recording when max duration is reached
            if (timerRef.current) {
              window.clearInterval(timerRef.current);
            }
            stopRecording();
            return MAX_RECORDING_DURATION;
          }
          return newTime;
        });
      }, 1000);

      toast.info('Recording started', {
        description: `Speak clearly and describe your content in detail. Maximum ${MAX_RECORDING_DURATION} seconds.`,
      });
    } catch (error) {
      console.error('Error starting recording:', error);

      let errorMessage = 'Unable to access your microphone. Please check permissions and try again.';

      if (error instanceof Error) {
        if (error.name === 'NotAllowedError') {
          errorMessage = browserInfo.isIOSSafari
            ? 'Microphone access denied. Please go to Settings > Safari > Camera & Microphone and allow access for this site.'
            : 'Microphone access denied. Please allow microphone access and try again.';
        } else if (error.name === 'NotFoundError') {
          errorMessage = 'No microphone found. Please connect a microphone and try again.';
        } else if (error.name === 'NotSupportedError') {
          errorMessage = browserInfo.isIOSSafari
            ? 'Voice recording is not supported on this version of iOS Safari. Please update to iOS 14.3 or later.'
            : 'Voice recording is not supported in this browser. Please try Chrome, Firefox, or Safari.';
        } else {
          errorMessage = error.message;
        }
      }

      toast.error('Microphone Access Error', {
        description: errorMessage,
      });
    }
  };

  const sendToTranscriptionAPI = async (audioBlob: Blob) => {
    try {
      // Check if audio is too large (warn at 1MB which is a safe size for most APIs)
      if (audioBlob.size > 1024 * 1024) {
        console.warn(`Large audio file (${(audioBlob.size / (1024 * 1024)).toFixed(2)}MB). May exceed server limits.`);
      }

      // Safari iOS-compatible base64 conversion
      let audioBase64: string;
      
      if (browserInfo.isIOSSafari) {
        // Use FileReader for iOS Safari compatibility
        audioBase64 = await new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => {
            if (typeof reader.result === 'string') {
              // Remove the data URL prefix (e.g., "data:audio/mp4;base64,")
              const base64Data = reader.result.split(',')[1] || reader.result;
              resolve(base64Data);
            } else {
              reject(new Error('Failed to convert audio to base64'));
            }
          };
          reader.onerror = () => reject(new Error('FileReader error'));
          reader.readAsDataURL(audioBlob);
        });
      } else {
        // Standard conversion for other browsers
        const buffer = await audioBlob.arrayBuffer();
        audioBase64 = btoa(new Uint8Array(buffer).reduce((data, byte) => data + String.fromCharCode(byte), ''));
      }

      console.log(`Audio prepared for transcription: ${audioBlob.size} bytes, ${audioBlob.type}, base64 length: ${audioBase64.length}`);

      // Send to transcription API using the mutation
      await transcribe({ audioBase64 });
    } catch (error) {
      console.error('Error preparing audio for transcription:', error);
      
      let errorMessage = 'Failed to process the audio. Please try again.';
      if (browserInfo.isIOSSafari) {
        errorMessage = 'Failed to process audio on iOS Safari. Please try using the text input instead.';
      }
      
      toast.error('Audio Processing Error', {
        description: errorMessage,
      });
    }
  };

  const stopRecording = () => {
    // Clear the timer
    if (timerRef.current) {
      window.clearInterval(timerRef.current);
      timerRef.current = null;
    }

    // Stop media recorder and process audio
    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      // We need to set up the onstop handler before calling stop()
      mediaRecorderRef.current.onstop = async () => {
        if (audioChunksRef.current.length > 0) {
          // Create audio blob from chunks with the correct MIME type
          const mimeType = getSupportedMimeType();
          let audioBlob: Blob;
          
          try {
            audioBlob = new Blob(audioChunksRef.current, { type: mimeType });
            
            // Verify blob is valid
            if (audioBlob.size === 0) {
              throw new Error('Audio blob is empty');
            }
            
            console.log(`Created audio blob: ${audioBlob.size} bytes, type: ${audioBlob.type}`);
          } catch (blobError) {
            console.warn('Failed to create blob with specific MIME type, using generic:', blobError);
            // Fallback: create blob without specific MIME type
            audioBlob = new Blob(audioChunksRef.current);
          }

          // Send to transcription API
          await sendToTranscriptionAPI(audioBlob);
        } else {
          console.warn('No audio data captured during recording');
          toast.error('Recording Error', {
            description: browserInfo.isIOSSafari 
              ? 'No audio was captured. This may be due to iOS microphone restrictions. Please try again or use text input.'
              : 'No audio was captured. Please try again.',
          });
        }

        // Clean up the stream
        if (streamRef.current) {
          streamRef.current.getTracks().forEach((track) => track.stop());
          streamRef.current = null;
        }
      };

      // Now stop the recorder which will trigger the onstop event
      mediaRecorderRef.current.stop();
    }

    setIsRecording(false);
    setRecordingTime(0);
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        when: 'beforeChildren',
        staggerChildren: 0.1,
      },
    },
  };

  // Show unsupported message if browser doesn't support recording
  if (!isSupported) {
    return (
      <motion.div
        className="rounded-lg p-4 sm:p-6 space-y-4 bg-yellow-50/50 border border-yellow-200 shadow-md"
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <h3 className="text-lg sm:text-xl font-semibold text-gray-800">Voice Recording Unavailable</h3>
            <span className="bg-yellow-100 text-yellow-800 text-xs px-2 py-0.5 rounded-full font-medium">
              Not Supported
            </span>
          </div>
          <p className="text-sm text-gray-600">
            {browserInfo.isIOSSafari
              ? 'Voice recording requires iOS 14.3 or later. Please update your iOS or use the text input to type your description.'
              : 'Voice recording is not supported in this browser. Please use Chrome, Firefox, Safari, or Edge, or use the text input below.'}
          </p>
        </div>

        <div className="mt-3 space-y-2.5">
          <TranscriptionArea
            placeholder="Please type your description here..."
            value={transcript}
            onChange={(value) => {
              setTranscript(value);
              setHasTranscript(value.length > 0);
            }}
            disabled={false}
            isMobile={isMobile}
            className="border-yellow-200"
          />
          <AnalyzeButton
            onAnalyze={() => {
              if (transcript.trim()) {
                onAnalysisComplete(transcript, 'text');
              } else {
                toast.error('No content to analyze', {
                  description: 'Please enter some text before analyzing.',
                });
              }
            }}
            isDisabled={isLoading || !transcript.trim()}
          />
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      className="rounded-lg p-4 sm:p-5 space-y-3 sm:space-y-4 bg-indigo-50/50 border border-indigo-200 shadow-md"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <div className="space-y-2.5">
        <StatusBadges isIOSSafari={browserInfo.isIOSSafari} />
        <InputModeToggle inputMode={inputMode} onModeChange={setInputMode} isSupported={isSupported} />
        <InstructionText
          inputMode={inputMode}
          isMobile={isMobile}
          isIOSSafari={browserInfo.isIOSSafari}
          maxDuration={MAX_RECORDING_DURATION}
        />
      </div>

      {/* Voice Recording Mode */}
      {inputMode === 'voice' && (
        <div className="flex flex-col items-center space-y-2.5 sm:space-y-3">
          <RecordingButton
            isRecording={isRecording}
            isTranscribing={isTranscribing}
            onToggleRecording={isRecording ? stopRecording : startRecording}
            isMobile={isMobile}
          />

          {isRecording && <RecordingTimer recordingTime={recordingTime} maxDuration={MAX_RECORDING_DURATION} />}

          {/* Recording status text - only show when actively recording */}
          {isRecording && (
            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="text-xs sm:text-sm font-medium">
              <span className="text-red-600 font-semibold">{isMobile ? 'Tap to stop' : 'Click to stop recording'}</span>
            </motion.div>
          )}
        </div>
      )}

      {/* Text Input Mode */}
      {inputMode === 'text' && (
        <motion.div
          className="space-y-2.5"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <TranscriptionArea
            placeholder="Type your description here..."
            value={transcript}
            onChange={(value) => {
              setTranscript(value);
              setHasTranscript(value.length > 0);
            }}
            disabled={isLoading}
            isMobile={isMobile}
          />

          {hasTranscript && transcript.trim() && (
            <AnalyzeButton onAnalyze={() => onAnalysisComplete(transcript, 'text')} isDisabled={isLoading} />
          )}
        </motion.div>
      )}

      {/* Voice Mode Transcript Display */}
      {inputMode === 'voice' && (
        <AnimatePresence>
          {(hasTranscript || isRecording || isTranscribing || isLoading) && (
            <motion.div
              className="space-y-2"
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
            >
              <TranscriptionArea
                placeholder={
                  isMobile ? 'Voice transcript will appear here...' : 'Your recorded description will appear here...'
                }
                value={transcript}
                onChange={setTranscript}
                disabled={isRecording || isTranscribing || isLoading}
                isMobile={isMobile}
                className={hasTranscript ? 'focus:border-indigo-300 focus:ring-indigo-200' : ''}
                isTranscribing={isTranscribing}
              />

              {hasTranscript && !isTranscribing && !isRecording && !isLoading && (
                <motion.div
                  className="flex justify-center"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                >
                  <p className="text-xs text-indigo-700 font-medium flex items-center">
                    <Sparkles className="h-3 w-3 mr-1" />
                    AI analysis complete - form will be filled automatically
                  </p>
                </motion.div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      )}

      <AnalysisLoadingState isLoading={isLoading} />
    </motion.div>
  );
}
