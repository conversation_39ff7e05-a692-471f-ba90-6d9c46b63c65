import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverPortal, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { useInfiniteHazards } from '@/hooks/use-paginated-data';
import { cn } from '@/lib/utils';
import { hazardCategoryEnum } from '@shared/schema';
import { RouterOutputs } from '@shared/types/router.types';
import { CATEGORY_MAP } from '@shared/types/schema.types';
import { useDebounce } from '@uidotdev/usehooks';
import { Check, ChevronsUpDown, Loader2, Plus, Search, X } from 'lucide-react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

type HazardOption = RouterOutputs['hazards']['list']['result'][number];

type NewHazardOption = Pick<HazardOption, 'name' | 'type'>;

export const AsyncHazardsMultiSelect = ({
  onChange,
  onNewValueChange,
  className,
  value: selected = null,
  newValue = null,
  placeholder = 'Select hazards...',
  disabled = false,
  ...props
}: {
  value?: HazardOption['id'][] | null;
  newValue?: NewHazardOption[] | null;
  onChange: (selected: HazardOption['id'][] | undefined) => void;
  onNewValueChange?: (newValues: NewHazardOption[] | undefined) => void;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
}) => {
  const [open, setOpen] = useState(false);
  const [selectedHazards, setSelectedHazards] = useState<Record<string, HazardOption>>({});
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  const [search, setSearch] = useState('');
  const [customInput, setCustomInput] = useState('');
  const debouncedSearch = useDebounce(search, 300);

  const {
    data: hazards,
    isLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = useInfiniteHazards({
    filters: {
      search: debouncedSearch,
    },
  });

  const options = useMemo(() => {
    return (
      hazards?.reduce(
        (acc, hazard) => {
          acc[hazard.id] = hazard;
          return acc;
        },
        {} as Record<string, HazardOption>,
      ) || {}
    );
  }, [hazards]);

  // Computed value for selected hazard objects
  const selectedOptions = useMemo(() => {
    if (!Array.isArray(selected)) return [];
    return selected.map((id) => selectedHazards[id] || options[id]).filter(Boolean);
  }, [selected, selectedHazards, options]);

  // Custom hazards that will be created
  const customHazards = useMemo(() => {
    return newValue || [];
  }, [newValue]);

  const handleUnselect = useCallback(
    (hazardId: HazardOption['id']) => {
      if (disabled) return;

      const updatedSelectedIds = (selected || []).filter((id) => id !== hazardId);
      onChange(updatedSelectedIds.length > 0 ? updatedSelectedIds : undefined);

      // Remove from selected hazards when unselected
      setSelectedHazards((prev) => {
        const updated = { ...prev };
        delete updated[hazardId];
        return updated;
      });
    },
    [disabled, selected, onChange],
  );

  const handleUnselectCustom = useCallback(
    (customHazardName: string) => {
      if (disabled || !onNewValueChange) return;

      const updatedCustomHazards = customHazards.filter((hazard) => hazard.name !== customHazardName);
      onNewValueChange(updatedCustomHazards.length > 0 ? updatedCustomHazards : undefined);
    },
    [disabled, onNewValueChange, customHazards],
  );

  const handleAddCustomHazard = useCallback(
    (inputValue?: string) => {
      const valueToAdd = inputValue || search.trim();
      if (disabled || !onNewValueChange || !valueToAdd) return;

      // Check if it already exists in existing hazards
      const existsInHazards = hazards?.some((hazard) => hazard.name.toLowerCase() === valueToAdd.toLowerCase());

      // Check if it already exists in custom hazards
      const existsInCustom = customHazards.some((hazard) => hazard.name.toLowerCase() === valueToAdd.toLowerCase());

      if (!existsInHazards && !existsInCustom) {
        const newHazard = { name: valueToAdd, type: hazardCategoryEnum.enumValues[17] };
        const updatedCustomHazards = [...customHazards, newHazard];
        onNewValueChange(updatedCustomHazards);
        if (inputValue) {
          setCustomInput('');
        } else {
          setSearch('');
        }
      }
    },
    [disabled, onNewValueChange, search, hazards, customHazards],
  );

  const handleAddFromCustomInput = useCallback(() => {
    if (customInput.trim()) {
      handleAddCustomHazard(customInput.trim());
    }
  }, [customInput, handleAddCustomHazard]);

  // Store hazard data as it gets loaded to preserve across searches
  useEffect(() => {
    if (hazards && hazards.length > 0) {
      setSelectedHazards((prev) => {
        const updated = { ...prev };
        hazards.forEach((hazard) => {
          updated[hazard.id] = hazard;
        });
        return updated;
      });
    }
  }, [hazards]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (disabled) return;

      if (e.key === 'Escape') {
        setOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [disabled]);

  // Handle infinite scrolling
  useEffect(() => {
    if (!open || !hasNextPage) return;

    let cleanup: (() => void) | undefined;

    // Use a timeout to ensure the PopoverContent is fully rendered
    const timeoutId = window.setTimeout(() => {
      const scrollArea = scrollAreaRef.current;
      if (!scrollArea) return;

      let scrollTimeoutId: number;

      const handleScroll = () => {
        clearTimeout(scrollTimeoutId);
        scrollTimeoutId = window.setTimeout(() => {
          const { scrollTop, scrollHeight, clientHeight } = scrollArea;
          const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

          if (distanceFromBottom < 50 && !isFetchingNextPage) {
            fetchNextPage();
          }
        }, 100);
      };

      scrollArea.addEventListener('scroll', handleScroll, { passive: true });

      cleanup = () => {
        scrollArea.removeEventListener('scroll', handleScroll);
        clearTimeout(scrollTimeoutId);
      };
    }, 100);

    return () => {
      clearTimeout(timeoutId);
      cleanup?.();
    };
  }, [open, hasNextPage, fetchNextPage, isFetchingNextPage]);

  const handleSearchChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      if (disabled) return;
      setSearch(event.target.value);
    },
    [disabled],
  );

  const handleSearchKeyDown = useCallback(
    (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (disabled) return;

      if (event.key === 'Enter' && search.trim() && onNewValueChange) {
        event.preventDefault();
        handleAddCustomHazard();
      }
    },
    [disabled, search, onNewValueChange, handleAddCustomHazard],
  );

  const handleCustomInputKeyDown = useCallback(
    (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (disabled) return;

      if (event.key === 'Enter' && customInput.trim() && onNewValueChange) {
        event.preventDefault();
        handleAddFromCustomInput();
      }
    },
    [disabled, customInput, onNewValueChange, handleAddFromCustomInput],
  );

  const onSelect = useCallback(
    (hazardId: HazardOption['id']) => {
      if (!onChange || disabled) return;

      const currentSelected = selected || [];
      const isSelected = currentSelected.includes(hazardId);

      const updatedSelected = isSelected
        ? currentSelected.filter((id) => id !== hazardId)
        : [...currentSelected, hazardId];

      onChange(updatedSelected.length > 0 ? updatedSelected : undefined);
      setOpen(true);
    },
    [onChange, disabled, selected],
  );

  const renderSelectedValue = () => {
    const totalCount = selectedOptions.length + customHazards.length;

    if (totalCount === 0) {
      return <span className="text-muted-foreground">{placeholder}</span>;
    }

    return (
      <span className="text-sm">
        {totalCount} hazard{totalCount === 1 ? '' : 's'} selected
      </span>
    );
  };

  const renderSelectedItems = () => {
    if (selectedOptions.length === 0 && customHazards.length === 0) {
      return null;
    }

    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-2">
        <div className="text-xs font-medium mb-2 text-red-800">Selected Hazard(s):</div>
        <div className="flex flex-wrap gap-1">
          {selectedOptions.map((hazard) => {
            const displayType = CATEGORY_MAP[hazard.type] || hazard.type;
            return (
              <div
                key={hazard.id}
                className="inline-flex items-center gap-1 px-2 py-1 rounded text-xs bg-white/50 text-red-800"
              >
                <span>
                  {hazard.name} ({displayType})
                </span>
                <Button
                  variant="ghost"
                  size="xs"
                  className="border-none p-0 h-auto w-auto ml-1"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleUnselect(hazard.id);
                    }
                  }}
                  onMouseDown={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleUnselect(hazard.id);
                  }}
                >
                  <X size={12} className="text-red-600" />
                </Button>
              </div>
            );
          })}
          {customHazards.map((hazard) => {
            const customType = CATEGORY_MAP[hazard?.type as keyof typeof CATEGORY_MAP] || 'Custom';

            return (
              <div
                key={`custom-${hazard.name}`}
                className="inline-flex items-center gap-1 px-2 py-1 rounded text-xs bg-white/50 text-red-800"
              >
                <span>
                  {hazard.name} ({customType})
                </span>
                <Button
                  variant="ghost"
                  size="xs"
                  className="border-none p-0 h-auto w-auto ml-1"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleUnselectCustom(hazard.name);
                    }
                  }}
                  onMouseDown={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleUnselectCustom(hazard.name);
                  }}
                >
                  <X size={12} className="text-red-600" />
                </Button>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div>
      {renderSelectedItems()}
      <Popover open={open} onOpenChange={setOpen} modal>
        <PopoverTrigger asChild className={className}>
          <div
            {...props}
            role="combobox"
            aria-expanded={open}
            aria-disabled={disabled}
            aria-haspopup="listbox"
            aria-label={
              selectedOptions.length > 0 || customHazards.length > 0
                ? `Selected hazards: ${[...selectedOptions.map((hazard) => hazard.name), ...customHazards.map((hazard) => hazard.name)].join(', ')}`
                : placeholder
            }
            className={cn(
              'group flex w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background transition-[color,box-shadow] placeholder:text-muted-foreground focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:bg-input/30',
              open && !disabled && 'border-ring ring-[3px] ring-ring/50',
              'aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40',
              'h-9',
              disabled && 'cursor-not-allowed opacity-50',
              'cursor-pointer',
            )}
            onClick={() => !disabled && setOpen(!open)}
          >
            <div className="flex items-center gap-2 flex-1 min-w-0">{renderSelectedValue()}</div>

            <div className="flex items-center gap-2 flex-shrink-0">
              {isLoading && <Loader2 className="animate-spin size-4" />}
              <ChevronsUpDown className="size-4 shrink-0 opacity-50" />
            </div>
          </div>
        </PopoverTrigger>
        <PopoverPortal>
          <PopoverContent avoidCollisions className="w-[var(--radix-popover-trigger-width)] p-0">
            <div>
              <div className="flex items-center p-2 pl-4">
                <Search size={15} />
                <input
                  className="w-full border-none px-2 py-1 outline-hidden placeholder:text-muted-foreground placeholder:text-sm text-sm focus-visible:outline-hidden disabled:cursor-not-allowed disabled:opacity-50"
                  value={search}
                  placeholder="Search hazards..."
                  onChange={handleSearchChange}
                  onKeyDown={handleSearchKeyDown}
                  disabled={disabled}
                />
              </div>
              {onNewValueChange && (
                <>
                  <Separator />
                  <div className="flex items-center p-2 pl-4 gap-2">
                    <input
                      className="flex-1 border-none px-2 py-1 outline-hidden placeholder:text-muted-foreground focus-visible:outline-hidden disabled:cursor-not-allowed disabled:opacity-50 text-sm bg-muted/30 rounded"
                      value={customInput}
                      placeholder="Add custom hazard..."
                      onChange={(e) => setCustomInput(e.target.value)}
                      onKeyDown={handleCustomInputKeyDown}
                      disabled={disabled}
                    />
                    <Button
                      variant="ghost"
                      size="xs"
                      className="border-none p-1 h-6 w-6"
                      onClick={handleAddFromCustomInput}
                      disabled={disabled || !customInput.trim()}
                    >
                      <Plus size={14} />
                    </Button>
                  </div>
                </>
              )}
              <Separator />
              <div ref={scrollAreaRef} className="p-2 overflow-auto" style={{ height: 300 }}>
                {Object.keys(options).length === 0 && !isLoading && (
                  <div className="p-2 text-sm text-muted-foreground text-center">No hazards found</div>
                )}

                {Object.values(options).map((hazard) => {
                  const isSelected = selected?.includes(hazard.id) ?? false;
                  return (
                    <div
                      key={hazard.id}
                      onClick={() => !disabled && onSelect(hazard.id)}
                      className={cn(
                        'flex cursor-pointer items-center rounded-md p-2 text-sm hover:bg-secondary/80 gap-3',
                        disabled && 'cursor-not-allowed opacity-50 hover:bg-transparent',
                      )}
                    >
                      <Check className={cn('size-4 flex-shrink-0', isSelected ? 'opacity-100' : 'opacity-0')} />
                      <div className="flex flex-col">
                        <span>{hazard.name}</span>
                        <span className="text-xs text-muted-foreground">{CATEGORY_MAP[hazard.type]}</span>
                      </div>
                    </div>
                  );
                })}

                {isFetchingNextPage && (
                  <div className="flex items-center justify-center p-2">
                    <Loader2 className="animate-spin size-4" />
                    <span className="ml-2 text-sm text-muted-foreground">Loading more...</span>
                  </div>
                )}

                {isLoading && !isFetchingNextPage && (
                  <div className="flex items-center justify-center p-2">
                    <Loader2 className="animate-spin size-4" />
                    <span className="ml-2 text-sm text-muted-foreground">Loading...</span>
                  </div>
                )}

                {/* Add custom hazard option */}
                {search.trim() &&
                  onNewValueChange &&
                  !hazards?.some((h) => h.name.toLowerCase() === search.trim().toLowerCase()) &&
                  !customHazards.some((h) => h.name.toLowerCase() === search.trim().toLowerCase()) && (
                    <>
                      <Separator className="my-2" />
                      <div
                        onClick={() => !disabled && handleAddCustomHazard()}
                        className={cn(
                          'flex cursor-pointer items-center rounded-md p-2 text-sm hover:bg-secondary/80 gap-3',
                          disabled && 'cursor-not-allowed opacity-50 hover:bg-transparent',
                        )}
                      >
                        <Plus className="size-4 flex-shrink-0" />
                        <div className="flex flex-col">
                          <span>Add custom hazard</span>
                          <span className="text-xs text-muted-foreground">"{search.trim()}"</span>
                        </div>
                      </div>
                    </>
                  )}

                {/* Custom hazards section */}
                {customHazards.length > 0 && (
                  <>
                    <Separator className="my-2" />
                    <div className="px-2 py-1 text-xs font-medium text-muted-foreground">Custom Hazards</div>
                    {customHazards.map((hazard) => (
                      <div key={`custom-${hazard.name}`} className="flex items-center rounded-md p-2 text-sm gap-3">
                        <Check className="size-4 flex-shrink-0 opacity-100" />
                        <div className="flex flex-col flex-1">
                          <span>{hazard.name}</span>
                          <span className="text-xs text-muted-foreground">{CATEGORY_MAP[hazard.type]}</span>
                        </div>
                        <Button
                          variant="ghost"
                          size="xs"
                          className="border-none"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleUnselectCustom(hazard.name);
                          }}
                        >
                          <X size={14} className="text-muted-foreground" />
                        </Button>
                      </div>
                    ))}
                  </>
                )}
              </div>
            </div>
          </PopoverContent>
        </PopoverPortal>
      </Popover>
    </div>
  );
};
