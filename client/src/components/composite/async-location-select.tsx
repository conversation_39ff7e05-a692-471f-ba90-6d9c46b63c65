import { Popover, PopoverContent, PopoverPortal, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { useAppContext } from '@/contexts/app-context';
import { useInfiniteLocationsPublic } from '@/hooks/use-paginated-data';
import { cn } from '@/lib/utils';
import { RouterOutputs } from '@shared/types/router.types';
import { useDebounce } from '@uidotdev/usehooks';
import { Check, ChevronsUpDown, Loader2, Search, X } from 'lucide-react';
import { ChangeEvent, useCallback, useEffect, useMemo, useRef, useState } from 'react';

type LocationOption = RouterOutputs['location']['searchPublic']['result'][number];

type AsyncLocationSelectProps = {
  value?: LocationOption['id'] | null;
  onChange: (selected: LocationOption['id'] | null) => void;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
  mustIncludeObjectIds?: LocationOption['id'][];
  upkeepCompanyId?: string | null;
};

export const AsyncLocationSelect = ({
  onChange,
  className,
  value: selected = null,
  placeholder = 'Select location...',
  disabled = false,
  mustIncludeObjectIds,
  upkeepCompanyId,
  ...props
}: AsyncLocationSelectProps) => {
  const { user } = useAppContext();
  const [open, setOpen] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<LocationOption | null>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  const [search, setSearch] = useState('');
  const debouncedSearch = useDebounce(search, 300);

  const upkeepCompanyIdValue = upkeepCompanyId ?? user?.upkeepCompanyId;

  const {
    data: locations,
    isLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = useInfiniteLocationsPublic({
    search: debouncedSearch,
    upkeepCompanyId: upkeepCompanyIdValue!,
    mustIncludeObjectIds,
    enabled: !!upkeepCompanyIdValue,
  });

  const options = useMemo(() => {
    return (
      locations?.reduce(
        (acc, location) => {
          acc[location.id] = location;
          return acc;
        },
        {} as Record<string, LocationOption>,
      ) || {}
    );
  }, [locations]);

  const handleUnselect = useCallback(() => {
    if (disabled) return;
    onChange(null);
    setSelectedLocation(null);
  }, [disabled, onChange]);

  useEffect(() => {
    if (selected && options[selected] && selectedLocation?.id !== selected) {
      setSelectedLocation(options[selected]);
    }

    if (!selected && selectedLocation) {
      setSelectedLocation(null);
    }
  }, [selected, options, selectedLocation]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (disabled) return;

      if (e.key === 'Escape') {
        setOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [disabled]);

  // Handle infinite scrolling
  useEffect(() => {
    if (!open || !hasNextPage) return;

    let cleanup: (() => void) | undefined;

    // Use a timeout to ensure the PopoverContent is fully rendered
    const timeoutId = window.setTimeout(() => {
      const scrollArea = scrollAreaRef.current;
      if (!scrollArea) return;

      let scrollTimeoutId: number;

      const handleScroll = () => {
        clearTimeout(scrollTimeoutId);
        scrollTimeoutId = window.setTimeout(() => {
          const { scrollTop, scrollHeight, clientHeight } = scrollArea;
          const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

          if (distanceFromBottom < 50 && !isFetchingNextPage) {
            fetchNextPage();
          }
        }, 100);
      };

      scrollArea.addEventListener('scroll', handleScroll, { passive: true });

      cleanup = () => {
        scrollArea.removeEventListener('scroll', handleScroll);
        clearTimeout(scrollTimeoutId);
      };
    }, 100);

    return () => {
      clearTimeout(timeoutId);
      cleanup?.();
    };
  }, [open, hasNextPage, fetchNextPage, isFetchingNextPage]);

  const handleSearchChange = useCallback(
    (event: ChangeEvent<HTMLInputElement>) => {
      if (disabled) return;
      setSearch(event.target.value);
    },
    [disabled],
  );

  const onSelect = useCallback(
    (locationId: LocationOption['id']) => {
      if (!onChange || disabled) return;

      onChange(locationId);
      setSelectedLocation(options[locationId]);
      setOpen(false);
    },
    [onChange, disabled, options],
  );

  const renderSelectedValue = () => {
    if (selectedLocation) {
      return (
        <div className="flex items-center gap-2">
          <span className="text-sm">{selectedLocation.name}</span>
        </div>
      );
    }

    return <span className="text-muted-foreground">{placeholder}</span>;
  };

  const hasOptions = Object.keys(options).length > 0;

  return (
    <Popover open={open} onOpenChange={setOpen} modal>
      <PopoverTrigger asChild className={className}>
        <div
          {...props}
          role="combobox"
          aria-expanded={open}
          aria-disabled={disabled}
          aria-haspopup="listbox"
          aria-label={selectedLocation ? `Selected location: ${selectedLocation.name}` : placeholder}
          className={cn(
            'group flex h-9 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background transition-[color,box-shadow] placeholder:text-muted-foreground focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:bg-input/30',
            open && !disabled && 'border-ring ring-[3px] ring-ring/50',
            'aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40',
            disabled && 'cursor-not-allowed opacity-50',
            'cursor-pointer',
          )}
          onClick={() => !disabled && setOpen(!open)}
        >
          <div className="flex items-center gap-2 flex-1 min-w-0">{renderSelectedValue()}</div>

          <div className="flex items-center gap-2 flex-shrink-0">
            {selectedLocation?.id && (
              <X
                size={16}
                className="text-muted-foreground"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleUnselect();
                }}
              />
            )}
            {isLoading && <Loader2 className="animate-spin size-4" />}
            <ChevronsUpDown className="size-4 shrink-0 opacity-50" />
          </div>
        </div>
      </PopoverTrigger>
      <PopoverPortal>
        <PopoverContent avoidCollisions className="w-[var(--radix-popover-trigger-width)] p-0">
          <div>
            <div className="flex items-center p-2 pl-4">
              <Search size={15} />
              <input
                className="w-full border-none px-2 py-1 outline-hidden placeholder:text-muted-foreground placeholder:text-sm text-sm focus-visible:outline-hidden disabled:cursor-not-allowed disabled:opacity-50"
                value={search}
                placeholder="Search locations..."
                onChange={handleSearchChange}
                disabled={disabled}
              />
            </div>
            <Separator />
            <div ref={scrollAreaRef} className="p-2 overflow-auto" style={{ height: 200 }}>
              <ul>
                {!hasOptions && !isLoading && (
                  <li className="p-2 text-sm text-muted-foreground text-center">No locations found</li>
                )}
                {Object.values(options).map((location) => (
                  <li
                    onClick={() => !disabled && onSelect(location.id)}
                    className={cn(
                      'flex cursor-pointer items-center rounded-md p-2 text-sm hover:bg-secondary/80 gap-3',
                      disabled && 'cursor-not-allowed opacity-50 hover:bg-transparent',
                    )}
                    key={location.id}
                  >
                    <Check
                      className={cn('size-4 flex-shrink-0', selected === location.id ? 'opacity-100' : 'opacity-0')}
                    />
                    <span>{location.name}</span>
                  </li>
                ))}
                {isFetchingNextPage && (
                  <li className="flex items-center justify-center p-2">
                    <Loader2 className="animate-spin size-4" />
                    <span className="ml-2 text-sm text-muted-foreground">Loading more...</span>
                  </li>
                )}
              </ul>
            </div>
          </div>
        </PopoverContent>
      </PopoverPortal>
    </Popover>
  );
};
