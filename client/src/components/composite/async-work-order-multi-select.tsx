import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverPortal, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { useAppContext } from '@/contexts/app-context';
import { useInfiniteWorkOrders } from '@/hooks/use-paginated-data';
import { cn } from '@/lib/utils';
import { RouterOutputs } from '@shared/types/router.types';
import { WORK_ORDER_STATUS_MAP, WORK_ORDER_PRIORITY_MAP } from '@shared/types/work-orders.types';
import { useDebounce } from '@uidotdev/usehooks';
import { Check, ChevronsUpDown, Loader2, Search, X } from 'lucide-react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

type WorkOrderOption = RouterOutputs['workOrder']['search']['result'][number];

export const AsyncWorkOrderMultiSelect = ({
  onChange,
  className,
  value: selected = null,
  placeholder = 'Select Work Orders...',
  disabled = false,
  upkeepCompanyId,
  ...props
}: {
  value?: WorkOrderOption['id'][] | null;
  onChange: (selected: WorkOrderOption['id'][]) => void;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
  upkeepCompanyId?: string | null;
}) => {
  const { user } = useAppContext();
  const [open, setOpen] = useState(false);
  const [selectedWorkOrders, setSelectedWorkOrders] = useState<Record<string, WorkOrderOption>>({});
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  const [search, setSearch] = useState('');
  const debouncedSearch = useDebounce(search, 300);

  const {
    data: workOrders,
    isLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = useInfiniteWorkOrders({
    search: debouncedSearch,
    enabled: !!upkeepCompanyId || !!user?.upkeepCompanyId,
  });

  const options = useMemo(() => {
    return (
      workOrders?.reduce(
        (acc, workOrder) => {
          acc[workOrder.id] = workOrder;
          return acc;
        },
        {} as Record<string, WorkOrderOption>,
      ) || {}
    );
  }, [workOrders]);

  // Computed value for selected Work Order objects
  const selectedOptions = useMemo(() => {
    if (!Array.isArray(selected)) return [];
    return selected
      .filter((id): id is string => id !== null)
      .map((id) => selectedWorkOrders[id] || options[id])
      .filter(Boolean);
  }, [selected, selectedWorkOrders, options]);

  const handleUnselect = useCallback(
    (id: WorkOrderOption['id']) => {
      if (disabled || !id) return;

      const updatedSelectedIds = (selected || []).filter((selectedId) => selectedId !== id);
      onChange(updatedSelectedIds.length > 0 ? updatedSelectedIds : []);

      // Remove from selected Work Orders when unselected
      setSelectedWorkOrders((prev) => {
        const updated = { ...prev };
        delete updated[id];
        return updated;
      });
    },
    [disabled, selected, onChange],
  );

  // Store Work Order data as it gets loaded to preserve across searches
  useEffect(() => {
    if (workOrders && workOrders.length > 0) {
      setSelectedWorkOrders((prev) => {
        const updated = { ...prev };
        workOrders.forEach((workOrder) => {
          if (workOrder.id) {
            updated[workOrder.id] = workOrder;
          }
        });
        return updated;
      });
    }
  }, [workOrders]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (disabled) return;

      if (e.key === 'Escape') {
        setOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [disabled]);

  // Handle infinite scrolling
  useEffect(() => {
    if (!open || !hasNextPage) return;

    let cleanup: (() => void) | undefined;

    // Use a timeout to ensure the PopoverContent is fully rendered
    const timeoutId = setTimeout(() => {
      const scrollArea = scrollAreaRef.current;
      if (!scrollArea) return;

      let scrollTimeoutId: number;

      const handleScroll = () => {
        clearTimeout(scrollTimeoutId);
        scrollTimeoutId = window.setTimeout(() => {
          const { scrollTop, scrollHeight, clientHeight } = scrollArea;
          const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

          if (distanceFromBottom < 50 && !isFetchingNextPage) {
            fetchNextPage();
          }
        }, 100);
      };

      scrollArea.addEventListener('scroll', handleScroll, { passive: true });

      cleanup = () => {
        scrollArea.removeEventListener('scroll', handleScroll);
        clearTimeout(scrollTimeoutId);
      };
    }, 100);

    return () => {
      clearTimeout(timeoutId);
      cleanup?.();
    };
  }, [open, hasNextPage, fetchNextPage, isFetchingNextPage]);

  const handleSearchChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      if (disabled) return;
      setSearch(event.target.value);
    },
    [disabled],
  );

  const onSelect = useCallback(
    (id: WorkOrderOption['id']) => {
      if (!onChange || disabled || !id) return;

      const currentSelected = selected || [];
      const isSelected = currentSelected.includes(id);

      const updatedSelected = isSelected
        ? currentSelected.filter((selectedId) => selectedId !== id)
        : [...currentSelected, id];

      onChange(updatedSelected.length > 0 ? updatedSelected : []);
      setOpen(true);
    },
    [onChange, disabled, selected],
  );

  const renderSelectedValue = () => {
    if (selectedOptions.length === 0) {
      return <span className="text-muted-foreground">{placeholder}</span>;
    }

    return selectedOptions.map((workOrder) => (
      <Badge
        variant="outline"
        key={workOrder.id}
        className="flex items-center gap-1 pr-1 max-w-xs group-hover:bg-background"
      >
        <div className="flex flex-col min-w-0">
          <span className="font-semibold text-xs truncate">
            {workOrder.workOrderNumber}: {workOrder.title}
          </span>
          <span className="text-[10px] text-muted-foreground truncate">
            {WORK_ORDER_STATUS_MAP[workOrder.currentStatus] || workOrder.currentStatus} -{' '}
            {WORK_ORDER_PRIORITY_MAP[workOrder.priority] || workOrder.priority}
          </span>
        </div>
        <Button
          variant="ghost"
          size="xs"
          className="border-none ml-1"
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              handleUnselect(workOrder.id);
            }
          }}
          onMouseDown={(e) => {
            e.preventDefault();
            e.stopPropagation();
          }}
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            handleUnselect(workOrder.id);
          }}
        >
          <X size={12} className="text-muted-foreground" />
        </Button>
      </Badge>
    ));
  };

  return (
    <Popover open={open} onOpenChange={setOpen} modal>
      <PopoverTrigger asChild className={className}>
        <div
          {...props}
          role="combobox"
          aria-expanded={open}
          aria-disabled={disabled}
          aria-haspopup="listbox"
          aria-label={
            selectedOptions.length > 0
              ? `Selected Work Orders: ${selectedOptions.map((workOrder) => `${workOrder.workOrderNumber}: ${workOrder.title}`).join(', ')}`
              : placeholder
          }
          className={cn(
            'group flex w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background transition-[color,box-shadow] placeholder:text-muted-foreground focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:bg-input/30',
            open && !disabled && 'border-ring ring-[3px] ring-ring/50',
            'aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40',
            Array.isArray(selected) && selected.length > 1
              ? 'h-full min-h-[2.25rem]'
              : disabled && 'cursor-not-allowed opacity-50',
            'cursor-pointer',
          )}
          onClick={() => !disabled && setOpen(!open)}
        >
          <div className="flex items-center gap-2 flex-1 min-w-0 flex-wrap">{renderSelectedValue()}</div>

          <div className="flex items-center gap-2 flex-shrink-0">
            {isLoading && <Loader2 className="animate-spin size-4" />}
            <ChevronsUpDown className="size-4 shrink-0 opacity-50" />
          </div>
        </div>
      </PopoverTrigger>
      <PopoverPortal>
        <PopoverContent avoidCollisions className="w-[var(--radix-popover-trigger-width)] p-0">
          <div>
            <div className="flex items-center p-2 pl-4">
              <Search size={15} />
              <input
                className="w-full border-none px-2 py-1 outline-hidden placeholder:text-muted-foreground placeholder:text-sm text-sm focus-visible:outline-hidden disabled:cursor-not-allowed disabled:opacity-50"
                value={search}
                placeholder="Search Work Orders by number or title..."
                onChange={handleSearchChange}
                disabled={disabled}
              />
            </div>
            <Separator />
            <div ref={scrollAreaRef} className="p-2 overflow-auto" style={{ height: 200 }}>
              {Object.keys(options).length === 0 && !isLoading && (
                <div className="p-2 text-sm text-muted-foreground text-center">No Work Orders found</div>
              )}

              {Object.values(options).map((workOrder) => {
                const isSelected = selected?.includes(workOrder.id) ?? false;
                const isAssignedToCapa = workOrder.capaSlug || workOrder.capaTitle;
                const isDisabled = disabled || isAssignedToCapa;

                return (
                  <div
                    key={workOrder.id}
                    onClick={() => !isDisabled && onSelect(workOrder.id)}
                    className={cn(
                      'flex cursor-pointer items-center rounded-md p-2 text-sm hover:bg-secondary/80 gap-3',
                      isDisabled && 'cursor-not-allowed opacity-50 hover:bg-transparent',
                    )}
                  >
                    <Check className={cn('size-4 flex-shrink-0', isSelected ? 'opacity-100' : 'opacity-0')} />
                    <div className="flex flex-col flex-1">
                      <span className={cn('font-semibold', isAssignedToCapa && 'text-muted-foreground')}>
                        {workOrder.workOrderNumber}: {workOrder.title}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {WORK_ORDER_STATUS_MAP[workOrder.currentStatus] || workOrder.currentStatus} -{' '}
                        {WORK_ORDER_PRIORITY_MAP[workOrder.priority] || workOrder.priority}
                        {isAssignedToCapa && ` - assigned to ${workOrder.capaSlug} ${workOrder.capaTitle}`}
                      </span>
                    </div>
                  </div>
                );
              })}

              {isFetchingNextPage && (
                <div className="flex items-center justify-center p-2">
                  <Loader2 className="animate-spin size-4" />
                  <span className="ml-2 text-sm text-muted-foreground">Loading more...</span>
                </div>
              )}
            </div>
          </div>
        </PopoverContent>
      </PopoverPortal>
    </Popover>
  );
};
