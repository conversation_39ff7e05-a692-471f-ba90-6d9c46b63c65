import { ClipboardList } from 'lucide-react';

export const EditSopLoading = () => {
  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 md:px-8 lg:px-10 py-6">
      <div className="animate-pulse space-y-8">
        {/* Header section */}
        <div className="flex items-center gap-5">
          <div className="flex flex-col">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 bg-gray-200 rounded-lg">
                <ClipboardList className="w-6 h-6 text-gray-400" />
              </div>
              <div className="h-8 bg-gray-200 rounded w-32"></div>
            </div>
            <div className="h-4 bg-gray-200 rounded w-24"></div>
          </div>
        </div>

        {/* Step progress skeleton */}
        <div className="space-y-4">
          <div className="flex gap-2">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-2 bg-gray-200 rounded flex-1"></div>
            ))}
          </div>
        </div>

        {/* Content area skeleton */}
        <div className="bg-white rounded-lg border p-6 space-y-6">
          <div className="space-y-4">
            <div className="h-6 bg-gray-200 rounded w-1/4"></div>
            <div className="space-y-2">
              <div className="h-10 bg-gray-200 rounded w-full"></div>
              <div className="h-10 bg-gray-200 rounded w-full"></div>
              <div className="h-10 bg-gray-200 rounded w-3/4"></div>
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="h-6 bg-gray-200 rounded w-1/3"></div>
            <div className="space-y-2">
              <div className="h-20 bg-gray-200 rounded w-full"></div>
              <div className="h-20 bg-gray-200 rounded w-full"></div>
            </div>
          </div>
        </div>

        {/* Footer skeleton */}
        <div className="flex justify-between items-center pt-6 border-t">
          <div className="h-10 bg-gray-200 rounded w-20"></div>
          <div className="flex gap-3">
            <div className="h-10 bg-gray-200 rounded w-24"></div>
            <div className="h-10 bg-gray-200 rounded w-20"></div>
          </div>
        </div>
      </div>
    </div>
  );
};
