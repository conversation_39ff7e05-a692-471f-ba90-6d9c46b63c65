import { Button } from '@/components/ui/button';
import { FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';
import {
  closestCenter,
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { CreateSopType, SOP_SECTION_LABELS } from '@shared/types/sop.types';
import { GripVertical, Plus, Trash2 } from 'lucide-react';
import { useFieldArray, useFormContext } from 'react-hook-form';

type SortableEmergencyItemProps = {
  id: string;
  index: number;
  sectionIndex: number;
  onRemove: () => void;
  canRemove: boolean;
};

const SortableEmergencyItem = ({ id, index, sectionIndex, onRemove }: SortableEmergencyItemProps) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id });
  const form = useFormContext<CreateSopType>();

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn('flex items-start gap-3 p-4 bg-white border rounded-lg', isDragging && 'shadow-lg z-50')}
    >
      <div className="flex items-center gap-2 mt-1">
        <span className="text-sm font-medium text-red-600 bg-red-50 rounded-full w-6 h-6 flex items-center justify-center">
          {index + 1}
        </span>
        <Button
          type="button"
          variant="ghost"
          size="icon"
          {...attributes}
          {...listeners}
          className="cursor-grab active:cursor-grabbing size-6"
        >
          <GripVertical className="h-4 w-4 text-gray-400" />
        </Button>
      </div>

      <div className="flex-1 space-y-3">
        <FormField
          control={form.control}
          name={`sections.${sectionIndex}.label`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  placeholder="Emergency item title..."
                  className="font-medium"
                  {...field}
                  value={field.value || ''}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name={`sections.${sectionIndex}.value`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Textarea
                  placeholder="Detailed emergency instructions..."
                  className="min-h-[120px]"
                  {...field}
                  value={field.value || ''}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <Button
        type="button"
        variant="ghost"
        size="icon"
        onClick={onRemove}
        className="text-red-600 hover:text-red-700 hover:bg-red-50 size-8"
      >
        <Trash2 className="h-4 w-4" />
      </Button>
    </div>
  );
};

export const EmergencyPlan = () => {
  const form = useFormContext<CreateSopType>();

  const {
    fields: allSections,
    append,
    remove,
    move,
  } = useFieldArray({
    control: form.control,
    name: 'sections',
    keyName: 'fieldId',
  });

  // Filter sections for this specific type
  const emergencySections = allSections
    .map((section, index) => ({ ...section, originalIndex: index }))
    .filter((section) => form.watch(`sections.${section.originalIndex}.sectionType`) === 'emergency');

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  const addSection = () => {
    const currentTypeSections = form.watch('sections').filter((s) => s.sectionType === 'emergency');
    const nextSerial = Math.max(0, ...currentTypeSections.map((s) => s.serial)) + 1;

    // Default to first available emergency label or create custom
    const defaultLabel =
      SOP_SECTION_LABELS.emergency.find((label) => !emergencySections.some((section) => section.label === label)) ||
      'Custom Emergency Field';

    append({
      sectionType: 'emergency',
      serial: nextSerial,
      value: '',
      label: defaultLabel,
    });
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = emergencySections.findIndex((section) => section.fieldId === active.id);
      const newIndex = emergencySections.findIndex((section) => section.fieldId === over?.id);

      if (oldIndex !== -1 && newIndex !== -1) {
        const oldOriginalIndex = emergencySections[oldIndex].originalIndex;
        const newOriginalIndex = emergencySections[newIndex].originalIndex;
        move(oldOriginalIndex, newOriginalIndex);
      }
    }
  };

  // Initialize with default emergency fields if none exist
  const initializeDefaultFields = () => {
    const defaultFields = [
      'Potential Emergencies',
      'Emergency Contacts',
      'Evacuation Procedures',
      'First Aid Response',
      'Incident Reporting Process',
    ];

    defaultFields.forEach((label, index) => {
      append({
        sectionType: 'emergency',
        serial: index + 1,
        value: '',
        label: label,
      });
    });
  };

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">Emergency Plan</h2>
        <p className="text-gray-600">Define emergency procedures and response protocols.</p>
      </div>

      <div className="space-y-6">
        <div className={`border rounded-lg border-red-200 bg-red-50/30`}>
          <div className="p-6 border-b">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-1">Emergency Fields</h3>
                <p className="text-sm text-gray-600">
                  Each field represents a critical aspect of emergency preparedness for this SOP.
                </p>
              </div>
              <Button type="button" variant="outline" size="sm" onClick={addSection}>
                <Plus className="h-4 w-4 mr-1" />
                Add Emergency Field
              </Button>
            </div>
          </div>

          <div className="p-6">
            {emergencySections.length === 0 ? (
              <div className="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                <div className="space-y-4">
                  <p className="text-gray-500">No emergency procedures defined yet</p>
                  <Button type="button" onClick={initializeDefaultFields}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Default Emergency Fields
                  </Button>
                </div>
              </div>
            ) : (
              <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
                <SortableContext
                  items={emergencySections.map((section) => section.fieldId)}
                  strategy={verticalListSortingStrategy}
                >
                  <div className="space-y-3">
                    {emergencySections.map((section, index) => (
                      <SortableEmergencyItem
                        key={section.fieldId}
                        id={section.fieldId}
                        index={index}
                        sectionIndex={section.originalIndex}
                        onRemove={() => remove(section.originalIndex)}
                        canRemove={emergencySections.length > 1}
                      />
                    ))}
                  </div>
                </SortableContext>
              </DndContext>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
