import { Button } from '@/components/ui/button';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Textarea } from '@/components/ui/textarea';
import { BookOpenIcon } from 'lucide-react';
import { useState } from 'react';

type SopReasonForRevisionProps = {
  isOpen: boolean;
  onClose: () => void;
  onSave: (reason: string) => void;
  isLoading?: boolean;
};

export const SopReasonForRevision = ({ isOpen, onClose, onSave, isLoading = false }: SopReasonForRevisionProps) => {
  const [reason, setReason] = useState('');

  const handleSave = () => {
    if (reason.trim()) {
      onSave(reason);
      onClose();
    }
  };

  const handleClose = () => {
    setReason(''); // Reset when closing
    onClose();
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={handleClose}>
      <AlertDialogContent className="sm:max-w-lg">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <BookOpenIcon className="h-5 w-5 text-blue-600" />
            Save Revision Notes
          </AlertDialogTitle>
          <AlertDialogDescription>
            Provide a brief description of the changes made to this SOP for audit trail purposes.
          </AlertDialogDescription>
        </AlertDialogHeader>

        <div className="space-y-4">
          <div>
            <label htmlFor="reason" className="block text-sm font-medium text-gray-900 mb-2">
              Reason for Change <span className="text-red-500">*</span>
            </label>
            <Textarea
              id="reason"
              placeholder="e.g., Updated PPE requirements, Added new hazard identification, Corrected step descriptions, etc."
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className="min-h-[120px] resize-none"
            />
          </div>
        </div>

        <AlertDialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={!reason.trim() || isLoading}>
            {isLoading ? 'Saving Changes...' : 'Save Revision'}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
