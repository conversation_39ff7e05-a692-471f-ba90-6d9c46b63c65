import { AsyncAssetMultiSelect } from '@/components/composite/async-asset-multi-select';
import { AsyncLocationSelect } from '@/components/composite/async-location-select';
import { AsyncUserSelect } from '@/components/composite/async-user-select';
import { Checkbox } from '@/components/ui/checkbox';
import { DateTimePicker } from '@/components/ui/date-time-picker';
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { CreateSopType } from '@shared/types/sop.types';
import { addYears } from 'date-fns';
import { InfoIcon } from 'lucide-react';
import { useFormContext } from 'react-hook-form';

type FormType = CreateSopType;

export const MetadataStep = () => {
  const form = useFormContext<FormType>();

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">Metadata</h2>
        <p className="text-gray-600">Complete the final details and settings for your Standard Operating Procedure.</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-start">
        {/* Owner */}
        <FormField
          control={form.control}
          name="sop.ownerId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Owner <span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <AsyncUserSelect
                  {...field}
                  placeholder="Select SOP owner..."
                  value={field.value}
                  onChange={field.onChange}
                  excludeViewOnly={true}
                />
              </FormControl>
              <FormDescription>The person responsible for maintaining this SOP</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Approver */}
        <FormField
          control={form.control}
          name="sop.approverId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Approver <span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <AsyncUserSelect
                  placeholder="Select approver..."
                  value={field.value}
                  onChange={field.onChange}
                  excludeViewOnly={true}
                />
              </FormControl>
              <FormDescription>The supervisor or manager who will approve this SOP</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        {/* Location, Assets, and Review Date - Responsive grid */}
        <FormField
          control={form.control}
          name="sop.locationId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Location</FormLabel>
              <FormControl>
                <AsyncLocationSelect
                  value={field.value}
                  onChange={field.onChange}
                  placeholder="Where is this job performed?"
                />
              </FormControl>
              <FormDescription>Specific area, building, or department where the job is performed</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="sop.assetIds"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Equipment/Assets</FormLabel>
              <FormControl>
                <AsyncAssetMultiSelect
                  {...field}
                  value={field.value || []}
                  onChange={field.onChange}
                  placeholder="Select equipment used"
                  locationId={form.watch('sop.locationId') ?? undefined}
                />
              </FormControl>
              <FormDescription>Equipment, tools, or assets involved in this job</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="sop.reviewDate"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Next Review Date</FormLabel>
              <FormControl>
                <DateTimePicker
                  selected={field.value || undefined}
                  onSelect={field.onChange}
                  disabled={{ before: new Date() }}
                  onlyDate
                  startMonth={new Date()}
                  endMonth={addYears(new Date(), 5)}
                />
              </FormControl>
              <FormDescription>When should this JHA be reviewed next?</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Additional Notes */}
      <FormField
        control={form.control}
        name="sop.notes"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Additional Notes</FormLabel>
            <FormControl>
              <Textarea
                placeholder="Any additional information or context about this SOP..."
                className="min-h-[100px]"
                {...field}
                value={field.value || ''}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Public Visibility */}
      <FormField
        control={form.control}
        name="sop.isPublic"
        render={({ field }) => (
          <FormItem className="flex items-start space-x-3 space-y-0">
            <FormControl>
              <Checkbox checked={field.value ?? false} onCheckedChange={field.onChange} />
            </FormControl>
            <div className="space-y-1 leading-none">
              <div className="flex items-center gap-2">
                <FormLabel>Make Public</FormLabel>
                <Tooltip>
                  <TooltipTrigger>
                    <InfoIcon className="w-4 h-4 text-gray-400" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <div className="max-w-xs">
                      <strong>Public Visibility</strong>
                      <br />
                      Checking this box will make this SOP accessible in a read-only format to all active users within
                      your organization, including those with 'View-Only' access.
                      <br />
                      <br />
                      <strong>Note:</strong> This will not affect the visibility of the SOP to the owner or approver.
                    </div>
                  </TooltipContent>
                </Tooltip>
              </div>
              <FormDescription>Allow all organization members to view this SOP in read-only mode</FormDescription>
            </div>
          </FormItem>
        )}
      />
    </div>
  );
};
