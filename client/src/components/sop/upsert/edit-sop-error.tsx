import { ROUTES } from '@shared/ROUTE_PATHS';
import { AlertCircle } from 'lucide-react';
import { useLocation } from 'wouter';

type EditSopErrorProps = {
  error?: { message: string } | null;
  title?: string;
  description?: string;
};

export const EditSopError = ({ 
  error, 
  title = "SOP Not Found",
  description = "The SOP you're looking for doesn't exist or you don't have permission to view it."
}: EditSopErrorProps) => {
  const [_, navigate] = useLocation();

  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 md:px-8 lg:px-10 py-6">
      <div className="text-center py-12">
        <div className="flex justify-center mb-4">
          <div className="p-3 bg-red-100 rounded-full">
            <AlertCircle className="w-8 h-8 text-red-600" />
          </div>
        </div>
        
        <h2 className="text-2xl font-bold text-gray-900 mb-4">{title}</h2>
        <p className="text-gray-600 mb-6">
          {description}
        </p>
        
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg text-left max-w-md mx-auto">
            <p className="text-sm text-red-800 font-medium">Error details:</p>
            <p className="text-sm text-red-700 mt-1">{error.message}</p>
          </div>
        )}
        
        <button
          onClick={() => navigate(ROUTES.SOP_LIST)}
          className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium"
        >
          Back to SOP List
        </button>
      </div>
    </div>
  );
};
