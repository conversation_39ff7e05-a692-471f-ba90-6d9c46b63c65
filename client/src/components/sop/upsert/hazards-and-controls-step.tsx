import { AsyncControlMeasuresMultiSelect } from '@/components/composite/async-control-measures-multi-select';
import { AsyncHazardsMultiSelect } from '@/components/composite/async-hazards-multi-select';
import { JhaRiskScoreBadge } from '@/components/jha/jha-risk-badge';
import { LikelihoodSelect } from '@/components/jha/likelihood-select';
import { SeveritySelect } from '@/components/jha/severity-select';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { Textarea } from '@/components/ui/textarea';
import { Toolt<PERSON>, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import {
  closestCenter,
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { CreateSopType, UpdateSopType } from '@shared/types/sop.types';
import { GripVertical, InfoIcon, Trash2 } from 'lucide-react';
import { useFieldArray, useFormContext } from 'react-hook-form';

const SortableStepRow = ({
  id,
  stepNumber,
  stepIndex,
  riskScore,
  canRemove,
  onRemove,
  totalHazardsSelected,
  totalControlMeasuresSelected,
}: {
  id: string;
  stepNumber: number;
  stepIndex: number;
  riskScore: number;
  canRemove: boolean;
  onRemove: () => void;
  totalHazardsSelected: number;
  totalControlMeasuresSelected: number;
}) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id });
  const form = useFormContext<CreateSopType>();

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        'flex items-center pl-4 py-2 hover:bg-gray-50/50 transition-colors',
        isDragging && 'z-50 bg-white shadow-lg rounded-lg border',
      )}
    >
      {/* Controls Section */}
      <div className="flex items-center gap-1">
        <AccordionTrigger className="hover:no-underline h-4 w-4 flex items-center justify-center" />

        <Button
          type="button"
          variant="ghost"
          size="icon"
          {...attributes}
          {...listeners}
          className="cursor-grab active:cursor-grabbing size-6"
        >
          <GripVertical className="h-4 w-4 text-gray-400" />
        </Button>

        <Button type="button" variant="ghost" size="icon" disabled={!canRemove} onClick={onRemove} className="size-6">
          <Trash2 className="h-4 w-4 text-red-600 hover:text-red-700 hover:bg-red-50" />
        </Button>
      </div>

      {/* Step Number */}
      <div className="flex justify-center items-center w-8">
        <span className="font-medium text-sm">{stepNumber}</span>
      </div>

      {/* Step Title */}
      <div className="w-64 flex-1 px-2">
        <FormField
          control={form.control}
          name={`sections.${stepIndex}.label`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  placeholder="Enter step title..."
                  className="border-0 bg-transparent p-0 text-sm focus-visible:ring-0 w-full border-b border-gray-300 rounded-none focus-visible:border-blue-500"
                  {...field}
                  value={field.value ?? ''}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Potential Hazards */}
      <div className="w-22 px-2">
        <div className="text-sm text-gray-500 truncate">
          {totalHazardsSelected > 0 ? `${totalHazardsSelected} selected` : 'None'}
        </div>
      </div>

      {/* Severity */}
      <div className="w-44 px-2">
        <FormField
          control={form.control}
          name={`sections.${stepIndex}.severity`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <SeveritySelect
                  value={field.value || 1}
                  onSelect={(value: string) => field.onChange(parseInt(value, 10))}
                />
              </FormControl>
            </FormItem>
          )}
        />
      </div>

      {/* Likelihood */}
      <div className="w-44 px-2">
        <FormField
          control={form.control}
          name={`sections.${stepIndex}.likelihood`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <LikelihoodSelect
                  value={field.value || 1}
                  onSelect={(value: string) => field.onChange(parseInt(value, 10))}
                />
              </FormControl>
            </FormItem>
          )}
        />
      </div>

      {/* Risk Score */}
      <div className="w-36 flex justify-center px-2">
        <JhaRiskScoreBadge score={riskScore} />
      </div>

      {/* Control Measures */}
      <div className="w-22 px-2">
        <div className="text-sm text-gray-500 truncate">
          {totalControlMeasuresSelected > 0 ? `${totalControlMeasuresSelected} selected` : 'None'}
        </div>
      </div>
    </div>
  );
};

export const HazardsAndControlsStep = () => {
  const form = useFormContext<CreateSopType | UpdateSopType>();

  const {
    fields: allFields,
    append,
    remove,
    move,
  } = useFieldArray({
    control: form.control,
    name: 'sections',
  });

  // Filter sections to only show 'step' type sections
  const stepSections = allFields.filter((field, index) => {
    const sectionType = form.watch(`sections.${index}.sectionType`);
    return sectionType === 'step';
  });

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = stepSections.findIndex((field) => field.id === active.id);
      const newIndex = stepSections.findIndex((field) => field.id === over?.id);
      move(oldIndex, newIndex);
    }
  };

  const addStepSection = () => {
    const nextSerial = stepSections.length + 1;
    append({
      sectionType: 'step',
      serial: nextSerial,
      label: '',
      value: '',
      hazardIds: [],
      controlMeasureIds: [],
      hazardsToCreate: [],
      controlMeasuresToCreate: [],
      severity: 1,
      likelihood: 1,
    });
  };

  return (
    <div>
      <div className="border border-gray-200 rounded-lg overflow-hidden">
        <ScrollArea className="min-h-96">
          {/* Table Header */}
          <div className="bg-gray-100 border-b border-gray-300">
            <div className="flex items-center py-4 pl-8 font-medium text-sm text-gray-800">
              <div className="w-14" />
              <div className="w-8 text-center">#</div>
              <div className="w-64 flex-1 px-2">
                Title <span className="text-red-500">*</span>
              </div>
              <div className="w-22 px-2">Hazard(s)</div>
              <div className="w-44 px-2">Severity</div>
              <div className="w-44 px-2">Likelihood</div>
              <div className="w-36 px-2 flex justify-center items-center gap-1">
                <span className="hidden sm:inline">Initial Risk Score</span>
                <span className="sm:hidden">Risk</span>
                <Tooltip>
                  <TooltipTrigger>
                    <InfoIcon className="w-3 h-3" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <strong>Risk Score Calculation</strong>
                    <br />
                    Risk Score = Severity (1-5) × Likelihood (1-5)
                    <br />
                    Risk Levels:
                    <br />
                    • Low: 1-5
                    <br />
                    • Medium: 6-14
                    <br />• High: 15-25
                  </TooltipContent>
                </Tooltip>
              </div>
              <div className="w-22 px-2">Control(s)</div>
            </div>
          </div>

          {/* Content */}
          <div className="bg-white">
            <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
              <Accordion type="multiple" defaultValue={[`step-${stepSections.at(0)?.id}`]}>
                <SortableContext items={stepSections.map((field) => field.id)} strategy={verticalListSortingStrategy}>
                  {stepSections.map((field, index) => {
                    // Find the actual index in the allFields array
                    const actualIndex = allFields.findIndex((f) => f.id === field.id);
                    const stepNumber = index + 1;
                    const severity = form.watch(`sections.${actualIndex}.severity`) || 1;
                    const likelihood = form.watch(`sections.${actualIndex}.likelihood`) || 1;
                    const riskScore = severity * likelihood;
                    const totalHazardsSelected =
                      (form.watch(`sections.${actualIndex}.hazardIds`)?.length ?? 0) +
                      (form.watch(`sections.${actualIndex}.hazardsToCreate`)?.length ?? 0);
                    const totalControlMeasuresSelected =
                      (form.watch(`sections.${actualIndex}.controlMeasureIds`)?.length ?? 0) +
                      (form.watch(`sections.${actualIndex}.controlMeasuresToCreate`)?.length ?? 0);

                    return (
                      <AccordionItem key={field.id} value={`step-${field.id}`} className="border-b border-gray-200">
                        <SortableStepRow
                          id={field.id}
                          stepNumber={stepNumber}
                          stepIndex={actualIndex}
                          riskScore={riskScore}
                          canRemove={stepSections.length > 1}
                          onRemove={() => remove(actualIndex)}
                          totalHazardsSelected={totalHazardsSelected}
                          totalControlMeasuresSelected={totalControlMeasuresSelected}
                        />

                        <AccordionContent className="pt-0 pb-6">
                          <div className="px-4 py-5 bg-gray-50/50 border-t border-gray-100">
                            <div className="flex flex-col gap-4">
                              <FormField
                                control={form.control}
                                name={`sections.${actualIndex}.value`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Step Description</FormLabel>
                                    <FormControl>
                                      <Textarea
                                        className="col-span-2"
                                        placeholder="Enter additional details and context for this step..."
                                        {...field}
                                        value={field.value ?? ''}
                                      />
                                    </FormControl>
                                  </FormItem>
                                )}
                              />
                              <div className="flex items-start w-full gap-5">
                                <FormField
                                  control={form.control}
                                  name={`sections.${actualIndex}.hazardIds`}
                                  render={({ field: hazardIdsField }) => (
                                    <FormField
                                      control={form.control}
                                      name={`sections.${actualIndex}.hazardsToCreate`}
                                      render={({ field: hazardsToCreateField }) => (
                                        <FormItem className="flex-1">
                                          <FormLabel>Hazards</FormLabel>
                                          <FormControl>
                                            <AsyncHazardsMultiSelect
                                              className="flex-1"
                                              value={hazardIdsField.value}
                                              onChange={hazardIdsField.onChange}
                                              onNewValueChange={hazardsToCreateField.onChange}
                                              newValue={hazardsToCreateField.value}
                                            />
                                          </FormControl>
                                        </FormItem>
                                      )}
                                    />
                                  )}
                                />

                                <FormField
                                  control={form.control}
                                  name={`sections.${actualIndex}.controlMeasureIds`}
                                  render={({ field: controlMeasureIdsField }) => (
                                    <FormField
                                      control={form.control}
                                      name={`sections.${actualIndex}.controlMeasuresToCreate`}
                                      render={({ field: controlMeasuresToCreateField }) => (
                                        <FormItem className="flex-1">
                                          <FormLabel>Control Measures</FormLabel>
                                          <FormControl>
                                            <AsyncControlMeasuresMultiSelect
                                              className="flex-1"
                                              value={controlMeasureIdsField.value}
                                              onChange={controlMeasureIdsField.onChange}
                                              onNewValueChange={controlMeasuresToCreateField.onChange}
                                              newValue={controlMeasuresToCreateField.value}
                                            />
                                          </FormControl>
                                        </FormItem>
                                      )}
                                    />
                                  )}
                                />
                              </div>
                            </div>
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    );
                  })}
                </SortableContext>
              </Accordion>
            </DndContext>
          </div>

          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </div>

      <Button type="button" variant="outline" onClick={addStepSection} className="w-full mt-4">
        + Add Step Section
      </Button>
    </div>
  );
};
