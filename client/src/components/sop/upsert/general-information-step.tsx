import { Button } from '@/components/ui/button';
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { CreateSopType, SOP_SECTION_LABELS } from '@shared/types/sop.types';
import { Plus, Trash2 } from 'lucide-react';
import { useFieldArray, useFormContext } from 'react-hook-form';

export const GeneralInformationStep = () => {
  const form = useFormContext<CreateSopType>();

  const {
    fields: generalSections,
    append: appendGeneralSection,
    remove: removeGeneralSection,
  } = useFieldArray({
    control: form.control,
    name: 'sections',
    keyName: 'fieldId',
  });

  const addGeneralSection = () => {
    const currentGeneralSections = form.watch('sections').filter((s) => s.sectionType === 'general');
    const nextSerial = Math.max(0, ...currentGeneralSections.map((s) => s.serial)) + 1;

    appendGeneralSection({
      sectionType: 'general',
      serial: nextSerial,
      value: '',
      label: '',
    });
  };

  const removeSection = (index: number) => {
    removeGeneralSection(index);
    // Update serials for remaining general sections
    const sections = form.watch('sections');
    const generalSections = sections
      .map((section, idx) => ({ ...section, originalIndex: idx }))
      .filter((section) => section.sectionType === 'general' && section.originalIndex !== index);

    generalSections.forEach((section, idx) => {
      form.setValue(`sections.${section.originalIndex}.serial`, idx + 1);
    });
  };

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">General Information</h2>
        <p className="text-gray-600">Provide the basic details and context about this Standard Operating Procedure</p>
      </div>

      {/* SOP Title */}
      <FormField
        control={form.control}
        name="sop.title"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              Title <span className="text-red-500">*</span>
            </FormLabel>
            <FormControl>
              <Input placeholder="Enter the SOP title" {...field} value={field.value || ''} />
            </FormControl>
            <FormDescription>A clear, descriptive title for this Standard Operating Procedure</FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* SOP Purpose */}
      <FormField
        control={form.control}
        name="sop.purpose"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Purpose</FormLabel>
            <FormControl>
              <Textarea placeholder="Enter the SOP purpose" {...field} value={field.value || ''} />
            </FormControl>
            <FormDescription>A clear, descriptive purpose for this Standard Operating Procedure</FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* SOP Responsibilities */}
      <FormField
        control={form.control}
        name="sop.responsibilities"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Responsibilities</FormLabel>
            <FormControl>
              <Textarea placeholder="Enter the SOP responsibilities" {...field} value={field.value || ''} />
            </FormControl>
            <FormDescription>
              A clear, descriptive responsibilities for this Standard Operating Procedure
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* General Sections */}
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium text-gray-900">General Sections</h3>
          <Button type="button" variant="outline" onClick={addGeneralSection}>
            <Plus className="h-4 w-4 mr-2" />
            Add Section
          </Button>
        </div>

        {generalSections.map((field, index) => {
          const section = form.watch(`sections.${index}`);
          if (section?.sectionType !== 'general') return null;

          return (
            <div key={field.fieldId} className="border border-gray-200 rounded-lg p-6 space-y-4">
              <div className="flex justify-between items-start">
                <div className="flex-1 space-y-4">
                  {/* Section Label */}
                  <FormField
                    control={form.control}
                    name={`sections.${index}.label`}
                    render={({ field }) => {
                      const isCustomMode =
                        field.value === 'Custom' || (field.value && !SOP_SECTION_LABELS.general.includes(field.value));

                      return (
                        <FormItem>
                          <FormLabel>Label</FormLabel>
                          <FormControl>
                            {isCustomMode ? (
                              <Input
                                value={field.value === 'Custom' ? '' : field.value || ''}
                                onChange={(e) => field.onChange(e.target.value)}
                                placeholder="Enter a custom section label"
                              />
                            ) : (
                              <Select value={field.value || ''} onValueChange={field.onChange}>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select a section type" />
                                </SelectTrigger>
                                <SelectContent>
                                  {SOP_SECTION_LABELS.general.map((label) => (
                                    <SelectItem key={label} value={label}>
                                      {label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            )}
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />

                  {/* Section Content */}
                  <FormField
                    control={form.control}
                    name={`sections.${index}.value`}
                    render={({ field }) => {
                      const sectionLabel = form.watch(`sections.${index}.label`);
                      const placeholderText =
                        sectionLabel && sectionLabel !== 'Custom'
                          ? `Enter ${sectionLabel.toLowerCase()} details...`
                          : 'Enter section details...';

                      return (
                        <FormItem>
                          <FormLabel>Content</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder={placeholderText}
                              className="min-h-[120px]"
                              {...field}
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />
                </div>

                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  onClick={() => removeSection(index)}
                  className="ml-2 text-red-600 hover:text-red-700 hover:bg-red-50 mt-5"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
