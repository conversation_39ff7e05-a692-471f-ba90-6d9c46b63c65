import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { ChevronLeft } from 'lucide-react';

export function SopDetailsLoading() {
  return (
    <div className="container mx-auto py-4 px-4">
      {/* Back button */}
      <div className="mb-3">
        <Button variant="ghost" className="pl-0 hover:pl-0 hover:bg-transparent" disabled>
          <ChevronLeft className="h-4 w-4 mr-1" />
          Back to SOP List
        </Button>
      </div>

      {/* SOP Header */}
      <div className="flex flex-col md:flex-row justify-between items-start gap-3 mb-3">
        <div className="w-full">
          {/* Badges row */}
          <div className="flex flex-wrap items-center gap-2 mb-1">
            <Skeleton className="h-5 w-24" /> {/* Slug */}
            <Skeleton className="h-6 w-20" /> {/* Status badge */}
            <Skeleton className="h-6 w-24" /> {/* Version badge */}
            <Skeleton className="h-6 w-20" /> {/* Archived badge */}
          </div>
          {/* Title */}
          <Skeleton className="h-8 md:h-10 w-3/4 mb-2" />
        </div>

        {/* Desktop buttons */}
        <div className="hidden md:flex gap-2 self-start">
          <Skeleton className="h-9 w-16" />
          <Skeleton className="h-9 w-10" />
        </div>
      </div>

      {/* Context bar with metadata */}
      <div className="flex flex-wrap items-center text-sm mb-4 gap-y-2">
        <Skeleton className="h-4 w-32" /> {/* Owner */}
        <div className="hidden sm:block mx-2">•</div>
        <Skeleton className="h-4 w-36" /> {/* Approver */}
        <div className="hidden sm:block mx-2">•</div>
        <Skeleton className="h-4 w-40" /> {/* Review Date */}
      </div>

      {/* Main content grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Left column - main content */}
        <div className="md:col-span-2 space-y-4">
          {/* Procedure Steps */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Skeleton className="h-5 w-5 rounded-full" />
                  <Skeleton className="h-6 w-48" />
                </div>
                <div className="flex items-center gap-2">
                  <Skeleton className="h-8 w-20" />
                  <Skeleton className="h-8 w-24" />
                </div>
              </div>
              <Skeleton className="h-4 w-full" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Step 1 */}
                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <Skeleton className="h-8 w-8 rounded-full" />
                    <div className="flex-1">
                      <Skeleton className="h-5 w-48 mb-1" />
                      <Skeleton className="h-4 w-full mb-2" />
                      <Skeleton className="h-4 w-3/4" />
                    </div>
                  </div>
                </div>

                {/* Step 2 */}
                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <Skeleton className="h-8 w-8 rounded-full" />
                    <div className="flex-1">
                      <Skeleton className="h-5 w-56 mb-1" />
                      <Skeleton className="h-4 w-full mb-2" />
                      <Skeleton className="h-4 w-4/5" />
                    </div>
                  </div>
                </div>

                {/* Step 3 */}
                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <Skeleton className="h-8 w-8 rounded-full" />
                    <div className="flex-1">
                      <Skeleton className="h-5 w-40 mb-1" />
                      <Skeleton className="h-4 w-full mb-2" />
                      <Skeleton className="h-4 w-2/3" />
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Additional Information */}
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-64" />
              <Skeleton className="h-4 w-full" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-5/6 mb-2" />
              <Skeleton className="h-4 w-4/5" />
            </CardContent>
          </Card>
        </div>

        {/* Right column - metadata & sidebar */}
        <div className="space-y-4">
          {/* Procedure Summary */}
          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <Skeleton className="h-4 w-4" />
                <Skeleton className="h-6 w-32" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <Skeleton className="h-4 w-32 mb-1" />
                  <Skeleton className="h-5 w-20" />
                </div>
                <div>
                  <Skeleton className="h-4 w-28 mb-1" />
                  <Skeleton className="h-5 w-24" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Location and Assets */}
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-36" />
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2 mb-4">
                <Skeleton className="h-4 w-4" />
                <Skeleton className="h-4 w-32" />
              </div>
              <div className="flex flex-wrap gap-2">
                <Skeleton className="h-6 w-24 rounded-full" />
                <Skeleton className="h-6 w-28 rounded-full" />
                <Skeleton className="h-6 w-20 rounded-full" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
