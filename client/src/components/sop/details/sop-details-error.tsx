import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { AlertCircle } from 'lucide-react';
import { Link } from 'wouter';

export const SopDetailsError = () => {
  return (
    <div className="container mx-auto py-4 px-4">
      <Card className="mb-4">
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center text-center p-6">
            <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
            <h2 className="text-2xl font-bold mb-2">Error Loading SOP</h2>
            <p className="text-muted-foreground mb-4">
              We couldn't load the SOP details. The SOP may have been deleted or you don't have permission to view it.
            </p>
            <Link href={ROUTES.SOP_LIST}>
              <Button>Back to SOP List</Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
