import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { trpc } from '@/providers/trpc';
import { approvalStatusEnum } from '@shared/schema';
import { Loader2 } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

export const SopConfirmationDialog = ({
  showDialog,
  setShowDialog,
  sopId,
  action,
}: {
  showDialog: boolean;
  setShowDialog: (show: boolean) => void;
  sopId: string;
  action: (typeof approvalStatusEnum.enumValues)[number];
}) => {
  const utils = trpc.useUtils();
  const [rejectionReason, setRejectionReason] = useState('');

  const labels: Record<
    (typeof approvalStatusEnum.enumValues)[number],
    { title: string; confirm: string; description: string; danger?: boolean }
  > = {
    [approvalStatusEnum.enumValues[1]]: {
      title: 'Submit SOP for Review',
      confirm: 'Submit',
      description: 'This will change the status to Under Review.',
    },
    [approvalStatusEnum.enumValues[2]]: {
      title: 'Approve SOP',
      confirm: 'Approve',
      description: 'This will change the status to Approved and make the SOP active.',
    },
    [approvalStatusEnum.enumValues[0]]: {
      title: 'Reject SOP',
      confirm: 'Reject',
      description: 'This will change the status to Draft and send it back to the owner for revision',
      danger: true,
    },
  };

  const mutation = trpc.sop.updateStatus.useMutation({
    onSuccess: () => {
      utils.sop.invalidate?.();
      utils.auditTrail.get.invalidate({ entityId: sopId, entityType: 'sop' });
      toast.success('Status updated', {
        description:
          action === approvalStatusEnum.enumValues[1]
            ? 'SOP submitted for review.'
            : action === approvalStatusEnum.enumValues[2]
              ? 'SOP approved.'
              : 'SOP rejected.',
      });

      setShowDialog(false);
      setRejectionReason('');
    },
    onError: () => {
      toast.error('Action failed', { description: 'Could not update SOP status. Please try again.' });
    },
  });

  const onConfirm = async () => {
    const isRejection = action === approvalStatusEnum.enumValues[0];
    await mutation.mutateAsync({ id: sopId, status: action, rejectionReason: isRejection ? rejectionReason.trim() : undefined });
  };

  const { title, confirm, description, danger } = labels[action];
  const isRejection = action === approvalStatusEnum.enumValues[0];

  const handleOpenChange = (open: boolean) => {
    setShowDialog(open);
    if (!open) setRejectionReason('');
  };

  return (
    <AlertDialog open={showDialog} onOpenChange={handleOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{title}</AlertDialogTitle>
          <AlertDialogDescription>{description}</AlertDialogDescription>
        </AlertDialogHeader>
        {isRejection && (
          <div className="space-y-2">
            <Label htmlFor="rejectionReason">Reason for rejection <span className="text-red-500">*</span></Label>
            <Textarea id="rejectionReason" value={rejectionReason} onChange={(e) => setRejectionReason(e.target.value)} placeholder="Please provide a reason for rejecting this SOP..." disabled={mutation.isPending} className="resize-none" />
          </div>
        )}
        <AlertDialogFooter>
          <AlertDialogCancel disabled={mutation.isPending}>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={onConfirm} disabled={mutation.isPending || (isRejection && !rejectionReason.trim())} className={danger ? 'bg-red-600 hover:bg-red-700' : ''}>
            {confirm} {mutation.isPending && <Loader2 className="w-4 h-4 ml-2 animate-spin" />}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

