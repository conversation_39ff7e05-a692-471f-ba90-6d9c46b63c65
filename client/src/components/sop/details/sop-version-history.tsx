import { SopStatusBadge } from '@/components/composite/sop-status-badge';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { trpc } from '@/providers/trpc';
import { formatDate } from '@shared/date-utils';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { Eye, History } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { useLocation } from 'wouter';

export const SopVersionHistory = ({ sopInstanceId, sopTitle }: { sopInstanceId: string; sopTitle?: string }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [, navigate] = useLocation();

  const { data: versions, isLoading } = trpc.sop.getVersions.useQuery({ id: sopInstanceId });

  const handleViewVersion = ({
    instanceId,
    versionId,
    isLatest,
  }: {
    instanceId: string | null;
    versionId: string;
    isLatest: boolean;
  }) => {
    if (!instanceId) {
      toast.error('Something went wrong', { description: 'Failed to view version history' });
      return;
    }

    if (isLatest) {
      navigate(ROUTES.BUILD_SOP_DETAILS_PATH(instanceId));
    } else {
      navigate(ROUTES.BUILD_SOP_DETAILS_PATH(instanceId) + `?version=${versionId}`);
    }
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <History className="h-4 w-4 mr-2" />
          View History
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-6xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            SOP Revision History{sopTitle ? `: ${sopTitle}` : ''}
          </DialogTitle>
          <p className="text-sm text-muted-foreground">
            Complete version history and audit trail for this Standard Operating Procedure.
          </p>
        </DialogHeader>
        <div className="overflow-auto">
          {isLoading ? (
            <div className="flex items-center justify-center py-8 text-sm text-muted-foreground">
              Loading version history...
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Version</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Date/Time</TableHead>
                  <TableHead>Revised By</TableHead>
                  <TableHead>Reason for Change</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {versions && versions.length > 0 ? (
                  versions.map((v, index) => (
                    <TableRow key={v.id}>
                      <TableCell>
                        {index === 0 ? <Badge>v{v.version}</Badge> : <Badge variant="outline">v{v.version}</Badge>}
                      </TableCell>
                      <TableCell>
                        {index === 0 ? (
                          <SopStatusBadge status={v.status} />
                        ) : (
                          <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                            Inactive
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="text-sm font-medium">
                            {v.updatedAt ? formatDate(new Date(v.updatedAt), true) : 'not set'}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {v.updatedAt ? formatDate(new Date(v.updatedAt)).split(' at ')[1] : ''}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm font-medium">{v.createdBy?.fullName}</div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">{v.reasonForRevision}</div>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            handleViewVersion({ instanceId: v.instanceId, versionId: v.id, isLatest: index === 0 })
                          }
                          className="h-8 px-3"
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                      No version history available
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
