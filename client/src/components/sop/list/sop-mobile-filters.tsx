import { AsyncLocationsFilter } from '@/components/composite/async-locations-filter';
import { AsyncUsersFilter } from '@/components/composite/async-users-filter';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Label } from '@/components/ui/label';
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { approvalStatusEnum } from '@shared/schema';
import { SopFilters, SopReviewStatusEnum, SOP_REVIEW_STATUS_MAP } from '@shared/types/sop.types';
import { Archive, ChevronDown, Filter } from 'lucide-react';

export const MobileFilters = ({
  toggleStatus,
  filters,
  updateFilters,
  activeFilterCount,
  resetFilters,
  trackFilterApplied,
}: {
  toggleStatus: (status: (typeof approvalStatusEnum.enumValues)[number]) => void;
  filters: SopFilters;
  updateFilters: (updates: Partial<SopFilters>) => void;
  activeFilterCount: number;
  resetFilters: () => void;
  trackFilterApplied: (
    type: 'status' | 'ownerId' | 'reviewStatus' | 'locationIds' | 'includeArchived',
    value: string,
  ) => void;
}) => {
  // Handle include archived toggle
  const handleArchiveToggle = () => {
    const newValue = !filters.includeArchived;
    trackFilterApplied('includeArchived', newValue.toString());
    updateFilters({ includeArchived: newValue });
  };

  // Handle review status filter
  const handleReviewStatusChange = (reviewStatus: (typeof SopReviewStatusEnum.options)[number] | undefined) => {
    trackFilterApplied('reviewStatus', reviewStatus || 'all');
    updateFilters({ reviewStatus });
  };

  return (
    <div className="md:hidden">
      <Sheet>
        <SheetTrigger asChild>
          <Button variant="outline" size={activeFilterCount > 0 ? 'default' : 'icon'}>
            <Filter className="h-4 w-4" />
            {activeFilterCount > 0 && (
              <span className="text-xs bg-primary text-primary-foreground rounded-full w-4 h-4 flex items-center justify-center">
                {activeFilterCount}
              </span>
            )}
          </Button>
        </SheetTrigger>
        <SheetContent side="right">
          <SheetHeader>
            <SheetTitle>Filter SOPs</SheetTitle>
            <SheetDescription>Apply filters to narrow down your SOP list.</SheetDescription>
          </SheetHeader>

          <div className="p-4 space-y-4">
            {/* Status Filter */}
            <Collapsible defaultOpen className="bg-muted rounded-md p-3">
              <CollapsibleTrigger className="flex items-center justify-between w-full">
                <h3 className="text-sm font-medium">Status</h3>
                <ChevronDown className="h-4 w-4 transition-transform ui-open:rotate-180" />
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-2 space-y-2">
                {approvalStatusEnum.enumValues.map((status) => (
                  <div key={status} className="flex items-center space-x-2">
                    <Checkbox
                      id={`status-${status}-mobile`}
                      checked={filters.status?.includes(status)}
                      onCheckedChange={() => {
                        toggleStatus(status);
                        trackFilterApplied('status', status);
                      }}
                    />
                    <label htmlFor={`status-${status}-mobile`} className="text-sm cursor-pointer">
                      {status}
                    </label>
                  </div>
                ))}
              </CollapsibleContent>
            </Collapsible>

            {/* Owner Filter */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Owner</Label>
              <AsyncUsersFilter
                selected={filters.ownerId}
                onSelect={(ownerIds) => {
                  updateFilters({ ownerId: ownerIds });
                  trackFilterApplied('ownerId', ownerIds.join(','));
                }}
                className="w-full"
                label=""
                placeholder="Search owner"
              />
            </div>

            {/* Review Status Filter */}
            <Collapsible defaultOpen className="bg-muted rounded-md p-3">
              <CollapsibleTrigger className="flex items-center justify-between w-full">
                <h3 className="text-sm font-medium">Next Review Date</h3>
                <ChevronDown className="h-4 w-4 transition-transform ui-open:rotate-180" />
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-2 space-y-2">
                {[undefined, ...SopReviewStatusEnum.options].map((status) => (
                  <div key={status || 'all'} className="flex items-center space-x-2">
                    <Checkbox
                      id={`review-${status || 'all'}-mobile`}
                      checked={filters.reviewStatus === status}
                      onCheckedChange={() => {
                        handleReviewStatusChange(status);
                        trackFilterApplied('reviewStatus', status || 'all');
                      }}
                    />
                    <label htmlFor={`review-${status || 'all'}-mobile`} className="text-sm cursor-pointer">
                      {status ? SOP_REVIEW_STATUS_MAP[status] : 'All'}
                    </label>
                  </div>
                ))}
              </CollapsibleContent>
            </Collapsible>

            {/* Location Filter */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Location</Label>
              <AsyncLocationsFilter
                selected={filters.locationIds}
                onSelect={(locationIds) => {
                  updateFilters({ locationIds });
                  trackFilterApplied('locationIds', locationIds.join(','));
                }}
                className="w-full"
                label=""
                placeholder="Search location"
              />
            </div>

            {/* Include Archived Filter */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Archive Status</Label>
              <Button
                variant={filters.includeArchived ? 'default' : 'outline'}
                size="sm"
                onClick={handleArchiveToggle}
                className={`justify-start w-full ${filters.includeArchived ? 'bg-amber-600 hover:bg-amber-700' : ''}`}
              >
                <Archive className="h-4 w-4 mr-2" />
                Include Archived
              </Button>
            </div>
          </div>

          <SheetFooter className="pt-4">
            <SheetClose asChild>
              <Button variant="outline" className="w-full" onClick={resetFilters}>
                Clear Filters
              </Button>
            </SheetClose>
            <SheetClose asChild>
              <Button className="w-full">Apply Filters ({activeFilterCount})</Button>
            </SheetClose>
          </SheetFooter>
        </SheetContent>
      </Sheet>
    </div>
  );
};
