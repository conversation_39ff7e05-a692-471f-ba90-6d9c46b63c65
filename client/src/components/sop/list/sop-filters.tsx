import { AsyncLocationsFilter } from '@/components/composite/async-locations-filter';
import { AsyncUsersFilter } from '@/components/composite/async-users-filter';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { approvalStatusEnum } from '@shared/schema';
import { SopFilters, SopReviewStatusEnum, SOP_REVIEW_STATUS_MAP, SOP_STATUS_MAP } from '@shared/types/sop.types';
import { Archive, ChevronDown, Filter, X } from 'lucide-react';

export const Filters = ({
  toggleStatus,
  filters,
  updateFilters,
  activeFilterCount,
  resetFilters,
  trackFilterApplied,
}: {
  toggleStatus: (status: (typeof approvalStatusEnum.enumValues)[number]) => void;
  filters: SopFilters;
  updateFilters: (updates: Partial<SopFilters>) => void;
  activeFilterCount: number;
  resetFilters: () => void;
  trackFilterApplied: (
    type: 'status' | 'ownerId' | 'reviewStatus' | 'locationIds' | 'includeArchived',
    value: string,
  ) => void;
}) => {
  // Handle include archived toggle
  const handleArchiveToggle = () => {
    const newValue = !filters.includeArchived;
    trackFilterApplied('includeArchived', newValue.toString());
    updateFilters({ includeArchived: newValue });
  };

  // Handle review status filter
  const handleReviewStatusChange = (reviewStatus: (typeof SopReviewStatusEnum.options)[number] | undefined) => {
    trackFilterApplied('reviewStatus', reviewStatus || 'all');
    updateFilters({ reviewStatus });
  };
  return (
    <div className="hidden  bg-gray-50 py-6 md:sticky md:top-0 md:z-10 md:block ">
      <div className="flex items-center gap-2 flex-wrap">
        <Button variant="outline" size="sm" className={activeFilterCount > 0 ? 'bg-muted' : ''}>
          <Filter className="h-4 w-4" />
          Filters {activeFilterCount > 0 && `(${activeFilterCount})`}
        </Button>

        {/* Status Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              Status
              <Badge className="ml-1 h-5 px-1 py-0" variant="secondary">
                {filters.status?.length || 'All'}
              </Badge>
              <ChevronDown className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            {approvalStatusEnum.enumValues.map((status) => (
              <DropdownMenuCheckboxItem
                key={status}
                className="flex items-center space-x-2"
                checked={filters.status?.includes(status)}
                onSelect={(e) => {
                  e.preventDefault();
                  toggleStatus(status);
                  trackFilterApplied('status', status);
                }}
              >
                {SOP_STATUS_MAP[status]}
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Owner Filter */}
        <AsyncUsersFilter
          selected={filters.ownerId}
          onSelect={(ownerIds) => {
            updateFilters({ ownerId: ownerIds });
            trackFilterApplied('ownerId', ownerIds.join(','));
          }}
          label="Owner"
          placeholder="Search owner"
        />

        {/* Review Status Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              Next Review Date
              <Badge className="ml-1 h-5 px-1 py-0" variant="secondary">
                {filters.reviewStatus ? SOP_REVIEW_STATUS_MAP[filters.reviewStatus] : 'All'}
              </Badge>
              <ChevronDown className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            {[undefined, ...SopReviewStatusEnum.options].map((status) => (
              <DropdownMenuCheckboxItem
                key={status || 'all'}
                className="flex items-center space-x-2"
                checked={filters.reviewStatus === status}
                onSelect={(e) => {
                  e.preventDefault();
                  handleReviewStatusChange(status);
                }}
              >
                {status ? SOP_REVIEW_STATUS_MAP[status] : 'All'}
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Location Filter */}
        <AsyncLocationsFilter
          selected={filters.locationIds}
          onSelect={(locationIds) => {
            updateFilters({ locationIds });
            trackFilterApplied('locationIds', locationIds.join(','));
          }}
          label="Location"
          placeholder="Search location"
        />

        {/* Archive Filter */}
        <Button
          variant={filters.includeArchived ? 'default' : 'outline'}
          size="sm"
          onClick={handleArchiveToggle}
          className={filters.includeArchived ? 'bg-amber-600 hover:bg-amber-700' : ''}
        >
          <Archive className="h-4 w-4" />
          Include Archived
        </Button>

        {/* Reset Filters */}
        {activeFilterCount > 0 && (
          <Button variant="ghost" size="sm" onClick={resetFilters} className="h-9">
            <X className="h-3.5 w-3.5" />
            Clear Filters
          </Button>
        )}
      </div>
    </div>
  );
};
