import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { Button } from '@/components/ui/button';
import { useAnalytics } from '@/hooks/use-analytics';
import { useIsMobile } from '@/hooks/use-mobile';
import { usePermissions } from '@/hooks/use-permissions';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { BookOpen } from 'lucide-react';
import { useLocation } from 'wouter';

type SopEmptyProps = {
  hasActiveFilters: boolean;
  onResetFilters: () => void;
};

export const SopEmpty = ({ hasActiveFilters, onResetFilters }: SopEmptyProps) => {
  const isMobile = useIsMobile();
  const [_, navigate] = useLocation();
  const analytics = useAnalytics();
  const { hasPermission } = usePermissions();

  if (hasActiveFilters) {
    // Empty state when filters are applied but no results
    return (
      <div
        className={`flex flex-col items-center justify-center py-12 px-4 ${isMobile ? 'min-h-[400px]' : 'min-h-[500px]'}`}
      >
        <div className="text-center max-w-md">
          <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-gray-100 flex items-center justify-center">
            <BookOpen className="h-6 w-6 text-gray-400" />
          </div>
          <h3 className={`font-semibold text-gray-900 mb-2 ${isMobile ? 'text-lg' : 'text-xl'}`}>
            No Standard Operating Procedures found
          </h3>
          <p className={`text-gray-500 mb-6 ${isMobile ? 'text-sm' : 'text-base'}`}>
            No Standard Operating Procedures match your current filters. Try adjusting your search criteria or clear
            filters to see all SOPs.
          </p>
          <Button variant="outline" onClick={onResetFilters} className={isMobile ? 'w-full' : ''}>
            Clear all filters
          </Button>
        </div>
      </div>
    );
  }

  // Empty state when no SOPs exist at all
  return (
    <div
      className={`flex flex-col items-center justify-center py-12 px-4 ${isMobile ? 'min-h-[400px]' : 'min-h-[500px]'}`}
    >
      <div className="text-center max-w-md">
        <div className="mx-auto mb-4 h-16 w-16 rounded-full bg-blue-100 flex items-center justify-center">
          <BookOpen className="h-8 w-8 text-blue-500" />
        </div>
        <h3 className={`font-semibold text-gray-900 mb-2 ${isMobile ? 'text-lg' : 'text-xl'}`}>
          No Standard Operating Procedures yet
        </h3>
        <p className={`text-gray-500 mb-6 ${isMobile ? 'text-sm leading-5' : 'text-base'}`}>
          Start building safer workflows by creating your first SOP. Use our AI-powered wizard to generate comprehensive
          procedures from simple descriptions or existing documents.
        </p>
        <div className="space-y-3">
          {hasPermission(MODULES.SOP, ALLOWED_ACTIONS.CREATE) && (
            <Button
              size="sm"
              className={`${isMobile ? 'w-full' : ''}`}
              onClick={() => {
                analytics.track(ANALYTICS_EVENTS.SOP.FORM_VIEWED, {
                  source: 'sop_log_row_action',
                });
                navigate(ROUTES.SOP_NEW);
              }}
            >
              + Create New SOP
            </Button>
          )}
          {isMobile && (
            <p className="text-xs text-gray-400 mt-3">
              Standard Operating Procedures help maintain consistency and safety in work processes.
            </p>
          )}
        </div>
      </div>
    </div>
  );
};
