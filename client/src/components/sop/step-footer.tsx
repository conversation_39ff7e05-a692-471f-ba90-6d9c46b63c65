import { useStepper } from '@/components/sop/stepper';
import { Button } from '@/components/ui/button';
import { approvalStatusEnum } from '@shared/schema';
import { Save } from 'lucide-react';
import { useFormContext } from 'react-hook-form';

export const StepFooter = ({
  isSubmitting,
  mode,
  isInitiatingRevision,
  setShowReasonForRevisionModal,
  reasonForRevision,
  isAiGenerated,
  disclaimerAccepted,
}: {
  isSubmitting: boolean;
  mode: 'create' | 'edit';
  isInitiatingRevision?: boolean;
  setShowReasonForRevisionModal?: (show: boolean) => void;
  reasonForRevision?: string;
  isAiGenerated?: boolean;
  disclaimerAccepted?: boolean;
}) => {
  const { next, isLast, prev, isFirst } = useStepper();
  const form = useFormContext();

  return (
    <>
      <div className="flex justify-between items-center pt-6 border-t">
        <div className="flex gap-2">
          <Button type="button" variant="outline" onClick={() => window.history.back()}>
            Cancel
          </Button>
          {!isFirst && (
            <Button type="button" variant="outline" onClick={prev} disabled={isFirst}>
              Previous
            </Button>
          )}

          {!isLast && (
            <Button type="button" onClick={next} disabled={isLast}>
              Next
            </Button>
          )}
        </div>

        {mode === 'create' && (
          <div className="flex gap-2">
            {!isFirst && isAiGenerated && !disclaimerAccepted ? (
              <>
                <Button
                  type="submit"
                  variant="outline"
                  onClick={() => form.setValue('sop.status', approvalStatusEnum.enumValues[0])}
                  disabled={isSubmitting}
                >
                  <Save className="h-4 w-4" />
                  Accept Disclaimer & Save Draft
                </Button>
                {isLast && (
                  <Button
                    type="submit"
                    onClick={() => form.setValue('sop.status', approvalStatusEnum.enumValues[1])}
                    disabled={isSubmitting}
                  >
                    Accept Disclaimer & Submit for Review
                  </Button>
                )}
              </>
            ) : (
              <>
                {!isFirst && (
                  <Button
                    type="submit"
                    variant="outline"
                    onClick={() => form.setValue('sop.status', approvalStatusEnum.enumValues[0])}
                    disabled={isSubmitting}
                  >
                    <Save className="h-4 w-4" />
                    {isSubmitting ? 'Creating SOP...' : 'Save Draft'}
                  </Button>
                )}

                {isLast && (
                  <Button
                    type="submit"
                    onClick={() => form.setValue('sop.status', approvalStatusEnum.enumValues[1])}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? 'Creating SOP...' : 'Submit for Review'}
                  </Button>
                )}
              </>
            )}
          </div>
        )}

        {mode === 'edit' && (
          <div className="flex gap-2">
            {!isInitiatingRevision && (
              <Button type="submit" disabled={isSubmitting || !form.formState.isDirty}>
                {isSubmitting ? 'Updating SOP...' : 'Update SOP'}
              </Button>
            )}

            {isInitiatingRevision && !reasonForRevision && (
              <Button
                type="button"
                disabled={isSubmitting || !form.formState.isDirty}
                onClick={() => {
                  setShowReasonForRevisionModal?.(true);
                }}
              >
                Add Reason for Revision
              </Button>
            )}

            {isInitiatingRevision && reasonForRevision && (
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Updating SOP...' : 'Update SOP with Reason for Revision'}
              </Button>
            )}
          </div>
        )}
      </div>
    </>
  );
};
