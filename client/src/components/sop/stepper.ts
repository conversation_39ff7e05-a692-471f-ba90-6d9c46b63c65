import { defineStepper } from '@stepperize/react';

export const { Scoped, useStepper, steps, utils } = defineStepper(
  { id: 'start', title: 'Start', description: 'Start the process' },
  { id: 'general-information', title: 'General Information', description: 'General Information' },
  { id: 'hazards-and-control', title: 'Hazards and Control', description: 'Hazards and Control' },
  { id: 'procedure-steps', title: 'Procedure Steps', description: 'Procedure Steps' },
  { id: 'emergency-plans', title: 'Emergency Plans', description: 'Emergency Plans' },
  { id: 'metadata', title: 'End', description: 'Metadata' },
);
