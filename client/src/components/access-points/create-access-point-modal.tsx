import { AsyncAssetSelect } from '@/components/composite/async-asset-select';
import { AsyncLocationSelect } from '@/components/composite/async-location-select';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { trpc } from '@/providers/trpc';
import { zodResolver } from '@hookform/resolvers/zod';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { AccessPoint, CreateAccessPointFormSchema } from '@shared/types/access-points.types';
import { QrCode } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { useLocation } from 'wouter';
import { z } from 'zod';

export const CreateAccessPointModal = ({
  isModalOpen,
  setIsModalOpen,
  isGenerating,
  handleGenerateQR,
}: {
  isModalOpen: boolean;
  setIsModalOpen: (isOpen: boolean) => void;
  isGenerating: boolean;
  handleGenerateQR: (accessPoint: AccessPoint) => void;
}) => {
  const [location, navigate] = useLocation();

  const utils = trpc.useUtils();
  const createAccessPoint = trpc.accessPoint.create.useMutation({
    onSuccess: () => {
      utils.accessPoint.list.invalidate();
    },
  });

  const form = useForm<z.infer<typeof CreateAccessPointFormSchema>>({
    resolver: zodResolver(CreateAccessPointFormSchema),
    defaultValues: {
      name: undefined,
      locationId: undefined,
      assetId: undefined,
    },
    mode: 'onSubmit',
  });

  const onSubmit = async (values: z.infer<typeof CreateAccessPointFormSchema>) => {
    // Prevent multiple submissions
    if (createAccessPoint.isPending || isGenerating) {
      return;
    }

    try {
      const createdAccessPoint = await createAccessPoint.mutateAsync({
        name: values.name,
        locationId: values.locationId,
        assetId: values.assetId,
      });

      handleGenerateQR(createdAccessPoint);
      form.reset();
    } catch (error) {
      // Error handling is already done by tRPC mutation
      console.error('Error creating access point:', error);
    }
  };

  const isSubmitting = createAccessPoint.isPending || isGenerating;

  return (
    <AlertDialog
      open={isModalOpen}
      onOpenChange={(open) => {
        setIsModalOpen(open);
        if (!open && location === ROUTES.ACCESS_POINTS_NEW) {
          navigate(ROUTES.ACCESS_POINTS_LIST);
        }
        // Reset form when modal closes
        if (!open) {
          form.reset();
        }
      }}
    >
      <AlertDialogContent className="sm:max-w-[425px]">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <AlertDialogHeader>
              <AlertDialogTitle>Create New Access Point</AlertDialogTitle>
              <AlertDialogDescription>Create a QR code access point for safety event reporting.</AlertDialogDescription>
            </AlertDialogHeader>

            <div className="space-y-4 mt-8 mb-8">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter access point name" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="locationId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location</FormLabel>
                    <FormControl>
                      <AsyncLocationSelect
                        placeholder="Select a location"
                        {...field}
                        value={field.value}
                        onChange={field.onChange}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="assetId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Asset (Optional)</FormLabel>
                    <FormControl>
                      <AsyncAssetSelect
                        placeholder="Select an asset"
                        {...field}
                        value={field.value}
                        onChange={field.onChange}
                        locationId={form.watch('locationId')}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <AlertDialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsModalOpen(false);
                  form.reset();
                  if (location === ROUTES.ACCESS_POINTS_NEW) {
                    navigate(ROUTES.ACCESS_POINTS_LIST);
                  }
                }}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={!form.formState.isValid || isSubmitting}>
                {isSubmitting ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                    Generating...
                  </>
                ) : (
                  <>
                    <QrCode className="mr-2 h-4 w-4" />
                    Generate QR Code
                  </>
                )}
              </Button>
            </AlertDialogFooter>
          </form>
        </Form>
      </AlertDialogContent>
    </AlertDialog>
  );
};
