import {
  <PERSON>ertD<PERSON>og,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { trpc } from '@/providers/trpc';
import { zodResolver } from '@hookform/resolvers/zod';
import { EditAccessPointNameData, EditAccessPointNameSchema } from '@shared/types/access-points.types';
import { RouterOutputs } from '@shared/types/router.types';
import React from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

const FormSchema = EditAccessPointNameSchema;

type FormType = EditAccessPointNameData;

export const EditAccessPointNameModal = ({
  isOpen,
  onClose,
  accessPoint,
}: {
  isOpen: boolean;
  onClose: () => void;
  accessPoint: RouterOutputs['accessPoint']['list']['result'][number] | null;
}) => {
  const utils = trpc.useUtils();

  const updateAccessPointMutation = trpc.accessPoint.update.useMutation({
    onSuccess: () => {
      utils.accessPoint.list.invalidate();
      toast.success('Access point name updated', {
        description: 'The access point name has been successfully updated. The QR code URL remains the same.',
      });
      onClose();
    },
    onError: (error) => {
      toast.error('Error updating access point name', {
        description: error.message || 'Please try again',
      });
    },
  });

  const form = useForm<FormType>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      name: accessPoint?.name || '',
    },
  });

  // Update form when access point changes
  React.useEffect(() => {
    if (accessPoint) {
      form.reset({
        name: accessPoint.name,
      });
    }
  }, [accessPoint, form]);

  const onSubmit = async (data: FormType) => {
    if (!accessPoint) return;

    try {
      await updateAccessPointMutation.mutateAsync({
        id: accessPoint.id,
        name: data.name,
      });
    } catch (error) {
      // Error handling is done by the mutation
      console.error('Error updating access point name:', error);
    }
  };

  const isSubmitting = updateAccessPointMutation.isPending;

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent className="sm:max-w-[425px]">
        <AlertDialogHeader>
          <AlertDialogTitle>Edit Access Point Name</AlertDialogTitle>
          <AlertDialogDescription>
            Update the display name of this access point. The QR code URL will remain unchanged, so existing QR codes
            will continue to work.
          </AlertDialogDescription>
        </AlertDialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Access Point Name</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Enter access point name" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <AlertDialogFooter>
              <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
                Cancel
              </Button>
              <Button type="submit" disabled={!form.formState.isValid || isSubmitting}>
                {isSubmitting ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                    Updating...
                  </>
                ) : (
                  'Update Name'
                )}
              </Button>
            </AlertDialogFooter>
          </form>
        </Form>
      </AlertDialogContent>
    </AlertDialog>
  );
};
