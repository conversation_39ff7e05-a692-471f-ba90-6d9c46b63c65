import { ArchiveConfirmationDialog } from '@/components/composite/archive-confirmation-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { trpc } from '@/providers/trpc';
import { RouterOutputs } from '@shared/types/router.types';
import { format } from 'date-fns';
import { Archive, CheckCircle, Edit, QrCode, Trash2, XCircle } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { EditAccessPointNameModal } from './edit-access-point-modal';

export const AccessPointsTable = ({
  accessPoints,
  onViewQRCode,
  qrCodeUrl,
}: {
  accessPoints: RouterOutputs['accessPoint']['list']['result'];
  onViewQRCode: (accessPoint: RouterOutputs['accessPoint']['list']['result'][number], qrCodeUrl: string) => void;
  qrCodeUrl: string;
}) => {
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const [selectedAccessPoint, setSelectedAccessPoint] = useState<
    RouterOutputs['accessPoint']['list']['result'][number] | null
  >(null);
  const [showEditNameModal, setShowEditNameModal] = useState(false);
  const [accessPointToEdit, setAccessPointToEdit] = useState<
    RouterOutputs['accessPoint']['list']['result'][number] | null
  >(null);

  const utils = trpc.useUtils();

  const { mutateAsync: updateAccessPoint } = trpc.accessPoint.update.useMutation({
    onSuccess: (data) => {
      utils.accessPoint.list.invalidate();
      toast.success(`Access point status updated`, {
        description: `Access point has been updated to ${data.status}`,
      });
    },
    onError: () => {
      toast.error('Error updating access point status', {
        description: 'Please try again',
      });
    },
  });

  return (
    <div className="overflow-hidden hidden md:block">
      {selectedAccessPoint && (
        <ArchiveConfirmationDialog
          archived={!!selectedAccessPoint?.archivedAt}
          showDialog={showArchiveConfirm}
          setShowDialog={setShowArchiveConfirm}
          entityId={selectedAccessPoint.id}
          entityType="accessPoint"
        />
      )}

      <EditAccessPointNameModal
        isOpen={showEditNameModal}
        onClose={() => {
          setShowEditNameModal(false);
          setAccessPointToEdit(null);
        }}
        accessPoint={accessPointToEdit}
      />
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="max-w-80">Access Point Name</TableHead>
            <TableHead>Location</TableHead>
            <TableHead>Asset</TableHead>
            <TableHead className="w-44">Created By</TableHead>
            <TableHead className="w-36">Created Date</TableHead>
            <TableHead className="w-24">Status</TableHead>
            <TableHead className="w-40 text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {accessPoints.map((accessPoint) => (
            <TableRow key={accessPoint.id}>
              <TableCell className="font-medium max-w-80 line-clamp-1 truncate">{accessPoint.name}</TableCell>
              <TableCell className="truncate">{accessPoint.location?.name || accessPoint.locationId || '—'}</TableCell>
              <TableCell className="truncate">{accessPoint.asset?.name || '—'}</TableCell>
              <TableCell className="w-44">
                <div className="flex flex-col">
                  <span className="font-medium truncate">
                    {accessPoint.createdByUser ? `${accessPoint.createdByUser.fullName}` : 'Unknown User'}
                  </span>
                  <span className="text-sm text-muted-foreground truncate">{accessPoint.createdByUser?.email}</span>
                </div>
              </TableCell>
              <TableCell className="w-36">{format(new Date(accessPoint.createdAt), 'MMM d, yyyy')}</TableCell>
              <TableCell className="w-24">
                <Badge
                  className={`${
                    accessPoint.status === 'active'
                      ? 'bg-green-50 text-green-700 border-green-200'
                      : 'bg-amber-50 text-amber-700 border-amber-200'
                  } font-medium flex items-center border px-2 py-1`}
                  variant="outline"
                >
                  {accessPoint.status === 'active' ? 'Active' : 'Inactive'}
                </Badge>
              </TableCell>
              <TableCell className="w-40 text-right">
                <div className="flex items-center justify-end gap-1">
                  {/* View QR Button */}
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 w-8 p-0 border-gray-300 hover:border-gray-400 rounded-sm"
                        onClick={() => onViewQRCode(accessPoint, qrCodeUrl)}
                      >
                        <QrCode className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>View QR Code</p>
                    </TooltipContent>
                  </Tooltip>

                  {/* Edit Name Button */}
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 w-8 p-0 border-gray-300 hover:border-gray-400 rounded-sm"
                        onClick={() => {
                          setAccessPointToEdit(accessPoint);
                          setShowEditNameModal(true);
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Edit Name</p>
                    </TooltipContent>
                  </Tooltip>

                  {/* Activate/Deactivate Button */}
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 w-8 p-0 border-gray-300 hover:border-gray-400 rounded-sm"
                        onClick={() =>
                          updateAccessPoint({
                            id: accessPoint.id,
                            status: accessPoint.status === 'active' ? 'inactive' : 'active',
                          })
                        }
                      >
                        {accessPoint.status === 'active' ? (
                          <XCircle className="h-4 w-4" />
                        ) : (
                          <CheckCircle className="h-4 w-4" />
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{accessPoint.status === 'active' ? 'Deactivate' : 'Activate'}</p>
                    </TooltipContent>
                  </Tooltip>

                  {/* Archive/Unarchive Button */}
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 w-8 p-0 text-red-600 hover:text-red-700 border-gray-300 hover:border-red-300 rounded-sm"
                        onClick={() => {
                          setSelectedAccessPoint(accessPoint);
                          setShowArchiveConfirm(true);
                        }}
                      >
                        {accessPoint.archivedAt ? <Archive className="h-4 w-4" /> : <Trash2 className="h-4 w-4" />}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{accessPoint.archivedAt ? 'Unarchive' : 'Archive'}</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
