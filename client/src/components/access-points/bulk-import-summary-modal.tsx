import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { BulkImportResult } from '@shared/types/schema.types';
import { AlertCircle, CheckCircle, Download, XCircle } from 'lucide-react';

export const BulkImportSummaryModal = ({
  isOpen,
  onClose,
  result,
}: {
  isOpen: boolean;
  onClose: () => void;
  result: BulkImportResult | null;
}) => {
  const handleDownloadFailedItems = () => {
    if (!result || !result.failedItems || result.failedItems.length === 0) return;

    // Create CSV content
    const csvContent = [
      'name,location,reason',
      ...result.failedItems.map((item) => [`"${item.name}"`, `"${item.location}"`, `"${item.reason}"`].join(',')),
    ].join('\n');

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `failed-access-points-${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
    window.URL.revokeObjectURL(url);
  };

  if (!result) return null;

  const hasFailures = result.failed > 0;
  const hasSuccesses = result.created > 0;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {hasFailures && hasSuccesses && <AlertCircle className="h-5 w-5 text-amber-500" />}
            {hasFailures && !hasSuccesses && <XCircle className="h-5 w-5 text-red-500" />}
            {!hasFailures && hasSuccesses && <CheckCircle className="h-5 w-5 text-green-500" />}
            Bulk Import Results
          </DialogTitle>
          <DialogDescription>Import completed. Here's a summary of the results:</DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Summary Stats */}
          <div className="grid grid-cols-3 gap-4">
            <div className="bg-slate-50 p-4 rounded-lg text-center">
              <div className="text-2xl font-bold text-slate-900">{result.total}</div>
              <div className="text-sm text-slate-600">Total Processed</div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg text-center">
              <div className="text-2xl font-bold text-green-700">{result.created}</div>
              <div className="text-sm text-green-600">Successfully Created</div>
            </div>
            <div className="bg-red-50 p-4 rounded-lg text-center">
              <div className="text-2xl font-bold text-red-700">{result.failed}</div>
              <div className="text-sm text-red-600">Failed</div>
            </div>
          </div>

          {/* Success Message */}
          {hasSuccesses && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="font-medium text-green-800">
                  {result.created} access point{result.created !== 1 ? 's' : ''} created successfully
                </span>
              </div>
            </div>
          )}

          {/* Failed Items Download */}
          {hasFailures && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <XCircle className="h-5 w-5 text-red-500" />
                  <span className="font-medium text-red-800">
                    {result.failed} access point{result.failed !== 1 ? 's' : ''} failed to import
                  </span>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDownloadFailedItems}
                  className="text-red-600 hover:text-red-700"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download CSV
                </Button>
              </div>
            </div>
          )}

          {/* No failures message */}
          {!hasFailures && hasSuccesses && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="text-green-800">All access points were imported successfully!</span>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button onClick={onClose} className="w-full">
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
