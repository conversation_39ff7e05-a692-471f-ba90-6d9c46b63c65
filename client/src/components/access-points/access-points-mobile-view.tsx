import { ArchiveConfirmationDialog } from '@/components/composite/archive-confirmation-dialog';
import { StatusIndicator } from '@/components/composite/status-indicator';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { trpc } from '@/providers/trpc';
import { RouterOutputs } from '@shared/types/router.types';
import { format } from 'date-fns';
import { Archive, CheckCircle, Edit, QrCode, Trash2, User, XCircle } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { EditAccessPointNameModal } from './edit-access-point-modal';

export const AccessPointsMobileView = ({
  accessPoints,
  onViewQRCode,
  qrCodeUrl,
}: {
  accessPoints: RouterOutputs['accessPoint']['list']['result'];
  onViewQRCode: (accessPoint: RouterOutputs['accessPoint']['list']['result'][number], qrCodeUrl: string) => void;
  qrCodeUrl: string;
}) => {
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const [selectedAccessPoint, setSelectedAccessPoint] = useState<
    RouterOutputs['accessPoint']['list']['result'][number] | null
  >(null);
  const [showEditNameModal, setShowEditNameModal] = useState(false);
  const [accessPointToEdit, setAccessPointToEdit] = useState<
    RouterOutputs['accessPoint']['list']['result'][number] | null
  >(null);

  const utils = trpc.useUtils();

  const { mutateAsync: updateAccessPoint } = trpc.accessPoint.update.useMutation({
    onSuccess: (data) => {
      utils.accessPoint.list.invalidate();
      toast.success(`Access point status updated to ${data.status}`, {
        description: `Access point has been updated to ${data.status}`,
      });
    },
    onError: () => {
      toast.error('Error updating access point status', {
        description: 'Please try again',
      });
    },
  });

  return (
    <div className="space-y-3">
      {selectedAccessPoint && (
        <ArchiveConfirmationDialog
          archived={!!selectedAccessPoint?.archivedAt}
          showDialog={showArchiveConfirm}
          setShowDialog={setShowArchiveConfirm}
          entityId={selectedAccessPoint.id}
          entityType="accessPoint"
        />
      )}

      <EditAccessPointNameModal
        isOpen={showEditNameModal}
        onClose={() => {
          setShowEditNameModal(false);
          setAccessPointToEdit(null);
        }}
        accessPoint={accessPointToEdit}
      />

      {accessPoints.map((accessPoint) => (
        <Card
          key={accessPoint.id}
          className={`group relative overflow-hidden border-0 shadow-sm hover:shadow-md transition-all duration-200 ${
            accessPoint.archivedAt ? 'bg-gradient-to-r from-amber-50/80 to-amber-50/60' : 'bg-white hover:bg-gray-50/50'
          }`}
        >
          <CardContent>
            <div className="flex">
              {/* Status indicator line - left side */}
              <StatusIndicator status={accessPoint.status} archived={!!accessPoint.archivedAt} />

              {/* Content Section */}
              <div className="flex-1">
                {/* Header Section */}
                <div className="px-4">
                  <div className="flex items-start justify-between gap-3">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="text-sm font-medium text-muted-foreground">{accessPoint.name}</span>
                        {accessPoint.archivedAt && (
                          <Badge
                            className="bg-amber-100 text-amber-700 border-amber-200 text-xs font-medium"
                            variant="outline"
                          >
                            <Archive className="h-3 w-3 mr-1" />
                            Archived
                          </Badge>
                        )}
                      </div>

                      <h3 className="font-semibold text-gray-900 text-base leading-tight line-clamp-2 mb-2">
                        {accessPoint.location?.name || accessPoint.locationId || 'No location'}
                      </h3>
                      {accessPoint.asset && (
                        <p className="text-sm text-gray-600 mb-2">Asset: {accessPoint.asset.name}</p>
                      )}
                    </div>

                    <div className="flex flex-col items-end gap-2">
                      <Badge
                        className={`${
                          accessPoint.status === 'active'
                            ? 'bg-green-100 text-green-700 border-green-200'
                            : 'bg-amber-100 text-amber-700 border-amber-200'
                        } text-xs font-medium`}
                        variant="outline"
                      >
                        {accessPoint.status === 'active' ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                  </div>
                </div>

                {/* Content Section */}
                <div className="px-4 pb-3">
                  <div className="space-y-2.5">
                    {/* Creator */}
                    <div className="flex items-center text-sm text-gray-600">
                      <User className="h-3.5 w-3.5 mr-2 text-gray-400" />
                      <div className="flex flex-col">
                        <span className="font-medium">
                          {accessPoint.createdByUser
                            ? `${accessPoint.createdByUser.firstName} ${accessPoint.createdByUser.lastName}`
                            : '--'}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {accessPoint.createdByUser?.username || accessPoint.createdBy}
                        </span>
                      </div>
                    </div>

                    {/* Date */}
                    <div className="flex items-center text-xs text-gray-500">
                      <span>{format(new Date(accessPoint.createdAt), 'MMM d, yyyy')}</span>
                    </div>
                  </div>
                </div>

                {/* Actions Section */}
                <div className="px-4">
                  <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                    <div className="flex items-center gap-1.5">
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-8 px-3 text-xs font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
                        onClick={() => onViewQRCode(accessPoint, qrCodeUrl)}
                      >
                        <QrCode className="h-3.5 w-3.5 mr-1.5" />
                        QR Code
                      </Button>

                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-8 px-3 text-xs font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
                        onClick={() => {
                          setAccessPointToEdit(accessPoint);
                          setShowEditNameModal(true);
                        }}
                      >
                        <Edit className="h-3.5 w-3.5 mr-1.5" />
                        Edit Name
                      </Button>

                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-8 px-3 text-xs font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
                        onClick={() =>
                          updateAccessPoint({
                            id: accessPoint.id,
                            status: accessPoint.status === 'active' ? 'inactive' : 'active',
                          })
                        }
                      >
                        {accessPoint.status === 'active' ? (
                          <XCircle className="h-3.5 w-3.5 mr-1.5" />
                        ) : (
                          <CheckCircle className="h-3.5 w-3.5 mr-1.5" />
                        )}
                        {accessPoint.status === 'active' ? 'Deactivate' : 'Activate'}
                      </Button>
                    </div>

                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-8 px-3 text-xs font-medium text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors"
                      onClick={() => {
                        setSelectedAccessPoint(accessPoint);
                        setShowArchiveConfirm(true);
                      }}
                    >
                      {accessPoint.archivedAt ? (
                        <Archive className="h-3.5 w-3.5 mr-1.5" />
                      ) : (
                        <Trash2 className="h-3.5 w-3.5 mr-1.5" />
                      )}
                      {accessPoint.archivedAt ? 'Unarchive' : 'Archive'}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
