import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { useAnalytics } from '@/hooks/use-analytics';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { TransientAccessPoint } from '@shared/types/access-points.types';
import { useCopyToClipboard } from '@uidotdev/usehooks';
import { Copy, Download } from 'lucide-react';
import { QRCodeSVG } from 'qrcode.react';
import { toast } from 'sonner';
import { useLocation } from 'wouter';

export const QrViewingModal = ({
  isQrModalOpen,
  setIsQrModalOpen,
  accessPoint,
  qrCodeUrl,
}: {
  isQrModalOpen: boolean;
  setIsQrModalOpen: (isOpen: boolean) => void;
  accessPoint: TransientAccessPoint | undefined;
  qrCodeUrl: string;
}) => {
  const [location, navigate] = useLocation();
  const analytics = useAnalytics();
  const [_, copyToClipboard] = useCopyToClipboard();

  // Handle copy to clipboard
  const handleCopyUrl = async () => {
    if (!qrCodeUrl) return;

    try {
      copyToClipboard(qrCodeUrl);
      toast.success('QR code URL copied to clipboard', {
        description: 'You can now paste it into your address bar to report a safety event.',
      });
    } catch (err) {
      console.error('Failed to copy URL:', err);
      toast.error('Failed to copy URL', {
        description: 'Please try again.',
      });
    }
  };

  // Add download QR code function
  const handleDownloadQR = () => {
    if (!accessPoint || !qrCodeUrl) return;

    // Track QR code download
    analytics.track(ANALYTICS_EVENTS.ACCESS_POINT.DOWNLOADED, {
      access_point_id: accessPoint.id,
      download_format: 'PNG',
    });

    // Get the SVG element
    const svg = document.querySelector('.QRCode') as SVGElement;
    if (!svg) return;

    // Create a canvas with high resolution
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set high-resolution canvas size (A4 proportions at 300 DPI for crisp printing)
    const scale = 4; // For high resolution
    canvas.width = 595 * scale; // A4 width at high DPI
    canvas.height = 842 * scale; // A4 height at high DPI
    ctx.scale(scale, scale);

    const width = 595;
    const height = 842;

    // Fill background with subtle gradient
    const gradient = ctx.createLinearGradient(0, 0, 0, height);
    gradient.addColorStop(0, '#ffffff');
    gradient.addColorStop(1, '#f8fafc');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);

    // Add subtle border
    ctx.strokeStyle = '#e2e8f0';
    ctx.lineWidth = 2;
    ctx.strokeRect(20, 20, width - 40, height - 40);

    // Create an image from the SVG
    const img = new Image();
    const svgData = new XMLSerializer().serializeToString(svg);
    const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
    const svgUrl = URL.createObjectURL(svgBlob);

    img.onload = () => {
      // Header section with professional styling
      ctx.textAlign = 'center';
      ctx.fillStyle = '#1e293b'; // Dark slate

      // Main title
      ctx.font = 'bold 32px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
      ctx.fillText(accessPoint.name, width / 2, 80);

      // Subtitle
      ctx.font = '18px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
      ctx.fillStyle = '#64748b'; // Slate
      ctx.fillText('Safety Reporting Access Point', width / 2, 110);

      // Professional QR code container
      const qrSize = 280;
      const containerPadding = 30;
      const containerSize = qrSize + containerPadding * 2;
      const containerX = (width - containerSize) / 2;
      const containerY = 150;

      // Draw container with shadow effect
      ctx.save();
      ctx.shadowColor = 'rgba(0, 0, 0, 0.1)';
      ctx.shadowBlur = 20;
      ctx.shadowOffsetY = 10;

      // Container background
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(containerX, containerY, containerSize, containerSize);

      // Container border
      ctx.strokeStyle = '#e2e8f0';
      ctx.lineWidth = 2;
      ctx.strokeRect(containerX, containerY, containerSize, containerSize);
      ctx.restore();

      // Draw QR code
      const qrX = containerX + containerPadding;
      const qrY = containerY + containerPadding;
      ctx.drawImage(img, qrX, qrY, qrSize, qrSize);

      // Location information with improved styling
      const locationY = containerY + containerSize + 50;

      // Location label
      ctx.font = '16px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
      ctx.fillStyle = '#64748b';
      ctx.fillText('LOCATION', width / 2, locationY);

      // Location name
      ctx.font = 'bold 24px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
      ctx.fillStyle = '#1e293b';
      ctx.fillText(accessPoint.location?.name || 'Not specified', width / 2, locationY + 30);

      // Asset information (if present)
      if (accessPoint.asset) {
        const assetY = locationY + 60;

        // Asset label
        ctx.font = '16px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
        ctx.fillStyle = '#64748b';
        ctx.fillText('ASSET', width / 2, assetY);

        // Asset name
        ctx.font = 'bold 20px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
        ctx.fillStyle = '#1e293b';
        ctx.fillText(accessPoint.asset.name, width / 2, assetY + 25);
      }

      // Instructions section
      const instructionY = locationY + 80;

      // Instructions header
      ctx.font = '16px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
      ctx.fillStyle = '#64748b';
      ctx.fillText('HOW TO USE', width / 2, instructionY);

      // Main instruction
      ctx.font = '20px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
      ctx.fillStyle = '#1e293b';
      const instructionText = 'Scan this QR code to report a safety event';
      ctx.fillText(instructionText, width / 2, instructionY + 35);

      // Secondary instruction
      ctx.font = '16px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
      ctx.fillStyle = '#64748b';
      ctx.fillText('to your safety team', width / 2, instructionY + 60);

      // Footer section
      const footerY = height - 100;

      // Add a subtle divider line
      ctx.strokeStyle = '#e2e8f0';
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.moveTo(width / 2 - 100, footerY - 30);
      ctx.lineTo(width / 2 + 100, footerY - 30);
      ctx.stroke();

      // Footer text
      ctx.font = '14px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
      ctx.fillStyle = '#94a3b8';
      ctx.fillText('Powered by UpKeep EHS', width / 2, footerY);

      // Add timestamp
      const now = new Date();
      const timestamp = now.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
      ctx.fillText(`Generated on ${timestamp}`, width / 2, footerY + 20);

      // Convert to blob and download
      canvas.toBlob((blob) => {
        if (!blob) return;
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${accessPoint.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}-safety-qr.png`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 'image/png');

      URL.revokeObjectURL(svgUrl);
    };

    img.src = svgUrl;
  };

  return (
    <AlertDialog open={isQrModalOpen} onOpenChange={setIsQrModalOpen}>
      <AlertDialogContent className="sm:max-w-[425px]">
        <AlertDialogHeader>
          <AlertDialogTitle>{accessPoint ? accessPoint.name : 'Access Point'} QR Code</AlertDialogTitle>
          <AlertDialogDescription>
            Scan this QR code to report a safety event at this location
            {accessPoint?.asset && ` and asset`}.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <div className="flex flex-col items-center justify-center py-4">
          {qrCodeUrl ? (
            <div className="p-4 bg-white rounded-lg border">
              <QRCodeSVG
                value={qrCodeUrl}
                size={280}
                bgColor={'#ffffff'}
                fgColor={'#000000'}
                level={'L'}
                className="QRCode"
              />
            </div>
          ) : (
            <div className="w-[280px] h-[280px] bg-muted flex items-center justify-center rounded-lg">
              <p className="text-muted-foreground">No QR code available</p>
            </div>
          )}
          <div className="mt-4 text-center">
            {qrCodeUrl && (
              <div className="flex items-center justify-center gap-2 mb-2 px-2">
                <p className="text-xs text-muted-foreground font-mono break-all flex-1 text-left">{qrCodeUrl}</p>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handleCopyUrl}
                  className="h-6 w-6 p-0 flex-shrink-0"
                  title="Copy URL"
                >
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
            )}
            {accessPoint && accessPoint.location && (
              <p className="text-sm text-muted-foreground">Location: {accessPoint.location.name}</p>
            )}
            {accessPoint && accessPoint.asset && (
              <p className="text-sm text-muted-foreground">Asset: {accessPoint.asset.name}</p>
            )}
            {accessPoint && (
              <p className="text-sm text-primary-600 mt-2">
                This QR code has been saved to your access points list and will remain after refresh.
              </p>
            )}
          </div>
        </div>
        <AlertDialogFooter className="sm:justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              setIsQrModalOpen(false);
              if (location === ROUTES.ACCESS_POINTS_NEW) {
                navigate(ROUTES.ACCESS_POINTS_LIST);
              }
            }}
          >
            Close
          </Button>
          <Button
            type="button"
            disabled={!qrCodeUrl}
            onClick={() => {
              handleDownloadQR();
              setIsQrModalOpen(false);
              if (location === ROUTES.ACCESS_POINTS_NEW) {
                navigate(ROUTES.ACCESS_POINTS_LIST);
              }
            }}
          >
            <Download className="h-4 w-4 mr-2" />
            Download
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
