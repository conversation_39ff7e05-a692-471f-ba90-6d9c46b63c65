import { AsyncLocationsFilter } from '@/components/composite/async-locations-filter';
import { AsyncUsersFilter } from '@/components/composite/async-users-filter';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { DateRangePicker } from '@/components/ui/date-range-picker';
import { Label } from '@/components/ui/label';
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { accessPointStatusEnum } from '@shared/schema';
import { ACCESS_POINT_STATUS_MAP, AccessPointsFilters } from '@shared/types/access-points.types';
import { Archive, ChevronDown, Filter } from 'lucide-react';

export const MobileFilters = ({
  activeFilterCount,
  filters,
  updateFilters,
  toggleFilter,
  resetFilters,
  trackFilterApplied,
}: {
  activeFilterCount: number;
  filters: AccessPointsFilters;
  updateFilters: (updates: Partial<AccessPointsFilters>) => void;
  toggleFilter: (type: 'status' | 'locationId' | 'createdBy', value: string) => void;
  resetFilters: () => void;
  trackFilterApplied: (
    type: 'status' | 'locationId' | 'createdBy' | 'createdDateRange' | 'includeArchived',
    value: string,
  ) => void;
}) => {
  const handleArchiveToggle = () => {
    const newValue = !filters.includeArchived;

    trackFilterApplied('includeArchived', newValue.toString());
    updateFilters({ includeArchived: newValue });
  };

  return (
    <div className="md:hidden">
      <Sheet>
        <SheetTrigger asChild>
          <Button variant="outline" size={activeFilterCount > 0 ? 'default' : 'icon'}>
            <Filter className="h-4 w-4" />
            {activeFilterCount > 0 && (
              <span className="text-xs bg-primary text-primary-foreground rounded-full w-4 h-4 flex items-center justify-center">
                {activeFilterCount}
              </span>
            )}
          </Button>
        </SheetTrigger>
        <SheetContent side="right">
          <SheetHeader>
            <SheetTitle>Filter Access Points</SheetTitle>
            <SheetDescription>Apply filters to narrow down your access point list.</SheetDescription>
          </SheetHeader>

          <div className="p-4 space-y-4">
            {/* Status Filter */}
            <Collapsible defaultOpen className="bg-muted rounded-md p-3">
              <CollapsibleTrigger className="flex items-center justify-between w-full">
                <h3 className="text-sm font-medium">Status</h3>
                <ChevronDown className="h-4 w-4 transition-transform ui-open:rotate-180" />
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-2 space-y-2">
                {accessPointStatusEnum.enumValues.map((status) => (
                  <div key={status} className="flex items-center space-x-2">
                    <Checkbox
                      id={`status-${status}-mobile`}
                      checked={filters.status?.includes(status)}
                      onCheckedChange={() => {
                        toggleFilter('status', status);
                        trackFilterApplied('status', status);
                      }}
                    />
                    <label htmlFor={`status-${status}-mobile`} className="text-sm cursor-pointer">
                      {ACCESS_POINT_STATUS_MAP[status]}
                    </label>
                  </div>
                ))}
              </CollapsibleContent>
            </Collapsible>

            {/* Location Filter */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Location</Label>
              <AsyncLocationsFilter
                selected={filters.locationId}
                onSelect={(locationIds) => updateFilters({ locationId: locationIds })}
                className="w-full"
                label=""
              />
            </div>

            {/* Created By Filter */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Created By</Label>
              <AsyncUsersFilter
                selected={filters.createdBy}
                onSelect={(createdBy) => {
                  updateFilters({ createdBy });
                  trackFilterApplied('createdBy', createdBy.join(','));
                }}
                label=""
                placeholder="Search created by"
                className="w-full"
              />
            </div>

            {/* Date Range Filter */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Created Date Range</Label>
              <DateRangePicker
                range={{
                  from: filters.createdDateRange?.from,
                  to: filters.createdDateRange?.to,
                }}
                setRange={(range) => {
                  updateFilters({ createdDateRange: range });
                  trackFilterApplied('createdDateRange', range?.from?.toISOString() ?? '');
                }}
                className="w-full"
              />
            </div>

            {/* Archive Filter */}
            <div className="flex flex-col">
              <h3 className="font-medium mb-2">Archive Status</h3>
              <Button
                variant={filters.includeArchived ? 'default' : 'outline'}
                size="sm"
                onClick={handleArchiveToggle}
                className={`justify-start ${filters.includeArchived ? 'bg-amber-600 hover:bg-amber-700' : ''}`}
              >
                <Archive className="h-4 w-4 mr-2" />
                Include Archived
              </Button>
            </div>
          </div>

          <SheetFooter className="pt-4">
            <SheetClose asChild>
              <Button variant="outline" className="w-full" onClick={resetFilters}>
                Clear Filters
              </Button>
            </SheetClose>
            <SheetClose asChild>
              <Button className="w-full">Apply Filters ({activeFilterCount})</Button>
            </SheetClose>
          </SheetFooter>
        </SheetContent>
      </Sheet>
    </div>
  );
};
