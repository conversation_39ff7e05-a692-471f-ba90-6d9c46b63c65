import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { useAppContext } from '@/contexts/app-context';
import { useAnalytics } from '@/hooks/use-analytics';
import { useConfig } from '@/hooks/use-config';
import { trpc } from '@/providers/trpc';
import { convertBulkImportResult } from '@shared/types/access-points.types';
import { BulkImportResult } from '@shared/types/schema.types';
import DromoUploader from 'dromo-uploader-react';
import { useState } from 'react';
import { toast } from 'sonner';

type DromoResult = {
  name: string;
  location: string;
};

export const AccessPointBulkImport = ({
  isOpen,
  onClose,
  onSuccess,
}: {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (result: BulkImportResult) => void;
}) => {
  const { user } = useAppContext();
  const { track } = useAnalytics();
  const [_isProcessing, setIsProcessing] = useState(false);
  const utils = trpc.useUtils();
  const { DROMO_API_KEY } = useConfig();

  const bulkCreateMutation = trpc.accessPoint.bulkCreate.useMutation({
    onSuccess: (data) => {
      console.log('AccessPointBulkImport: Bulk create success:', data);
      utils.accessPoint.list.invalidate();

      if (onSuccess) {
        // Transform the API response to match the expected format
        const result = convertBulkImportResult({
          total: data.total,
          created: data.created,
          failed: data.failed,
          failedItems: data.failedItems || [],
        });
        onSuccess(result);
      } else {
        // Fallback to old behavior if no onSuccess callback
        toast.success('Access points imported successfully!');
        onClose();
      }
    },
    onError: (error) => {
      track(ANALYTICS_EVENTS.ACCESS_POINT.BULK_IMPORT_ERROR, {
        error_message: error.message,
      });
      toast.error(`Failed to import access points: ${error.message}`);
    },
    onSettled: () => {
      setIsProcessing(false);
    },
  });

  const handleDromoResults = async (results: DromoResult[], _metadata: unknown) => {
    try {
      setIsProcessing(true);

      // Validate results
      if (!results || !Array.isArray(results) || results.length === 0) {
        toast.error('No data received from CSV upload');
        return;
      }

      console.log('Received Dromo results:', results);

      // Transform Dromo results to match our schema with better validation
      const accessPointsData = results
        .filter((result) => {
          // More thorough validation
          if (!result.name || !result.location) {
            console.warn('Skipping invalid entry:', result);
            return false;
          }

          const trimmedName = result.name.trim();
          const trimmedLocation = result.location.trim();

          if (trimmedName.length === 0 || trimmedLocation.length === 0) {
            console.warn('Skipping entry with empty fields:', result);
            return false;
          }

          return true;
        })
        .map((result) => ({
          name: result.name.trim(),
          location: result.location.trim(),
        }));

      if (accessPointsData.length === 0) {
        toast.error(
          'No valid access points found in the uploaded data. Please check that all rows have both name and location values.',
        );
        return;
      }

      console.log('Processed access points data:', accessPointsData);

      await bulkCreateMutation.mutateAsync(accessPointsData);
    } catch (error) {
      console.error('Error processing bulk import:', error);
      track(ANALYTICS_EVENTS.ACCESS_POINT.BULK_IMPORT_ERROR, {
        error_message: error instanceof Error ? error.message : 'Unknown error during processing',
      });
      toast.error('Failed to process bulk import. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDromoCancel = () => {
    track(ANALYTICS_EVENTS.ACCESS_POINT.BULK_IMPORT_CANCELLED, {});
    onClose();
  };

  if (!DROMO_API_KEY) {
    console.error('DROMO_API_KEY is not configured');
    return null;
  }

  return (
    <DromoUploader
      open={isOpen}
      licenseKey={DROMO_API_KEY}
      fields={[
        {
          label: 'Access Point Name',
          key: 'name',
          requireMapping: true,
          validators: [{ validate: 'required' }],
        },
        {
          label: 'Location Name',
          key: 'location',
          requireMapping: true,
          validators: [{ validate: 'required' }],
        },
      ]}
      settings={{
        importIdentifier: 'Access Points',
      }}
      user={{
        id: user?.id || '',
        name: user?.firstName + ' ' + user?.lastName,
        email: user?.email,
        companyId: user?.upkeepCompanyId || '',
      }}
      onResults={handleDromoResults}
      onCancel={handleDromoCancel}
    />
  );
};
