import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>ef<PERSON>, Mic, Upload } from 'lucide-react';
import { useState } from 'react';
import { VoiceInput } from '@/components/composite/voice-input';
import { JhaFileInput } from './jha-file-input';
import type { CreateJhaType } from '@shared/types/jha.types';
import { trpc } from '@/providers/trpc';
import { toast } from 'sonner';

type CreationMethod = 'select' | 'voice' | 'file';

type JhaAiCreateProps = {
  onVoiceAnalysisComplete?: (jhaData: CreateJhaType) => void;
  onDocumentAnalysisComplete?: (jhaData: CreateJhaType) => void;
};

export const JhaAiCreate = ({ onVoiceAnalysisComplete, onDocumentAnalysisComplete }: JhaAiCreateProps) => {
  const [currentMethod, setCurrentMethod] = useState<CreationMethod>('select');

  // Use the existing JHA document analysis endpoint
  const { mutateAsync: analyzeJhaDocument, isPending: isAnalyzingJhaDocument } = trpc.ai.analyzeJhaDocument.useMutation(
    {
      onSuccess: (data) => {
        if (data) {
          toast.success('Document Parsed Successfully', {
            description: 'Your document has been analyzed and processed.',
          });
          onDocumentAnalysisComplete?.(data);
        }
      },
      onError: (error) => {
        console.error('Document parsing error:', error);
        toast.error('Document Parsing Error', {
          description: error.message || 'Failed to parse the document. Please try again.',
        });
      },
    },
  );

  // Use the existing JHA document analysis endpoint
  const { mutateAsync: analyzeJha, isPending: isAnalyzingJha } = trpc.ai.analyzeJha.useMutation({
    onSuccess: (data) => {
      if (data) {
        toast.success('Voice Parsed Successfully', {
          description: 'Your voice has been analyzed and processed.',
        });
        onVoiceAnalysisComplete?.(data);
      }
    },
    onError: (error) => {
      console.error('Voice parsing error:', error);
      toast.error('Voice Parsing Error', {
        description: error.message || 'Failed to parse the voice. Please try again.',
      });
    },
  });

  const handleVoiceAnalysis = async (text: string) => {
    await analyzeJha({
      text,
    });
  };

  const handleFileAnalysis = async (documentBase64: string, filename: string) => {
    await analyzeJhaDocument({
      documentBase64,
      filename,
      mediaType: 'application/pdf',
    });
  };

  const handleChangeMethod = () => {
    setCurrentMethod('select');
  };

  // Show method selection screen
  if (currentMethod === 'select') {
    return (
      <div className="my-4 space-y-4">
        <div className="grid md:grid-cols-2 gap-6">
          {/* Voice Input Option */}
          <div className="group cursor-pointer h-full" onClick={() => setCurrentMethod('voice')}>
            <div className="border-2 border-dashed bg-white border-blue-200 rounded-lg p-8 text-center space-y-4 hover:border-blue-300 hover:bg-blue-50/30 transition-all duration-200 group-hover:shadow-md h-full flex flex-col">
              <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                <Mic className="w-8 h-8 text-blue-600" />
              </div>
              <div className="space-y-2 flex-1 flex flex-col justify-center">
                <h3 className="text-lg font-medium text-gray-900">Describe JHA with AI</h3>
                <p className="text-sm text-gray-600 leading-relaxed">
                  Use your voice or text to describe the procedure. Our AI will help fill in the fields.
                </p>
              </div>
            </div>
          </div>

          {/* File Upload Option */}
          <div className="group cursor-pointer h-full" onClick={() => setCurrentMethod('file')}>
            <div className="border-2 border-dashed bg-white border-green-200 rounded-lg p-8 text-center space-y-4 hover:border-green-300 hover:bg-green-50/30 transition-all duration-200 group-hover:shadow-md h-full flex flex-col">
              <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center group-hover:bg-green-200 transition-colors">
                <Upload className="w-8 h-8 text-green-600" />
              </div>
              <div className="space-y-2 flex-1 flex flex-col justify-center">
                <h3 className="text-lg font-medium text-gray-900">Import JHA with AI</h3>
                <p className="text-sm text-gray-600 leading-relaxed">
                  Upload an existing JHA document. Our AI will parse it and pre-fill the fields. Processing usually
                  takes 1-2 minutes.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show voice input method
  if (currentMethod === 'voice') {
    return (
      <div className="my-4 space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Mic className="w-6 h-6 text-blue-600" />
              <h2 className="text-xl font-semibold text-gray-900">Speak or type your JHA description</h2>
            </div>
          </div>
          <Button variant="ghost" size="sm" onClick={handleChangeMethod} className="text-blue-600 hover:text-blue-700">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Change Method
          </Button>
        </div>

        <p className="text-gray-600">
          Just say or type what the job is, who's doing it, and any hazards or equipment. We'll auto-fill the rest.
        </p>

        <VoiceInput onAnalysisComplete={handleVoiceAnalysis} isLoading={isAnalyzingJha} />
      </div>
    );
  }

  // Show file input method
  if (currentMethod === 'file') {
    return (
      <div className="my-4 space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Upload className="w-6 h-6 text-green-600" />
              <h2 className="text-xl font-semibold text-gray-900">Upload & AI Parse Document</h2>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleChangeMethod}
            className="text-green-600 hover:text-green-700"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Change Method
          </Button>
        </div>

        <p className="text-gray-600">
          Upload an existing JHA document. Our AI will analyze it and pre-fill the fields below. Processing usually
          takes 1-2 minutes.
        </p>

        <JhaFileInput onUpload={handleFileAnalysis} isParsing={isAnalyzingJhaDocument} />
      </div>
    );
  }

  return null;
};
