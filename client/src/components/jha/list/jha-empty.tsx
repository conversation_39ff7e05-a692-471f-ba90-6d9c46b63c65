import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { Button } from '@/components/ui/button';
import { useAnalytics } from '@/hooks/use-analytics';
import { useIsMobile } from '@/hooks/use-mobile';
import { usePermissions } from '@/hooks/use-permissions';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { HardHat } from 'lucide-react';
import { useLocation } from 'wouter';

interface JhaEmptyProps {
  hasActiveFilters: boolean;
  onResetFilters: () => void;
}

export const JhaEmpty = ({ hasActiveFilters, onResetFilters }: JhaEmptyProps) => {
  const isMobile = useIsMobile();
  const [_, navigate] = useLocation();
  const analytics = useAnalytics();
  const { hasPermission } = usePermissions();

  if (hasActiveFilters) {
    // Empty state when filters are applied but no results
    return (
      <div
        className={`flex flex-col items-center justify-center py-12 px-4 ${isMobile ? 'min-h-[400px]' : 'min-h-[500px]'}`}
      >
        <div className="text-center max-w-md">
          <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-gray-100 flex items-center justify-center">
            <HardHat className="h-6 w-6 text-gray-400" />
          </div>
          <h3 className={`font-semibold text-gray-900 mb-2 ${isMobile ? 'text-lg' : 'text-xl'}`}>
            No Job Hazard Analyses found
          </h3>
          <p className={`text-gray-500 mb-6 ${isMobile ? 'text-sm' : 'text-base'}`}>
            No Job Hazard Analyses match your current filters. Try adjusting your search criteria or clear filters to
            see all JHAs.
          </p>
          <Button variant="outline" onClick={onResetFilters} className={isMobile ? 'w-full' : ''}>
            Clear all filters
          </Button>
        </div>
      </div>
    );
  }

  // Empty state when no JHAs exist at all
  return (
    <div
      className={`flex flex-col items-center justify-center py-12 px-4 ${isMobile ? 'min-h-[400px]' : 'min-h-[500px]'}`}
    >
      <div className="text-center max-w-md">
        <div className="mx-auto mb-4 h-16 w-16 rounded-full bg-blue-100 flex items-center justify-center">
          <HardHat className="h-8 w-8 text-blue-500" />
        </div>
        <h3 className={`font-semibold text-gray-900 mb-2 ${isMobile ? 'text-lg' : 'text-xl'}`}>
          No Job Hazard Analyses yet
        </h3>
        <p className={`text-gray-500 mb-6 ${isMobile ? 'text-sm leading-5' : 'text-base'}`}>
          This is where all Job Hazard Analyses will be tracked and managed. Create your first JHA to identify and
          control workplace hazards systematically.
        </p>
        <div className="space-y-3">
          {hasPermission(MODULES.EHS_JHA, ALLOWED_ACTIONS.CREATE) && (
            <Button
              size="sm"
              className={`${isMobile ? 'w-full' : ''}`}
              onClick={() => {
                analytics.track(ANALYTICS_EVENTS.JHA.FORM_VIEWED, {
                  source: 'jha_log',
                });
                navigate(ROUTES.JHA_NEW);
              }}
            >
              + Create New JHA
            </Button>
          )}
          {isMobile && (
            <p className="text-xs text-gray-400 mt-3">
              Job Hazard Analyses help identify potential hazards and establish safe work procedures.
            </p>
          )}
        </div>
      </div>
    </div>
  );
};
