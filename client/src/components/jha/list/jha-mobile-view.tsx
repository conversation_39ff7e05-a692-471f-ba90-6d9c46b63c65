import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { ArchiveConfirmationDialog } from '@/components/composite/archive-confirmation-dialog';
import { JhaStatusBadge } from '@/components/composite/jha-status-badge';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useAnalytics } from '@/hooks/use-analytics';
import { usePermissions } from '@/hooks/use-permissions';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { RouterOutputs } from '@shared/types/router.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { format } from 'date-fns';
import { Archive, Calendar, Eye, HardHat, MapPin, MoreHorizontal, Pencil, Users, AlertTriangle } from 'lucide-react';
import { useState } from 'react';
import { useLocation } from 'wouter';

export const JhaMobileView = ({ jhas }: { jhas: RouterOutputs['jha']['list']['result'] }) => {
  const [_, navigate] = useLocation();
  const analytics = useAnalytics();
  const { hasPermission } = usePermissions();
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const [selectedJha, setSelectedJha] = useState<RouterOutputs['jha']['list']['result'][number] | null>(null);

  const handleRowAction = (jhaId: string, action: 'View' | 'Edit') => {
    analytics.track(ANALYTICS_EVENTS.JHA.ROW_ACTION_CLICKED, {
      jha_id: jhaId,
      action,
    });
  };

  const handleArchiveClick = (e: React.MouseEvent, jha: RouterOutputs['jha']['list']['result'][number]) => {
    e.stopPropagation();
    setSelectedJha(jha);
    setShowArchiveConfirm(true);
  };

  const handleEditClick = (e: React.MouseEvent, jhaId: string) => {
    e.stopPropagation();
    handleRowAction(jhaId, 'Edit');
    navigate(ROUTES.BUILD_JHA_EDIT_PATH(jhaId));
  };

  return (
    <div className="space-y-3">
      {selectedJha && (
        <ArchiveConfirmationDialog
          archived={!!selectedJha.archivedAt}
          showDialog={showArchiveConfirm}
          setShowDialog={setShowArchiveConfirm}
          entityId={selectedJha.instanceId!}
          entityType="jha"
        />
      )}
      {jhas.map((jha) => {
        const archived = !!jha.archivedAt;
        return (
          <Card
            key={jha.instanceId}
            className={`cursor-pointer transition-colors hover:bg-muted/50 ${
              archived ? 'bg-amber-50/50 border-amber-200' : ''
            }`}
            onClick={() => {
              handleRowAction(jha.instanceId!, 'View');
              navigate(ROUTES.BUILD_JHA_DETAILS_PATH(jha.instanceId!));
            }}
          >
            <CardContent className="p-4">
              {/* Header with ID, Status, and Actions */}
              <div className="flex justify-between items-start mb-3">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm">{jha.slug}</span>
                  <JhaStatusBadge status={jha.status} />
                  {archived && (
                    <Badge className="bg-amber-50 text-amber-600 border-amber-200" variant="outline">
                      <Archive className="h-3 w-3 mr-1" />
                      Archived
                    </Badge>
                  )}
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={(e) => e.stopPropagation()}>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRowAction(jha.instanceId!, 'View');
                        navigate(ROUTES.BUILD_JHA_DETAILS_PATH(jha.instanceId!));
                      }}
                    >
                      <Eye className="mr-2 h-4 w-4" />
                      View Details
                    </DropdownMenuItem>
                    {hasPermission(MODULES.EHS_JHA, ALLOWED_ACTIONS.EDIT) && (
                      <DropdownMenuItem onClick={(e) => handleEditClick(e, jha.instanceId!)}>
                        <Pencil className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                    )}
                    {hasPermission(MODULES.EHS_JHA, ALLOWED_ACTIONS.EDIT) && (
                      <DropdownMenuItem onClick={(e) => handleArchiveClick(e, jha)}>
                        <Archive className="mr-2 h-4 w-4" />
                        {archived ? 'Unarchive' : 'Archive'}
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {/* Title */}
              <h3 className="font-medium text-base mb-3 line-clamp-2">{jha.title}</h3>

              {/* Details Grid */}
              <div className="space-y-2 text-sm">
                {/* Owner */}
                <div className="flex items-center">
                  <Users className="h-4 w-4 mr-2 text-muted-foreground flex-shrink-0" />
                  <span className="text-muted-foreground min-w-[80px]">Owner:</span>
                  <span className="truncate">
                    {jha.owner ? `${jha.owner.firstName} ${jha.owner.lastName}` : 'Unassigned'}
                  </span>
                </div>

                {/* Location */}
                <div className="flex items-center">
                  <MapPin className="h-4 w-4 mr-2 text-muted-foreground flex-shrink-0" />
                  <span className="text-muted-foreground min-w-[80px]">Location:</span>
                  <span className="truncate">{jha.location?.name || 'No location'}</span>
                </div>

                {/* Assets */}
                <div className="flex items-center">
                  <HardHat className="h-4 w-4 mr-2 text-muted-foreground flex-shrink-0" />
                  <span className="text-muted-foreground min-w-[80px]">Assets:</span>
                  <span className="truncate">
                    {jha.assets && jha.assets.length > 0
                      ? jha.assets.length === 1
                        ? jha.assets[0].name
                        : `${jha.assets[0].name} +${jha.assets.length - 1} more`
                      : 'No assets'}
                  </span>
                </div>

                {/* Review Date */}
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-2 text-muted-foreground flex-shrink-0" />
                  <span className="text-muted-foreground min-w-[80px]">Review Due:</span>
                  <span>{jha.reviewDate ? format(new Date(jha.reviewDate), 'MMM d, yyyy') : 'No review date'}</span>
                </div>

                {/* Risk and Steps */}
                <div className="flex items-center justify-between pt-2">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                    <span className="text-muted-foreground text-xs">Risk:</span>
                    {jha.highestSeverity}
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-muted-foreground text-xs">Steps:</span>
                    <Badge variant="secondary" className="text-xs">
                      {jha.stepCount || 0}
                    </Badge>
                  </div>
                </div>

                {/* Last Revised */}
                <div className="flex items-center justify-between pt-1 text-xs text-muted-foreground">
                  <span>
                    Last revised: {jha.lastRevised ? format(new Date(jha.lastRevised), 'MMM d, yyyy') : 'Never'}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};
