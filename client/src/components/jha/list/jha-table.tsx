import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { ArchiveConfirmationDialog } from '@/components/composite/archive-confirmation-dialog';
import { JhaStatusBadge } from '@/components/composite/jha-status-badge';
import { JhaRiskBadge } from '@/components/jha/jha-risk-badge';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useAnalytics } from '@/hooks/use-analytics';
import { usePermissions } from '@/hooks/use-permissions';
import { formatDate } from '@shared/date-utils';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { approvalStatusEnum } from '@shared/schema';
import { RouterOutputs } from '@shared/types/router.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { Archive, HardHat, MapPin, MoreHorizontal, Pencil, Users } from 'lucide-react';
import { useState } from 'react';
import { useLocation } from 'wouter';

export const JhaTable = ({ jhas }: { jhas: RouterOutputs['jha']['list']['result'] }) => {
  const [_, navigate] = useLocation();
  const analytics = useAnalytics();
  const { hasPermission } = usePermissions();
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const [selectedJha, setSelectedJha] = useState<RouterOutputs['jha']['list']['result'][number] | null>(null);

  // Helper function to track row actions
  const handleRowAction = (jhaId: string, action: 'View' | 'Edit' | 'Archive') => {
    analytics.track(ANALYTICS_EVENTS.JHA.ROW_ACTION_CLICKED, {
      jha_id: jhaId,
      action,
    });
  };

  const handleArchiveClick = (e: React.MouseEvent, jha: RouterOutputs['jha']['list']['result'][number]) => {
    e.stopPropagation();
    setSelectedJha(jha);
    setShowArchiveConfirm(true);
  };

  const handleEditClick = (e: React.MouseEvent, jhaId: string) => {
    e.stopPropagation();
    handleRowAction(jhaId, 'Edit');
    navigate(ROUTES.BUILD_JHA_EDIT_PATH(jhaId));
  };

  return (
    <div className="overflow-hidden">
      {selectedJha && (
        <ArchiveConfirmationDialog
          archived={!!selectedJha.archivedAt}
          showDialog={showArchiveConfirm}
          setShowDialog={setShowArchiveConfirm}
          entityId={selectedJha.instanceId!}
          entityType="jha"
        />
      )}
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="max-w-80">ID / Title</TableHead>
            <TableHead className="w-32">Owner</TableHead>
            <TableHead className="w-28">Status</TableHead>
            <TableHead className="w-36">Next Review Due</TableHead>
            <TableHead>Location</TableHead>
            <TableHead>Asset</TableHead>
            <TableHead className="w-28">Highest Risk</TableHead>
            <TableHead className="w-20 text-center">Steps</TableHead>
            <TableHead className="w-36">Last Revised</TableHead>
            <TableHead className="w-32 text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {jhas.map((jha) => {
            const archived = !!jha.archivedAt;
            return (
              <TableRow
                key={jha.id}
                className={`cursor-pointer hover:bg-muted/50 ${archived ? 'bg-amber-50/50 hover:bg-amber-50/80' : ''}`}
                onClick={() => {
                  handleRowAction(jha.instanceId!, 'View');
                  navigate(ROUTES.BUILD_JHA_DETAILS_PATH(jha.instanceId!));
                }}
              >
                <TableCell className="max-w-80">
                  <div>
                    <div className="flex items-center gap-1">
                      <span className="font-medium truncate">{jha.slug}</span>
                      {archived && (
                        <Badge className="bg-amber-50 text-amber-600 border-amber-200" variant="outline">
                          <Archive className="h-3 w-3 mr-1" />
                          Archived
                        </Badge>
                      )}
                    </div>
                    <div className="text-sm text-muted-foreground line-clamp-1 truncate">{jha.title}</div>
                  </div>
                </TableCell>
                <TableCell className="w-32">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="flex items-center">
                          <Users className="h-3 w-3 mr-1" />
                          <span className="truncate">
                            {jha.owner ? `${jha.owner.firstName} ${jha.owner.lastName}` : 'Unassigned'}
                          </span>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{jha.owner ? `${jha.owner.firstName} ${jha.owner.lastName}` : 'Unassigned'}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </TableCell>
                <TableCell className="w-28">
                  <JhaStatusBadge status={jha.status} />
                </TableCell>
                <TableCell className="w-36">
                  {jha.reviewDate ? (
                    <span className="text-sm">{formatDate(jha.reviewDate, true)}</span>
                  ) : (
                    <span className="text-muted-foreground text-sm">No review date</span>
                  )}
                </TableCell>
                <TableCell>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="flex items-center">
                          <MapPin className="h-3 w-3 mr-1" />
                          <span className="truncate">{jha.location?.name || 'No location'}</span>
                        </div>
                      </TooltipTrigger>
                      {jha.location && (
                        <TooltipContent>
                          <p>{jha.location?.name}</p>
                        </TooltipContent>
                      )}
                    </Tooltip>
                  </TooltipProvider>
                </TableCell>
                <TableCell>
                  <div className="flex items-center">
                    <HardHat className="h-3 w-3 mr-1" />
                    {jha.assets && jha.assets.length > 0 ? (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="truncate">
                              {jha.assets.length === 1
                                ? jha.assets[0].name
                                : `${jha.assets[0].name} +${jha.assets.length - 1} more`}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="max-w-xs">
                              {jha.assets.map((asset, index) => (
                                <p key={asset.id}>
                                  {index > 0 && ', '}
                                  {asset.name}
                                </p>
                              ))}
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    ) : (
                      <span className="text-muted-foreground">No assets</span>
                    )}
                  </div>
                </TableCell>
                <TableCell className="w-28">
                  <JhaRiskBadge risk={jha.highestSeverity} />
                </TableCell>
                <TableCell className="w-20 text-center">
                  <Badge variant="secondary" className="text-xs">
                    {jha.stepCount || 0}
                  </Badge>
                </TableCell>
                <TableCell className="w-36">
                  <span className="text-sm">{formatDate(jha.lastRevised, true)}</span>
                </TableCell>
                <TableCell className="w-32 text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={(e) => {
                          e.stopPropagation();
                          handleRowAction(jha.instanceId!, 'View');
                          navigate(ROUTES.BUILD_JHA_DETAILS_PATH(jha.instanceId!));
                        }}
                      >
                        <HardHat className="mr-2 h-4 w-4" />
                        View Details
                      </DropdownMenuItem>
                      {hasPermission(MODULES.EHS_JHA, ALLOWED_ACTIONS.EDIT) &&
                        jha.status === approvalStatusEnum.enumValues[0] && (
                          <DropdownMenuItem onClick={(e) => handleEditClick(e, jha.instanceId!)}>
                            <Pencil className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                        )}
                      {hasPermission(MODULES.EHS_JHA, ALLOWED_ACTIONS.EDIT) && (
                        <DropdownMenuItem onClick={(e) => handleArchiveClick(e, jha)}>
                          <Archive className="mr-2 h-4 w-4" />
                          {archived ? 'Unarchive' : 'Archive'}
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
};
