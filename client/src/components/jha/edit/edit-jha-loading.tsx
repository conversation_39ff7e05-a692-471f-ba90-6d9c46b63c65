import { Skeleton } from '@/components/ui/skeleton';

export const EditJhaLoading = () => {
  return (
    <div className="px-4 sm:px-6 md:px-8 lg:px-10 py-6 sm:py-10">
      <div>
        {/* Header section */}
        <div className="border-b pb-4 mb-4">
          <Skeleton className="h-8 w-80 mb-2" /> {/* Title */}
          <Skeleton className="h-5 w-96" /> {/* Description */}
        </div>

        <form className="space-y-8">
          {/* Basic Information Section */}
          <div className="space-y-6">
            <div className="border-b pb-4">
              <Skeleton className="h-6 w-48" /> {/* Section title */}
              <Skeleton className="h-4 w-80 mt-1" /> {/* Section description */}
            </div>

            {/* JHA Title - Full width */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-20" /> {/* Label */}
              <Skeleton className="h-10 w-full" /> {/* Input */}
              <Skeleton className="h-4 w-3/4" /> {/* Description */}
            </div>

            {/* Owner and Approver - Responsive grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Skeleton className="h-4 w-16" /> {/* Owner label */}
                <Skeleton className="h-10 w-full" /> {/* Owner select */}
                <Skeleton className="h-4 w-5/6" /> {/* Owner description */}
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-20" /> {/* Approver label */}
                <Skeleton className="h-10 w-full" /> {/* Approver select */}
                <Skeleton className="h-4 w-4/5" /> {/* Approver description */}
              </div>
            </div>

            {/* Location, Assets, and Review Date - Responsive grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 items-start">
              <div className="space-y-2">
                <Skeleton className="h-4 w-20" /> {/* Location label */}
                <Skeleton className="h-10 w-full" /> {/* Location select */}
                <Skeleton className="h-4 w-4/5" /> {/* Location description */}
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-32" /> {/* Assets label */}
                <Skeleton className="h-10 w-full" /> {/* Assets select */}
                <Skeleton className="h-4 w-3/4" /> {/* Assets description */}
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" /> {/* Review Date label */}
                <Skeleton className="h-10 w-full" /> {/* Date picker */}
                <Skeleton className="h-4 w-5/6" /> {/* Review Date description */}
              </div>
            </div>
          </div>

          {/* Task Steps & Risk Assessment Section */}
          <div>
            <div className="pb-4">
              <Skeleton className="h-6 w-64" /> {/* Section title */}
              <Skeleton className="h-4 w-full mt-1" /> {/* Section description line 1 */}
              <Skeleton className="h-4 w-4/5 mt-1" /> {/* Section description line 2 */}
            </div>

            {/* Steps skeleton - simulate a few steps */}
            <div className="space-y-4">
              {[1, 2, 3].map((step) => (
                <div key={step} className="border rounded-lg p-4 space-y-4">
                  <div className="flex items-center justify-between">
                    <Skeleton className="h-5 w-24" /> {/* Step title */}
                    <Skeleton className="h-8 w-20" /> {/* Step actions */}
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-20" /> {/* Field label */}
                      <Skeleton className="h-10 w-full" /> {/* Field input */}
                    </div>
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-24" /> {/* Field label */}
                      <Skeleton className="h-10 w-full" /> {/* Field input */}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-28" /> {/* Description label */}
                    <Skeleton className="h-20 w-full" /> {/* Description textarea */}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Additional Notes Section */}
          <div className="space-y-6">
            <div className="border-b pb-4">
              <Skeleton className="h-6 w-40" /> {/* Section title */}
              <Skeleton className="h-4 w-80 mt-1" /> {/* Section description */}
            </div>

            <div className="space-y-2">
              <Skeleton className="h-20 w-full" /> {/* Notes textarea */}
            </div>
          </div>

          {/* Footer with checkbox and buttons */}
          <div className="flex justify-between items-center border-t pt-8">
            <div className="flex items-center gap-2">
              <Skeleton className="h-4 w-4" /> {/* Checkbox */}
              <Skeleton className="h-4 w-24" /> {/* Make Public label */}
              <Skeleton className="h-4 w-4" /> {/* Info icon */}
            </div>
            <div className="flex justify-end space-x-4">
              <Skeleton className="h-10 w-16" /> {/* Reset button */}
              <Skeleton className="h-10 w-28" /> {/* Submit button */}
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};
