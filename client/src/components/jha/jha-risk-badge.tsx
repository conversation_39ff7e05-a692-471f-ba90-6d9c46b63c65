import { Badge } from '@/components/ui/badge';
import { getRiskLevel } from '@shared/types/jha.types';
import { AlertCircle, Flag } from 'lucide-react';

export const JhaRiskScoreBadge = ({ score }: { score: number }) => {
  return (
    <Badge className={getRiskLevel(score).color} variant="outline">
      {score}
    </Badge>
  );
};

export const JhaRiskLevelBadge = ({ score }: { score: number }) => {
  return (
    <Badge className={getRiskLevel(score).color} variant="outline">
      {getRiskLevel(score).level}
    </Badge>
  );
};

export const JhaRiskBadge = ({ risk }: { risk?: number | null }) => {
  if (!risk) {
    return null;
  }

  const riskLevel = getRiskLevel(risk);

  // Render icon based on iconType
  const renderIcon = () => {
    switch (riskLevel.iconType) {
      case 'alert':
        return <AlertCircle className="h-3 w-3 mr-1" />;
      case 'flag':
        return <Flag className="h-3 w-3 mr-1" />;
      default:
        return null;
    }
  };

  return (
    <Badge className={riskLevel.color} variant="outline">
      {renderIcon()}
      {riskLevel.level} [{risk}]
    </Badge>
  );
};
