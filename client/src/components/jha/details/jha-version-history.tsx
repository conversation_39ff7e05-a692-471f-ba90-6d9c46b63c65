import { JhaStatusBadge } from '@/components/composite/jha-status-badge';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { trpc } from '@/providers/trpc';
import { formatDate } from '@shared/date-utils';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { Eye, History } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { useLocation } from 'wouter';

export const JhaVersionHistory = ({ jhaInstanceId, jhaTitle }: { jhaInstanceId: string; jhaTitle?: string }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [_, navigate] = useLocation();

  const { data: jhaVersions, isLoading } = trpc.jha.getVersions.useQuery({
    id: jhaInstanceId,
  });

  const handleViewVersion = ({
    instanceId,
    versionId,
    isLatest,
  }: {
    instanceId: string | null;
    versionId: string;
    isLatest: boolean;
  }) => {
    if (!instanceId) {
      toast.error('Something went wrong', {
        description: 'Failed to view version history',
      });
      return;
    }

    if (isLatest) {
      navigate(ROUTES.BUILD_JHA_DETAILS_PATH(instanceId));
    } else {
      navigate(ROUTES.BUILD_JHA_DETAILS_PATH(instanceId, versionId));
    }

    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <div>
          <Button variant="outline" size="sm" className="hidden md:flex">
            <History className="h-4 w-4 mr-2" />
            Revision History
          </Button>
          <div className="flex items-center gap-2 cursor-pointer md:hidden px-2 py-1.5 text-sm text-foreground">
            <History className="mr-2 h-4 w-4 text-muted-foreground" />
            <span>Revision History</span>
          </div>
        </div>
      </DialogTrigger>
      <DialogContent className="max-w-6xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            JHA Revision History{jhaTitle ? `: ${jhaTitle}` : ''}
          </DialogTitle>
          <p className="text-sm text-muted-foreground">
            Complete version history and audit trail for this Job Hazard Analysis document.
          </p>
        </DialogHeader>

        <div className="overflow-auto">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-sm text-muted-foreground">Loading version history...</div>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Version</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Date/Time</TableHead>
                  <TableHead>Revised By</TableHead>
                  <TableHead>Reason for Change</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {jhaVersions && jhaVersions.length > 0 ? (
                  jhaVersions.map((version, index) => (
                    <TableRow key={version.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {index === 0 && <Badge>v{version.version}</Badge>}
                          {index !== 0 && <Badge variant="outline">v{version.version}</Badge>}
                        </div>
                      </TableCell>
                      <TableCell>
                        {index === 0 && <JhaStatusBadge status={version.status} />}
                        {index !== 0 && (
                          <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                            Inactive
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="text-sm font-medium">
                            {version.updatedAt ? formatDate(new Date(version.updatedAt), true) : 'not set'}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {version.updatedAt ? formatDate(new Date(version.updatedAt)).split(' at ')[1] : ''}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm font-medium">{version.createdBy?.fullName}</div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">{version.reasonForRevision}</div>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            handleViewVersion({
                              instanceId: version.instanceId,
                              versionId: version.id,
                              isLatest: index === 0,
                            })
                          }
                          className="h-8 px-3"
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                      No version history available
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
