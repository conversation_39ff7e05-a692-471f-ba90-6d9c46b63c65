import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { FileText, Sparkles, Upload, Zap } from 'lucide-react';
import { AnimatePresence, motion } from 'motion/react';
import { useCallback, useRef, useState } from 'react';
import { toast } from 'sonner';

type JhaFileInputProps = {
  onUpload: (documentBase64: string, filename: string) => void;
  isParsing?: boolean;
};

// Supported file types (currently only PDF as per the API schema)
const SUPPORTED_FILE_TYPES = ['application/pdf'];

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

export function JhaFileInput({ onUpload, isParsing }: JhaFileInputProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): string | null => {
    if (!SUPPORTED_FILE_TYPES.includes(file.type)) {
      return 'Unsupported file type. Please upload PDF files only.';
    }

    if (file.size > MAX_FILE_SIZE) {
      return 'File size too large. Maximum file size is 5MB.';
    }

    return null;
  };

  const handleFileSelect = useCallback(
    async (file: File) => {
      const validationError = validateFile(file);
      if (validationError) {
        toast.error('Invalid File', {
          description: validationError,
        });
        return;
      }

      setUploadedFile(file);

      try {
        // Convert file to base64
        const buffer = await file.arrayBuffer();
        const fileBase64 = btoa(new Uint8Array(buffer).reduce((data, byte) => data + String.fromCharCode(byte), ''));

        // Send to parsing API
        onUpload(fileBase64, file.name);
      } catch (error) {
        console.error('Error processing file:', error);
        toast.error('File Processing Error', {
          description: 'Failed to process the file. Please try again.',
        });
        setUploadedFile(null);
      }
    },
    [onUpload],
  );

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragOver(false);

      const files = Array.from(e.dataTransfer.files);
      if (files.length > 0) {
        handleFileSelect(files[0]);
      }
    },
    [handleFileSelect],
  );

  const handleFileInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = e.target.files;
      if (files && files.length > 0) {
        handleFileSelect(files[0]);
      }
    },
    [handleFileSelect],
  );

  const handleBrowseClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.includes('pdf')) return '📄';
    if (fileType.includes('word') || fileType.includes('document')) return '📝';
    if (fileType.includes('text')) return '📄';
    if (fileType.includes('image')) return '🖼️';
    return '📄';
  };

  return (
    <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.3 }}>
      {/* File Upload Area */}
      <div
        className={cn(
          'border-2 border-dashed rounded-lg p-6 text-center transition-all duration-200 cursor-pointer relative',
          isDragOver
            ? 'border-green-400 bg-green-50/50'
            : 'border-green-200 hover:border-green-300 hover:bg-green-50/30',
          isParsing && 'pointer-events-none opacity-60',
        )}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={!isParsing ? handleBrowseClick : undefined}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept=".pdf"
          onChange={handleFileInputChange}
          className="hidden"
          disabled={isParsing}
        />

        <AnimatePresence mode="wait">
          {isParsing ? (
            <motion.div
              key="parsing"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="space-y-3"
            >
              <div className="flex justify-center">
                <div className="relative">
                  <div className="absolute inset-0 animate-ping opacity-20 bg-green-400 rounded-full" />
                  <div
                    className="absolute inset-0 animate-ping opacity-40 bg-green-500 rounded-full"
                    style={{ animationDelay: '0.5s' }}
                  />
                  <div className="absolute inset-0 animate-pulse opacity-60 bg-green-300 rounded-full" />
                  <Sparkles
                    className="h-10 w-10 text-green-600 relative z-10 animate-spin"
                    style={{ animationDuration: '3s' }}
                  />
                </div>
              </div>
              <div className="space-y-1">
                <h3 className="text-base font-semibold text-gray-900">AI Analyzing Document</h3>
                <p className="text-sm text-green-700">
                  Our AI is parsing your document and extracting relevant information. This usually takes 1-2 minutes...
                </p>
                <div className="flex items-center justify-center gap-1 text-xs text-green-600">
                  <Zap className="h-3 w-3 animate-pulse" />
                  <span className="animate-pulse">Processing content</span>
                </div>
              </div>
            </motion.div>
          ) : uploadedFile ? (
            <motion.div
              key="uploaded"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="space-y-3"
            >
              <div className="flex justify-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                  <FileText className="w-6 h-6 text-green-600" />
                </div>
              </div>
              <div className="space-y-2">
                <h3 className="text-base font-medium text-gray-900">File Ready for Processing</h3>
                <div className="bg-green-50 border border-green-200 rounded-md p-2 text-left">
                  <div className="flex items-center space-x-2">
                    <span className="text-base">{getFileIcon(uploadedFile.type)}</span>
                    <div>
                      <p className="text-sm font-medium text-gray-900">{uploadedFile.name}</p>
                      <p className="text-xs text-gray-500">{formatFileSize(uploadedFile.size)}</p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ) : (
            <motion.div
              key="upload"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="space-y-3"
            >
              <div className="flex justify-center">
                <div
                  className={cn(
                    'w-12 h-12 rounded-full flex items-center justify-center transition-colors',
                    isDragOver ? 'bg-green-200' : 'bg-green-100',
                  )}
                >
                  <Upload
                    className={cn('w-6 h-6 transition-colors', isDragOver ? 'text-green-700' : 'text-green-600')}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <h3 className="text-base font-medium text-gray-900">
                  Drag & drop your JHA file here, or browse to upload
                </h3>
                <p className="text-sm text-gray-600">Our AI will analyze it and pre-fill the fields below. Processing usually takes 1-2 minutes.</p>
              </div>
              <Button type="button" variant="outline" className="border-green-300 text-green-700 hover:bg-green-50">
                <FileText className="w-4 h-4 mr-2" />
                Browse Files{' '}
                <p className="text-xs text-gray-400">
                  <strong>PDF only</strong> • Max 5MB
                </p>
              </Button>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  );
}
