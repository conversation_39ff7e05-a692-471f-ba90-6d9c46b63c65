import { StatusBadge } from '@/components/composite/status-badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { useInfiniteCapas } from '@/hooks/use-paginated-data';
import { usePermissions } from '@/hooks/use-permissions';
import { ROUTES } from '@shared/ROUTE_PATHS';
import type { RouterOutputs } from '@shared/types/router.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { ClipboardCheck } from 'lucide-react';
import { useLocation } from 'wouter';

export const LinkedCapas = ({ eventId }: { eventId: string }) => {
  const [_, navigate] = useLocation();
  const { hasPermission } = usePermissions();

  const { data: capas } = useInfiniteCapas({
    filters: {
      status: [],
      type: [],
      priority: [],
      owner: [],
      dueDateRange: undefined,
      includeArchived: false,
      tags: [],
      eventId,
    },
    enabled: true,
  });

  const canCreateCapas = hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.CREATE);

  const truthyCapas = capas && capas.length > 0;

  if (!canCreateCapas && !truthyCapas) {
    return null;
  }

  return (
    <Card>
      {canCreateCapas && (
        <CardHeader>
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">Linked CAPAs</h2>
            <Button variant="outline" size="sm" onClick={() => navigate(ROUTES.CAPA_NEW + `?eventId=${eventId}`)}>
              <ClipboardCheck className="h-4 w-4 mr-2" />
              Create CAPA
            </Button>
          </div>
        </CardHeader>
      )}
      <CardContent>
        {truthyCapas && (
          <div className="space-y-3">
            {capas.map((capa: RouterOutputs['capa']['list']['result'][number]) => (
              <div key={capa.id} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="text-blue-600 font-medium text-sm">
                        {capa.slug}: {capa.title}
                      </h3>
                    </div>
                    <div className="flex items-center gap-4 text-xs text-gray-600">
                      <StatusBadge status={capa.status} />
                      <span>Owner: {capa.owner?.fullName}</span>
                    </div>
                  </div>
                  <Button variant="outline" size="sm" onClick={() => navigate(ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id))}>
                    View
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
