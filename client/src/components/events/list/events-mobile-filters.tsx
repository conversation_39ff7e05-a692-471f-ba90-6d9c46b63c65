import { AsyncLocationsFilter } from '@/components/composite/async-locations-filter';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Label } from '@/components/ui/label';
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { reportTypeEnum, severityEnum, statusEnum } from '@shared/schema';
import { EventsFilters } from '@shared/types/events.types';
import {
  REPORT_TYPE_MAP,
  ReportTypeSchema,
  SEVERITY_MAP,
  SeveritySchema,
  STATUS_MAP,
  StatusSchema,
} from '@shared/types/schema.types';
import { Archive, ChevronDown, Filter, Flag } from 'lucide-react';
import z from 'zod';

export const MobileFilters = ({
  toggleFilter,
  filters,
  updateFilters,
  activeFilterCount,
  resetFilters,
  trackFilterApplied,
}: {
  toggleFilter: (
    type: 'status' | 'type' | 'severity',
    value: z.infer<typeof StatusSchema> | z.infer<typeof ReportTypeSchema> | z.infer<typeof SeveritySchema>,
  ) => void;
  filters: EventsFilters;
  updateFilters: (updates: Partial<EventsFilters>) => void;
  activeFilterCount: number;
  resetFilters: () => void;
  trackFilterApplied: (
    type: 'status' | 'type' | 'severity' | 'locationIds' | 'oshaReportable' | 'includeArchived',
    value: string,
  ) => void;
}) => {
  // Handle OSHA reportable toggle with tri-state logic
  const handleOshaToggle = () => {
    const newValue = filters.oshaReportable === undefined ? true : filters.oshaReportable === true ? false : undefined;

    trackFilterApplied('oshaReportable', newValue?.toString() ?? 'undefined');
    updateFilters({ oshaReportable: newValue });
  };

  // Handle include archived toggle
  const handleArchiveToggle = () => {
    const newValue = !filters.includeArchived;

    trackFilterApplied('includeArchived', newValue.toString());
    updateFilters({ includeArchived: newValue });
  };

  return (
    <div className="md:hidden">
      <Sheet>
        <SheetTrigger asChild>
          <Button variant="outline" size={activeFilterCount > 0 ? 'default' : 'icon'}>
            <Filter className="h-4 w-4" />
            {activeFilterCount > 0 && (
              <span className="text-xs bg-primary text-primary-foreground rounded-full w-4 h-4 flex items-center justify-center">
                {activeFilterCount}
              </span>
            )}
          </Button>
        </SheetTrigger>
        <SheetContent side="right">
          <SheetHeader>
            <SheetTitle>Filter Safety Events</SheetTitle>
            <SheetDescription>Apply filters to narrow down your safety event list.</SheetDescription>
          </SheetHeader>

          <div className="p-4 space-y-4">
            {/* Status Filter */}
            <Collapsible defaultOpen className="bg-muted rounded-md p-3">
              <CollapsibleTrigger className="flex items-center justify-between w-full">
                <h3 className="text-sm font-medium">Status</h3>
                <ChevronDown className="h-4 w-4 transition-transform ui-open:rotate-180" />
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-2 space-y-2">
                {statusEnum.enumValues.map((status) => (
                  <div key={status} className="flex items-center space-x-2">
                    <Checkbox
                      id={`status-${status}-mobile`}
                      checked={filters.status?.includes(status)}
                      onCheckedChange={() => {
                        toggleFilter('status', status);
                        trackFilterApplied('status', status);
                      }}
                    />
                    <label htmlFor={`status-${status}-mobile`} className="text-sm cursor-pointer">
                      {STATUS_MAP[status]}
                    </label>
                  </div>
                ))}
              </CollapsibleContent>
            </Collapsible>

            {/* Type Filter */}
            <Collapsible defaultOpen className="bg-muted rounded-md p-3">
              <CollapsibleTrigger className="flex items-center justify-between w-full">
                <h3 className="text-sm font-medium">Type</h3>
                <ChevronDown className="h-4 w-4 transition-transform ui-open:rotate-180" />
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-2 space-y-2">
                {reportTypeEnum.enumValues.map((type) => (
                  <div key={type} className="flex items-center space-x-2">
                    <Checkbox
                      id={`type-${type}-mobile`}
                      checked={filters.type?.includes(type)}
                      onCheckedChange={() => {
                        toggleFilter('type', type);
                        trackFilterApplied('type', type);
                      }}
                    />
                    <label htmlFor={`type-${type}-mobile`} className="text-sm cursor-pointer">
                      {REPORT_TYPE_MAP[type]}
                    </label>
                  </div>
                ))}
              </CollapsibleContent>
            </Collapsible>

            {/* Location Filter */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Location</Label>
              <AsyncLocationsFilter
                selected={filters.locationIds}
                onSelect={(locationIds) => {
                  updateFilters({ locationIds });
                  trackFilterApplied('locationIds', locationIds.join(','));
                }}
                className="w-full"
                label=""
              />
            </div>

            {/* Severity Filter */}
            <Collapsible defaultOpen className="bg-muted rounded-md p-3">
              <CollapsibleTrigger className="flex items-center justify-between w-full">
                <h3 className="text-sm font-medium">Severity</h3>
                <ChevronDown className="h-4 w-4 transition-transform ui-open:rotate-180" />
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-2 space-y-2">
                {severityEnum.enumValues.map((severity) => (
                  <div key={severity} className="flex items-center space-x-2">
                    <Checkbox
                      id={`severity-${severity}-mobile`}
                      checked={filters.severity?.includes(severity)}
                      onCheckedChange={() => {
                        toggleFilter('severity', severity);
                        trackFilterApplied('severity', severity);
                      }}
                    />
                    <label htmlFor={`severity-${severity}-mobile`} className="text-sm cursor-pointer">
                      {SEVERITY_MAP[severity]}
                    </label>
                  </div>
                ))}
              </CollapsibleContent>
            </Collapsible>

            {/* OSHA Reportable Filter */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">OSHA Reportable</Label>
              <Button
                variant={filters.oshaReportable !== undefined ? 'default' : 'outline'}
                size="sm"
                onClick={handleOshaToggle}
                className="justify-start w-full"
              >
                <Flag className="h-4 w-4 mr-2" />
                OSHA Reportable
                {filters.oshaReportable !== undefined && (
                  <span className="ml-2 text-xs">({filters.oshaReportable ? 'Yes' : 'No'})</span>
                )}
              </Button>
            </div>

            {/* Include Archived Filter */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Archive Status</Label>
              <Button
                variant={filters.includeArchived ? 'default' : 'outline'}
                size="sm"
                onClick={handleArchiveToggle}
                className={`justify-start w-full ${filters.includeArchived ? 'bg-amber-600 hover:bg-amber-700' : ''}`}
              >
                <Archive className="h-4 w-4 mr-2" />
                Include Archived
              </Button>
            </div>
          </div>

          <SheetFooter className="pt-4">
            <SheetClose asChild>
              <Button variant="outline" className="w-full" onClick={resetFilters}>
                Clear Filters
              </Button>
            </SheetClose>
            <SheetClose asChild>
              <Button className="w-full">Apply Filters ({activeFilterCount})</Button>
            </SheetClose>
          </SheetFooter>
        </SheetContent>
      </Sheet>
    </div>
  );
};
