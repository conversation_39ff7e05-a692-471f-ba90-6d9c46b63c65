import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { CapaTags } from '@/components/capas/list/capa-tags';
import { CapaTypeBadge } from '@/components/capas/list/capa-type-badge';
import { PriorityBadge } from '@/components/capas/list/priority-badge';
import { ArchiveConfirmationDialog } from '@/components/composite/archive-confirmation-dialog';
import { ArchivedBadge } from '@/components/composite/archived-badge';
import { StatusBadge } from '@/components/composite/status-badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useAnalytics } from '@/hooks/use-analytics';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { capaPriorityEnum, capaTypeEnum, statusEnum } from '@shared/schema';
import { RouterOutputs } from '@shared/types/router.types';
import { format } from 'date-fns';
import { Archive, Calendar, Copy, Eye, Link, MoreHorizontal, Pencil, Trash2, User } from 'lucide-react';
import { useState } from 'react';
import { useLocation } from 'wouter';

export const CapaTable = ({ capas }: { capas?: RouterOutputs['capa']['list']['result'] }) => {
  const [_, navigate] = useLocation();
  const analytics = useAnalytics();
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const [selectedCapa, setSelectedCapa] = useState<RouterOutputs['capa']['list']['result'][number] | null>(null);
  const handleRowAction = (capaId: string, action: 'View' | 'Edit' | 'Duplicate') => {
    analytics.track(ANALYTICS_EVENTS.CAPA.ROW_ACTION_CLICKED, {
      capa_id: capaId,
      action,
    });
  };

  return (
    <div className="overflow-hidden">
      {selectedCapa && (
        <ArchiveConfirmationDialog
          archived={!!selectedCapa.archivedAt}
          showDialog={showArchiveConfirm}
          setShowDialog={setShowArchiveConfirm}
          entityId={selectedCapa.id}
          entityType="capa"
        />
      )}
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="max-w-80">CAPA</TableHead>
            <TableHead className="w-24">Type</TableHead>
            <TableHead className="w-24">Status</TableHead>
            <TableHead className="w-24">Priority</TableHead>
            <TableHead className="w-40">Linked Safety Event</TableHead>
            <TableHead className="w-32">Owner</TableHead>
            <TableHead className="w-32">Due Date</TableHead>
            <TableHead className="w-32">Tags</TableHead>
            <TableHead className="w-32 text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {capas?.map((capa: RouterOutputs['capa']['list']['result'][number]) => {
            return (
              <TableRow
                key={capa.id}
                className={`cursor-pointer ${!!capa.archivedAt ? 'bg-amber-50/50 hover:bg-amber-50/80' : ''}`}
                onClick={() => {
                  handleRowAction(capa.id, 'View');
                  navigate(ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id));
                }}
              >
                <TableCell className="max-w-80">
                  <div className="flex flex-col">
                    <div className="flex items-center gap-1">
                      <div className="font-medium truncate">{capa.slug}</div>
                      {!!capa.archivedAt && <ArchivedBadge />}
                    </div>
                    <div className="text-sm text-muted-foreground line-clamp-1 truncate">{capa.title}</div>
                  </div>
                </TableCell>
                <TableCell className="w-24">
                  <CapaTypeBadge type={capa.type as (typeof capaTypeEnum.enumValues)[number]} />
                </TableCell>
                <TableCell className="w-24">
                  <StatusBadge status={capa.status as (typeof statusEnum.enumValues)[number]} />
                </TableCell>
                <TableCell className="w-24">
                  <PriorityBadge priority={capa.priority as (typeof capaPriorityEnum.enumValues)[number]} />
                </TableCell>
                <TableCell className="w-40">
                  {capa.eventId ? (
                    <div
                      className="text-blue-600 font-medium flex items-center"
                      onClick={(e) => {
                        e.stopPropagation();
                        analytics.track(ANALYTICS_EVENTS.EVENT.ROW_ACTION_CLICKED, {
                          event_id: capa.eventId!,
                          action: 'View',
                        });
                        navigate(ROUTES.BUILD_EVENT_DETAILS_PATH(capa.eventId!));
                      }}
                    >
                      <Link className="h-4 w-4 mr-1.5" />
                      <span className="truncate">{capa.eventSlug}</span>
                    </div>
                  ) : (
                    <span className="text-muted-foreground">—</span>
                  )}
                </TableCell>
                <TableCell className="w-32">
                  <div className="flex items-center">
                    <User className="h-4 w-4 mr-1.5 text-gray-400" />
                    <span className="truncate">{capa?.owner?.fullName || 'Unknown User'}</span>
                  </div>
                </TableCell>
                <TableCell className="w-32">
                  {capa.dueDate ? (
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1.5 text-gray-400" />
                      {format(new Date(capa.dueDate), 'MMM d, yyyy')}
                    </div>
                  ) : (
                    <span className="text-gray-400">No due date</span>
                  )}
                </TableCell>
                <TableCell className="w-32">{capa.tags && capa.tags.length > 0 && <CapaTags tags={capa.tags} />}</TableCell>
                <TableCell className="w-32 text-right">
                  <div className="flex items-center justify-end space-x-1" onClick={(e) => e.stopPropagation()}>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRowAction(capa.id, 'View');
                        navigate(ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id));
                      }}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRowAction(capa.id, 'Edit');
                        navigate(ROUTES.BUILD_CAPA_EDIT_PATH(capa.id));
                      }}
                    >
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" onClick={(e) => e.stopPropagation()}>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRowAction(capa.id, 'Duplicate');
                            navigate(ROUTES.BUILD_CAPA_DUPLICATE_PATH(capa.id));
                          }}
                        >
                          <>
                            <Copy className="h-4 w-4 mr-2" />
                            Duplicate
                          </>
                        </DropdownMenuItem>
                        <Separator className="my-2" />
                        <DropdownMenuItem
                          className={!!capa.archivedAt ? 'text-amber-600' : 'text-red-600'}
                          onClick={async (e) => {
                            e.stopPropagation();
                            setSelectedCapa(capa);
                            setShowArchiveConfirm(true);
                          }}
                        >
                          {!!capa.archivedAt ? (
                            <>
                              <Archive className="h-4 w-4 mr-2 text-amber-600" />
                              Unarchive
                            </>
                          ) : (
                            <>
                              <Trash2 className="h-4 w-4 mr-2 text-red-600" />
                              Archive
                            </>
                          )}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
};
