import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { capaTagsEnum } from '@shared/schema';
import { CAPA_TAGS_MAP } from '@shared/types/capas.types';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { useState } from 'react';

export const CapaTags = ({ tags }: { tags: string[] }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const maxVisibleTags = 1;
  const hasMoreTags = tags.length > maxVisibleTags;
  const visibleTags = isExpanded ? tags : tags.slice(0, maxVisibleTags);
  const hiddenCount = tags.length - maxVisibleTags;

  return (
    <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
      <div className="flex flex-wrap gap-1 items-center">
        {visibleTags.map((tag) => (
          <Badge key={tag} variant="secondary" className="bg-gray-100 text-xs px-1.5 py-0">
            {CAPA_TAGS_MAP[tag as (typeof capaTagsEnum.enumValues)[number]]}
          </Badge>
        ))}
        {hasMoreTags && (
          <CollapsibleTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-5 px-1 text-xs text-muted-foreground hover:text-foreground"
              onClick={(e) => e.stopPropagation()}
            >
              {isExpanded ? (
                <>
                  <ChevronUp className="h-3 w-3 mr-1" />
                  Less
                </>
              ) : (
                <>
                  <ChevronDown className="h-3 w-3 mr-1" />+{hiddenCount} more
                </>
              )}
            </Button>
          </CollapsibleTrigger>
        )}
      </div>
      <CollapsibleContent>{/* Content is handled by the visible tags logic above */}</CollapsibleContent>
    </Collapsible>
  );
};
