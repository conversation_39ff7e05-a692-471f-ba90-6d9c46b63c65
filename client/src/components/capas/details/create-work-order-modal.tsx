import { But<PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { AsyncUserSelect } from '@/components/composite/async-user-select';
import { trpc } from '@/providers/trpc';
import { zodResolver } from '@hookform/resolvers/zod';
import { CreateWorkOrderFromCapaSchema } from '@shared/types/work-orders.types';
import { RouterOutputs } from '@shared/types/router.types';
import { format } from 'date-fns';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

export const CreateWorkOrderModal = ({
  showWorkOrderModal,
  setShowWorkOrderModal,
  capa,
}: {
  showWorkOrderModal: boolean;
  setShowWorkOrderModal: (show: boolean) => void;
  capa: RouterOutputs['capa']['getById'];
}) => {
  const utils = trpc.useUtils();

  const { mutateAsync: createWorkOrder, isPending: isCreatingWorkOrder } = trpc.workOrder.createFromCapa.useMutation({
    onSuccess: () => {
      utils.workOrder.getByCapa.invalidate({ capaId: [capa.id], cursor: undefined, limit: 10 });
      toast.success('Work Order Created', {
        description: 'The work order has been created successfully.',
      });
      setShowWorkOrderModal(false);
      form.reset();
    },
    onError: () => {
      toast.error('Error creating work order', {
        description: 'There was a problem creating the work order. Please try again.',
      });
    },
  });

  const form = useForm<z.infer<typeof CreateWorkOrderFromCapaSchema>>({
    resolver: zodResolver(CreateWorkOrderFromCapaSchema),
    defaultValues: {
      id: capa.id,
      slug: capa.slug!,
      title: capa.title,
      actionsToAddress: capa.actionsToAddress ?? undefined,
      priority: capa.priority,
      dueDate: capa.dueDate ? format(capa.dueDate, 'yyyy-MM-dd') : undefined,
      locationId: capa.locationId,
      assetId: capa.assetId,
      userAssignedTo: capa.owner?.id,
    },
  });

  const onSubmit = async (values: z.infer<typeof CreateWorkOrderFromCapaSchema>) => {
    try {
      await createWorkOrder({
        ...values,
        locationId: capa.locationId || undefined,
        assetId: capa.assetId || undefined,
        // Use the form's userAssignedTo value instead of overriding it
      });
    } catch (error) {
      // Error handling is done in the mutation's onError callback
      console.error('Error submitting work order', error);
    }
  };

  return (
    <Dialog open={showWorkOrderModal} onOpenChange={setShowWorkOrderModal}>
      <DialogContent className="sm:max-w-[425px] md:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Create Work Order</DialogTitle>
          <DialogDescription>
            Create a work order from this CAPA. The form will be pre-filled with data from the CAPA.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid gap-4 py-4">
              {/* Title */}
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <FormLabel className="text-right font-medium">
                        Title <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input {...field} className="col-span-3" />
                      </FormControl>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Description */}
              <FormField
                control={form.control}
                name="actionsToAddress"
                render={({ field }) => (
                  <FormItem>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <FormLabel className="text-right font-medium">Description</FormLabel>
                      <FormControl>
                        <Textarea
                          {...field}
                          value={
                            field.value ||
                            `This Work Order was created from ${capa.slug || 'CAPA'}\n\n${capa.actionsToAddress}`
                          }
                          className="col-span-3 min-h-[100px]"
                        />
                      </FormControl>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Priority */}
              <FormField
                control={form.control}
                name="priority"
                render={({ field }) => (
                  <FormItem>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <FormLabel className="text-right font-medium">Priority</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value || 'medium'}>
                        <FormControl>
                          <SelectTrigger className="col-span-3">
                            <SelectValue placeholder="Select priority" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="low">Low</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="high">High</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Location */}
              <FormField
                control={form.control}
                name="locationId"
                render={({ field }) => (
                  <FormItem>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <FormLabel className="text-right font-medium">Location</FormLabel>
                      <FormControl>
                        <Input {...field} value={capa.location?.name || ''} className="col-span-3" disabled readOnly />
                      </FormControl>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Asset */}
              <FormField
                control={form.control}
                name="assetId"
                render={({ field }) => (
                  <FormItem>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <FormLabel className="text-right font-medium">Asset</FormLabel>
                      <FormControl>
                        <Input {...field} value={capa.asset?.name || ''} className="col-span-3" disabled readOnly />
                      </FormControl>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Due Date */}
              <FormField
                control={form.control}
                name="dueDate"
                render={({ field }) => (
                  <FormItem>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <FormLabel className="text-right font-medium">Due Date</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} value={field.value || ''} className="col-span-3" />
                      </FormControl>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Primary Assignee */}
              <FormField
                control={form.control}
                name="userAssignedTo"
                render={({ field }) => (
                  <FormItem>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <FormLabel className="text-right font-medium">Primary Assignee</FormLabel>
                      <FormControl>
                        <AsyncUserSelect
                          value={field.value}
                          onChange={field.onChange}
                          placeholder="Select assignee..."
                          className="col-span-3"
                          mustIncludeObjectIds={capa.owner?.id ? [capa.owner.id] : undefined}
                          includeAllUserTypes={true}
                        />
                      </FormControl>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button variant="outline" type="button" onClick={() => setShowWorkOrderModal(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={isCreatingWorkOrder}>
                {isCreatingWorkOrder ? 'Creating...' : 'Create Work Order'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
