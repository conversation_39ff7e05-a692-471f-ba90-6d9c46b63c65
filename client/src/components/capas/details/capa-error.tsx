import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { AlertCircle } from 'lucide-react';
import { useLocation } from 'wouter';

export const CapaError = ({
  title = 'Error Loading CAPA',
  description = "We couldn't load the requested CAPA. Please try again later.",
  showReturnButton = true,
  onRetry,
}: {
  title?: string;
  description?: string;
  showReturnButton?: boolean;
  onRetry?: () => void;
}) => {
  const [, navigate] = useLocation();

  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center text-center p-6">
            <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
            <h2 className="text-2xl font-bold mb-2">{title}</h2>
            <p className="text-gray-600 mb-6">{description}</p>

            <div className="flex gap-3">
              {onRetry && (
                <Button variant="outline" onClick={onRetry}>
                  Try Again
                </Button>
              )}

              {showReturnButton && <Button onClick={() => navigate(ROUTES.CAPA_LIST)}>Return to CAPA Tracker</Button>}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
