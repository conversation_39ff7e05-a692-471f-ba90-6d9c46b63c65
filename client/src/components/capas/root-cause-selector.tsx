import { Badge } from '@/components/ui/badge';
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { rootCauseEnum } from '@shared/schema';
import { ROOT_CAUSE_MAP } from '@shared/types/capas.types';
import { Check } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Control, FieldPath, FieldValues, UseFormSetValue } from 'react-hook-form';

interface RootCauseSelectorProps<T extends FieldValues> {
  control: Control<T>;
  setValue: UseFormSetValue<T>;
  rootCausesFieldName: FieldPath<T>;
  otherRootCauseFieldName: FieldPath<T>;
  label?: string;
  description?: string;
  autoFilledFields?: string[];
}

export function RootCauseSelector<T extends FieldValues>({
  control,
  setValue,
  rootCausesFieldName,
  otherRootCauseFieldName,
  label = 'Identified Root Cause Categories',
  description = 'Select all applicable root cause categories identified in your analysis. Multiple causes can contribute to an event.',
  autoFilledFields = [],
}: RootCauseSelectorProps<T>) {
  const [selectedRootCauses, setSelectedRootCauses] = useState<string[]>([]);

  // Handle root cause selection
  const handleRootCauseToggle = (rootCause: string, _currentValue: string[]) => {
    const isSelected = selectedRootCauses.includes(rootCause);
    let newRootCauses: string[];

    if (isSelected) {
      // Remove root cause
      newRootCauses = selectedRootCauses.filter((cause) => cause !== rootCause);
      // Clear otherRootCause if 'other' is removed
      if (rootCause === 'other') {
        // shouldDirty: true - Ensures React Hook Form knows the field was modified programmatically
        // This maintains consistent form state for change detection and submit button behavior
        setValue(otherRootCauseFieldName, '' as never, { shouldDirty: true });
      }
    } else {
      // Add root cause
      newRootCauses = [...selectedRootCauses, rootCause];
    }

    // Update both the UI state and form value
    setSelectedRootCauses(newRootCauses);
    // shouldDirty: true - Ensures React Hook Form knows the field was modified programmatically
    // This maintains consistent form state for change detection and submit button behavior
    setValue(rootCausesFieldName, newRootCauses as never, { shouldDirty: true });
  };

  return (
    <div className="space-y-4">
      {/* Root Cause Categories */}
      <FormField
        control={control}
        name={rootCausesFieldName}
        render={({ field }) => {
          // Sync selected root causes when form value changes
          useEffect(() => {
            if (field.value && Array.isArray(field.value)) {
              const validRootCauses = field.value.filter(
                (cause: unknown): cause is string => typeof cause === 'string',
              );
              if (JSON.stringify(validRootCauses.sort()) !== JSON.stringify(selectedRootCauses.sort())) {
                setSelectedRootCauses(validRootCauses);
              }
            }
          }, [field.value]);

          return (
            <FormItem className={autoFilledFields.includes(rootCausesFieldName) ? 'relative' : ''}>
              {autoFilledFields.includes(rootCausesFieldName) && (
                <div className="absolute -inset-1 rounded-md bg-indigo-50/50 border border-indigo-200 -z-10" />
              )}
              <FormLabel>{label}</FormLabel>
              <div className="mt-2 flex flex-wrap gap-2">
                {rootCauseEnum.enumValues.map((cause) => (
                  <Badge
                    key={cause}
                    size="lg"
                    className={cn(
                      'cursor-pointer',
                      selectedRootCauses.includes(cause)
                        ? 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                        : 'bg-gray-100 text-gray-800 hover:bg-gray-200',
                    )}
                    onClick={() => handleRootCauseToggle(cause, field.value)}
                  >
                    {selectedRootCauses.includes(cause) && <Check className="size-4" />}
                    {ROOT_CAUSE_MAP[cause]}
                  </Badge>
                ))}
              </div>
              <FormDescription>{description}</FormDescription>
              <FormMessage />
            </FormItem>
          );
        }}
      />

      {/* Other Root Cause Input - only show when "other" is selected */}
      {selectedRootCauses.includes('other') && (
        <FormField
          control={control}
          name={otherRootCauseFieldName}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Specify Other Root Cause</FormLabel>
              <FormControl>
                <Input
                  value={(field.value as string) || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                  placeholder="Describe the root cause"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}
    </div>
  );
}
