import { ANALYTICS_EVENTS } from '../event-names';

export interface CAPAEvents {
  // CAPA Creation & Form Interaction Events (from CSV spec)
  [ANALYTICS_EVENTS.CAPA.CREATE_INITIATED]: {
    source?: 'global_create' | 'event_detail_page' | 'event_log_row_action' | 'capa_log_row_duplicate';
  };

  [ANALYTICS_EVENTS.CAPA.FORM_VIEWED]: {
    source?:
      | 'global_create'
      | 'event_detail_page'
      | 'event_log_row_action'
      | 'event_log_row_action'
      | 'capa_log_row_duplicate';
  };

  [ANALYTICS_EVENTS.CAPA.VOICE_STARTED]: {};

  [ANALYTICS_EVENTS.CAPA.VOICE_SUCCESSFUL]: {
    duration_ms?: number;
    fields_populated_count?: number;
  };

  [ANALYTICS_EVENTS.CAPA.VOICE_FAILED]: {
    reason?: string;
    duration_ms?: number;
  };

  [ANALYTICS_EVENTS.CAPA.FIELD_AI_POPULATED]: {
    field_name?: string;
    was_edited?: boolean;
    ai_confidence_score?: number;
  };

  [ANALYTICS_EVENTS.CAPA.FORM_VALIDATION_FAILED]: {
    validation_errors?: string[];
  };

  [ANALYTICS_EVENTS.CAPA.FORM_SAVED_DRAFT]: {
    capa_id?: string;
  };

  [ANALYTICS_EVENTS.CAPA.FORM_SUBMITTED]: {
    capa_id?: string;
    event_id?: string;
    capa_type?: 'Corrective' | 'Preventive';
    location?: string;
    owner_id?: string;
    priority?: string;
    tags?: string[];
    is_private_to_admins?: boolean;
    media_attached_count?: number;
    is_ai_assisted?: boolean;
  };

  [ANALYTICS_EVENTS.CAPA.FORM_ABANDONED]: {
    source?: string;
    duration_on_form_seconds?: number;
  };

  // CAPA View & Interaction Events (from CSV spec)
  [ANALYTICS_EVENTS.CAPA.TRACKER_VIEW_OPENED]: {
    default_sort_by?: string;
    default_sort_order?: string;
  };

  [ANALYTICS_EVENTS.CAPA.DETAIL_VIEW_OPENED]: {
    capa_id?: string;
    current_status?: string;
    owner_id?: string;
    priority?: string;
    is_private_to_admins?: boolean;
    entry_point?: 'tracker_table' | 'notification_link' | 'direct_url' | 'event_link';
  };

  [ANALYTICS_EVENTS.CAPA.FILTER_APPLIED]: {
    filter_name?: string;
    filter_value?: string | string[];
    include_archived_toggle_state?: boolean;
  };

  [ANALYTICS_EVENTS.CAPA.FILTER_RESET]: {};

  [ANALYTICS_EVENTS.CAPA.SEARCH_PERFORMED]: {
    search_term?: string;
    result_count?: number;
  };

  [ANALYTICS_EVENTS.CAPA.ROW_ACTION_CLICKED]: {
    capa_id?: string;
    action?: 'View' | 'Edit' | 'Duplicate' | 'Archive';
  };

  [ANALYTICS_EVENTS.CAPA.EDIT_INITIATED]: {
    capa_id?: string;
    source?: 'detail_page' | 'tracker_table';
  };

  [ANALYTICS_EVENTS.CAPA.ARCHIVED]: {
    capa_id?: string;
    source?: 'detail_page' | 'tracker_table';
  };

  [ANALYTICS_EVENTS.CAPA.MARKED_COMPLETE]: {
    capa_id?: string;
    previous_status?: 'Open';
    user_role_trigger?: 'Admin' | 'Technician';
  };

  [ANALYTICS_EVENTS.CAPA.PRINT_TRIGGERED]: {
    capa_id?: string;
  };

  [ANALYTICS_EVENTS.CAPA.EXPORT_TRIGGERED]: {
    export_format?: 'PDF' | 'CSV';
    capa_id?: string;
    source?: 'detail_page' | 'tracker_table';
  };

  // CAPA Status & Workflow Events (from CSV spec)
  [ANALYTICS_EVENTS.CAPA.STATUS_CHANGED]: {
    capa_id?: string;
    previous_status?: string;
    new_status?: string;
    triggered_by_role?: 'admin' | 'user' | 'view-only' | 'unknown';
    trigger_action?: 'Save Draft' | 'Submit' | 'Mark Complete' | 'Verify' | 'System - Overdue';
  };

  [ANALYTICS_EVENTS.CAPA.WORK_ORDER_CREATED]: {
    capa_id?: string;
    work_order_id?: string;
    trigger_point?: 'detail_page' | 'tracker_menu';
  };
}
