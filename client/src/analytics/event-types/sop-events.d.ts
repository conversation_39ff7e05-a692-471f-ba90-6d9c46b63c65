import { ANALYTICS_EVENTS } from '../event-names';

export interface SopEvents {
  // SOP Creation & Form Interaction Events
  [ANALYTICS_EVENTS.SOP.CREATE_INITIATED]: {
    source?: 'global_create' | 'sop_detail_page' | 'sop_log_row_action' | 'sop_log_row_duplicate' | 'sop_log';
  };

  [ANALYTICS_EVENTS.SOP.FORM_VIEWED]: {
    source?: 'global_create' | 'sop_detail_page' | 'sop_log_row_action';
  };

  [ANALYTICS_EVENTS.SOP.VOICE_STARTED]: {};

  [ANALYTICS_EVENTS.SOP.VOICE_SUCCESSFUL]: {
    duration_ms?: number;
    fields_populated_count?: number;
  };

  [ANALYTICS_EVENTS.SOP.VOICE_FAILED]: {
    reason?: 'no_speech_detected' | 'mic_denied' | 'api_error';
    duration_ms?: number;
  };

  [ANALYTICS_EVENTS.SOP.FIELD_AI_POPULATED]: {
    field_name?: string;
    was_edited?: boolean;
    ai_confidence_score?: number;
  };

  [ANALYTICS_EVENTS.SOP.FORM_VALIDATION_FAILED]: {
    validation_errors?: string[];
  };

  [ANALYTICS_EVENTS.SOP.FORM_SAVED_DRAFT]: {
    sop_id?: string;
  };

  [ANALYTICS_EVENTS.SOP.FORM_SUBMITTED]: {
    sop_id?: string;
    procedure_name?: string;
    location?: string;
    department?: string;
    tags?: string[];
    steps_count?: number;
    safety_requirements_count?: number;
    equipment_required_count?: number;
    is_ai_assisted?: boolean;
  };

  [ANALYTICS_EVENTS.SOP.FORM_ABANDONED]: {
    source?: string;
    duration_on_form_seconds?: number;
  };

  // SOP View & Interaction Events
  [ANALYTICS_EVENTS.SOP.LOG_VIEW_OPENED]: {
    default_sort_by?: string;
    default_sort_order?: string;
  };

  [ANALYTICS_EVENTS.SOP.DETAIL_VIEW_OPENED]: {
    sop_id?: string;
    instance_id?: string;
    current_status?: string;
    version?: string;
    department?: string;
    entry_point?: 'log_table' | 'notification_link' | 'direct_url' | 'search_results';
  };

  [ANALYTICS_EVENTS.SOP.FILTER_APPLIED]: {
    filter_name?: string;
    filter_value?: string | string[];
    include_archived_toggle_state?: boolean;
  };

  [ANALYTICS_EVENTS.SOP.FILTER_RESET]: {};

  [ANALYTICS_EVENTS.SOP.SEARCH_PERFORMED]: {
    search_term?: string;
    result_count?: number;
  };

  [ANALYTICS_EVENTS.SOP.ROW_ACTION_CLICKED]: {
    sop_id?: string;
    action?: 'View' | 'Edit' | 'Archive' | 'Approve' | 'Request Review' | 'Duplicate';
  };

  [ANALYTICS_EVENTS.SOP.EDIT_INITIATED]: {
    sop_id?: string;
    source?: 'detail_page' | 'log_table';
  };

  [ANALYTICS_EVENTS.SOP.ARCHIVED]: {
    sop_id?: string;
    instance_id?: string;
    version?: string;
  };

  [ANALYTICS_EVENTS.SOP.APPROVED]: {
    sop_id?: string;
    instance_id?: string;
    approver_id?: string;
    version?: string;
  };

  [ANALYTICS_EVENTS.SOP.REJECTED]: {
    sop_id?: string;
    instance_id?: string;
    approver_id?: string;
    version?: string;
  };

  [ANALYTICS_EVENTS.SOP.REVIEW_REQUESTED]: {
    sop_id?: string;
    instance_id?: string;
    reviewer_id?: string;
    version?: string;
  };

  [ANALYTICS_EVENTS.SOP.REVISION_REQUESTED]: {
    sop_id?: string;
    instance_id?: string;
    reviewer_id?: string;
    revision_reason?: string;
    version?: string;
  };

  [ANALYTICS_EVENTS.SOP.PRINT_TRIGGERED]: {
    sop_id?: string;
    instance_id?: string;
    version?: string;
  };

  [ANALYTICS_EVENTS.SOP.EXPORT_TRIGGERED]: {
    sop_id?: string;
    format?: string;
  };

  // SOP Status & Workflow Events
  [ANALYTICS_EVENTS.SOP.STATUS_CHANGED]: {
    sop_id?: string;
    from_status?: string;
    to_status?: string;
  };

  [ANALYTICS_EVENTS.SOP.REVIEW_DATE_UPDATED]: {
    sop_id?: string;
    new_review_date?: string;
  };

  [ANALYTICS_EVENTS.SOP.STEP_ADDED]: {
    sop_id?: string;
    step_number?: number;
  };

  [ANALYTICS_EVENTS.SOP.STEP_REMOVED]: {
    sop_id?: string;
    step_number?: number;
  };

  [ANALYTICS_EVENTS.SOP.SAFETY_REQUIREMENT_ADDED]: {
    sop_id?: string;
    requirement_type?: string;
  };

  [ANALYTICS_EVENTS.SOP.EQUIPMENT_ADDED]: {
    sop_id?: string;
    equipment_type?: string;
  };
}
