import { ANALYTICS_EVENTS } from '../event-names';

export interface JhaEvents {
  // JHA Creation & Form Interaction Events
  [ANALYTICS_EVENTS.JHA.CREATE_INITIATED]: {
    source?: 'global_create' | 'jha_detail_page' | 'jha_log_row_action' | 'jha_log_row_duplicate';
  };

  [ANALYTICS_EVENTS.JHA.FORM_VIEWED]: {
    source?: 'global_create' | 'event_detail_page' | 'event_log_row_action' | 'jha_log';
  };

  [ANALYTICS_EVENTS.JHA.VOICE_STARTED]: {};

  [ANALYTICS_EVENTS.JHA.VOICE_SUCCESSFUL]: {
    duration_ms?: number;
    fields_populated_count?: number;
  };

  [ANALYTICS_EVENTS.JHA.VOICE_FAILED]: {
    reason?: 'no_speech_detected' | 'mic_denied' | 'api_error';
    duration_ms?: number;
  };

  [ANALYTICS_EVENTS.JHA.FIELD_AI_POPULATED]: {
    field_name?: string;
    was_edited?: boolean;
    ai_confidence_score?: number;
  };

  [ANALYTICS_EVENTS.JHA.FORM_VALIDATION_FAILED]: {
    validation_errors?: string[];
  };

  [ANALYTICS_EVENTS.JHA.FORM_SAVED_DRAFT]: {
    jha_id?: string;
  };

  [ANALYTICS_EVENTS.JHA.FORM_SUBMITTED]: {
    jha_id?: string;
    job_name?: string;
    location?: string;
    lead_person_id?: string;
    priority?: string;
    tags?: string[];
    steps_count?: number;
    hazards_identified_count?: number;
    control_measures_count?: number;
    is_ai_assisted?: boolean;
  };

  [ANALYTICS_EVENTS.JHA.FORM_ABANDONED]: {
    source?: string;
    duration_on_form_seconds?: number;
  };

  // JHA View & Interaction Events
  [ANALYTICS_EVENTS.JHA.LOG_VIEW_OPENED]: {
    default_sort_by?: string;
    default_sort_order?: string;
  };

  [ANALYTICS_EVENTS.JHA.DETAIL_VIEW_OPENED]: {
    jha_id?: string;
    current_status?: string;
    lead_person_id?: string;
    priority?: string;
    entry_point?: 'log_table' | 'notification_link' | 'direct_url' | 'event_link';
  };

  [ANALYTICS_EVENTS.JHA.FILTER_APPLIED]: {
    filter_name?: string;
    filter_value?: string | string[];
    include_archived_toggle_state?: boolean;
  };

  [ANALYTICS_EVENTS.JHA.FILTER_RESET]: {};

  [ANALYTICS_EVENTS.JHA.SEARCH_PERFORMED]: {
    search_term?: string;
    result_count?: number;
  };

  [ANALYTICS_EVENTS.JHA.ROW_ACTION_CLICKED]: {
    jha_id?: string;
    action?: 'View' | 'Edit' | 'Archive' | 'Approve' | 'Request Review';
  };

  [ANALYTICS_EVENTS.JHA.EDIT_INITIATED]: {
    jha_id?: string;
    source?: 'detail_page' | 'log_table';
  };

  [ANALYTICS_EVENTS.JHA.ARCHIVED]: {
    jha_id?: string;
  };

  [ANALYTICS_EVENTS.JHA.APPROVED]: {
    jha_id?: string;
    approver_id?: string;
  };

  [ANALYTICS_EVENTS.JHA.REVIEW_REQUESTED]: {
    jha_id?: string;
    reviewer_id?: string;
  };

  [ANALYTICS_EVENTS.JHA.REVISION_REQUESTED]: {
    jha_id?: string;
    reviewer_id?: string;
    revision_reason?: string;
  };

  [ANALYTICS_EVENTS.JHA.PRINT_TRIGGERED]: {
    jha_id?: string;
  };

  [ANALYTICS_EVENTS.JHA.EXPORT_TRIGGERED]: {
    jha_id?: string;
    format?: string;
  };

  // JHA Status & Workflow Events
  [ANALYTICS_EVENTS.JHA.STATUS_CHANGED]: {
    jha_id?: string;
    from_status?: string;
    to_status?: string;
  };

  [ANALYTICS_EVENTS.JHA.REVIEW_DATE_UPDATED]: {
    jha_id?: string;
    new_review_date?: string;
  };

  [ANALYTICS_EVENTS.JHA.STEP_ADDED]: {
    jha_id?: string;
    step_number?: number;
  };

  [ANALYTICS_EVENTS.JHA.STEP_REMOVED]: {
    jha_id?: string;
    step_number?: number;
  };

  [ANALYTICS_EVENTS.JHA.HAZARD_IDENTIFIED]: {
    jha_id?: string;
    step_number?: number;
    hazard_type?: string;
  };

  [ANALYTICS_EVENTS.JHA.CONTROL_MEASURE_ADDED]: {
    jha_id?: string;
    step_number?: number;
    control_type?: string;
  };
}
