/**
 * Analytics Event Name Constants
 *
 * This file contains all analytics event names as constants to prevent typos
 * and provide better IDE support. Event names are organized by domain and
 * match exactly with the event types defined in the event-types folder.
 *
 * Usage:
 * analytics.track(ANALYTICS_EVENTS.EVENT.FORM_VIEWED, { ... });
 *
 * Instead of:
 * analytics.track('event.form_viewed', { ... });
 */

export const ANALYTICS_EVENTS = {
  /**
   * Event-related analytics events
   * Covers event creation, form interactions, voice features, and management
   */
  EVENT: {
    // Event Creation & Form Interaction Events
    FORM_VIEWED: 'event.form_viewed',
    VOICE_STARTED: 'event.voice_started',
    VOICE_SUCCESSFUL: 'event.voice_successful',
    VOICE_FAILED: 'event.voice_failed',
    FIELD_AI_POPULATED: 'event.field_ai_populated',
    FORM_VALIDATION_FAILED: 'event.form_validation_failed',
    FORM_SUBMITTED: 'event.form_submitted',
    VOICE_TO_FORM_ABANDONED: 'event.voice_to_form_abandoned',
    FORM_ABANDONED: 'event.form_abandoned',

    // Event View & Interaction Events
    LOG_VIEW_OPENED: 'event.log_view_opened',
    DETAIL_VIEW_OPENED: 'event.detail_view_opened',
    FILTER_APPLIED: 'event.filter_applied',
    FILTER_RESET: 'event.filter_reset',
    SEARCH_PERFORMED: 'event.search_performed',
    ROW_ACTION_CLICKED: 'event.row_action_clicked',
    ACTION_TAKEN: 'event.action_taken',
    EDIT_INITIATED: 'event.edit_initiated',
    CAPA_CREATED_FROM_EVENT: 'event.capa_created_from_event',
    CAPA_CREATED_FROM_CAPA: 'event.capa_created_from_capa',
    OSHA_LOG_CREATED_FROM_EVENT: 'event.osha_log_created_from_event',
    REVIEWED: 'event.reviewed',
    CLOSED_WITHOUT_ACTION: 'event.closed_without_action',
    ARCHIVED: 'event.archived',
    PRINT_TRIGGERED: 'event.print_triggered',
    EXPORT_TRIGGERED: 'event.export_triggered',
  },

  /**
   * CAPA (Corrective and Preventive Action) related analytics events
   * Covers CAPA creation, form interactions, tracking, and workflow management
   */
  CAPA: {
    // CAPA Creation & Form Interaction Events
    CREATE_INITIATED: 'capa.create_initiated',
    FORM_VIEWED: 'capa.form_viewed',
    VOICE_STARTED: 'capa.voice_started',
    VOICE_SUCCESSFUL: 'capa.voice_successful',
    VOICE_FAILED: 'capa.voice_failed',
    FIELD_AI_POPULATED: 'capa.field_ai_populated',
    FORM_VALIDATION_FAILED: 'capa.form_validation_failed',
    FORM_SAVED_DRAFT: 'capa.form_saved_draft',
    FORM_SUBMITTED: 'capa.form_submitted',
    FORM_ABANDONED: 'capa.form_abandoned',

    // CAPA View & Interaction Events
    TRACKER_VIEW_OPENED: 'capa.tracker_view_opened',
    DETAIL_VIEW_OPENED: 'capa.detail_view_opened',
    FILTER_APPLIED: 'capa.filter_applied',
    FILTER_RESET: 'capa.filter_reset',
    SEARCH_PERFORMED: 'capa.search_performed',
    ROW_ACTION_CLICKED: 'capa.row_action_clicked',
    EDIT_INITIATED: 'capa.edit_initiated',
    ARCHIVED: 'capa.archived',
    MARKED_COMPLETE: 'capa.marked_complete',
    PRINT_TRIGGERED: 'capa.print_triggered',
    EXPORT_TRIGGERED: 'capa.export_triggered',

    // CAPA Status & Workflow Events
    STATUS_CHANGED: 'capa.status_changed',
    WORK_ORDER_CREATED: 'capa.work_order_created',
  },

  /**
   * JHA (Job Hazard Analysis) related analytics events
   * Covers JHA creation, form interactions, tracking, and workflow management
   */
  JHA: {
    // JHA Creation & Form Interaction Events
    CREATE_INITIATED: 'jha.create_initiated',
    FORM_VIEWED: 'jha.form_viewed',
    VOICE_STARTED: 'jha.voice_started',
    VOICE_SUCCESSFUL: 'jha.voice_successful',
    VOICE_FAILED: 'jha.voice_failed',
    FIELD_AI_POPULATED: 'jha.field_ai_populated',
    FORM_VALIDATION_FAILED: 'jha.form_validation_failed',
    FORM_SAVED_DRAFT: 'jha.form_saved_draft',
    FORM_SUBMITTED: 'jha.form_submitted',
    FORM_ABANDONED: 'jha.form_abandoned',

    // JHA View & Interaction Events
    LOG_VIEW_OPENED: 'jha.log_view_opened',
    DETAIL_VIEW_OPENED: 'jha.detail_view_opened',
    FILTER_APPLIED: 'jha.filter_applied',
    FILTER_RESET: 'jha.filter_reset',
    SEARCH_PERFORMED: 'jha.search_performed',
    ROW_ACTION_CLICKED: 'jha.row_action_clicked',
    EDIT_INITIATED: 'jha.edit_initiated',
    ARCHIVED: 'jha.archived',
    APPROVED: 'jha.approved',
    REVIEW_REQUESTED: 'jha.review_requested',
    REVISION_REQUESTED: 'jha.revision_requested',
    PRINT_TRIGGERED: 'jha.print_triggered',
    EXPORT_TRIGGERED: 'jha.export_triggered',

    // JHA Status & Workflow Events
    STATUS_CHANGED: 'jha.status_changed',
    REVIEW_DATE_UPDATED: 'jha.review_date_updated',
    STEP_ADDED: 'jha.step_added',
    STEP_REMOVED: 'jha.step_removed',
    HAZARD_IDENTIFIED: 'jha.hazard_identified',
    CONTROL_MEASURE_ADDED: 'jha.control_measure_added',
  },

  /**
   * SOP (Standard Operating Procedure) related analytics events
   * Covers SOP creation, form interactions, tracking, and workflow management
   */
  SOP: {
    // SOP Creation & Form Interaction Events
    CREATE_INITIATED: 'sop.create_initiated',
    FORM_VIEWED: 'sop.form_viewed',
    VOICE_STARTED: 'sop.voice_started',
    VOICE_SUCCESSFUL: 'sop.voice_successful',
    VOICE_FAILED: 'sop.voice_failed',
    FIELD_AI_POPULATED: 'sop.field_ai_populated',
    FORM_VALIDATION_FAILED: 'sop.form_validation_failed',
    FORM_SAVED_DRAFT: 'sop.form_saved_draft',
    FORM_SUBMITTED: 'sop.form_submitted',
    FORM_ABANDONED: 'sop.form_abandoned',
    // SOP View & Interaction Events
    LOG_VIEW_OPENED: 'sop.log_view_opened',
    DETAIL_VIEW_OPENED: 'sop.detail_view_opened',
    FILTER_APPLIED: 'sop.filter_applied',
    FILTER_RESET: 'sop.filter_reset',
    SEARCH_PERFORMED: 'sop.search_performed',
    ROW_ACTION_CLICKED: 'sop.row_action_clicked',
    EDIT_INITIATED: 'sop.edit_initiated',
    ARCHIVED: 'sop.archived',
    APPROVED: 'sop.approved',
    REJECTED: 'sop.rejected',
    REVIEW_REQUESTED: 'sop.review_requested',
    REVISION_REQUESTED: 'sop.revision_requested',
    PRINT_TRIGGERED: 'sop.print_triggered',
    EXPORT_TRIGGERED: 'sop.export_triggered',
    // SOP Status & Workflow Events
    STATUS_CHANGED: 'sop.status_changed',
    REVIEW_DATE_UPDATED: 'sop.review_date_updated',
  },

  /**
   * Access Point (QR Code) related analytics events
   * Covers QR code generation, management, and usage tracking
   */
  ACCESS_POINT: {
    // QR Code Access Point Events
    TABLE_VIEW_OPENED: 'access_point.table_view_opened',
    CREATE_INITIATED: 'access_point.create_initiated',
    CREATED: 'access_point.created',
    DOWNLOADED: 'access_point.downloaded',
    ARCHIVED: 'access_point.archived',
    FILTER_APPLIED: 'access_point.filter_applied',
    SEARCH_PERFORMED: 'access_point.search_performed',

    // Bulk Import Events
    BULK_IMPORT_INITIATED: 'access_point.bulk_import_initiated',
    BULK_IMPORT_SUCCESS: 'access_point.bulk_import_success',
    BULK_IMPORT_ERROR: 'access_point.bulk_import_error',
    BULK_IMPORT_CANCELLED: 'access_point.bulk_import_cancelled',

    // Custom QR Code Usage Events
    EVENT_SUBMITTED_VIA_QR: 'access_point.event_submitted_via_qr',
  },
} as const;

/**
 * Type helper to extract all event names as a union type
 * This ensures type safety when using the constants
 */
export type AnalyticsEventName =
  (typeof ANALYTICS_EVENTS)[keyof typeof ANALYTICS_EVENTS][keyof (typeof ANALYTICS_EVENTS)[keyof typeof ANALYTICS_EVENTS]];
