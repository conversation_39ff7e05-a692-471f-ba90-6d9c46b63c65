import type { Mixpanel } from 'mixpanel-browser';
import { AnalyticsEvents } from './analytics-events';
import { ANALYTICS_EVENTS } from './event-names';
import { ConsentManager } from './consent';
import { User } from '@shared/types/users.types';

// Analytics state management
type AnalyticsState = {
  mixpanel: Mixpanel | null;
  isInitialized: boolean;
  anonymousId: string | null;
  isIdentified: boolean;
  debugEnabled: boolean;
};

const state: AnalyticsState = {
  mixpanel: null,
  isInitialized: false,
  anonymousId: null,
  isIdentified: false,
  debugEnabled: false,
};

const consentManager = ConsentManager();

// Initialize Mixpanel when needed
const initializeMixpanel = async (token: string, debug: boolean = false): Promise<void> => {
  try {
    if (!state.isInitialized && token?.trim()) {
      const { default: mixpanelLib } = await import('mixpanel-browser');
      mixpanelLib.init(token, {
        debug,
        track_pageview: false,
        persistence: 'localStorage',
        ignore_dnt: true,
      });

      state.mixpanel = mixpanelLib;
      state.anonymousId = mixpanelLib.get_distinct_id();
      state.isInitialized = true;
    }

    state.debugEnabled = debug;
    console.log('[EHS Analytics] Mixpanel initialized', { token, debug });
  } catch (error) {
    console.error('[EHS Analytics] Initialization failed:', error);
  }
};

// Session ID management
const SESSION_ID_KEY = 'ehs_analytics_session_id';

const generateSessionId = (): string => `session_${Date.now()}_${Math.random().toString(36).slice(2, 11)}`;

const getSessionId = (): string => {
  if (typeof window === 'undefined' || !window.sessionStorage) {
    return generateSessionId();
  }

  let sessionId = window.sessionStorage.getItem(SESSION_ID_KEY);
  if (!sessionId) {
    sessionId = generateSessionId();
    window.sessionStorage.setItem(SESSION_ID_KEY, sessionId);
  }
  return sessionId;
};

// User data extraction utilities
const getCompliantUserData = (user?: User) => {
  if (!user) return {};

  return {
    user_id: user.id,
    organization_id: user.upkeepCompanyId,
    role: user.role,
    platform: 'web' as const,
  };
};

const getPersonalUserData = (user?: User) => {
  if (!user) return {};

  return {
    email: user.email,
    first_name: user.firstName,
    last_name: user.lastName,
    full_name: user.fullName,
    username: user.username,
    created_at: user.createdAt,
    last_login_at: user.lastLoginAt,
  };
};

// Component mapping and utilities
const COMPONENT_MAP = {
  event: 'Events',
  capa: 'CAPAs',
  access_point: 'QR Access Points',
  jha: 'Job Hazard Analyses',
  notification: 'Notifications',
} as const;

const getComponentFromEvent = (eventName: string): string => {
  for (const [componentKey, events] of Object.entries(ANALYTICS_EVENTS)) {
    const eventValues = Object.values(events) as string[];
    if (eventValues.includes(eventName)) {
      const lowercaseKey = componentKey.toLowerCase();
      return COMPONENT_MAP[lowercaseKey as keyof typeof COMPONENT_MAP] || 'Unknown';
    }
  }
  return 'Unknown';
};

const getBaseEventProperties = (eventName: string, userContext?: User) => {
  if (!userContext) {
    return {
      platform: 'web' as const,
      role: 'Public' as const,
      access_scope: 'public' as const,
    };
  }

  return {
    platform: 'web' as const,
    role: userContext.role,
    organization_id: userContext.upkeepCompanyId,
    component: getComponentFromEvent(eventName),
  };
};

const track = async <T extends keyof AnalyticsEvents>(
  eventName: T,
  properties: AnalyticsEvents[T] = {} as AnalyticsEvents[T],
  userContext?: User,
) => {
  try {
    const consent = consentManager.getConsent();
    const eventNameStr = eventName as string;
    const compliantData = getCompliantUserData(userContext);
    const baseProperties = getBaseEventProperties(eventNameStr, userContext);

    const eventData = {
      ...baseProperties,
      ...compliantData,
      ...properties,
      timestamp: new Date().toISOString(),
      session_id: getSessionId(),
    };

    // Add personal data if consent is granted
    if (consent.analytics && userContext) {
      Object.assign(eventData, getPersonalUserData(userContext));
    }

    // Debug logging
    if (state.debugEnabled) {
      console.log(`[EHS Analytics] Tracking: ${eventNameStr}`, {
        properties: eventData,
        consent_state: consent.analytics ? 'granted' : 'denied',
        has_personal_data: consent.analytics && !!userContext,
        mixpanel_initialized: state.isInitialized,
      });
    }

    // Send to Mixpanel if available
    if (state.isInitialized && state.mixpanel) {
      state.mixpanel.track(`ehs.${eventNameStr}`, eventData);

      if (state.debugEnabled) {
        console.log(`[EHS Analytics] Sent to Mixpanel: ${eventNameStr}`);
      }
    }
  } catch (error) {
    console.error('[EHS Analytics] Track failed:', error);
  }
};

const handleConsentUpdate = async (consentChoices: { analytics?: boolean }, userContext?: User): Promise<void> => {
  try {
    if (!state.mixpanel) return;

    const previousConsent = consentManager.getConsent();
    consentManager.updateConsent(consentChoices);
    const newConsent = consentManager.getConsent();

    if (newConsent.analytics && !previousConsent.analytics && userContext) {
      // User granted consent - link anonymous history
      if (!state.isIdentified && state.anonymousId) {
        state.mixpanel.alias(userContext.id);
      }

      state.mixpanel.identify(userContext.id);

      const userData = {
        ...getCompliantUserData(userContext),
        ...getPersonalUserData(userContext),
      };

      if (Object.keys(userData).length > 0) {
        state.mixpanel.people.set(userData);
      }

      state.isIdentified = true;
    } else if (!newConsent.analytics && previousConsent.analytics) {
      // User revoked consent - reset to anonymous
      state.mixpanel.reset();
      state.isIdentified = false;
      state.anonymousId = state.mixpanel.get_distinct_id();
    } else if (newConsent.analytics && previousConsent.analytics && state.isIdentified && userContext) {
      // Update existing user data
      const userData = {
        ...getCompliantUserData(userContext),
        ...getPersonalUserData(userContext),
      };

      if (Object.keys(userData).length > 0) {
        state.mixpanel.people.set(userData);
      }
    }
  } catch (error) {
    console.error('[EHS Analytics] Consent update failed:', error);
  }
};

// Consent management utilities
const updateConsent = (consent: { analytics?: boolean }): void => {
  try {
    consentManager.updateConsent(consent);
  } catch (error) {
    console.error('[EHS Analytics] Consent update failed:', error);
  }
};

const getConsent = () => {
  try {
    return consentManager.getConsent();
  } catch (error) {
    console.error('[EHS Analytics] Get consent failed:', error);
    return { analytics: true, functional: true, lastUpdated: null };
  }
};

const shouldShowConsentBanner = (): boolean => {
  try {
    return consentManager.shouldShowConsentBanner();
  } catch (error) {
    console.error('[EHS Analytics] Consent banner check failed:', error);
    return false;
  }
};

const initialize = async (token: string, debug: boolean = false): Promise<void> => {
  await initializeMixpanel(token, debug);
};

export const analytics = {
  track,
  handleConsentUpdate,
  updateConsent,
  getConsent,
  shouldShowConsentBanner,
  initialize,
};
